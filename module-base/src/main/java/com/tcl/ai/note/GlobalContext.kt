package com.tcl.ai.note

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import androidx.lifecycle.ViewModelStore
import androidx.lifecycle.ViewModelStoreOwner
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.track.TclAnalytics.reportLaunchTime
import com.tcl.ai.note.utils.AppActivityManager
import com.tcl.ai.note.utils.GlobalExceptionCaughtManager
import com.tcl.ai.note.utils.Logger


import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel

open class GlobalContext : Application(), ViewModelStoreOwner {

    private val appViewModelStore by lazy {
        ViewModelStore()
    }

    override val viewModelStore: ViewModelStore
        get() = appViewModelStore

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(base)
        instance = this
    }

    override fun onCreate() {
        super.onCreate()
        // 初始化全局异常捕获
        GlobalExceptionCaughtManager.init()
        // 初始化埋点
        TclAnalytics.init()
        // 上报冷启动时间埋点
        reportLaunchTime(System.currentTimeMillis().toString())
        registerActivityLifecycleCallbacks(AppActivityManager)
        Logger.d(TAG, "onCreate")
    }

    @Deprecated("Deprecated in Java")
    override fun onLowMemory() {
        Logger.d(TAG, "onLowMemory")
        applicationScope.cancel()
        applicationScope = MainScope()
        super.onLowMemory()
    }

    override fun onTerminate() {
        super.onTerminate()
        appViewModelStore.clear()
        Logger.v(TAG, "onTerminate")
    }

    companion object {
        @SuppressLint("StaticFieldLeak")
        @JvmStatic
        lateinit var instance: GlobalContext
            private set
        @JvmStatic
        val appContext: Context get() = instance.applicationContext
        val screenWidth: Int get() = instance.resources.displayMetrics.widthPixels
        val screenHeight: Int get() = instance.resources.displayMetrics.heightPixels
        val densityDpi: Int get() = instance.resources.displayMetrics.densityDpi
        var applicationScope = MainScope()
        val Application.appScope get() = applicationScope
        val Context.appScope get() = applicationScope
        private const val TAG = "GlobalContext"
    }
}