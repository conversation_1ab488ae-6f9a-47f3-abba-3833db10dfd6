package com.tcl.ai.note.theme

import android.content.res.Configuration
import android.util.Log
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity

/**
 * 根据UI设计图得出动态密度适配不同屏幕
 * @param designWidth 填入UI设计图的屏幕短边dp值（绝对宽度）
 * @param designHeight 填入UI设计图的屏幕长边dp值（绝对高度）
 */
@Composable
internal fun dynamicDensity(designWidth: Float, designHeight: Float): Float {
    val displayMetrics = LocalContext.current.resources.displayMetrics
    val widthPixels = displayMetrics.widthPixels    //屏幕短边像素（绝对宽度）
    val heightPixels = displayMetrics.heightPixels  //屏幕长边像素（绝对高度）
    val isPortrait = LocalContext.current.resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT  //判断横竖屏
    return if (isPortrait) widthPixels / designWidth else heightPixels / designHeight //计算密度
}

/**
 * font 大于1 则是放到了字体 最大1.3f
 */
@Composable
fun getFontScale(): Float {
    val fontScale = LocalDensity.current.fontScale
    Log.d("FontScale", "getFontScale: $fontScale")
    getDisplayMetrics()
    return fontScale
}
@Composable
fun getDisplayMetrics(){
    val displayMetrics = LocalContext.current.resources.displayMetrics
    val isFontScaled = displayMetrics.density != displayMetrics.scaledDensity

    Log.d("DisplayMetrics", "getDisplayMetrics: ${displayMetrics.density} ${displayMetrics.densityDpi} ${displayMetrics.scaledDensity}")
}
