package com.tcl.ai.note.theme

import android.annotation.SuppressLint
import android.app.Activity
import android.util.Log
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.areNavigationBarsVisible
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.Density
import androidx.core.view.WindowInsetsControllerCompat


private val DarkColorScheme = darkColorScheme(
    primary = Purple80,
    secondary = PurpleGrey80,
    tertiary = Pink80
)

private val LightColorScheme = lightColorScheme(
    primary = Purple40,
    secondary = PurpleGrey40,
    tertiary = Pink40
)


@SuppressLint("DesignSystem")
@Composable
fun NoteTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = false,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    TclTheme(darkTheme) {
        MaterialTheme(
            colorScheme = colorScheme,
            typography = Typography,
            content = content
        )
    }
}

/**
 * TCl8.2 主题
 */
@Composable
fun NoteTclTheme(darkTheme: Boolean = isSystemInDarkTheme(), content: @Composable () -> Unit){
    com.tct.theme.core.designsystem.theme.TclTheme(
        darkTheme = darkTheme,
        dynamicColor = false,
    ){
        TclTheme(darkTheme,content)
    }
}

/**
 * 纯净的TCL8.2 主题
 * 包裹单个TCL 8.2 控件使用TclDropdownMenu和TclDialog 这些
 */
@Composable
fun TCLThemeComponent(content: @Composable () -> Unit){
    com.tct.theme.core.designsystem.theme.TclTheme(
        darkTheme = isSystemInDarkTheme(),
        dynamicColor = false,
    ){
        content()
    }
}
/**
 * 这个是一个自定义的Theme，没有给 MaterialTheme colorScheme适配暗黑模式
 */
@Composable
fun TclTheme(darkTheme: Boolean = isSystemInDarkTheme(), content: @Composable () -> Unit) {
    val colorScheme = when {
        darkTheme -> DarkColorPalette
        else -> LightColorPalette
    }
    val fontScale = LocalDensity.current.fontScale
    val appDensity = Density(density = dynamicDensity(360F, 820F), fontScale = LocalDensity.current.fontScale)
    Log.d(TAG, "TclTheme: fontScale: $fontScale  appDensity: $appDensity" )
    CompositionLocalProvider(
        LocalTclComposeColors provides colorScheme,
        LocalContentColor provides colorScheme.tctStanderTextPrimary,
//        LocalDensity provides Density(density = appDensity.density, fontScale = appDensity.fontScale)
    ) {
        Box {
            content()
        }
    }
}
const val TAG= "TclTheme"
/**
 *
 * 设置页面底部导航栏颜色随着用户手机主题变化
 * Compose方案
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun SetNavigationBarColor(darkTheme: Boolean) {
    val areNavigationBarsVisible = WindowInsets.areNavigationBarsVisible
    if(areNavigationBarsVisible){
        val view = LocalView.current
        SideEffect {
            val window = (view.context as? Activity)?.window
            val windowInsetsController = window?.let { WindowInsetsControllerCompat(it, view) }
            //设置导航栏的外观是否为亮色 适配亮色或暗色主题
            windowInsetsController?.isAppearanceLightNavigationBars = !darkTheme
        }
    }

}
