package com.tcl.ai.note.utils

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map

/**
 * Created by tao.ning on 2024/11/6 15:48
 * Describe：DataStoreExt.kt
 */

/**
 * 存放数据
 */
suspend fun <T> DataStore<Preferences>.putData(key: String, value: T) {
    edit {
        when (value) {
            is String -> it[stringPreferencesKey(key)] = value
            is Int -> it[intPreferencesKey(key)] = value
            is Long -> it[longPreferencesKey(key)] = value
            is Float -> it[floatPreferencesKey(key)] = value
            is Double -> it[doublePreferencesKey(key)] = value
            is Boolean -> it[booleanPreferencesKey(key)] = value
            else -> throw IllegalArgumentException("Unsupported data type")
        }
    }
}

suspend fun <T> DataStore<Preferences>.getData(key: String, defaultValue: T): T {
    val preferencesKey = when (defaultValue) {
        is String -> stringPreferencesKey(key)
        is Int -> intPreferencesKey(key)
        is Long -> longPreferencesKey(key)
        is Float -> floatPreferencesKey(key)
        is Double -> doublePreferencesKey(key)
        is Boolean -> booleanPreferencesKey(key)
        else -> throw IllegalArgumentException("Unsupported data type")
    }

    return data.map {
        it[preferencesKey] ?: defaultValue
    }.first() as T
}

suspend fun DataStore<Preferences>.clear() {
    edit { it.clear() }
}
