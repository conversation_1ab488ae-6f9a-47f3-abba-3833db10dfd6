package com.tcl.ai.note.utils

import android.util.DisplayMetrics
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp


/**
 * 手机设计图宽度
 */
const val PHONE_DESIGN_WIDTH = 360f
/**
 * 手机设计图高度
 */
const val PHONE_DESIGN_HEIGHT = 780f

const val PHONE_DESIGN_STATUS_HEIGHT = 38f


/**
 * 屏幕缩放计算的核心函数
 * 封装公共逻辑，避免重复代码
 */
@Composable
private fun rememberScreenScale(
    designSize: Float,
    getScreenSize: (displayMetrics: DisplayMetrics) -> Int
): Float {
    if (isTablet) return 1f // 平板不缩放

    val context = LocalContext.current
    val density = LocalDensity.current.density

    return remember(density) {
        val displayMetrics = context.resources.displayMetrics
        getScreenSize(displayMetrics) / designSize / density
    }
}

/**
 * 根据屏幕宽度缩放的dp值
 * 优化版本：使用remember缓存计算结果，避免重复计算
 */
val Number.dpw: Dp
    @Composable
    get() {
        val widthScale = rememberScreenScale(PHONE_DESIGN_WIDTH) { it.widthPixels }
        return (toFloat() * widthScale).dp
    }

/**
 * 根据屏幕高度缩放的dp值
 * 优化版本：使用remember缓存计算结果，避免重复计算
 */
val Number.dph: Dp
    @Composable
    get() {
        val heightScale = rememberScreenScale(PHONE_DESIGN_HEIGHT) { it.heightPixels }
        return (toFloat() * heightScale).dp
    }

/**
 * 获取屏幕宽度缩放比例的Composable函数
 * 更高效的方式：在组件顶层调用一次，然后传递给子组件使用
 */
@Composable
fun rememberWidthScale(): Float {
    return rememberScreenScale(PHONE_DESIGN_WIDTH) { it.widthPixels }
}

/**
 * 获取屏幕高度缩放比例的Composable函数
 */
@Composable
fun rememberHeightScale(): Float {
    return rememberScreenScale(PHONE_DESIGN_HEIGHT) { it.heightPixels }
}

/**
 * 非Composable版本的缩放计算
 * 适用于在非Compose环境中使用
 */
fun Number.dpwNonCompose(widthScale: Float): Float {
    return toFloat() * widthScale
}

/**
 * 非Composable版本的高度缩放计算
 * 适用于在非Compose环境中使用
 */
fun Number.dphNonCompose(heightScale: Float): Float {
    return toFloat() * heightScale
}
/**
 * 按屏幕缩放的宽度
 */
@Composable
fun globalDialogWidth(): Dp {
    return 336.dpw
}

/**
 * 手机上状态栏高度适配
 */
fun getStatusBarHeightAdapter(statusBarHeight: Dp): Dp {
    if (isTablet) return statusBarHeight
    return maxOf(statusBarHeight, PHONE_DESIGN_STATUS_HEIGHT.dp)
}

/**
 * 手机上 实际的状态栏高度 26dp 和 设计图要求的状态栏高度38dp 不一样
 */
@Composable
fun getStatusBarHeightAdapter(): Dp {
//    val density = LocalDensity.current.density
    val statusBarHeight = getStatusBarHeight()
    return statusBarHeight
//    return remember(density, statusBarHeight) {
//        val statusBarHeightAdapter = getStatusBarHeightAdapter(statusBarHeight)
//        Logger.i(
//            "HomeNoteContentPanel",
//            "statusBarHeight:$statusBarHeight statusBarHeightAdapter:$statusBarHeightAdapter"
//        )
//        statusBarHeightAdapter
//    }
}