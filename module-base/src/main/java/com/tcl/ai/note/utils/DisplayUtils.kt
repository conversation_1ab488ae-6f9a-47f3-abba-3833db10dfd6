package com.tcl.ai.note.utils

import android.content.Context
import android.content.res.Configuration
import android.util.DisplayMetrics
import android.view.WindowManager
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.GlobalContext

fun getScreenSize() =
    GlobalContext
        .instance
        .getSystemService(WindowManager::class.java)!!
        .currentWindowMetrics
        .bounds

/**
 * 判断当前是否为夜间模式
 * @return true为夜间模式，false为日间模式
 */
fun isDarkMode(context: Context): Boolean {
    val uiMode = context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
    return uiMode == Configuration.UI_MODE_NIGHT_YES
}
/**
 * 屏幕尺寸数据类
 */
data class ScreenSize(
    val width: Int,//dp
    val height: Int,//dp
    val widthPx: Int,
    val heightPx: Int,
    val density: Density
)

/**
 * 获取完整屏幕信息 compose 实现
 * 一次性获取所有屏幕相关信息，避免重复计算
 */
@Composable
fun rememberScreenInfo(): ScreenSize {
    val density = LocalDensity.current
    val containerSize = LocalWindowInfo.current.containerSize
    return remember(containerSize.width, containerSize.height, density.density) {
        val widthPx = containerSize.width
        val heightPx = containerSize.height
        val widthDp = widthPx.px2dp
        val heightDp =heightPx.px2dp
        Logger.d("ScreenSize", "rememberScreenInfo Screen size: $widthDp x $heightDp widthPx: $widthPx, heightPx: $heightPx")
        ScreenSize(
            width = widthDp,
            height = heightDp,
            widthPx = widthPx,
            heightPx = heightPx,
            density = density
        )
    }
}

/**
 * 获取屏幕宽度 compose 实现
 */
@Composable
fun rememberScreenWidth(): Dp {
    return rememberScreenInfo().width.dp
}

/**
 * 获取屏幕高度 compose 实现
 */
@Composable
fun rememberScreenHeight(): Dp {
    return rememberScreenInfo().height.dp
}

/**
 * 获取屏幕尺寸 compose 实现
 */
@Composable
fun rememberScreenSize(): Pair<Dp, Dp> {
    val screenInfo = rememberScreenInfo()
    return Pair(screenInfo.width.dp, screenInfo.height.dp)
}

/**
 * 获取屏幕密度 compose 实现
 */
@Composable
fun rememberScreenDensity(): Density {
    return rememberScreenInfo().density
}

/**
 * 获取屏幕宽度像素值 compose 实现
 */
@Composable
fun rememberScreenWidthPx(): Int {
    return rememberScreenInfo().widthPx
}

/**
 * 获取屏幕高度像素值 compose 实现
 */
@Composable
fun rememberScreenHeightPx(): Int {
    return rememberScreenInfo().heightPx
}

/**
 * ScreenSize 扩展函数
 */

/**
 * 检查是否为横屏
 */
fun ScreenSize.isLandscape(): Boolean = width > height

/**
 * 检查是否为竖屏
 */
fun ScreenSize.isPortrait(): Boolean = height > width

/**
 * 检查是否为平板尺寸 (宽度 >= 600dp)
 */
//fun ScreenSize.isTablet(): Boolean = width >= 600.dp

/**
 * 检查是否为大屏设备 (宽度 >= 840dp)
 */
fun ScreenSize.isLargeScreen(): Boolean = width.dp >= 840.dp

/**
 * 获取屏幕比例 (宽/高)
 */
fun ScreenSize.aspectRatio(): Float = width.dp.value / height.dp.value





val deviceDensity = DisplayMetrics.DENSITY_DEVICE_STABLE

val isDensity440 = deviceDensity == DisplayMetrics.DENSITY_440