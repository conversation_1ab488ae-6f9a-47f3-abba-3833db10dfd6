package com.tcl.ai.note.utils

import android.content.Context
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.core.view.OnApplyWindowInsetsListener
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.postDelayed
import com.tcl.ai.note.base.BaseActivity
import java.lang.ref.WeakReference

/**
 * 获取软键盘高度
 */
fun View.getImeHeight(): Int {
    val insets = ViewCompat.getRootWindowInsets(this)
    val imeInsets = insets?.getInsets(WindowInsetsCompat.Type.ime())
    return imeInsets?.bottom ?: 0
}

fun View.showIme() {
    requestFocus()
    val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    imm.showSoftInput(this, InputMethodManager.SHOW_IMPLICIT)
}

fun View.hideIme() {
    val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    imm.hideSoftInputFromWindow(windowToken, 0)
}

fun View.awaitShowIme(callback: (imeHeight: Int) -> Unit) {
    val baseActivityRef = WeakReference(context as BaseActivity)
    baseActivityRef.get()?.addOnApplyWindowInsetsListener(object : OnApplyWindowInsetsListener {
        override fun onApplyWindowInsets(v: View, insets: WindowInsetsCompat): WindowInsetsCompat {
            val imeHeight = insets.getInsets(WindowInsetsCompat.Type.ime()).bottom
            if (imeHeight > 0) {
                // 等待键盘动画结束, 300ms时通过自测得出的。可能每个设备都不一样
                postDelayed(300) {
                    callback.invoke(imeHeight)
                }
                baseActivityRef.get()?.removeOnApplyWindowInsetsListener(this)
            }
            return insets
        }
    })
}

fun View.awaitHideIme(callback: (imeHeight: Int) -> Unit) {
    val baseActivityRef = WeakReference(context as BaseActivity)
    val imeRealHeight = getImeHeight()
    baseActivityRef.get()?.addOnApplyWindowInsetsListener(object : OnApplyWindowInsetsListener {
        override fun onApplyWindowInsets(v: View, insets: WindowInsetsCompat): WindowInsetsCompat {
            val curImeHeight = insets.getInsets(WindowInsetsCompat.Type.ime()).bottom
            if (curImeHeight == 0) {
                callback.invoke(imeRealHeight)
                baseActivityRef.get()?.removeOnApplyWindowInsetsListener(this)
            }
            return insets
        }
    })
}