package com.tcl.ai.note.utils

import androidx.compose.foundation.interaction.HoverInteraction
import androidx.compose.foundation.interaction.Interaction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.PressInteraction
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow


fun HoverMutableInteractionSource(): MutableInteractionSource = HoverCancelInteractionSourceImpl()

class HoverCancelInteractionSourceImpl : MutableInteractionSource {
    private var lastEnter: HoverInteraction.Enter? = null

    override val interactions = MutableSharedFlow<Interaction>(
        extraBufferCapacity = 16,
        onBufferOverflow = BufferOverflow.DROP_OLDEST,
    )

    override suspend fun emit(interaction: Interaction) {
        when (interaction) {
            is HoverInteraction.Enter -> lastEnter = interaction
            is HoverInteraction.Exit -> lastEnter = null
            is PressInteraction.Press,is PressInteraction.Release ,is PressInteraction.Cancel -> {
                lastEnter?.let {
                    interactions.emit(HoverInteraction.Exit(it))
                }
                lastEnter = null
            }
        }
        interactions.emit(interaction)
    }

    override fun tryEmit(interaction: Interaction): Boolean {
        when (interaction) {
            is HoverInteraction.Enter -> lastEnter = interaction
            is HoverInteraction.Exit -> lastEnter = null
            is PressInteraction.Press,is PressInteraction.Release ,is PressInteraction.Cancel ->{
                lastEnter?.let {
                    interactions.tryEmit(HoverInteraction.Exit(it))
                }
                lastEnter = null
            }
        }
        return interactions.tryEmit(interaction)
    }
}