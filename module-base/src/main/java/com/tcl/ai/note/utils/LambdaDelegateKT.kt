package com.tcl.ai.note.utils

import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty

open class LambdaReadWriteProperty<in T, V>(default: V? = null) : ReadWriteProperty<T, V> {
    /**
     * [func]变量,用于保存Function或者Lambda表达式
     * [invokeFunc]就是返回并调用了func这个方法
     */
    private var func: V? = default

    /**
     * 调用[func]变量保存的函数
     */
    val invokeFunc: V get() = if (func == null) throw NotImplementedError() else func!!

    /**
     * 委托给by关键词，获取func函数
     * 和[invokeFunc]的作用是一模一样的
     */
    override fun getValue(thisRef: T, property: KProperty<*>): V {
        return invokeFunc
    }

    /**
     * 委托给by关键词，把函数保存到[func]变量中
     */
    override fun setValue(thisRef: T, property: KProperty<*>, value: V) {
        func = value
    }
}

/**
 * 无参
 * @param R 返回类型
 */
fun <R> (() -> R)?.delegate() =
    object : LambdaReadWriteProperty<Any?, () -> R>() {}

fun <R> delegateFunc(default: (() -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, () -> R>(default) {}

// 兼容suspend
inline fun <R> suspend(noinline block: suspend () -> R): suspend () -> R = block

fun <R> delegateSuspendFunc(default: (suspend () -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, suspend () -> R>(default) {}


/**
 * @param A 入参类型
 *
 * @param R 返回类型
 */
fun <A, R> ((A) -> R)?.delegate() =
    object : LambdaReadWriteProperty<Any?, (A) -> R>() {}

fun <A, R> delegateFunc(default: ((A) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, (A) -> R>(default) {}

// 兼容suspend
inline fun <A, R> suspend(noinline block: suspend (A) -> R): suspend (A) -> R = block

fun <A, R> delegateSuspendFunc(default: (suspend (A) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, suspend (A) -> R>(default) {}

/**
 * @param A 入参类型
 * @param B 入参类型
 *
 * @param R 返回类型
 */
fun <A, B, R> ((A, B) -> R)?.delegate() =
    object : LambdaReadWriteProperty<Any?, (A, B) -> R>() {}

fun <A, B, R> delegateFunc(default: ((A, B) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, (A, B) -> R>(default) {}

// 兼容suspend
inline fun <A, B, R> suspend(noinline block: suspend (A, B) -> R): suspend (A, B) -> R = block

fun <A, B, R> delegateSuspendFunc(default: (suspend (A, B) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, suspend (A, B) -> R>(default) {}

/**
 * @param A 入参类型
 * @param B 入参类型
 * @param C 入参类型
 *
 * @param R 返回类型
 */
fun <A, B, C, R> ((A, B, C) -> R)?.delegate() =
    object : LambdaReadWriteProperty<Any?, (A, B, C) -> R>() {}

fun <A, B, C, R> delegateFunc(default: ((A, B, C) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, (A, B, C) -> R>(default) {}

// 兼容suspend
inline fun <A, B, C, R> suspend(noinline block: suspend (A, B, C) -> R): suspend (A, B, C) -> R = block

fun <A, B, C, R> delegateSuspendFunc(default: (suspend (A, B, C) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, suspend (A, B, C) -> R>(default) {}

/**
 * @param A 入参类型
 * @param B 入参类型
 * @param C 入参类型
 * @param D 入参类型
 *
 * @param R 返回类型
 */
fun <A, B, C, D, R> ((A, B, C, D) -> R)?.delegate() =
    object : LambdaReadWriteProperty<Any?, (A, B, C, D) -> R>() {}

fun <A, B, C, D, R> delegateFunc(default: ((A, B, C, D) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, (A, B, C, D) -> R>(default) {}

// 兼容suspend
inline fun <A, B, C, D, R> suspend(noinline block: suspend (A, B, C, D) -> R): suspend (A, B, C, D) -> R = block

fun <A, B, C, D, R> delegateSuspendFunc(default: (suspend (A, B, C, D) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, suspend (A, B, C, D) -> R>(default) {}

/**
 * @param A 入参类型
 * @param B 入参类型
 * @param C 入参类型
 * @param D 入参类型
 * @param E 入参类型
 *
 * @param R 返回类型
 */
fun <A, B, C, D, E, R> ((A, B, C, D, E) -> R)?.delegate() =
    object : LambdaReadWriteProperty<Any?, (A, B, C, D, E) -> R>() {}

fun <A, B, C, D, E, R> delegateFunc(default: ((A, B, C, D, E) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, (A, B, C, D, E) -> R>(default) {}

// 兼容suspend
inline fun <A, B, C, D, E, R> suspend(noinline block: suspend (A, B, C, D, E) -> R): suspend (A, B, C, D, E) -> R = block

fun <A, B, C, D, E, R> delegateSuspendFunc(default: (suspend (A, B, C, D, E) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, suspend (A, B, C, D, E) -> R>(default) {}

/**
 * @param A 入参类型
 * @param B 入参类型
 * @param C 入参类型
 * @param D 入参类型
 * @param E 入参类型
 * @param F 入参类型
 *
 * @param R 返回类型
 */
fun <A, B, C, D, E, F, R> ((A, B, C, D, E, F) -> R)?.delegate() =
    object : LambdaReadWriteProperty<Any?, (A, B, C, D, E, F) -> R>() {}

fun <A, B, C, D, E, F, R> delegateFunc(default: ((A, B, C, D, E, F) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, (A, B, C, D, E, F) -> R>(default) {}

// 兼容suspend
inline fun <A, B, C, D, E, F, R> suspend(noinline block: suspend (A, B, C, D, E, F) -> R): suspend (A, B, C, D, E, F) -> R = block

fun <A, B, C, D, E, F, R> delegateSuspendFunc(default: (suspend (A, B, C, D, E, F) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, suspend (A, B, C, D, E, F) -> R>(default) {}

/**
 * @param A 入参类型
 * @param B 入参类型
 * @param C 入参类型
 * @param D 入参类型
 * @param E 入参类型
 * @param F 入参类型
 * @param G 入参类型
 *
 * @param R 返回类型
 */
fun <A, B, C, D, E, F, G, R> ((A, B, C, D, E, F, G) -> R)?.delegate() =
    object : LambdaReadWriteProperty<Any?, (A, B, C, D, E, F, G) -> R>() {}

fun <A, B, C, D, E, F, G, R> delegateFunc(default: ((A, B, C, D, E, F, G) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, (A, B, C, D, E, F, G) -> R>(default) {}

// 兼容suspend
inline fun <A, B, C, D, E, F, G, R> suspend(noinline block: suspend (A, B, C, D, E, F, G) -> R): suspend (A, B, C, D, E, F, G) -> R = block

fun <A, B, C, D, E, F, G, R> delegateSuspendFunc(default: (suspend (A, B, C, D, E, F, G) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, suspend (A, B, C, D, E, F, G) -> R>(default) {}

/**
 * @param A 入参类型
 * @param B 入参类型
 * @param C 入参类型
 * @param D 入参类型
 * @param E 入参类型
 * @param F 入参类型
 * @param G 入参类型
 *
 * @param R 返回类型
 */
fun <A, B, C, D, E, F, G, H, R> ((A, B, C, D, E, F, G, H) -> R)?.delegate() =
    object : LambdaReadWriteProperty<Any?, (A, B, C, D, E, F, G, H) -> R>() {}

fun <A, B, C, D, E, F, G, H, R> delegateFunc(default: ((A, B, C, D, E, F, G, H) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, (A, B, C, D, E, F, G, H) -> R>(default) {}

// 兼容suspend
inline fun <A, B, C, D, E, F, G, H, R> suspend(noinline block: suspend (A, B, C, D, E, F, G, H) -> R): suspend (A, B, C, D, E, F, G, H) -> R = block

fun <A, B, C, D, E, F, G, H, R> delegateSuspendFunc(default: (suspend (A, B, C, D, E, F, G, H) -> R)? = null) =
    object : LambdaReadWriteProperty<Any?, suspend (A, B, C, D, E, F, G, H) -> R>(default) {}