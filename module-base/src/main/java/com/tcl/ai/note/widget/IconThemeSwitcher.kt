package com.tcl.ai.note.widget

import androidx.compose.foundation.Indication
import androidx.compose.foundation.LocalIndication
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.indication
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.PressInteraction
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonColors
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalUseFallbackRippleImplementation
import androidx.compose.material3.minimumInteractiveComponentSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.ripple.AppRipple
import com.tcl.ai.note.ripple.rippleOrFallbackImplementation
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.HoverMutableInteractionSource
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.judge
import kotlinx.coroutines.delay


/**
 * 支持深色模式，通过IconButton,Icon
 */
@Composable
fun IconThemeSwitcher(
    modifier: Modifier = Modifier,
    btnSize: Dp,
    iconSize: Dp = btnSize,
    onClick: () -> Unit,
    painter: Painter,
    enabled: Boolean = true,
    contentDescription: String?,
    tint: Color = LocalContentColor.current
) {
    HoverProofIconButton(
        onClick = onClick,
        enabled = enabled,
        modifier = modifier.size(btnSize),
        rippleSize = btnSize,
    ) {
        Icon(
            painter = painter,
            contentDescription = contentDescription,
            modifier = Modifier.size(iconSize),
            tint = tint
        )
    }

}

/**
 * 无水波纹残留IconButton
 */
@Composable
fun HoverProofIconButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    rippleSize:Dp = TclTheme.dimens.btnSize,
    enabled: Boolean = true,
    content: @Composable () -> Unit
) {
    val hoverInteraction = remember {
        HoverMutableInteractionSource()
    }
    IconButton(
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        interactionSource = hoverInteraction,
        content = content
    )
}





/**
 * 支持圆形水波纹且支持方形圆角背景
 * 点击时先触发水波纹效果后再显示方形背景
 */
@Composable
fun DelayedBackgroundIconButton(
    modifier: Modifier = Modifier,
    btnSize: Dp,
    iconSize: Dp = btnSize,
    onClick: () -> Unit,
    painter: Painter,
    isChecked: Boolean = false,
    enabled: Boolean = true,
    contentDescription: String?,
) {
    DelayedBackgroundIconButton(
        modifier = modifier,
        btnSize = btnSize,
        iconSize = iconSize,
        onClick = onClick,
        isChecked = isChecked,
        enabled = enabled,
        contentDescription = contentDescription
    ) {
        Icon(
            painter = painter,
            contentDescription = contentDescription,
            modifier = Modifier.size(iconSize)
        )
    }
}

/**
 * 支持圆形水波纹且支持方形圆角背景
 * 点击时先触发水波纹效果后再显示方形背景
 * 支持自定义内容
 */
@Composable
fun DelayedBackgroundIconButton(
    modifier: Modifier = Modifier,
    btnSize: Dp,
    iconSize: Dp = btnSize,
    onClick: () -> Unit,
    isChecked: Boolean = false,
    enabled: Boolean = true,
    contentDescription: String?,
    content: @Composable () -> Unit
) {
    var showBackground by remember { mutableStateOf(false) }

    LaunchedEffect(isChecked) {
        if (isChecked) {
            delay(300)
            showBackground = true
        } else {
            showBackground = false
        }
    }

    Box(
        modifier = Modifier
            .size(btnSize)
            .indication(
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            )
            .then(
                showBackground.judge(
                    Modifier.background(
                        color = R.color.tablet_btn_checked_bg.colorRes(),
                        shape = RoundedCornerShape(btnSize / 2),
                    ),
                    Modifier
                )
            )
    ) {
        HoverProofIconButton(
            onClick = onClick,
            enabled = enabled,
            modifier = modifier.size(btnSize),
            rippleSize = btnSize,
            content = content
        )
    }
}




@Composable
fun LongPressIconButton(
    onLongPress: (isLongPressing:Boolean) -> Unit = { },
    onClick: () -> Unit ={},
    modifier: Modifier = Modifier,
    colors: IconButtonColors = IconButtonDefaults.iconButtonColors(),
    content: @Composable () -> Unit
) {
    val hoverInteraction = remember {
        HoverMutableInteractionSource()
    }
    Box(
        modifier = modifier
            .minimumInteractiveComponentSize()
            .clip(CircleShape)
            .pointerInput(Unit) {
                detectTapGestures(
                    onLongPress = {
                        onLongPress(true)
                    },
                    onPress = { offset ->
                        val press = PressInteraction.Press(offset)
                        hoverInteraction.emit(press)
                        tryAwaitRelease()
                        onLongPress(false)
                        hoverInteraction.emit(PressInteraction.Release(press))
                    },
                    onTap = {
                        onLongPress(false)
                        onClick()

                    }

                )
            }
            .indication(hoverInteraction, LocalIndication.current),
        contentAlignment = Alignment.Center
    ) {
        val contentColor = colors.contentColor
        CompositionLocalProvider(LocalContentColor provides contentColor, content = content)
    }
}





/**
 * 支持圆形水波纹且支持方形圆角背景，使用 Image 组件保持图片原始颜色
 * 点击时先触发水波纹效果后再显示方形背景
 */
@Composable
fun DelayedBackgroundImageButton(
    modifier: Modifier = Modifier,
    btnSize: Dp,
    iconSize: Dp = btnSize,
    onClick: () -> Unit,
    painter: Painter,
    isChecked: Boolean = false,
    enabled: Boolean = true,
    contentDescription: String?,
) {
    var showBackground by remember { mutableStateOf(false) }
    val hoverInteraction = remember { HoverMutableInteractionSource() }

    LaunchedEffect(isChecked) {
        if (isChecked) {
            delay(300)
            showBackground = true
        } else {
            showBackground = false
        }
    }

    Box(
        modifier = modifier
            .size(btnSize)
            .clickable(
                onClick = onClick,
                enabled = enabled,
                role = Role.Button,
                interactionSource = hoverInteraction,
                indication = rippleOrFallbackImplementation(
                    bounded = false,
                    radius = btnSize / 2
                )
            )
            .then(
                showBackground.judge(
                    Modifier.background(
                        color = R.color.tablet_btn_checked_bg.colorRes(),
                        shape = RoundedCornerShape(btnSize / 2),
                    ),
                    Modifier
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        androidx.compose.foundation.Image(
            painter = painter,
            contentDescription = contentDescription,
            modifier = Modifier.size(iconSize)
            // 注意：这里没有设置 colorFilter，保持图片原始颜色
        )
    }
}


