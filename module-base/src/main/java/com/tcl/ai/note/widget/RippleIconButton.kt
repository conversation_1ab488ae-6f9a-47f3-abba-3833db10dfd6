package com.tcl.ai.note.widget

/*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.material3.IconButtonColors
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.minimumInteractiveComponentSize
import androidx.compose.material3.rippleOrFallbackImplementation
import androidx.compose.material3.tokens.ColorSchemeKeyTokens
import androidx.compose.material3.tokens.IconButtonTokens
import androidx.compose.material3.tokens.ShapeKeyTokens
import androidx.compose.material3.value
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.HoverMutableInteractionSource
import com.tcl.ai.note.utils.judge

@Composable
fun RippleIconButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    colors: IconButtonColors = IconButtonDefaults.iconButtonColors(),
    interactionSource: MutableInteractionSource? = null,
    content: @Composable () -> Unit
) {
    Box(
        modifier =
        modifier
            .minimumInteractiveComponentSize()
            .size(AppIconButtonTokens.StateLayerSize)
            .clip(AppIconButtonTokens.StateLayerShape.value)
            .background(color = colors.containerColor(enabled))
            .clickable(
                onClick = onClick,
                enabled = enabled,
                role = Role.Button,
                interactionSource = interactionSource,
                indication =
                rippleOrFallbackImplementation(
                    bounded = false,
                    radius = AppIconButtonTokens.StateLayerSize / 2
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        val contentColor = colors.contentColor(enabled)
        CompositionLocalProvider(LocalContentColor provides contentColor, content = content)
    }
}


internal object AppIconButtonTokens {
    val DisabledIconColor = ColorSchemeKeyTokens.OnSurface
    const val DisabledIconOpacity = 0.38f
    val IconSize = 24.0.dp
    val SelectedFocusIconColor = ColorSchemeKeyTokens.Primary
    val SelectedHoverIconColor = ColorSchemeKeyTokens.Primary
    val SelectedIconColor = ColorSchemeKeyTokens.Primary
    val SelectedPressedIconColor = ColorSchemeKeyTokens.Primary
    val StateLayerShape = ShapeKeyTokens.CornerFull
    val StateLayerSize = 40.0.dp
    val UnselectedFocusIconColor = ColorSchemeKeyTokens.OnSurfaceVariant
    val UnselectedHoverIconColor = ColorSchemeKeyTokens.OnSurfaceVariant
    val UnselectedIconColor = ColorSchemeKeyTokens.OnSurfaceVariant
    val UnselectedPressedIconColor = ColorSchemeKeyTokens.OnSurfaceVariant
}
*/


/*
@Composable
fun RippleIconButton(
    isDarkTheme:Boolean = isSystemInDarkTheme(),
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    rippleSize: Dp = TclTheme.dimens.btnSize,

    enabled: Boolean = true,
    content: @Composable () -> Unit
) {
    val hoverInteraction = remember {
        HoverMutableInteractionSource()
    }
    Box(
        modifier = modifier
            .minimumInteractiveComponentSize()
            .clickable(
                onClick = onClick,
                enabled = enabled,
                role = Role.Button,
                interactionSource = hoverInteraction,
                indication =
                com.tcl.ai.note.ripple.rippleOrFallbackImplementation(
                    bounded = false,
                    radius = rippleSize / 2,
                    color = isDarkTheme.judge(Color.White, Color.Black)
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        content()
    }
}*/
