package com.tcl.ai.note.widget.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.NoteTclTheme
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.widget.MarqueeText


/**
 * AI底部提示组件
 */
@PreviewLightDark
@Composable
fun AIBottomPrompt(modifier: Modifier = Modifier.fillMaxWidth()) {
    val promptStr = stringResource(id = R.string.content_prompt)
    Row(
        modifier = modifier.padding(top = 12.dp)
            .semantics {
                contentDescription = promptStr
            },
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_ai_bottom_prompt),
            modifier = Modifier.size(14.dp),
            contentDescription = null,
        )
        Spacer(Modifier.width(2.dp))
        MarqueeText(
            text = promptStr,
            textAlign = TextAlign.Center,
            fontSize = if (isTablet) 12.sp else 9.sp,
            fontWeight = FontWeight.W500,
            lineHeight = 28.sp,
            wrapContentWidth = true,
            color = colorResource(id = R.color.tct_btn_checked_disable_primary_color)
        )
    }
}

/**
 * AI底部操作组件
 * 四个弹窗复用
 * 包括重试、替换、复制按钮,和插入按钮
 */
@Composable
private fun AIBottomOperateRow(
    modifier: Modifier = Modifier,
    showPrompt: Boolean = true,
    content: @Composable () -> Unit
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.Bottom
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (showPrompt) {
                AIBottomPrompt(
                    modifier = Modifier.wrapContentWidth()
                )
            }
            content()
        }
    }
}

@Composable
fun AIBottomOperateComponent(
    modifier: Modifier = Modifier,
    content: String = "",
    onRetryClick: (() -> Unit)? = null,
    onCopyClick: () -> Unit,
    onReplaceClick: ((String) -> Unit)? = null,
    onInsertClick: ((String) -> Unit)? = null,
) {
    val isContentNotEmpty = content.isNotEmpty()
    AIBottomOperateRow(modifier = modifier, showPrompt = isTablet) {
        BottomOperateIcons(
            onRetryClick,
            isContentNotEmpty,
            content,
            onCopyClick,
            onReplaceClick,
            onInsertClick
        )
    }
}

/**
 * AI底部操作组件 for 手写转文
 */
@Composable
fun AIBottomOperateComponentForHandwriting(
    modifier: Modifier = Modifier,
    content: String = "",
    onRetryClick: (() -> Unit)? = null,
    onCopyClick: () -> Unit,
    onReplaceClick: ((String) -> Unit)? = null,
    onInsertClick: ((String) -> Unit)? = null,
) {
    val isContentNotEmpty = content.isNotEmpty()
    AIBottomOperateRow(
        modifier = modifier,
        showPrompt = isTablet  //
    ) {
        Row(
            modifier = Modifier,
            verticalAlignment = Alignment.Bottom,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Spacer(modifier = Modifier.weight(1f))
            ReplaceIcon(
                modifier = Modifier,
                onReplaceClick = onReplaceClick,
                isContentNotEmpty = isContentNotEmpty,
                content = content
            )
            CopyIcon(
                modifier = Modifier,
                isContentNotEmpty = isContentNotEmpty,
                content = content,
                onCopyClick = onCopyClick
            )
        }
    }
}

@Composable
private fun BottomOperateIcons(
    onRetryClick: (() -> Unit)?,
    isContentNotEmpty: Boolean,
    content: String,
    onCopyClick: () -> Unit,
    onReplaceClick: ((String) -> Unit)?,
    onInsertClick: ((String) -> Unit)?
) {
    Row(
        modifier = Modifier,
        verticalAlignment = Alignment.Bottom,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Spacer(modifier = Modifier.weight(1f))
        RetryIcon(onRetryClick)
        // 内容为空则 复制按钮为不可点击
        ReplaceIcon(
            onReplaceClick = onReplaceClick,
            isContentNotEmpty = isContentNotEmpty,
            content = content
        )
        CopyIcon(
            isContentNotEmpty = isContentNotEmpty,
            content = content,
            onCopyClick = onCopyClick
        )
        InsertIcon(onInsertClick, isContentNotEmpty, content)
    }
}

@Composable
private fun InsertIcon(
    onInsertClick: ((String) -> Unit)?,
    isContentNotEmpty: Boolean,
    content: String
) {
    if (onInsertClick != null) {
        Image(
            painter = painterResource(id = R.drawable.ai_icon_insert),
            contentDescription = stringResource(id = R.string.insert),
            alpha = if (isContentNotEmpty) 1f else 0.5f,
            modifier = Modifier
                .size(32.dp)
                .clickable(
                    enabled = isContentNotEmpty,
                    interactionSource = null,
                    indication = ripple(bounded = false)
                ) {
                    onInsertClick.invoke(content)
                })
    }
}

@Composable
private fun ReplaceIcon(
    modifier: Modifier = Modifier,
    onReplaceClick: ((String) -> Unit)?,
    isContentNotEmpty: Boolean,
    content: String
) {
    if (onReplaceClick != null) {
        Image(
            painter = painterResource(id = R.drawable.ic_replace),
            contentDescription = stringResource(id = R.string.replace),
            alpha = if (isContentNotEmpty) 1f else 0.5f,
            modifier = modifier
                .size(32.dp)
                .clickable(
                    enabled = isContentNotEmpty,
                    interactionSource = null,
                    indication = ripple(bounded = false)
                ) {
                    onReplaceClick.invoke(content)
                })
    }
}

@Composable
private fun RetryIcon(onRetryClick: (() -> Unit)?) {
    if (onRetryClick != null) {
        Image(
            painter = painterResource(id = R.drawable.ic_retry),
            contentDescription = stringResource(id = R.string.retry),
            modifier = Modifier
                .size(32.dp)
                .clickable(
                    interactionSource = null,
                    indication = ripple(bounded = false)
                ) {
                    onRetryClick.invoke()
                })
    }
}

@Composable
private fun CopyIcon(
    modifier: Modifier = Modifier,
    isContentNotEmpty: Boolean,
    content: String,
    onCopyClick: () -> Unit
) {
    val clipboardManager = LocalClipboardManager.current
    Image(
        painter = painterResource(id = R.drawable.ic_copy),
        contentDescription = stringResource(id = R.string.writing_copy),
        alpha = if (isContentNotEmpty) 1f else 0.5f,
        modifier = modifier
            .size(32.dp)
            .clickable(
                enabled = isContentNotEmpty,
                interactionSource = null,
                indication = ripple(bounded = false)
            ) {
                val clipData = AnnotatedString(
                    content
                )
                clipboardManager.setText(clipData)
                onCopyClick.invoke()
            })
}

@PreviewLightDark
@Composable
private fun AIBottomOperatePreview() {
    NoteTclTheme {
        Column {
            AIBottomOperateComponent(onRetryClick = {}, onCopyClick = {})
            AIBottomOperateComponent(
                content = "content",
                onRetryClick = {},
                onCopyClick = {},
                onReplaceClick = {}, onInsertClick = {})
            AIBottomOperateComponentForHandwriting(onCopyClick = {}, onReplaceClick = {})
        }
    }

}