package com.tcl.ai.note.dashboard.ui

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.width
import androidx.compose.material.Divider
import androidx.compose.material.FloatingActionButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.FabPosition
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.tcl.ai.note.base.R
import com.tcl.ai.note.dashboard.intent.ConfigIntent
import com.tcl.ai.note.dashboard.ui.landscape.components.LandScapeListSection
import com.tcl.ai.note.dashboard.ui.landscape.components.PreviewPane
import com.tcl.ai.note.dashboard.vm.DashboardModel
import com.tcl.ai.note.dashboard.vm.SharedDashboardModel
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel
import com.tcl.ai.note.widget.SearchBox
import com.tcl.ai.note.widget.VerticalLine

/**
 * 横屏首页UI
 */
@SuppressLint("DesignSystem")
@Composable
fun LandscapeDashboardScreen(
    navController: NavController,
    sharedDashboardModel: SharedDashboardModel,
    dashboardModel: DashboardModel = hiltViewModel(),
    audioToTextViewModel: AudioToTextViewModel
) {
    val isFabVisible = dashboardModel.isFabVisible
    val noteState by dashboardModel.noteState.collectAsState()
    val searchText = noteState.searchText
    val isSearching = noteState.isSearching
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(TclTheme.colorScheme.tctGlobalBgColor) // 添加全局背景
        ){
        Scaffold(
            contentWindowInsets = WindowInsets(0, 0, 0, 0), // 禁用默认内边距
            containerColor = TclTheme.colorScheme.tctGlobalBgColor,
            floatingActionButton = {
                if(isFabVisible){
                    FloatingActionButton(
                        onClick = {
                            navController.navigate("edit_screen")
                        },
                        backgroundColor = colorResource(R.color.bg_float_action_button), // 设置FloatingActionButton的背景颜色
                        contentColor = Color.White, // 设置图标的颜色
                        modifier = Modifier.navigationBarsPadding(),
                    ) {
                        Icon(
                            Icons.Filled.Add,
                            contentDescription = R.string.btn_add_sticky_note.stringRes(),
                            tint = Color.White)
                    }
                }
            },
            floatingActionButtonPosition = FabPosition.End
        ) { paddingValues ->
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                // 左侧区域
                LandScapeListSection(
                    navController,
                    dashboardModel,
                    audioToTextViewModel = audioToTextViewModel,
                    sharedDashboardModel,
                    modifier = Modifier.weight(0.37f),
                    isSearching = isSearching,
                    searchText = searchText,
                    onIsSearching = {
                        dashboardModel.updateSearchState(it)
                    })

                // 垂直分割线
                VerticalLine()

                // 右侧预览区域 (占据2/3宽度)
                PreviewPane(
                    navController = navController,
                    dashboardModel = dashboardModel,
                    audioToTextViewModel = audioToTextViewModel,
                    modifier = Modifier.weight(0.63f),
                    isEditMode = noteState.editMode,
                )
            }
        }

        // 如果 isSearching 为 true，监听返回键进行处理
        BackHandler(isSearching) {
            dashboardModel.updateFabVisibleState(true)
            dashboardModel.handleIntent(ConfigIntent.SearchNotes(""))
            dashboardModel.updateSearchState(false)
            dashboardModel.loadInitialNotes()
        }

        if(isSearching){
            // 搜索框内容为空则显示半透明遮罩层
            if(searchText.trim().isEmpty()){
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .invisibleSemantics()
                        .padding(top = 56.dp+WindowInsets.statusBars
                            .asPaddingValues()
                            .calculateTopPadding())
                        .background(Color.Black.copy(alpha = 0.5f))
                        .clickable { dashboardModel.updateSearchState(false) } // 点击遮罩层取消搜索
                        .zIndex(0.5f) // 将遮罩层置于搜索框下方，但在内容上方
                )
            }

            // 搜索框
            SearchBox(
                searchText,
                onTextChange = {
                    val isAllSpaces = it.text.isNotEmpty() && it.text.trim().isEmpty()
                    if(!isAllSpaces){
                        dashboardModel.updateFabVisibleState(it.text.isEmpty())
                        dashboardModel.handleIntent(ConfigIntent.SearchNotes(it.text))
                    }else{
                        dashboardModel.updateFabVisibleState(true)
                    }
                },
                onClear = {
                    dashboardModel.updateFabVisibleState(true)
                    dashboardModel.handleIntent(ConfigIntent.SearchNotes(""))
                },
                onBack = {
                    dashboardModel.updateFabVisibleState(true)
                    dashboardModel.handleIntent(ConfigIntent.SearchNotes(""))
                    dashboardModel.updateSearchState(false)
                },
                modifier = Modifier
                .align(Alignment.TopCenter)
                    .padding(top = WindowInsets.statusBars.asPaddingValues().calculateTopPadding(), start = 0.dp, end = 0.dp)
                    .zIndex(1f) // Ensure the search box is on top
            )
        }

    }
}

@Preview(
    widthDp = 1080,// 360 // 设置宽度以模拟横屏
    heightDp = 2340,// 820 // 设置高度
    showBackground = true
)
@Composable
fun PreviewLandscapeFullConfigScreen() {

    // 调用 DashboardScreen 进行预览
//    DashboardScreen()
}