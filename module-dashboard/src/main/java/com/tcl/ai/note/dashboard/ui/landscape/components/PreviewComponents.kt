package com.tcl.ai.note.dashboard.ui.landscape.components

import android.graphics.BitmapFactory
import android.widget.Toast
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.LocalOverscrollConfiguration
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.FlingBehavior
import androidx.compose.foundation.gestures.ScrollScope
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.drawscope.withTransform
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.fastForEach
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.dashboard.intent.ConfigIntent
import com.tcl.ai.note.dashboard.states.ListNoteCategoryState
import com.tcl.ai.note.dashboard.states.NoteState
import com.tcl.ai.note.dashboard.ui.CategoryScreen
import com.tcl.ai.note.dashboard.utils.NoteUtils
import com.tcl.ai.note.dashboard.vm.DashBoardPreviewViewModel
import com.tcl.ai.note.dashboard.vm.DashboardModel
import com.tcl.ai.note.handwritingtext.database.entity.BgMode
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.ImageBlockScaleMode
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.MeshStyle
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.PreviewTopAppBar
import com.tcl.ai.note.handwritingtext.vm.ShareContentComponent
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.editorTitleTextStyle
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.addOrExchangeWithPadding
import com.tcl.ai.note.utils.isFastClick
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.utils.openFileDescriptorSafely
import com.tcl.ai.note.utils.px2dp
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.voicetotext.view.widget.AudioBlockComponent
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel
import com.tcl.ai.note.widget.AutoScrollText
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.math.max

/**
 * 列表预览区
 */
@Composable
fun PreviewPane(
    navController: NavController,
    dashboardModel: DashboardModel = hiltViewModel(),
    audioToTextViewModel: AudioToTextViewModel,
    modifier: Modifier = Modifier,
    isEditMode: Boolean = false,
) {
    val noteState by dashboardModel.noteState.collectAsState()
    var topAppBarHeight by remember { mutableStateOf(0) }
    Box(modifier = modifier
        .fillMaxHeight()
        .background(TclTheme.colorScheme.tctGlobalBgColor)
        .onGloballyPositioned {
            topAppBarHeight = it.size.height
        }
    ) {
        if (noteState.note == null) {
            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize(),
            ) {
                Text(
                    text = stringResource(R.string.click_to_create_note),
                    color = Color.LightGray
                )
            }
        } else {
            Column(modifier = Modifier.fillMaxSize()) {
                // 预览模式
                PreviewTopSection(
                    topAppBarHeight = topAppBarHeight,
                    dashboardModel = dashboardModel,
                    audioToTextViewModel = audioToTextViewModel,
                    isEditMode = isEditMode,
                )
                // 内容预览区
                PreviewSection(navController, noteState)
            }
        }
    }
}

/**
 * 列表预览区顶部
 */
@Composable
fun PreviewTopSection(
    topAppBarHeight: Int,
    dashboardModel: DashboardModel,
    audioToTextViewModel: AudioToTextViewModel,
    shareContentComponent: ShareContentComponent = remember { ShareContentComponent() },
    isEditMode: Boolean = false,
) {
    val noteState by dashboardModel.noteState.collectAsState()
    val coroutineScope = rememberCoroutineScope()
    val density = LocalDensity.current.density
    val screenWidthDp = LocalConfiguration.current.screenWidthDp
    val noContentShareTip = stringResource(R.string.not_content_share)
    // 是否删除当前Note数据
    var isDeleteDialogShow by remember { mutableStateOf(false) }
    // 分类选择弹窗
    var showCategoryPopup by remember { mutableStateOf(false) }

    var currentCategoryId by remember { mutableStateOf<String>("") }
    var currentCategoryIcon by remember { mutableStateOf<String>("") }
    var currentCategoryColorIndex by remember { mutableIntStateOf(0) }
    if(noteState.currentNoteCategory != null){
        noteState.currentNoteCategory?.let {
            currentCategoryId = it.categoryId.toString()
            currentCategoryIcon = it.icon.toString()
            currentCategoryColorIndex = it.colorIndex
        }
    }
    // 注入分享模块
//    noteState.note?.let { shareContentComponent.injectRecordBitmapComposable(note = it) }
    PreviewTopAppBar(
        showShare = !noteState.editMode,
        showReturn = false,
        noteId = noteState.note?.noteId ?: 0,
        currentCategoryIcon = currentCategoryIcon,
        currentCategoryColorIndex = currentCategoryColorIndex,
        showCategoryPopup = showCategoryPopup,
        isDeleteDialogShow = isDeleteDialogShow,
        backgroundColor = TclTheme.colorScheme.tctGlobalBgColor,
        isEditMode = isEditMode,
        onReturn = {},
        onChangeCategory = {
            showCategoryPopup = true
        },
        onShare = { context, type ->
            if (type == "Image") {
                if (noteState.note != null) {
                    if (NoteUtils().contentOnlyVoice(noteState.note!!)) {
                        Toast.makeText(context, noContentShareTip, Toast.LENGTH_SHORT).show()
                    } else {
                        coroutineScope.launchIO {
//                            shareContentComponent.shareWithImage(context, noteState.note!!)
                        }
                    }
                }
            } else if (type == "Text") {
                if (noteState.note != null) {
                    if (NoteUtils().contentOnlyVoice(noteState.note!!)) {
                        Toast.makeText(context, noContentShareTip, Toast.LENGTH_SHORT).show()
                    } else {
                        coroutineScope.launchIO {
                            shareContentComponent.shareWithHtml(context, noteState.note!!)
                        }
                    }
                }
            } else if (type == "PDF") {
                if (noteState.note != null) {
                    if (NoteUtils().contentOnlyVoice(noteState.note!!)) {
                        Toast.makeText(context, noContentShareTip, Toast.LENGTH_SHORT).show()
                    } else {
                        coroutineScope.launchIO {
//                            shareContentComponent.shareWithPdf(context, noteState.note!!)
                        }
                    }
                }
            }

        },
        onDelete = {
            // 删除操作
            isDeleteDialogShow = true
        },
        categoryPopup = {
            SelectCategory(
                showPopup = showCategoryPopup,
                onDismiss = { showCategoryPopup = false },
                currentCategoryId = currentCategoryId,
                onSelected = {
                    currentCategoryId = it.categoryId.toString()
                    currentCategoryIcon = it.icon.toString()
                    if (noteState.note != null && noteState.note!!.noteId > 0) {
                        dashboardModel.handleIntent(
                            ConfigIntent.UpdateNoteCategoryId(
                                noteState.note!!.noteId,
                                it.categoryId
                            )
                        )
                    }
                },
                dashboardModel,
                topAppBarHeight = topAppBarHeight,
                modifier = Modifier
                    .padding(top = 8.dp, end = 0.dp)
            )
        }
    )



    if (isDeleteDialogShow) {
        DeleteDataDialog(
            text = stringResource(R.string.dialog_title_delete_one_items),
            onDelete = {
                if (noteState.note != null && noteState.note!!.noteId > 0) {
                    isDeleteDialogShow = false
                    dashboardModel.viewModelScope.launchIO { //删除音频文件
                        dashboardModel.loadNoteContents(listOf(noteState.note!!.noteId)).forEach {
                            if (it is EditorContent.AudioBlock) {
                                // 删除音频文件
                                audioToTextViewModel.deleteAudioFile(it.audioPath)
                            }
                        }
                    }
                    dashboardModel.handleIntent(ConfigIntent.DeleteOneNote(noteState.note!!.noteId))
                }
            },
            onDismiss = {
                isDeleteDialogShow = false
            }
        )
    }
}

/**
 * 列表预览区内容
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun PreviewSection(
    navController: NavController,
    noteState: NoteState,
    modifier: Modifier = Modifier,
    lazyListState: LazyListState = rememberLazyListState(),
    dashboardModel: DashboardModel = hiltViewModel(),
    dashBoardPreviewViewModel: DashBoardPreviewViewModel = hiltViewModel(),
) {
    val coroutineScope = rememberCoroutineScope()
    val offsetList = remember { mutableListOf(0) }
    var bottomScrollPaddingCount by remember { mutableIntStateOf(0) }
    val darkTheme: Boolean = (isSystemInDarkTheme() && (noteState.note?.bgColor ?: Skin.defColor) == Skin.defColor)
    var parentHeight by remember { mutableIntStateOf(0) }
    val context = LocalContext.current
    val dimens = getGlobalDimens()
    val currentNoteId by dashboardModel.currentNoteId.collectAsState()
    var titleHeight by remember { mutableIntStateOf(0) }

    LaunchedEffect(lazyListState) {
        snapshotFlow { lazyListState.firstVisibleItemScrollOffset }
            .distinctUntilChanged()
            .collect {
                withContext(Dispatchers.IO) {
                    try {
                        if(lazyListState.layoutInfo.visibleItemsInfo.isNotEmpty()){
                            lazyListState.layoutInfo.visibleItemsInfo.fastForEach {
                                offsetList.addOrExchangeWithPadding(it.index, it.size, 0)
                            }
                        }
                    }catch (_:IndexOutOfBoundsException){
                        Logger.d(TAG,"visibleItemsInfo is empty, skipping fastForEach")
                    }
                    var offset = 0
                    for (i in 0 until lazyListState.firstVisibleItemIndex) {
                        offset += offsetList[i]
                    }
                    offset += lazyListState.firstVisibleItemScrollOffset
                    with(dashBoardPreviewViewModel) {
                        translation = translation.copy(0f, -offset.toFloat())
                    }
                }
            }
    }

    LaunchedEffect(lazyListState) {
        snapshotFlow { lazyListState.layoutInfo }
            .map { layoutInfo ->
                layoutInfo.visibleItemsInfo.lastOrNull()?.index ?: 0
            }
            .distinctUntilChanged()
            .collect { lastVisibleIndex ->
                val totalItems = lazyListState.layoutInfo.totalItemsCount
                if (lastVisibleIndex >= totalItems - 1) {
                    bottomScrollPaddingCount++
                }
            }
    }

    LaunchedEffect(noteState.note?.noteId) {
        dashBoardPreviewViewModel.translation = Offset.Zero
        offsetList.clear()
        coroutineScope.launch {
            lazyListState.scrollToItem(0)
        }
        dashBoardPreviewViewModel.loadStrokes(noteState.note?.noteId)
    }

    Box(
        modifier = modifier
            .semantics {
                this.contentDescription = buildString {
                    append(context.getString(R.string.preview_note))
                    /*append(";")
                    append(context.getString(R.string.title))
                    if (noteState.note != null && noteState.note.title.isNotEmpty()) {
                        append(noteState.note.title)
                    }*/

                }
            }
            .fillMaxSize()
            .onSizeChanged { size -> parentHeight = size.height }
    ) {
        CompositionLocalProvider(
            LocalOverscrollConfiguration provides null
        ) {


            // 禁用形变
            LazyColumn(
                state = lazyListState,
                flingBehavior = object : FlingBehavior {
                    // 加速度0
                    override suspend fun ScrollScope.performFling(initialVelocity: Float) = 0f
                },
                modifier = Modifier
                    .fillMaxSize()
                    .padding(start = 24.dp, end = 24.dp, bottom = 5.dp)
                    .pointerInput(Unit) {
                        detectTapGestures(
                            onTap = { _ ->
                                if (isFastClick()) {
                                    return@detectTapGestures
                                }
                                // 跳转到全屏页面
                                currentNoteId.takeIf { it != -1L }?.let {
                                    navController.navigate("edit_screen?noteId=$it&isLandscape=true")
                                }
//                                if(noteState.note!=null){
//                                    navController.navigate("edit_screen?noteId=${noteState.note.noteId}&isLandscape=true")
//                                }
                            }
                        )
                    }
            ) {
                // 标题
                item {
                    Spacer(modifier = Modifier.height(dimens.titleTopMargin))
                    if (noteState.note != null && noteState.note.title.isNotEmpty()) {
                        Text(
                            modifier = Modifier.onSizeChanged {size -> titleHeight = size.height},
                            text = noteState.note.title,
                            style = TextStyle(
                                fontSize = editorTitleTextStyle.fontSize,
                                lineHeight = editorTitleTextStyle.lineHeight,
                                fontWeight = editorTitleTextStyle.fontWeight,
                                color = colorResource(darkTheme.judge(R.color.rich_text_title_night,R.color.rich_text_title))
                            )
                        )
                    } else {
                        Text(
                            modifier = Modifier.onSizeChanged {size -> titleHeight = size.height},
                            text = stringResource(R.string.title),
                            style = TextStyle(
                                fontSize = editorTitleTextStyle.fontSize,
                                lineHeight = editorTitleTextStyle.lineHeight,
                                fontWeight = editorTitleTextStyle.fontWeight,
                                color = colorResource(darkTheme.judge(
                                    R.color.text_input_hint_dark,
                                    R.color.text_input_hint
                                ))
                            )
                        )
                    }
                    Spacer(modifier = Modifier.height(dimens.titleBottomMargin))

                }

                // 富文本内容
                itemsIndexed(noteState.contents) { index, item ->
                    when (item) {
                        is EditorContent.TextBlock -> {
                            ShowTextBlock(
                               item = item,
                               index =  index,
                                contents=noteState.contents ,
                                darkTheme =  darkTheme
                            )
                        }

                        is EditorContent.ImageBlock -> {
                            val bitmap = item.uri.openFileDescriptorSafely(LocalContext.current)?.use {
                                BitmapFactory.decodeFileDescriptor(it.fileDescriptor).asShared()
                            }
                            if (bitmap == null) return@itemsIndexed

                            val measuredWidth = bitmap.width
                            val measuredHeight = bitmap.height
                            val shortEdge = max(GlobalContext.screenWidth, GlobalContext.screenHeight)
                            val scaleModifier = when (item.scaleMode) {
                                ImageBlockScaleMode.Large -> Modifier.fillMaxWidth()
                                ImageBlockScaleMode.Origin -> Modifier.width(measuredWidth.px2dp.dp)
                                ImageBlockScaleMode.Small -> {
                                    val widthPx = when {
                                        measuredWidth > shortEdge -> shortEdge * 0.4f
                                        measuredWidth > shortEdge / 2 -> measuredWidth * 0.4f
                                        else -> measuredWidth * 0.6f
                                    }
                                    Modifier.width(widthPx.toInt().px2dp.dp)
                                }
                            }
                            Logger.d("showImageBlock", "ImageBlockScaleMode: ${item.scaleMode}, measuredWidth: $measuredWidth, measuredWidth: $measuredHeight")

                            Image(
                                bitmap = bitmap.asImageBitmap(),
                                contentDescription = null,
                                contentScale = ContentScale.FillWidth,
                                modifier = Modifier
                                    .then(scaleModifier)
                                    .padding(bottom = 12.dp)
                            )
                        }

                        is EditorContent.TodoBlock -> {
                            ShowTodoBlock(
                                item=item,
                                darkTheme =darkTheme)
                        }

                        is EditorContent.AudioBlock -> {
                            AudioBlockComponent(
                                darkTheme = darkTheme,
                                focusRequester = FocusRequester(),
                                audioPath = item.audioPath,
                                onStopRecordClick = {

                                },
                                onMoreClick = {},
                                onAudioToTextClick = {},
                                onDeleteClick = {},
                                onEnterKeyPressed = {
                                },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(start = 0.dp, end = 0.dp),
                                isTabletPreviewMode = true
                            )
                        }

                        is EditorContent.RichTextV2 -> {}
                    }
                }

                repeat(bottomScrollPaddingCount) {
                    item {
                        Spacer(
                            modifier = Modifier
                                .height(1000.dp)
                        )
                    }
                }
            }
        }
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .clipToBounds()
        ) {
            withTransform({
                translate(
                    dashBoardPreviewViewModel.translation.x,
                    dashBoardPreviewViewModel.translation.y,
                )
                scale(0.67f, 0.67f, Offset.Zero)
            }) {
                with(drawContext.canvas.nativeCanvas) {
                    val checkPoint = saveLayer(null, null)
                    dashBoardPreviewViewModel.persisPaths.fastForEach { paths ->
                        paths.fastForEach {
                            it.draw(this@withTransform)
                        }
                    }
                    restoreToCount(checkPoint)
                }
            }
        }
    }
}


/**
 * 选择分类
 */
@Composable
internal fun SelectCategory(
    showPopup: Boolean,
    onDismiss: () -> Unit,
    currentCategoryId: String,
    onSelected: (NoteCategory) -> Unit,
    viewModel: DashboardModel = hiltViewModel(),
    topAppBarHeight: Int,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    val screenWidthPx = with(density) {
        LocalConfiguration.current.screenWidthDp.dp.roundToPx()
    }
    val popupWidthPx = with(density) { 226.dp.roundToPx() }
    val horizontalPaddingPx = with(density) { 16.dp.roundToPx() }

    // 当前分类名称
    val listNoteCategoryState by viewModel.listNoteCategoryState.collectAsState()
    var showNewCategory by remember { mutableStateOf(false) }
    // state作为flow存在并发可能，因此需要用临时变量，保证类型转换安全
    val tmpListNoteCategoryState = listNoteCategoryState
    if (showPopup && tmpListNoteCategoryState is ListNoteCategoryState.Success) {
        Popup(
            alignment = Alignment.TopEnd,
            onDismissRequest = { onDismiss() },
            offset = IntOffset(226/2 + 28, 80),
            properties = PopupProperties(
                focusable = true,
                excludeFromSystemGesture = true,
                dismissOnBackPress = true
            )
        ) {
            Surface(
                elevation = 8.dp,
                shape = RoundedCornerShape(8.dp),
                modifier = modifier.width(226.dp)
            ) {
                Box(
                    modifier = Modifier
                        .width(226.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .background(
                            colorResource(R.color.bg_dialog)
                        )
                ) {
                    Column(modifier = Modifier) {
                        // Category list
                        LazyColumn(
                            modifier = Modifier
                                .heightIn(max = 292.dp)// 限制弹出菜单高度，使其可以滚动
                                .padding(16.dp)
                        ) {
                            items(tmpListNoteCategoryState.items) { item ->
                                if (item.categoryId != -1L) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 8.dp)
                                            .clickable {
                                                // 更改当前note分类
                                                onSelected(item)
                                                onDismiss()
                                            }
                                    ) {
                                        Image(
                                            painter = painterResource(id = item.icon),
                                            contentDescription = null,
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        var textColor = colorResource(R.color.text_title)
                                        if (item.categoryId == currentCategoryId.toLongOrNull()) {
                                            textColor =
                                                colorResource(R.color.text_category_list_selected)
                                        } else if (currentCategoryId.isEmpty() && item.categoryId == 1L) {
                                            textColor =
                                                colorResource(R.color.text_category_list_selected)
                                        }
                                        AutoScrollText(
                                            text = item.name+"(${item.noteCounts})",
                                            color = textColor,
                                            modifier = Modifier
                                                .weight(1f, fill = false)
                                                .wrapContentWidth()
                                        )
                                    }
                                }
                            }
                        }

                        Divider(color = colorResource(R.color.bg_outline_10), thickness = 1.dp)

                        // New Category
                        val context = LocalContext.current
                        val tip = stringResource(R.string.dialog_category_name_title)
                        Box(
                            modifier = Modifier
                                .padding(16.dp)
                                .fillMaxWidth()
                                .clickable {
//                                    Toast.makeText(context, tip, Toast.LENGTH_SHORT).show()
                                    onDismiss()
                                    showNewCategory = true
                                }
                        ) {
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Image(
                                    painter = painterResource(id = R.drawable.ic_category_add),
                                    contentDescription = null,
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = stringResource(R.string.dialog_category_name_title),
                                    color = colorResource(R.color.text_title),
                                    fontSize = 14.sp
                                )
                            }
                        }
                    }
                }

            }
        }
    }

    if (showNewCategory) {
        CategoryScreen(
            isPreviewMode = true,
            isAddCategoryMode = true,
            onDismissRequest = {
                viewModel.tempNewCategoryName = ""
                viewModel.tempNewColorIndex = 1
                showNewCategory = false
            }
        )
    }

}
private const val TAG = "PreviewComponents"