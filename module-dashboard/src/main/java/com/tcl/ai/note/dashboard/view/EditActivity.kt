package com.tcl.ai.note.dashboard.view

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.tcl.ai.note.base.BaseActivity
import com.tcl.ai.note.dashboard.track.AnalyticsEditScreenModel
import com.tcl.ai.note.handwritingtext.repo.NoteRepository2
import com.tcl.ai.note.handwritingtext.richtext.utils.NoteContentUtil
import com.tcl.ai.note.handwritingtext.ui.EditScreen2
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.state.RichTextDataState
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.theme.NoteTclTheme
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.runBlocking

/**
 * 编辑页面
 */
@AndroidEntryPoint
class EditActivity : BaseActivity() {

    companion object {
        private const val TAG = "EditActivity"
        private const val PARAM_NOTE_ID = "noteId"
        private const val PARAM_IS_PEN = "isPen"

    }

    // 获取RichTextViewModel2实例
    private val richTextViewModel: RichTextViewModel2 by viewModels()

    // 获取SuniaDrawViewModel实例
    private val suniaDrawViewModel: SuniaDrawViewModel by viewModels()

    // 保存noteId和状态，用于onDestroy时的判断
    private var savedNoteId: Long? = null
    private var savedUiState: RichTextDataState? = null
    private var savedDrawStrokeEmpty: Boolean = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Logger.d(TAG, "onCreate: $intent")
        enableEdgeToEdge()
        // 分析编辑界面的埋点情况
        lifecycleScope.launchIO {
            AnalyticsEditScreenModel.init()
        }
        // 通过 Intent 获取参数（可能为null表示新建）
        val isPen = intent.getBooleanExtra(PARAM_IS_PEN, false)
        val noteIdString = intent.getStringExtra(PARAM_NOTE_ID)
        val noteId = noteIdString?.toLongOrNull()
        setContent {
            NoteTclTheme {
                EditScreen2(
                    noteId = noteId,
                    isPen = isPen,
                )
            }
        }
    }


    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Logger.d(TAG, "onNewIntent: $intent")
        // 更新 Intent，以便 Compose 可以获取到新的参数
        setIntent(intent)
    }

    /**
     *重新设置enableEdgeToEdge() 不然暗黑模式切换成白天模式时 会导致看不清状态栏了
     */
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enableEdgeToEdge()
    }

    override fun onStop() {
        super.onStop()
        Logger.d(TAG, "onStop" )

        // 在onStop时保存当前状态，用于onDestroy时的判断
        saveCurrentStateForDestroy()
    }

    /**
     * 保存当前状态用于onDestroy时的判断
     */
    private fun saveCurrentStateForDestroy() {
        try {
            val noteIdString = intent.getStringExtra(PARAM_NOTE_ID)
            val intentNoteId = noteIdString?.toLongOrNull()
            val effectiveNoteId = intentNoteId ?: richTextViewModel.mNoteId
            val uiState = richTextViewModel.uiState.value
            val drawStrokeEmpty = suniaDrawViewModel.drawStokeVisibleRectState.value.isEmpty

            savedNoteId = effectiveNoteId
            savedUiState = uiState
            savedDrawStrokeEmpty = drawStrokeEmpty
        } catch (e: Exception) {
            Logger.e(TAG, "Error saving state for destroy: ${e.message}")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Logger.d(TAG, "onDestroy" )

        // 重新从数据库查询数据进行准确判断，避免uiState中未保存数据的影响
        val noteIdToCheck = savedNoteId
        Logger.d(TAG, "onDestroy savedNoteId: $noteIdToCheck, savedDrawStrokeEmpty: $savedDrawStrokeEmpty")

        // 如果有效的noteId存在，重新从数据库查询数据进行准确判断
        if (noteIdToCheck != null && noteIdToCheck > 0) {
            Logger.d(TAG, "onDestroy - querying database for accurate content check...")

            try {
                runBlocking {
                    // 重新从数据库查询Note数据，确保判断的准确性
                    val noteFromDb = NoteRepository2.get(noteIdToCheck)
                    Logger.d(TAG, "onDestroy - queried note from database: ${noteFromDb?.noteId}")

                    if (noteFromDb != null) {
                        // 使用数据库中的实际数据创建RichTextDataState进行判断
                        val dbUiState = RichTextDataState(
                            noteId = noteFromDb.noteId,
                            title = noteFromDb.title,
                            content = noteFromDb.content,
                            images = noteFromDb.contents.filterIsInstance<com.tcl.ai.note.handwritingtext.database.entity.EditorContent.ImageBlock>(),
                            audios = noteFromDb.contents.filterIsInstance<com.tcl.ai.note.handwritingtext.database.entity.EditorContent.AudioBlock>()
                        )

                        // 判断Note内容是否为空（包括富文本内容和手绘内容）
                        val isContentEmpty = NoteContentUtil.isContentEmpty(dbUiState)
                        val isDrawEmpty = savedDrawStrokeEmpty

                        if (isContentEmpty && isDrawEmpty) {
                            richTextViewModel.deleteOneNote(noteIdToCheck)
                            Logger.d(TAG, "Empty note deleted on activity destroy: $noteIdToCheck")
                        }
                    }
                }
            } catch (e: Exception) {
                Logger.e(TAG, "Failed to check/delete empty note: ${e.message}")
            }
        } else {
            Logger.d(TAG, "No valid noteId found for deletion check")
        }
    }
}

