package com.tcl.ai.note.dashboard.vm

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.geometry.Offset
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.handwritingtext.repo.DrawBoardRepository
import com.tcl.ai.note.handwritingtext.vo.DrawPathDisplay
import com.tcl.ai.note.utils.launchIO

class DashBoardPreviewViewModel : ViewModel() {
    var translation by mutableStateOf(Offset.Zero)

    private val _persisPaths = mutableStateListOf<List<DrawPathDisplay>>()
    val persisPaths: List<List<DrawPathDisplay>>
        get() = _persisPaths

    fun loadStrokes(noteId: Long?) {
        viewModelScope.launchIO {
            _persisPaths.clear()
            if (noteId == -1L || noteId == null)
                return@launchIO
            val draw = DrawBoardRepository.getDrawByNoteIdBlock(noteId)
            draw?.let { drawEntity ->
                _persisPaths.add(drawEntity.strokes.map { it.toDrawPathDisplay() })
            }
        }
    }
}