package com.tcl.ai.note.dashboard.vm

import android.content.res.Resources
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.base.R
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.dashboard.intent.ConfigIntent
import com.tcl.ai.note.dashboard.states.ConfigState
import com.tcl.ai.note.dashboard.states.ListNoteCategoryState
import com.tcl.ai.note.dashboard.states.ListNotesUiState
import com.tcl.ai.note.dashboard.states.NoteState
import com.tcl.ai.note.handwritingtext.database.CategoryRepository
import com.tcl.ai.note.handwritingtext.database.NoteRepository
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.database.entity.NoteListItem
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.CategoryColors
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class DashboardModel : ViewModel() {

    private val _configState = MutableStateFlow(ConfigState())
    val configState = _configState.asStateFlow()

    // 首页笔记数据状态的流
    private val _listNotesUiState = MutableStateFlow<ListNotesUiState>(ListNotesUiState.Loading)
    val listNotesUiState: StateFlow<ListNotesUiState> = _listNotesUiState.asStateFlow()

    // 分类列表数据状态的流
    private val _listNoteCategoryState =
        MutableStateFlow<ListNoteCategoryState>(ListNoteCategoryState.Loading)
    val listNoteCategoryState: StateFlow<ListNoteCategoryState> =
        _listNoteCategoryState.asStateFlow()

    // 首页列表数据分页相关状态
    //当前页
    var currentPage by mutableIntStateOf(1)
    // 每页Note数据量
    private val itemsPerPage = 10
    // 是否还有下一页数据
    var hasMore by mutableStateOf(true)
        private set
    // 是否正在加载
    var isLoading by mutableStateOf(false)
        private set

    // 是否显示FloatingActionButton
    var isFabVisible by mutableStateOf(true)
        private set

    // 是否显示添加分类弹窗
    var isNewCategoryVisible by mutableStateOf(false)
        private set

    // 是否添加模式（有添加/修改两种模式，默认添加模式）
    var isAddCategoryMode by mutableStateOf(true)
        private set

    //当前选择的分类
    private val _currentCategoryId = MutableStateFlow("")
    val currentCategoryId: StateFlow<String> = _currentCategoryId.asStateFlow()
    private val _currentCategoryName = MutableStateFlow("")
    val currentCategoryName: StateFlow<String> = _currentCategoryName.asStateFlow()
    private val _currentColorIndex = MutableStateFlow("")
    val currentColorIndex: StateFlow<String> = _currentColorIndex.asStateFlow()
    private val _currentCategoryIcon = MutableStateFlow("")
    val currentCategoryIcon: StateFlow<String> = _currentCategoryIcon.asStateFlow()

    // 是否显示移动到分类弹窗
    private val _showMoveToCategoryDialog = MutableStateFlow(false)
    val showMoveToCategoryDialog: StateFlow<Boolean> = _showMoveToCategoryDialog.asStateFlow()

    // 是否显示分类选择弹窗
    private val _showCategoryPopup = MutableStateFlow(false)
    val showCategoryPopup: StateFlow<Boolean> = _showCategoryPopup.asStateFlow()

    // 是否显示排序方式选择提示框
    private val _shownSortOrderDialog = MutableStateFlow(false)
    val shownSortOrderDialog: StateFlow<Boolean> = _shownSortOrderDialog.asStateFlow()


    // 当前Note
    private val _note = MutableStateFlow<Note?>(null)
    val note:MutableStateFlow<Note?> = _note
    private val _noteState = MutableStateFlow(NoteState())
    val noteState: StateFlow<NoteState> = _noteState.asStateFlow()

    // 新建分类时的临时状态
    // 临时分类名称
    var tempNewCategoryName by mutableStateOf("")
    // 临时分类颜色
    var tempNewColorIndex by mutableIntStateOf(1)

    // 当前列表点击的noteId
    private val _currentNoteId = MutableStateFlow<Long>(-1L)
    val currentNoteId: StateFlow<Long> = _currentNoteId.asStateFlow()


    //系统语言状态
    private val _currentSysLanguage = MutableStateFlow(getCurrentSysLanguage())
    val currentSysLanguage: StateFlow<String> = _currentSysLanguage.asStateFlow()

    //获取当前系统语言
    private fun getCurrentSysLanguage(): String {
        return Resources.getSystem().configuration.locales[0].language
    }
    fun updateSysLanguage() {
        _currentSysLanguage.value = getCurrentSysLanguage()
    }


    // 设置当前点击的noteId
    fun setCurrentNoteId(noteId: Long) {
        _currentNoteId.value = noteId
        handleIntent(ConfigIntent.UpdatePreviewNote(noteId))
    }

    /**
     * 首页 FloatingActionButton 显示隐藏状态更新
     * @param newIsFabVisible 是否显示
     */
    fun updateFabVisibleState(newIsFabVisible: Boolean) {
        isFabVisible = newIsFabVisible
    }

    /**
     * 是否显示移动到分类弹窗
     */
    fun updateShowMoveToCategoryDialogState(isVisible: Boolean){
        _showMoveToCategoryDialog.value = isVisible
    }

    // 是否显示分类选择弹窗
    fun updateShowCategoryPopupState(isVisible: Boolean){
        _showCategoryPopup.value = isVisible
    }

    fun updateShownSortOrderDialog(isVisible: Boolean){
        _shownSortOrderDialog.value = isVisible
    }

    /**
     * 更新编辑模式状态
     * @param enabled 是否处于长按编辑模式
     */
    private fun updateEditMode(enabled: Boolean) {
        _noteState.update {
            it.copy(editMode = enabled)
        }
    }

    /**
     * 更新长按下选中的notes数据
     */
    private fun updateSelectedNotes(selectedNotes:List<NoteListItem>){
        _noteState.update {
            it.copy(selectedNotes = selectedNotes)
        }
    }

    /**
     * 更新分类弹窗显示隐藏状态
     * @param isVisible 是否显示
     */
    fun updateNewCategoryVisibleState(isVisible: Boolean) {
        isNewCategoryVisible = isVisible
    }

    /**
     * 更新新建分类弹窗添加/修改状态
     * @param isAdd 是否为添加模式
     */
    fun updateNewCategoryModeState(isAdd: Boolean) {
        isAddCategoryMode = isAdd
    }

    /**
     * 更新列表排序方式（创建时间-默认/更新时间）
     * @param isCreateTime 是否按照创建时间排序
     */
    fun updateSortModeState(isCreateTime: Boolean) {
        viewModelScope.launch {
            _noteState.update {
                it.copy(isCreateTimeSort = isCreateTime)
            }
            AppDataStore.putBoolean("isCreateTimeSort",isCreateTime)
        }

    }

    /**
     * 更新选中的分类
     * @param noteCategory 要更新的分类
     */
    private fun updateSelectedCategory(noteCategory: NoteCategory) {
        viewModelScope.launch {
            // 重置分页状态
            currentPage = 1
            hasMore = false
            if(noteCategory.categoryId!=-1L){
                AppDataStore.putStringData("currentCategoryId", noteCategory.categoryId.toString())
                AppDataStore.putStringData("currentCategoryName", noteCategory.name)
                AppDataStore.putStringData("currentColorIndex", noteCategory.colorIndex.toString())
                AppDataStore.putStringData("currentCategoryIcon", noteCategory.icon.toString())
                _currentCategoryId.value = noteCategory.categoryId.toString()
                _currentCategoryName.value = noteCategory.name
                _currentColorIndex.value = noteCategory.colorIndex.toString()
                _currentCategoryIcon.value = noteCategory.icon.toString()
            }else{
                AppDataStore.putStringData("currentCategoryId", "")
                AppDataStore.putStringData("currentCategoryName", "")
                AppDataStore.putStringData("currentColorIndex", "")
                AppDataStore.putStringData("currentCategoryIcon", "")
                _currentCategoryId.value = ""
                _currentCategoryName.value = ""
                _currentColorIndex.value = ""
                _currentCategoryIcon.value = ""
            }
            // 重置之前显示的内容
            _noteState.update {
                it.copy(note = null, noteId = -1, contents = emptyList())
            }
            loadInitialNotes()
        }
    }


    init {
        viewModelScope.launch {
            _noteState.update {
                it.copy(isCreateTimeSort = AppDataStore.getBoolean("isCreateTimeSort",true))
            }
        }
        loadDefaultConfigState() // 加载默认配置状态
        handleIntent(ConfigIntent.GetCategories) // 处理获取分类的意图
        handleIntent(ConfigIntent.GetNotes) // 处理获取笔记的意图
    }

    /**
     * 重置当前选择的分类
     */
    private fun resetToDefaultCategory(){
        _currentCategoryName.value = ""
        _currentColorIndex.value = ""
        _currentCategoryIcon.value = ""
        _currentCategoryIcon.value = ""
    }

    /**
     * 加载默认配置状态
     */
    private fun loadDefaultConfigState() {
        viewModelScope.launchIO {
            // 从DataStore中获取最新ViewType
            val savedViewType = AppDataStore.getStringData("view_type", DataStoreParam.VIEW_TYPE_LIST)
            launch {
                _configState.update {
                    it.copy(viewType = savedViewType)
                }
            }
        }
    }

    fun loadAllNotes(completion: (List<NoteListItem>) -> Unit) {
        viewModelScope.launch {
            try {
                var page = 1
                val allItems = mutableListOf<NoteListItem>()
                var hasMoreData = true

                while (hasMoreData) {
                    val (items, hasMore) = loadPagedNotes(page, itemsPerPage)
                    allItems.addAll(items)
                    hasMoreData = hasMore
                    page++
                }

                completion(allItems)
            } catch (e: Exception) {
                _listNotesUiState.value = ListNotesUiState.Error(e.message ?: "Unknown error")
            }
        }
    }

    private suspend fun loadPagedNotes(page: Int, pageSize: Int): Pair<List<NoteListItem>, Boolean> {
        val categoryId = _currentCategoryId.value.toLongOrNull()
        return if (categoryId != null && categoryId > 0) {
            if (_noteState.value.isCreateTimeSort) {
                NoteRepository.getNotesByCategoryIdPaged(categoryId, page, pageSize)
            } else {
                NoteRepository.getNotesByCategoryIdByModifyTimePaged(categoryId, page, pageSize)
            }
        } else {
            if (_noteState.value.isCreateTimeSort) {
                NoteRepository.getAllNotesPaged(page, pageSize)
            } else {
                NoteRepository.getAllNotesByModifyTimePaged(page, pageSize)
            }
        }
    }

    /**
     * 初始化加载列表数据
     */
    fun loadInitialNotes() {
        currentPage = 1
        hasMore = true
        loadNotes(currentPage)
    }


    /**
     * 加载Note列表数据
     */
    private fun loadNotes(page: Int) {
        viewModelScope.launch {
            if (isLoading) return@launch

            isLoading = true
            val currentItems = when (val current = _listNotesUiState.value) {
                is ListNotesUiState.Success -> current.items
                else -> emptyList()
            }
            try {
                val categoryId = _currentCategoryId.value.toLongOrNull()
                val result = if (categoryId != null && categoryId > 0) {
                    if (_noteState.value.isCreateTimeSort) {
                        // 按照创建时间排序
                        NoteRepository.getNotesByCategoryIdPaged(
                            categoryId,
                            page,
                            itemsPerPage
                        )
                    } else {
                        // 按照更新时间排序
                        NoteRepository.getNotesByCategoryIdByModifyTimePaged(
                            categoryId,
                            page,
                            itemsPerPage
                        )
                    }
                } else {
                    // 查询全部分类数据
                    if (_noteState.value.isCreateTimeSort) {
                        // 按照创建时间排序
                        NoteRepository.getAllNotesPaged(page, itemsPerPage)
                    } else {
                        // 按照更新时间排序
                        NoteRepository.getAllNotesByModifyTimePaged(page, itemsPerPage)
                    }
                }
                val newItems = result.first
                hasMore = result.second
                Logger.d("refresh:", "load success")
                _listNotesUiState.value = when {
                    page == 1 && newItems.isEmpty() -> {
                        ListNotesUiState.Success(emptyList(), hasMore = false)
                    }
                    page == 1 -> {
                        ListNotesUiState.Success(newItems, hasMore)
                    }
                    else -> {
                        val combined = currentItems + newItems
                        ListNotesUiState.Success(combined, hasMore)
                    }
                }
                if(page==1 && newItems.isNotEmpty()){
                    val currentNoteId = _noteState.value.noteId
                    val addNoteId = AppDataStore.getAddNoteId()
                    if(addNoteId>0){
                        AppDataStore.putAddNoteId(0)
                        loadNote(addNoteId)
                    }else if(currentNoteId > 0 && newItems.any { it.noteId == currentNoteId }){
                        loadNote(_noteState.value.noteId)
                    }else{
                        loadNote(newItems[0].noteId)
                    }
                }else if(page==1){
                    _noteState.update {
                        it.copy(note = null, noteId = -1, contents = emptyList())
                    }
                }
                currentPage = if (newItems.isNotEmpty()) page else page - 1
            } catch (e: Exception) {
                _listNotesUiState.value = ListNotesUiState.Error(e.message ?: "Unknown error")
            } finally {
                isLoading = false
            }
        }
    }

    // 加载需要显示的Note
    private fun loadNote(noteId:Long)=viewModelScope.launch{
        if(_currentNoteId.value!=noteId){
            _currentNoteId.value =noteId
        }
        // 初始化第一条需要显示的数据
        NoteRepository.getNote(noteId)?.let { note->
            if(_currentNoteId.value!=noteId){
                _currentNoteId.value =noteId
            }
            _noteState.update {
                it.copy(
                    note = note,
                    noteId = note.noteId,
                    contents = note.contents,
                )
            }
            _noteState.update { state ->
                state.copy(currentNoteCategory = note.categoryId?.let { id ->
                    CategoryRepository.getCategory(id)
                })
            }
        }?: run {
            _noteState.update {
                it.copy(note = null, noteId = -1, contents = emptyList())
            }
        }
    }

    // 加载需要显示的Note
    suspend fun loadNoteContents(noteIds: List<Long>): List<EditorContent> {
        // 初始化第一条需要显示的数据
        val contents = mutableListOf<EditorContent>()
        noteIds.forEach {
            val content = NoteRepository.getNote(it)
            Logger.d("loadNoteContents", "noteId: $it, content: ${content?.contents}")
            contents.addAll(content?.contents?: emptyList())
        }
        return contents
    }

    /**
     * 加载下一页Note数据
     */
    fun loadMoreNotes() {
        if (hasMore && !isLoading) {
            loadNotes(currentPage + 1)
        }
    }

    /**
     * 修改搜索状态
     */
    fun updateSearchState(searching: Boolean) {
        _noteState.update {
            it.copy(
                isSearching = searching, searchText = if (searching) it.searchText else "",
            )
        }
    }


    /**
     * 搜索笔记数据
     */
    private fun searchNotes(text: String) {
        viewModelScope.launch {
            _noteState.update {
                it.copy(searchText = text, isSearching = if (text.isNotEmpty()) true else it.isSearching)
            }
            if (text.isNotEmpty()) {
                try {
                    val categoryId = _currentCategoryId.value.toLongOrNull()
                    val newItems = if (categoryId != null && categoryId > 0) {
                        NoteRepository.searchNotesByCategoryAndQuery(
                            text,
                            categoryId
                        )
                    } else {
                        NoteRepository.searchNotes(text)
                    }
                    _listNotesUiState.value = when {
                        newItems.isEmpty() -> {
                            ListNotesUiState.Success(emptyList(), hasMore = false)
                        }
                        else -> {
                            ListNotesUiState.Success(newItems, hasMore = false)
                        }
                    }
                    if(newItems.isNotEmpty()){
                        loadNote(newItems[0].noteId)
                    }else{
                        _noteState.update {
                            it.copy(note = null, noteId = -1, contents = emptyList())
                        }
                    }
                } catch (e: Exception) {
                    _listNotesUiState.value = ListNotesUiState.Error(e.message ?: "Unknown error")
                    _noteState.update {
                        it.copy(note = null, noteId = -1, contents = emptyList())
                    }
                } finally {
                    isLoading = false
                }
            } else {
                loadInitialNotes()
            }

        }
    }


    /**
     * 获取初始化的分类数据
     */
    private fun getCategories() {
        viewModelScope.launch {
            _listNoteCategoryState.value = ListNoteCategoryState.Loading
            try {
                val categories = CategoryRepository.getAllCategories()
                if (categories.isNotEmpty()) {
                    val allCategories = NoteCategory(categoryId = -1,
                            name = "",
                            colorIndex = 0
                        )
                    allCategories.noteCounts = NoteRepository.getNoteCount()
                    allCategories.icon = R.drawable.ic_all_notes
                    val listAllCategories = listOf(allCategories)+categories
                    _listNoteCategoryState.value = ListNoteCategoryState.Success(listAllCategories)
                    if (currentCategoryName.value.isEmpty()) {
                        resetToDefaultCategory()
                    }
                }else{
                    _listNoteCategoryState.value = ListNoteCategoryState.Error("无分类数据")
                }
            }catch (e: Exception) {
                _listNoteCategoryState.value = ListNoteCategoryState.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 刷新分类列表
     */
    private fun refreshCategories() {
        viewModelScope.launch {
            _listNoteCategoryState.value = ListNoteCategoryState.Loading
            try {
                val categories = CategoryRepository.getAllCategories()
                if (categories.isNotEmpty()) {
                    val allCategories = NoteCategory(categoryId = -1,
                        name = "",
                        colorIndex = 0
                    )
                    allCategories.noteCounts = NoteRepository.getNoteCount()
                    allCategories.icon = R.drawable.ic_all_notes
                    val listAllCategories = listOf(allCategories)+categories
                    _listNoteCategoryState.value = ListNoteCategoryState.Success(listAllCategories)
                }else{
                    _listNoteCategoryState.value = ListNoteCategoryState.Error("无分类数据")
                }
            }catch (e: Exception) {
                _listNoteCategoryState.value = ListNoteCategoryState.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 新增category
     */
    private fun addCategory(noteCategory: NoteCategory,isPreviewMode:Boolean) = viewModelScope.launch {
        val categoryId = CategoryRepository.addCategory(noteCategory)
        if (categoryId > 0) {
            tempNewCategoryName = ""
            tempNewColorIndex = 2
            if(_noteState.value.selectedNotes.isNotEmpty()){
                _noteState.update { it.copy(newCategory = CategoryRepository.getCategory(categoryId)) }
                // 将选中的notes移动到新增的分组中
                val noteIds = _noteState.value.selectedNotes.map { it.noteId }
                updateNotesCategoryId(noteIds, categoryId)
            } else {
                if(isPreviewMode){
                    // 将当前Note移之新增分类下
                    noteState.value.note?.let { updateNoteCategoryId(it.noteId, categoryId) }
                    _noteState.update { it.copy(newCategory = CategoryRepository.getCategory(categoryId)) }
                    // 刷新分类列表
                    refreshCategories()
                }else{
                    // 刷新分类列表及当前选中分类信息
                    getCategories()
                    // 更新当前选中的分类
                    AppDataStore.putStringData("currentCategoryId", categoryId.toString())
                    AppDataStore.putStringData("currentCategoryName", noteCategory.name)
                    AppDataStore.putStringData("currentColorIndex", noteCategory.colorIndex.toString())
                    AppDataStore.putStringData("currentCategoryIcon", determineIcon(noteCategory).toString())
                    _currentCategoryId.value = categoryId.toString()
                    _currentCategoryName.value = noteCategory.name
                    _currentColorIndex.value = noteCategory.colorIndex.toString()
                    _currentCategoryIcon.value = determineIcon(noteCategory).toString()
                    // 刷新列表数据
                    loadInitialNotes()
                }
            }

        }

    }

    /**
     * 确定分类icon
     */
    private fun determineIcon(category: NoteCategory): Int {
        // 这里是确定图标的逻辑，可以根据分类的某些属性或预置逻辑设置图标。
        if (category == null) return R.drawable.ic_all_notes
        val colorIndex = category.colorIndex ?: CategoryColors.NONE_COLOR
        return when (colorIndex) {
            CategoryColors.NONE_COLOR -> R.drawable.ic_category_uncategorised // 未分类
            CategoryColors.YELLOW_COLOR -> R.drawable.ic_category_yellow
            CategoryColors.ORANGE_COLOR -> R.drawable.ic_category_orange
            CategoryColors.PINK_COLOR -> R.drawable.ic_category_pink
            CategoryColors.PURPLE_COLOR -> R.drawable.ic_category_purple
            CategoryColors.BLUE_COLOR -> R.drawable.ic_category_blue
            CategoryColors.GREEN_COLOR -> R.drawable.ic_category_green
            else -> R.drawable.ic_all_notes
        }
    }

    /**
     * 删除一个Category
     * @param isDeleteSelectedCategoryNotes 是否删除分类下的Note数据
     */
    private fun deleteCategory(isDeleteSelectedCategoryNotes: Boolean, noteCategory: NoteCategory) =
        viewModelScope.launch {
            noteCategory?.let {
                val res = CategoryRepository.deleteCategory(noteCategory)
                if (res > 0) {
                    if (isDeleteSelectedCategoryNotes) {
                        // 删除分类下的note数据
                        deleteNotesByCategoryId(noteCategory.categoryId)
                    } else {
                        // 将该分类下的数据迁移到未分类下面
                        updateCategoryId(noteCategory.categoryId, 1)
                    }
                }
                // 将缓存中的当前分类信息设置为未分类
                val category = CategoryRepository.getCategory(1L)
                category?.let {
                    AppDataStore.putStringData("currentCategoryId", "1")
                    AppDataStore.putStringData("currentCategoryName", category.name)
                    AppDataStore.putStringData("currentColorIndex", category.colorIndex.toString())
                    AppDataStore.putStringData("currentCategoryIcon", category.icon.toString())
                    _currentCategoryId.value = "1"
                    _currentCategoryName.value = category.name
                    _currentColorIndex.value = category.colorIndex.toString()
                    _currentCategoryIcon.value = category.icon.toString()
                }?:run {
                    resetToDefaultCategory()
                }

                // 刷新分类列表
                getCategories()
                // 重新刷新数据
                loadInitialNotes()
            }
        }

    /**
     * 重命名一个Category
     */
    private fun renameCategory(noteCategory: NoteCategory) = viewModelScope.launch {
        tempNewCategoryName = ""
        tempNewColorIndex = 2
        CategoryRepository.updateCategory(noteCategory)
        val newCategory = CategoryRepository.getCategory(noteCategory.categoryId)
        newCategory?.let {
            AppDataStore.putStringData("currentCategoryName", newCategory.name)
            AppDataStore.putStringData("currentColorIndex",newCategory.colorIndex.toString())
            AppDataStore.putStringData("currentCategoryIcon", newCategory.icon.toString())
            _currentCategoryId.value = newCategory.categoryId.toString()
            _currentCategoryName.value = newCategory.name
            _currentColorIndex.value = newCategory.categoryId.toString()
            _currentCategoryIcon.value = newCategory.icon.toString()
        }?:run {
            resetToDefaultCategory()
        }


        // 刷新分类列表
        getCategories()
    }


    /**
     * 删除一个Category下的notes
     */
    private fun deleteNotesByCategoryId(categoryId: Long) = viewModelScope.launch {
        NoteRepository.deleteNotesByCategoryId(categoryId)
        // 刷新分类列表
        getCategories()
        // 刷新列表
        loadInitialNotes()
    }

    /**
     * 删除选中的notes
     */
    private fun deleteNotes(listNote: List<Long>) = viewModelScope.launch {
        NoteRepository.deleteNotes(listNote)
        // 刷新分类列表
        getCategories()
        // 刷新列表
        loadInitialNotes()
    }

    /**
     * 删除一条Note
     */
    private fun deleteOneNote(noteId:Long) = viewModelScope.launch {
        NoteRepository.deleteNote(noteId)
        // 重置之前显示的内容
        _noteState.update {
            it.copy(note = null, noteId = -1, contents = emptyList())
        }
        // 刷新分类列表
        getCategories()
        // 刷新列表
        loadInitialNotes()
    }

    /**
     * 更新单条指定 Note 的 categoryId
     */
    private fun updateNoteCategoryId(noteId: Long, categoryId: Long) = viewModelScope.launch{
        NoteRepository.updateNoteCategoryId(noteId,categoryId)
        // 刷新分类列表
        getCategories()
        // 刷新列表
        loadInitialNotes()
    }


    /**
     * 更新指定 Note 的 categoryId
     */
    private fun updateNotesCategoryId(listNote: List<Long>, categoryId: Long) =
        viewModelScope.launch {
            NoteRepository.updateNotesCategoryId(listNote, categoryId)
            updateSelectedNotes(listOf())
            // 将缓存中的当前分类信息设置为移动后的分类
            val category = CategoryRepository.getCategory(categoryId)
            category?.let {
                AppDataStore.putStringData("currentCategoryId", category.categoryId.toString())
                AppDataStore.putStringData("currentCategoryName", category.name)
                AppDataStore.putStringData("currentColorIndex", category.colorIndex.toString())
                AppDataStore.putStringData("currentCategoryIcon", category.icon.toString())
                _currentCategoryId.value = category.categoryId.toString()
                _currentCategoryName.value = category.name
                _currentColorIndex.value = category.colorIndex.toString()
                _currentCategoryIcon.value = category.icon.toString()
            }?:run {
                resetToDefaultCategory()
            }
            // 刷新分类列表
            getCategories()
            // 刷新列表
            loadInitialNotes()
        }

    /**
     *  更新Notes的分类信息
     */
    private fun updateCategoryId(categoryId: Long, newCategoryId: Long) = viewModelScope.launch {
        NoteRepository.updateCategoryId(categoryId, newCategoryId)
        // 刷新分类列表
        getCategories()
    }



    fun handleIntent(intent: ConfigIntent) {
        Logger.d(TAG, "intent : $intent")
        when (intent) {
            is ConfigIntent.ChangeViewType -> {
                viewModelScope.launch {
                    // 将viewType类型保存到DataStore以免每次切换分类列表刷新时viewType数据状态重置
                    AppDataStore.putStringData("view_type", intent.viewType)
                }
                _configState.update { it.copy(viewType = intent.viewType) }
            }

            is ConfigIntent.GetNotes -> {
                loadInitialNotes()
            }

            is ConfigIntent.GetCategories -> {
                getCategories()
            }

            is ConfigIntent.SearchNotes -> {
                searchNotes(intent.text)
            }

            is ConfigIntent.AddCategory -> {
                addCategory(intent.category,intent.isPreviewMode)
            }

            is ConfigIntent.DeleteCategory -> {
                deleteCategory(intent.isDeleteSelectedCategoryNotes, intent.category)
            }

            is ConfigIntent.DeleteNotesByCategoryId -> {
                deleteNotesByCategoryId(intent.categoryId)
            }

            is ConfigIntent.RenameCategory -> {
                renameCategory(intent.category)
            }

            is ConfigIntent.UpdateSelectedCategory -> {
                updateSelectedCategory(intent.noteCategory)
            }

            is ConfigIntent.DeleteNotes -> {
                deleteNotes(intent.listNotes)
            }

            is ConfigIntent.DeleteOneNote -> {
                deleteOneNote(intent.noteId)
            }

            is ConfigIntent.UpdateNotesCategoryId -> {
                updateNotesCategoryId(intent.listNotes, intent.categoryId)
            }

            is ConfigIntent.UpdatePreviewNote -> {
                _currentNoteId.value = intent.noteId
                loadNote(intent.noteId)
            }

            is ConfigIntent.UpdateNoteCategoryId -> {
                updateNoteCategoryId(intent.noteId, intent.categoryId)
            }

            is ConfigIntent.UpdateEditMode -> {
                updateEditMode(intent.enabled)
            }

            is ConfigIntent.UpdateSelectedNotes -> {
                updateSelectedNotes(intent.selectedNotes)
            }

            else -> {} // 注意还有其他未用上的intent
        }
    }

    companion object {
        private const val TAG = "DashboardModel"
    }
}