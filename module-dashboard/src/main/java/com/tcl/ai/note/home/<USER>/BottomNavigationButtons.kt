package com.tcl.ai.note.home.components

import android.annotation.SuppressLint
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.journalhome.components.BottomOperateViewHeight
import com.tcl.ai.note.theme.TclTheme

import com.tcl.ai.note.utils.getNavigationBarHeight
import com.tcl.ai.note.utils.isButtonNavigation
import com.tcl.ai.note.widget.clickableHover

/**
 * 底部导航按钮组件
 *
 * @param modifier 修饰符
 * @param selectedTab 当前选中的Tab，为null时使用内部状态管理（非受控模式）
 * @param onToTab Tab切换回调，传递选中的NavigationTab
 */
@SuppressLint("DesignSystem")
@Composable
internal fun BottomNavigationButtons(
    modifier: Modifier = Modifier,
    selectedTab: NavigationTab? = null,
    onToTab: (NavigationTab) -> Unit = {},
) {
    // 支持受控和非受控模式
    var internalSelectedTab by remember { mutableStateOf(selectedTab ?:NavigationTab.NOTE) }
    val currentSelectedTab = selectedTab ?: internalSelectedTab
    val hasNavigationBar = isButtonNavigation()
    val navigationBarHeight = getNavigationBarHeight()
    Row(
        modifier = Modifier.Companion
            .background(color = TclTheme.colorScheme.bottomBarColor)
            //.conditionalNavigationBarsPadding(hasNavigationBar),
            .padding(bottom = navigationBarHeight), // 增加底部间距，为导航按钮留出空间
        horizontalArrangement = Arrangement.Center
    ) {
        NavigationButton(
            modifier = Modifier.Companion.weight(1f),
            tab = NavigationTab.NOTE,
            isSelected = currentSelectedTab == NavigationTab.NOTE,
            onClick = {
                // 如果是非受控模式，更新内部状态
                if (selectedTab == null) {
                    internalSelectedTab = NavigationTab.NOTE
                }
                onToTab(NavigationTab.NOTE)
            }
        )

        // Journal 按钮
        NavigationButton(
            modifier = Modifier.Companion.weight(1f),
            tab = NavigationTab.JOURNAL,
            isSelected = currentSelectedTab == NavigationTab.JOURNAL,
            onClick = {
                // 如果是非受控模式，更新内部状态
                if (selectedTab == null) {
                    internalSelectedTab = NavigationTab.JOURNAL
                }
                onToTab(NavigationTab.JOURNAL)
            }
        )
    }
}
/**
 * 单个导航按钮
 */
@Composable
private fun NavigationButton(
    modifier: Modifier,
    tab: NavigationTab,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val iconColor by animateColorAsState(
        targetValue = colorResource(
            if (isSelected) tab.selectedColor else tab.unselectedColor
        ),
        animationSpec = tween(durationMillis = 300),
        label = "navigation_icon_color"
    )

    val textColor by animateColorAsState(
        targetValue = colorResource(
            if (isSelected) tab.selectedColor else tab.unselectedColor
        ),
        animationSpec = tween(durationMillis = 300),
        label = "navigation_text_color"
    )

    Column(
        modifier = modifier
            .size(180.dp, BottomOperateViewHeight)
            .clickableHover(
                onClick = onClick,
                role = Role.Tab
            )
            .padding(horizontal = 16.dp, vertical = 8.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Image(
            painter = painterResource(id = tab.iconRes),
            contentDescription = stringResource(tab.textRes),
            modifier = Modifier.size(20.dp),
            colorFilter = ColorFilter.tint(iconColor)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = stringResource(tab.textRes),
            fontSize = 12.sp,
            color = textColor
        )
    }
}

/**
 * 导航标签枚举
 */
enum class NavigationTab(
    val iconRes: Int,
    val textRes: Int,
    val selectedColor: Int,
    val unselectedColor: Int
) {
    NOTE(
        iconRes = R.drawable.ic_tab_note, // 使用星形图标代表 Note
        textRes = R.string.note,
        selectedColor = R.color.text_category_list_selected,
        unselectedColor = R.color.text_title
    ),
    JOURNAL(
        iconRes = R.drawable.ic_tab_journal, // 使用书签图标代表 Journal
        textRes = R.string.journal,
        selectedColor = R.color.bg_float_action_button, // 橙色
        unselectedColor = R.color.text_title
    )
}