package com.tcl.ai.note.home.utils

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.transform
import kotlinx.coroutines.CancellationException
import com.tcl.ai.note.utils.Logger

/**
 * Flow 性能追踪工具
 */
object FlowPerformanceTracker {
    
    /**
     * 测量 Flow 的执行耗时
     * @param tag 日志标签
     * @param operationName 操作名称
     */
    fun <T> Flow<T>.measureTime(tag: String, operationName: String): Flow<T> {
        var startTime = 0L

        return this
            .onStart {
                startTime = System.currentTimeMillis()
                Logger.d(tag, "[$operationName] Started at: $startTime")
            }
            .onCompletion { throwable ->
                val endTime = System.currentTimeMillis()
                val duration = endTime - startTime
                when (throwable) {
                    null -> {
                        Logger.d(tag, "[$operationName] Completed at: ${endTime}, Duration: ${duration}ms")
                    }
                    is CancellationException -> {
                        Logger.d(tag, "[$operationName] Cancelled at: ${endTime}, Duration: ${duration}ms")
                    }

                    else -> {
                        Logger.e(tag, "[$operationName] Failed: ${throwable.message}, Duration: ${duration}ms")
                    }
                }
            }
    }
    
    /**
     * 测量 Flow 中每次数据发射的耗时
     * @param tag 日志标签
     * @param operationName 操作名称
     */
    fun <T> Flow<T>.measureEachEmission(tag: String, operationName: String): Flow<T> {
        return transform { value ->
            val emissionStartTime = System.currentTimeMillis()
            emit(value)
            val emissionEndTime = System.currentTimeMillis()
            Logger.d(tag, "[$operationName] Emission duration: ${emissionEndTime - emissionStartTime}ms")
        }
    }
    
    /**
     * 测量数据转换操作的耗时
     * @param tag 日志标签
     * @param operationName 操作名称
     * @param transform 转换函数
     */
    fun <T, R> Flow<T>.measureTransform(
        tag: String,
        operationName: String,
        transform: (T) -> R
    ): Flow<R> {
        return this.transform { value ->
            val transformStartTime = System.currentTimeMillis()
            val result = transform(value)
            val transformEndTime = System.currentTimeMillis()
            Logger.d(tag, "[$operationName] Transform duration: ${transformEndTime - transformStartTime}ms")
            emit(result)
        }
    }
}
