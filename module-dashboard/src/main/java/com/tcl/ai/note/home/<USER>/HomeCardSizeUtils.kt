package com.tcl.ai.note.home.utils

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.home.components.base.rememberNoteLayoutConfig
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.rememberHeightScale
import com.tcl.ai.note.utils.rememberWidthScale
import kotlin.math.roundToInt

/**
 * 首页卡片尺寸配置数据类
 */
data class HomeCardSizeConfig(
    val originalWidth: Int,
    val originalHeight: Int,
    val scaledWidth: Int,
    val scaledHeight: Int,
    val widthScale: Float,
    val heightScale: Float
)

/**
 * 记住卡片尺寸配置的 Composable 函数
 * 封装了卡片尺寸计算和缩放逻辑，供其他组件复用
 * 
 * @return CardSizeConfig 包含原始尺寸、缩放后尺寸和缩放比例的配置对象
 */
@Composable
fun rememberCardSizeConfig(): HomeCardSizeConfig {
    val contentConfig = rememberNoteLayoutConfig()
    val cardSize = contentConfig.cardSize
    val widthScale = rememberWidthScale()
    val heightScale = rememberHeightScale()
    
    return remember(cardSize, widthScale, heightScale) {
        val originalWidth = cardSize.width
        val originalHeight = cardSize.height
        val scaledWidth = (originalWidth * widthScale).roundToInt()
        val scaledHeight = (originalHeight * heightScale).roundToInt()
        
        Logger.d("CardSizeUtils", "Original size: $originalWidth x $originalHeight")
        Logger.d("CardSizeUtils", "Scale factors: width=${widthScale}, height=${heightScale}")
        Logger.d("CardSizeUtils", "Scaled size: $scaledWidth x $scaledHeight")
        
        HomeCardSizeConfig(
            originalWidth = originalWidth,
            originalHeight = originalHeight,
            scaledWidth = scaledWidth,
            scaledHeight = scaledHeight,
            widthScale = widthScale,
            heightScale = heightScale
        )
    }
}

/**
 * 简化版本，只返回缩放后的宽度
 * 适用于只需要宽度的场景
 */
@Composable
fun rememberScaledCardWidth(): Dp {
    val config = rememberCardSizeConfig()
    return config.scaledWidth.dp
}

/**
 * 简化版本，只返回缩放后的高度
 * 适用于只需要高度的场景
 */
@Composable
fun rememberScaledCardHeight(): Dp {
    val config = rememberCardSizeConfig()
    return config.scaledHeight.dp
}

/**
 * 简化版本，返回缩放后的宽度和高度
 * 适用于需要宽高但不需要其他信息的场景
 */
@Composable
fun rememberScaledCardSize(): IntSize {
    val config = rememberCardSizeConfig()
    return IntSize(config.scaledWidth, config.scaledHeight)
}

/**
 * 获取宽度缩放比例
 * 适用于需要手动计算缩放的场景
 */
@Composable
fun rememberCardWidthScale(): Float {
    val config = rememberCardSizeConfig()
    return config.widthScale
}

/**
 * 获取高度缩放比例
 * 适用于需要手动计算缩放的场景
 */
@Composable
fun rememberCardHeightScale(): Float {
    val config = rememberCardSizeConfig()
    return config.heightScale
}

/**
 * 扩展函数：为 CardSizeConfig 添加便捷方法
 */

/**
 * 检查是否有缩放
 */
fun HomeCardSizeConfig.isScaled(): Boolean {
    return widthScale != 1f || heightScale != 1f
}

/**
 * 获取缩放比例的描述字符串
 */
fun HomeCardSizeConfig.getScaleDescription(): String {
    return "Scale: ${(widthScale * 100).toInt()}% x ${(heightScale * 100).toInt()}%"
}

/**
 * 计算指定尺寸按当前缩放比例的结果
 */
fun HomeCardSizeConfig.scaleSize(width: Dp, height: Dp): Pair<Dp, Dp> {
    return Pair(width * widthScale, height * heightScale)
}

/**
 * 计算指定宽度按当前缩放比例的结果
 */
fun HomeCardSizeConfig.scaleWidth(width: Dp): Dp {
    return width * widthScale
}

/**
 * 计算指定高度按当前缩放比例的结果
 */
fun HomeCardSizeConfig.scaleHeight(height: Dp): Dp {
    return height * heightScale
}
