package com.tcl.ai.note.home.utils

import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.home.model.CategoryIcon
import com.tcl.ai.note.home.model.HomeCategoryItemModel
import com.tcl.ai.note.utils.getCategoryIconColor
import com.tcl.ai.note.utils.getCategoryIconResId


/**
 * 未分类的分类ID
 */
const val TYPE_UN_CATEGORISED_ID = 1L

/**
 * 获取"全部"分类项
 */
fun allCategoryItem(totalNoteCount: Int): HomeCategoryItemModel {
    // 在开头添加"全部"分类
    val elementAllItem = HomeCategoryItemModel(
        categoryIcon = CategoryIcon(R.drawable.ic_all_notes, getCategoryIconColor(-1)),
        noteCounts = totalNoteCount,
        id = "", // 全部分类的ID为空字符串,
    )
    return elementAllItem
}
/**
 * 将 HomeCategoryItem 转换为 NoteCategory
 */
fun translateToCategory(
    categoryItem: HomeCategoryItemModel,
): NoteCategory {
    val noteCategory = NoteCategory(
        categoryId = categoryItem.id.toLong(),
        colorIndex = categoryItem.colorIndex,
        name = categoryItem.name ?: ""
    )
    return noteCategory
}

/**
 * 获取分类图标
 */
 fun getCategoryIcon(colorIndex: Int): CategoryIcon {
    return CategoryIcon(getCategoryIconResId(colorIndex), getCategoryIconColor(colorIndex))
}