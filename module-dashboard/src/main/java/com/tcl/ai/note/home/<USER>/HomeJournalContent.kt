package com.tcl.ai.note.home.journal

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.tcl.ai.note.anim.BottomBarAnim
import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.components.BottomNavigationButtons
import com.tcl.ai.note.home.components.NavigationTab
import com.tcl.ai.note.home.components.base.rememberNoteLayoutConfig
import com.tcl.ai.note.journaldashboard.ui.DeleteDialog
import com.tcl.ai.note.journalhome.components.AddNoteFloatingBtn
import com.tcl.ai.note.journalhome.vm.state.HomeNoteUiState
import com.tcl.ai.note.journalhome.vm.state.NoteListAction
import com.tcl.ai.note.journaldashboard.ui.JournalDashboard2
import com.tcl.ai.note.journalhome.components.BottomOperateViewHeight
import com.tcl.ai.note.journalhome.components.notelist.HomeNoteListTopTitle
import com.tcl.ai.note.journalhome.components.notelist.NoteContentBottomOperateView
import com.tcl.ai.note.journalhome.entity.HomeNoteItemEntity
import com.tcl.ai.note.journalhome.vm.state.HomeTitleMode
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getNavigationBarHeight
import com.tcl.ai.note.utils.isButtonNavigation
import com.tcl.ai.note.utils.judge
import kotlinx.coroutines.delay

/**
 * Journal 页面内容组件
 */
@Composable
internal fun HomeJournalContent(
    navController: NavController,
    modifier: Modifier = Modifier,
    homeNoteUiState: HomeNoteUiState,
    showBlur: Boolean = false,
    onEditJournal: (HomeNoteItemEntity) -> Unit = {},
    onAction: (NoteListAction) -> Unit = {}
) {
    //val columnCount = rememberGridColumnCount()
    val hasNavigationBar = isButtonNavigation()
    val navigationBarHeight = getNavigationBarHeight()

    //是否需要高斯模糊
    var showBlurState by remember { mutableStateOf(showBlur) }
    val finalModifier = remember(showBlurState) {
        if (showBlurState) {
            modifier
                .fillMaxSize()
                .blur(20.dp)
        } else {
            modifier.fillMaxSize()
        }
    }

    LaunchedEffect(showBlurState) {
        if (showBlurState) {
            delay(600)
            showBlurState = false
        }
    }

    Box(
        modifier = finalModifier
            .fillMaxSize()
            .background(color = colorResource(R.color.home_note_list_bg_color))
            //.conditionalNavigationBarsPadding(hasNavigationBar)
    ) {
        Column {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                Spacer(Modifier.statusBarsPadding())
                // 标题栏（仅平板显示）
                Logger.d("HomeJournalContent", "isSearchMode: ${homeNoteUiState.isSearchMode}, searchText: ${homeNoteUiState.searchText}")
                HomeNoteListTopTitle(
                    homeNoteUiState = homeNoteUiState,
                    onAction = onAction
                )
                JournalDashboard2(
                    navController = navController,
                    homeNoteUiState = homeNoteUiState,
                    setBlurState = { state ->
                        showBlurState = state
                    },
                    onItemLongClick = {
                        onAction(NoteListAction.OnItemLongClick(it))
                    },
                    onItemCheckedChange = { journalId, checked ->
                        onAction(NoteListAction.OnItemCheckedChange(journalId, checked))
                    },
                    onTitleChangeToNormal = {
                        onAction(NoteListAction.OnChangeTitleMode(HomeTitleMode.Normal))
                    },
                    onEditJournal = { journal ->
                        onEditJournal(journal)
                    },
                    onDeleteJournal = { journal ->
                        onAction(NoteListAction.OnShowDeleteItemDialog(true, journal))
                    },
                    onMoveJournal = { journal ->
                        onAction(NoteListAction.OnMoveItem(journal))
                    },
                )
            }
            // 悬浮按钮（在导航按钮上方）
            Column(modifier = Modifier
                .height(BottomOperateViewHeight + navigationBarHeight)
            ) {
                BottomBarAnim(
                    visibleA = homeNoteUiState.isShowOperateView,
                    contentA = {
                        NoteContentBottomOperateView(modifier = Modifier, homeNoteUiState, onAction)
                    },
                    contentB = {
                        // 底部导航按钮（最底部，只在手机上显示）
                        if (homeNoteUiState.isShowBottomNav) {
                            BottomNavigationButtons(
                                modifier = Modifier,
                                selectedTab = NavigationTab.JOURNAL,
                                onToTab = { route ->
                                    if (route == NavigationTab.NOTE) {
                                        onAction(NoteListAction.OnNavigateTo(com.tcl.ai.note.journalhome.components.notelist.NavigationTab.NOTE))
                                    }
                                }
                            )
                        }
                    }
                )
            }
        }
        val layoutConfig = rememberNoteLayoutConfig()
        val end = layoutConfig.contentPadding.dp
        // 在手机上需要为导航按钮留出空间
        val paddingBottom = navigationBarHeight + 16.dp + homeNoteUiState.isShowBottomNav.judge(BottomOperateViewHeight, 0.dp)
        // 添加笔记按钮（在导航按钮上方）
        AddNoteFloatingBtn(modifier = Modifier
            .padding(end = end, bottom = paddingBottom)
            .align(Alignment.BottomEnd), homeNoteUiState, onAction)
    }
    // 删除对话框
    DeleteDialog(homeNoteUiState, onAction)
}
