package com.tcl.ai.note.home.entity

import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity

/**
 * 缩略图相关信息
 */
data class ThumbnailInfo(
    val type: ThumbnailType = ThumbnailType.TEXT_ICON, // 缩略图显示类型
    val showAudioIcon: Boolean = false, // 是否显示音频图标
    val image: String? = null, // 图片路径
    val handwritingThumbnail: String? = null, // 手绘缩略图
    val handwritingThumbnailDark: String? = null, // 手绘缩略图 深色模式
    val firstPicture: String? = null, // 首图
    val hasAudio: Boolean = false // 是否有录音
)

// 数据类定义
data class HomeNoteItemEntity(
    val noteId: String,
    val noteTitle: String,
    val summary: String? = null,
    val content: String?=null,
    val richTextStyleEntity: RichTextStyleEntity? = null,//二期富文本样式
    val titleResId: Int? = null,
    val date: String,
    val isChecked: Boolean = false,
    val categoryId: String = "", // 分类ID
    val categoryName: String? = null, // 分类名称
    val createTime: Long?=null,
    val modifyTime: Long?=null,
    val searchKey: String = "", // 新增字段，表示搜索关键字
    // 新增：高亮信息
    val highlightInfo: HighlightInfo? = null,
    val categoryIcon: CategoryIcon? = null,
    // 缩略图相关信息（封装）
    val thumbnailInfo: ThumbnailInfo = ThumbnailInfo()
) {
    // 为了保持向后兼容，提供便捷访问属性
    val thumbnailType: ThumbnailType get() = thumbnailInfo.type
    val showAudioIcon: Boolean get() = thumbnailInfo.showAudioIcon
    val image: String? get() = thumbnailInfo.image
    val handwritingThumbnail: String? get() = thumbnailInfo.handwritingThumbnail
    val handwritingThumbnailDark: String? get() = thumbnailInfo.handwritingThumbnailDark
    val firstPicture: String? get() = thumbnailInfo.firstPicture
    val hasAudio: Boolean get() = thumbnailInfo.hasAudio
}
// 2. 高亮信息数据类
data class HighlightInfo(
    val searchKeyword: String,
    val titleHighlights: List<HighlightRange> = emptyList(),
    val contentHighlights: List<HighlightRange> = emptyList()
)

// 3. 高亮范围数据类
data class HighlightRange(
    val start: Int,
    val end: Int
)


/**
 * 缩略图显示类型
 */
enum class ThumbnailType {
    TEXT_CONTENT,    // 显示正文内容
    FIRST_SCREEN,    // 显示第一屏信息（图片/手绘）
    AUDIO_ICON,      // 显示录音图标
    TEXT_ICON,       // 显示文本图标
    MIXED_CONTENT    // 混合模式：底部富文本，上层叠加手绘
}

