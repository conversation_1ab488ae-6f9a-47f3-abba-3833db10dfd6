package com.tcl.ai.note.home.screen

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.navigation.NavController
import com.tcl.ai.note.home.journal.HomeJournalScreen
import com.tcl.ai.note.utils.AppActivityManager
import com.tcl.ai.note.utils.isSupportJournal
import kotlinx.coroutines.launch

/**
 * 主页屏幕 Pager 组件
 * 使用 HorizontalPager 实现 Note 和 Journal 两个页面的切换
 */
@Composable
fun HomeScreenPager(
    navController: NavController,
    showBlur: Boolean,
) {

    // Pager 状态管理
    val pagerState = rememberPagerState(
        initialPage = 0,
        pageCount = { if (isSupportJournal) 2 else 1 }
    )
    val coroutineScope = rememberCoroutineScope()

    fun scrollToPage(page: Int) {
        coroutineScope.launch {
            pagerState.scrollToPage(page)
            AppActivityManager.recordJournalUsage(page)
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // 主要内容 - HorizontalPager
        HorizontalPager(
            state = pagerState,
            userScrollEnabled = false,
            modifier = Modifier.fillMaxSize(),
        ) { page ->
            when (page) {
                0 -> {
                    // Note 页面
                    HomeNoteScreen(
                        navController = navController,
                        onToTab = { route ->
                            scrollToPage(1)
                        }
                    )
                }

                1 -> {
                    // Journal 页面
                    HomeJournalScreen(
                        navController = navController,
                        pagerState = pagerState,
                        showBlur = showBlur,
                        onToTab = {
                            scrollToPage(0)
                        }
                    )
                }
            }
        }

    }
}
