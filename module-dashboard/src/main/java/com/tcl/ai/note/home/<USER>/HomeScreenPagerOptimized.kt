package com.tcl.ai.note.home.screen

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogScreenManager
import com.tcl.ai.note.home.components.base.HomeAdaptiveNavigationScreen
import com.tcl.ai.note.home.components.categorylist.HomeCategoryDrawerContent
import com.tcl.ai.note.home.components.BottomNavigationButtons
import com.tcl.ai.note.home.journal.HomeJournalContent
import com.tcl.ai.note.home.components.notelist.HomeNoteListContent
import com.tcl.ai.note.home.components.NavigationTab
import com.tcl.ai.note.home.vm.HomeCoordinator
import com.tcl.ai.note.home.vm.rememberHomeCoordinator
import com.tcl.ai.note.home.vm.action.HomeCategoryAction
import com.tcl.ai.note.home.vm.action.HomeNoteListAction
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import kotlinx.coroutines.launch

/**
 * 优化版本的 HomeScreenPager
 * 正确处理页面生命周期，优化性能和资源管理
 */
@Composable
fun HomeScreenPagerOptimized(
    navController: NavController,
    coordinator: HomeCoordinator = rememberHomeCoordinator(),
    journalCoordinator: com.tcl.ai.note.journalhome.vm.HomeCoordinator = com.tcl.ai.note.journalhome.vm.rememberHomeCoordinator(),
) {
    val homeUiState = coordinator.homeUiState.collectAsStateWithLifecycle()
    val categoryUiState = coordinator.categoryUiState.collectAsStateWithLifecycle()
    val rememberDrawerState = rememberDrawerState(DrawerValue.Closed)
    val rememberCoroutineScope = rememberCoroutineScope()

    val journalHomeUIState = journalCoordinator.homeUiState.collectAsStateWithLifecycle()
    val journalCategoryUiState = journalCoordinator.categoryUiState.collectAsStateWithLifecycle()

    // Pager 状态管理
    val pagerState = rememberPagerState(
        initialPage = 0,
        pageCount = { 2 }
    )
    
    // 使用 derivedStateOf 优化重组性能
    val currentTab by remember {
        derivedStateOf {
            when (pagerState.currentPage) {
                0 -> NavigationTab.NOTE
                1 -> NavigationTab.JOURNAL
                else -> NavigationTab.NOTE
            }
        }
    }
    val context = LocalContext.current

    // UI Events
    val actionsHandler: (HomeNoteListAction) -> Unit = { action ->
        coordinator.handleHomeAction(action)
        if (action is HomeNoteListAction.OnAddHomeNoteClick) {

        } else if (action is HomeNoteListAction.OnOpenDrawer) {
            rememberCoroutineScope.launch {
                rememberDrawerState.open()
            }
        }
    }

    val journalActionsHandler: (com.tcl.ai.note.journalhome.vm.state.NoteListAction) -> Unit = { action ->
        journalCoordinator.handleHomeAction(action)
        if (action is com.tcl.ai.note.journalhome.vm.state.NoteListAction.OnAddNoteClick) {
            navController.navigate("edit_screen?noteId=${action.noteId}")
        } else if (action is com.tcl.ai.note.journalhome.vm.state.NoteListAction.OnOpenDrawer) {
            rememberCoroutineScope.launch {
                rememberDrawerState.open()
            }
        }
    }
    
    val categoryActionHandler: (HomeCategoryAction) -> Unit = { action ->
        coordinator.handleCategoryAction(action)
    }

    val journalCategoryActionHandler: (com.tcl.ai.note.journalhome.vm.state.CategoryAction) -> Unit = { action ->
        journalCoordinator.handleCategoryAction(action)
    }
    
    // 监听页面变化，用于生命周期管理
    LaunchedEffect(pagerState.currentPage) {
        Logger.d("HomeScreenPager", "Current page changed to: ${pagerState.currentPage}")
        
        // 根据当前页面执行相应的生命周期操作
        when (pagerState.currentPage) {
            0 -> {
                // Note 页面变为活跃
                Logger.d("HomeScreenPager", "Note page became active")
                coordinator.onNotePageActive()
            }
            1 -> {
                // Journal 页面变为活跃
                Logger.d("HomeScreenPager", "Journal page became active")
                coordinator.onJournalPageActive()
            }
        }
    }
    
    HomeAdaptiveNavigationScreen(
        isTablet = isTablet,
        drawerState = rememberDrawerState,
        drawerContent = {
            when (pagerState.currentPage) {
                0 -> {
                    HomeCategoryDrawerContent(
                        categoryUiState = categoryUiState.value,
                        onCategoryAction = categoryActionHandler
                    )
                }
                1 -> {
                    com.tcl.ai.note.journalhome.components.categorylist.HomeCategoryDrawerContent(
                        categoryUiState = journalCategoryUiState.value,
                        onCategoryAction = journalCategoryActionHandler
                    )
                }
            }
        },
        content = {
            Box(modifier = Modifier.fillMaxSize()) {
                // 主要内容 - HorizontalPager
                HorizontalPager(
                    state = pagerState,
                    modifier = Modifier.fillMaxSize()
                ) { page ->
                    // 计算页面是否为当前活跃页面
                    val isCurrentPage by remember {
                        derivedStateOf { page == pagerState.currentPage }
                    }
                    
                    when (page) {
                        0 -> {
                            // Note 页面
                            OptimizedNotePageContent(
                                isCurrentPage = isCurrentPage,
                                homeUiState = homeUiState.value,
                                onAction = actionsHandler
                            )
                        }
                        1 -> {
                            // Journal 页面
                            OptimizedJournalPageContent(
                                navController = navController,
                                isCurrentPage = isCurrentPage,
                                homeUiState = journalHomeUIState.value,
                                onAction = journalActionsHandler
                            )
                        }
                    }
                }
                
                // 底部导航按钮
                BottomNavigationButtons(
                    modifier = Modifier.align(Alignment.BottomCenter),
                    selectedTab = currentTab,
                    onToTab = { tab ->
                        Logger.d("HomeScreenPager", "Navigate to tab: $tab")
                        rememberCoroutineScope.launch {
                            when (tab) {
                                NavigationTab.NOTE -> pagerState.animateScrollToPage(0)
                                NavigationTab.JOURNAL -> pagerState.animateScrollToPage(1)
                            }
                        }
                    }
                )
            }
        }
    )
    
    CategoryDialogScreenManager(screenKey = "HomeScreenPager")
}

/**
 * 优化的 Note 页面内容
 * 正确处理生命周期事件
 */
@Composable
private fun OptimizedNotePageContent(
    isCurrentPage: Boolean,
    homeUiState: com.tcl.ai.note.home.vm.state.HomeNoteUiState,
    onAction: (HomeNoteListAction) -> Unit
) {
    // 页面创建时的日志
    DisposableEffect(Unit) {
        Logger.d("HomeScreenPager", "Note page created")
        
        onDispose {
            Logger.d("HomeScreenPager", "Note page disposed")
        }
    }
    
    // 页面可见性变化处理
    LaunchedEffect(isCurrentPage) {
        if (isCurrentPage) {
            Logger.d("HomeScreenPager", "Note page became visible - start operations")
            // 这里可以添加页面变为可见时的逻辑
            // 例如：刷新数据、开始定时任务等
        } else {
            Logger.d("HomeScreenPager", "Note page became invisible - pause operations")
            // 这里可以添加页面变为不可见时的逻辑
            // 例如：暂停网络请求、保存状态等
        }
    }
    
    HomeNoteListContent(
        modifier = Modifier.fillMaxSize(),
        homeNoteUiState = homeUiState,
        onAction = onAction
    )
}

/**
 * 优化的 Journal 页面内容
 * 正确处理生命周期事件
 */
@Composable
private fun OptimizedJournalPageContent(
    navController: NavController,
    isCurrentPage: Boolean,
    homeUiState: com.tcl.ai.note.journalhome.vm.state.HomeNoteUiState,
    onAction: (com.tcl.ai.note.journalhome.vm.state.NoteListAction) -> Unit
) {
    // 页面创建时的日志
    DisposableEffect(Unit) {
        Logger.d("HomeScreenPager", "Journal page created")
        
        onDispose {
            Logger.d("HomeScreenPager", "Journal page disposed")
        }
    }
    
    // 页面可见性变化处理
    LaunchedEffect(isCurrentPage) {
        if (isCurrentPage) {
            Logger.d("HomeScreenPager", "Journal page became visible - start operations")
            // Journal 页面特定的激活逻辑
        } else {
            Logger.d("HomeScreenPager", "Journal page became invisible - pause operations")
            // Journal 页面特定的暂停逻辑
        }
    }
    
    HomeJournalContent(
        navController = navController,
        modifier = Modifier.fillMaxSize(),
        homeNoteUiState = homeUiState,
        onAction = onAction
    )
}

/**
 * HomeCoordinator 扩展函数
 * 处理页面生命周期事件
 */
private fun HomeCoordinator.onNotePageActive() {
    // Note 页面变为活跃时的处理
    // 例如：刷新笔记列表、恢复搜索状态等
    Logger.d("HomeCoordinator", "Note page activated - refresh data if needed")
}

private fun HomeCoordinator.onJournalPageActive() {
    // Journal 页面变为活跃时的处理
    // 例如：加载日记数据、初始化相关服务等
    Logger.d("HomeCoordinator", "Journal page activated - initialize journal features")
}
