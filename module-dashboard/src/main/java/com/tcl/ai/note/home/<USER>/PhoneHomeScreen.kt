package com.tcl.ai.note.home.screen

import android.annotation.SuppressLint
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ModalDrawerSheet
import androidx.compose.material3.ModalNavigationDrawer
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.tcl.ai.note.home.components.notelist.HomeNoteList
import com.tcl.ai.note.home.components.categorylist.HomeCategoryDrawerContent
import com.tcl.ai.note.home.vm.action.HomeCategoryAction
import com.tcl.ai.note.home.vm.action.HomeNoteListAction
import com.tcl.ai.note.home.vm.state.HomeCategoryUiState
import com.tcl.ai.note.home.vm.state.HomeNoteUiState

@OptIn(ExperimentalMaterial3Api::class)
@SuppressLint("DesignSystem")
@Composable
fun PhoneHomeScreen(
    homeNoteUiState: HomeNoteUiState,
    categoryUiState: HomeCategoryUiState,
    onAction: (HomeNoteListAction) -> Unit,
    onCategoryAction: (HomeCategoryAction) -> Unit = { },
    onDrawerWidthChanged: ((width: Int) -> Unit)? = null // 新增回调参数
) {
    val drawerState = rememberDrawerState(DrawerValue.Closed)
    val coroutineScope = rememberCoroutineScope()
    val density = LocalDensity.current
    var drawerWidth by remember { mutableStateOf(0.dp) }

    // 实时计算抽屉打开的进度 (0f = 关闭, 1f = 完全打开)
    val drawerProgress by remember {
        derivedStateOf {
            if (drawerWidth > 0.dp) {
                // 使用实际的 offset 值来计算进度，实现同步移动
                val offsetValue = drawerState.offset.value
                val maxOffset = with(density) { drawerWidth.toPx() }

                // offset 从负值到0，我们需要转换为0到1的进度
                if (maxOffset > 0) {
                    ((maxOffset + offsetValue) / maxOffset).coerceIn(0f, 1f)
                } else {
                    0f
                }
            } else {
                0f
            }
        }
    }

    // 根据实时进度计算内容偏移量（无额外动画，直接跟随抽屉）
    val contentOffsetX by remember {
        derivedStateOf {
            drawerWidth * drawerProgress
        }
    }

    // 内容缩放（跟随抽屉进度）
    val contentScale by remember {
        derivedStateOf {
            1f - (drawerProgress * 0.15f) // 从1.0缩放到0.85
        }
    }

    // 遮罩透明度（跟随抽屉进度）
    val overlayAlpha by remember {
        derivedStateOf {
            drawerProgress * 0.3f // 从0到0.3的透明度
        }
    }

    ModalNavigationDrawer(
        drawerState = drawerState,
        drawerContent = {
            ModalDrawerSheet(
                modifier = Modifier
                    .width(280.dp)
                    .onSizeChanged { size ->
                        drawerWidth = with(density) { size.width.toDp() }
                        onDrawerWidthChanged?.invoke(size.width)
                    }
            ) {
                HomeCategoryDrawerContent(
                    modifier = Modifier.fillMaxWidth(),
                    categoryUiState = categoryUiState,
                    onCategoryAction = {
                        onCategoryAction(it)
                        if (it is HomeCategoryAction.OnCategorySelected) {
                            coroutineScope.launch {
                                drawerState.close()
                            }
                        }
                    }
                )
            }
        },
    ) {
        Box {
            // 主内容
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .offset(x = contentOffsetX)
//                    .scale(contentScale)
                    .clip(
                        if (drawerProgress > 0f)
                            RoundedCornerShape(16.dp)
                        else
                            RoundedCornerShape(0.dp)
                    )
            ) {
                // 顶部导航栏
                TopAppBar(
                    title = {
                        Text(
                            homeNoteUiState.selectedCategoryName
                                ?: stringResource(com.tcl.ai.note.base.R.string.app_name)
                        )
                    },
                    navigationIcon = {
                        IconButton(onClick = {
                            coroutineScope.launch {
                                drawerState.open()
                            }
                        }) {
                            Icon(Icons.Default.Menu, contentDescription = "Menu")
                        }
                    },
                    actions = {
                        IconButton(onClick = { }) {
                            Icon(Icons.Default.Search, contentDescription = "Search")
                        }
                        IconButton(onClick = { }) {
                            Icon(Icons.Default.MoreVert, contentDescription = "More")
                        }
                    }
                )

                Box {
                    HomeNoteList(
                        homeNoteUiState = homeNoteUiState,
                        modifier = Modifier.fillMaxSize(),
                        onAction = onAction,
                    )
                    // 添加笔记按钮
//                    AddNoteFloatingBtn(homeNoteUiState, onAction)

                    // 调试信息：显示抽屉状态（可选）
//                    if (drawerWidth > 0.dp) {
//                        Column(
//                            modifier = Modifier.align(androidx.compose.ui.Alignment.TopEnd)
//                        ) {
//                            Text(text = "抽屉宽度: $drawerWidth")
//                            Text(text = "进度: ${String.format("%.2f", drawerProgress)}")
//                            Text(text = "偏移: $contentOffsetX")
//                            Text(text = "缩放: ${String.format("%.2f", contentScale)}")
//                        }
//                    }

                    // 半透明遮罩层，点击可关闭抽屉
                    if (overlayAlpha > 0f) {
//                        Box(
//                            modifier = Modifier
//                                .fillMaxSize()
//                                .offset(x = contentOffsetX)
//                                .scale(contentScale)
//                                .background(Color.Black.copy(alpha = overlayAlpha))
//                                .clickable {
//                                    coroutineScope.launch {
//                                        drawerState.close()
//                                    }
//                                }
//                        )
                    }
                }
            }
        }
    }
}
@Preview(showBackground = true)
@Composable
fun NotesAppPhonePreview() {
        PhoneHomeScreen(
            homeNoteUiState = HomeNoteUiState(),
            categoryUiState = HomeCategoryUiState(),
            onAction = {},
            onCategoryAction = {}
        )
}








