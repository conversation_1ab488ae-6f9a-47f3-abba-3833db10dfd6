package com.tcl.ai.note.home.data

import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.entity.CategoryIcon
import com.tcl.ai.note.home.entity.HomeCategoryItemEntity
import com.tcl.ai.note.home.entity.HomeNoteItemEntity
import com.tcl.ai.note.home.entity.ThumbnailInfo
import com.tcl.ai.note.home.entity.ThumbnailType
import com.tcl.ai.note.home.vm.state.HomeContentListState

// 模拟数据
fun getSampleNotes(): List<HomeNoteItemEntity> {
    return listOf(
        HomeNoteItemEntity(
            noteId = "1",
            noteTitle = "This is a title,this i...",
            date = "16:51",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.TEXT_CONTENT)
        ),
        HomeNoteItemEntity(
            noteId = "2",
            noteTitle = "Audio",
            date = "14:00 🔊",
            thumbnailInfo = ThumbnailInfo(
                type = ThumbnailType.AUDIO_ICON,
                showAudioIcon = true,
                hasAudio = true
            )
        ),
        HomeNoteItemEntity(
            noteId = "3",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),

        HomeNoteItemEntity(
            noteId = "5",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),
        HomeNoteItemEntity(
            noteId = "6",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),
        HomeNoteItemEntity(
            noteId = "7",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),

        HomeNoteItemEntity(
            noteId = "9",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),
        HomeNoteItemEntity(
            noteId = "10",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),
        HomeNoteItemEntity(
            noteId = "11",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),
        HomeNoteItemEntity(
            noteId = "12",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),
        HomeNoteItemEntity(
            noteId = "13",
            noteTitle = "Image",
            date = "29 APR 2025",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        )
    )
}

 fun homeDemoContentListState() = HomeContentListState(
     notes = getSampleNotes()
)
val demoCategories = listOf(
    HomeCategoryItemEntity(
        name = "All notes",
        noteCounts = 30,
        categoryIcon = CategoryIcon(R.drawable.ic_all_notes),
        id = "all_notes"
    ),
    HomeCategoryItemEntity(
        name = "Uncategorised",
        noteCounts = 26,
        categoryIcon = CategoryIcon(R.drawable.ic_category_uncategorised),
        id = "uncategorised"
    ),
    HomeCategoryItemEntity(
        name = "Recently Deleted",
        noteCounts = 60,
        categoryIcon = CategoryIcon(R.drawable.ic_category_uncategorised),
        id = "recently_deleted"
    ),
)

