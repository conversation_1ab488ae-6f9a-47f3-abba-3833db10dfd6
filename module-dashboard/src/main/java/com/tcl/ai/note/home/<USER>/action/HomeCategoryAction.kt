package com.tcl.ai.note.home.vm.action

import com.tcl.ai.note.home.entity.HomeCategoryItemEntity
import java.util.Locale

/**
 * 左侧分类相关操作
 */
sealed class HomeCategoryAction {
    //分类选中
    data class OnCategorySelected(val category: HomeCategoryItemEntity) : HomeCategoryAction()
    //新建分类
    data class OnCreateNewCategoryClick(val isShowDialog: Boolean=false) : HomeCategoryAction()
    // 重命名分类
    data class OnRenameCategoryClick(val isShowDialog: Boolean=false, val category: HomeCategoryItemEntity) : HomeCategoryAction()
    //删除分类
    data class OnDeleteCategoryClick(
        val isDeleteNotesSelected: Boolean,
        val category: HomeCategoryItemEntity
    ) : HomeCategoryAction()
    //长按选中 效果
    data class OnLongClickSelected(
        val selectedCategory: HomeCategoryItemEntity,
        val isLongClickSelected: Boolean =true) : HomeCategoryAction()
    // show dialog 删除分类弹窗
    data class OnShowDeleteCategoryNotesDialog(val isShowDialog: Boolean) : HomeCategoryAction()

    data class OnLocaleChange(val newLocale: Locale) : HomeCategoryAction()

    data class OnCheckUpdate(val isCheckUpdate: Boolean=false) : HomeCategoryAction()
}