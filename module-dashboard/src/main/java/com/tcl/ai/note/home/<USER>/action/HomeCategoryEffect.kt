package com.tcl.ai.note.home.vm.action

import com.tcl.ai.note.exception.AppException
import com.tcl.ai.note.home.entity.HomeCategoryItemEntity

/**
 * 一次性事件
 */
sealed class HomeCategoryEffect {
    data class ShowToastRes(val resourceId: Int,val currentTime:Long=System.currentTimeMillis()) : HomeCategoryEffect()
    data class Exception(val exception: AppException) : HomeCategoryEffect()
    data class OnCategoryChange(val changeCategoryItem: HomeCategoryItemEntity, val currentTime:Long=System.currentTimeMillis()) : HomeCategoryEffect()
}