package com.tcl.ai.note.home.components.base

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.DrawerState
import androidx.compose.material3.ModalDrawerSheet
import androidx.compose.material3.ModalNavigationDrawer
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp

/**
 * 自适应导航抽屉
 */
@Composable
fun HomeAdaptiveNavigationScreen(
    isTablet: Boolean,
    drawerState: DrawerState,
    gesturesEnabled: Boolean = true,
    leftPanelWeight: Float = 0.39f,
    drawerContent: @Composable ColumnScope.() -> Unit,
    content: @Composable () -> Unit
) = if (isTablet) {
    PermanentNavigationScreen(
        leftPanelWeight = leftPanelWeight,
        drawerContent = drawerContent,
        content = content
    )
} else {
    ModalNavigationScreen(
        drawerState = drawerState,
        gesturesEnabled = gesturesEnabled,
        drawerContent = drawerContent,
        content = content
    )
}


/**
 * 永久导航抽屉 - 支持自定义权重
 */
@Composable
private fun PermanentNavigationScreen(
    leftPanelWeight: Float = 0.39f,
    drawerContent: @Composable ColumnScope.() -> Unit,
    content: @Composable () -> Unit
) {
    val rightPanelWeight = 1f - leftPanelWeight
    Row(modifier = Modifier.fillMaxSize()) {
        // 左侧抽屉内容
        Column(
            modifier = Modifier.weight(leftPanelWeight)
        ) {
            drawerContent()
        }

        // 右侧主要内容
        Box(
            modifier = Modifier.weight(rightPanelWeight)
        ) {
            content()
        }
    }
}

/**
 * 模态导航抽屉
 * drawerState 用于控制抽屉的打开和关闭
 * gesturesEnabled 用于控制是否允许手势操作抽屉
 * drawerContent 用于绘制抽屉内容
 * content 用于绘制主内容
 */
@Composable
private fun ModalNavigationScreen(
    drawerState: DrawerState,
    gesturesEnabled: Boolean = true,
    drawerContent: @Composable ColumnScope.() -> Unit,
    content: @Composable () -> Unit
) {
    val density = LocalDensity.current
    var drawerWidth by remember { mutableStateOf(0.dp) }

    // 实时计算抽屉打开的进度 (0f = 关闭, 1f = 完全打开)
    val drawerProgress by remember {
        derivedStateOf {
            if (drawerWidth > 0.dp) {
                // 使用实际的 offset 值来计算进度，实现同步移动
                val offsetValue = drawerState.currentOffset
                val maxOffset = with(density) { drawerWidth.toPx() }

                // offset 从负值到0，我们需要转换为0到1的进度
                if (maxOffset > 0) {
                    ((maxOffset + offsetValue) / maxOffset).coerceIn(0f, 1f)
                } else {
                    0f
                }
            } else {
                0f
            }
        }
    }
    // 根据实时进度计算内容偏移量（无额外动画，直接跟随抽屉）
    val contentOffsetX by remember {
        derivedStateOf {
            drawerWidth * drawerProgress
        }
    }
    ModalNavigationDrawer(
        drawerContent = {
            ModalDrawerSheet(
                drawerState = drawerState,
                drawerShape = RoundedCornerShape(0.dp),
                windowInsets = WindowInsets(0, 0, 0, 0),
                modifier = Modifier
                    .width(270.dp)
                    .onSizeChanged { size ->
                        drawerWidth = with(density) { size.width.toDp() }
                    }
            ) {
                drawerContent()
            }
        },
        drawerState = drawerState,
        gesturesEnabled = gesturesEnabled,
        content = {
            // 主内容
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .offset(x = contentOffsetX)
                    .clip(
                        RoundedCornerShape(0.dp)
                    )
            ) {
                content()
            }
        }
    )
}