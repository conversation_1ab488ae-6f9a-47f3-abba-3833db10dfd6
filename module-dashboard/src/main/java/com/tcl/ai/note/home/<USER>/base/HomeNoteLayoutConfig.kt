package com.tcl.ai.note.home.components.base

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.ui.unit.IntSize
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.isTabletLandscape

/**
 * 设备布局配置
 * 统一管理不同设备和方向下的布局参数
 * 默认值以平板为准
 */
@Stable
data class HomeNoteLayoutConfig(
    val gridColumnCount: Int,// grid列数
    val listItemSpacing: Int, // dp 列表间隔
    val gridItemSpacing: Int=24, // dp grid内容间隔
    val contentPadding: Int=24, // dp 左右边距
    val cardSize: IntSize,
    val leftPanelWeight: Float = 0.39f, // 左侧边栏权重
    val contentMaxLines: Int=15, // 最大显示行数
    val topSpaceHeight:Int=6  //列表距离顶部的间距
) {
    companion object {
        /**
         * 获取当前设备的布局配置
         */
        @Composable
        fun current(): HomeNoteLayoutConfig {
            return when {
                isTablet -> {
                    if (isTabletLandscape) {
                        // 平板横屏
                        Presets.TABLET_LANDSCAPE
                    } else {
                        // 平板竖屏
                        Presets.TABLET_PORTRAIT
                    }
                }
                else -> {
                    // 手机
                    Presets.PHONE
                }
            }
        }
        
        /**
         * 预定义的配置常量
         */
        object Presets {
            val PHONE = HomeNoteLayoutConfig(
                gridColumnCount = 3,
                listItemSpacing = 6,
                gridItemSpacing = 16, // 手机上Item间距与左右边距保持一致
                contentPadding = 16,
                cardSize = NoteListContentConstants.PhoneCardSize,
                contentMaxLines = 15,
                topSpaceHeight = 8
            )

            val TABLET_PORTRAIT = HomeNoteLayoutConfig(
                gridColumnCount = 2,
                listItemSpacing = 8,
                cardSize = NoteListContentConstants.TabletCardSize_Two_Column,
                leftPanelWeight = 0.388888f, // 平板竖屏：左侧边栏占39%
                contentMaxLines = 15,
            )

            val TABLET_LANDSCAPE = HomeNoteLayoutConfig(
                gridColumnCount = 5,
                listItemSpacing = 8,
                cardSize = NoteListContentConstants.TabletCardSize_Five_Column,
                leftPanelWeight = 0.254545f, // 平板横屏：左侧边栏占25%
                contentMaxLines = 15,
            )
        }
    }
}

/**
 * 网格列数配置
 * 专门用于管理网格布局的列数
 */
object GridColumnConfig {
    /**
     * 获取网格列数
     */
    @Composable
    fun getColumnCount(): Int {
        return HomeNoteLayoutConfig.current().gridColumnCount
    }
    
    /**
     * 根据设备类型和方向获取列数
     */
    fun getColumnCount(isTablet: Boolean, isLandscape: Boolean): Int {
        return when {
            isTablet && isLandscape -> 5
            isTablet && !isLandscape -> 2
            else -> 3
        }
    }
    
    /**
     * 获取推荐的列数范围
     */
    fun getColumnRange(isTablet: Boolean): IntRange {
        return if (isTablet) 2..5 else 2..4
    }
}


/**
 * 扩展函数：简化布局配置的使用
 */
@Composable
fun rememberNoteLayoutConfig(): HomeNoteLayoutConfig = HomeNoteLayoutConfig.current()

@Composable
fun rememberNoteLayoutCardSize() {
    rememberNoteLayoutConfig().cardSize
}


/**
 * 扩展函数：获取网格列数
 */
@Composable
fun rememberGridColumnCount(): Int = GridColumnConfig.getColumnCount()

/**
 * 扩展函数：获取平板权重配置
 */
@Composable
fun rememberTabletLeftWeight(): Float{
    val config = rememberNoteLayoutConfig()
    return config.leftPanelWeight
}

/**
 * 布局配置的便捷访问器
 */
object NoteListContentConstants {

    val TabletCardSize_Five_Column = IntSize(width = 135, height = 191)
    val TabletCardSize_Two_Column = IntSize(width = 184, height = 260)

    val PhoneCardSize = IntSize(width = 99, height = 141)
}
