package com.tcl.ai.note.home.components.notelist

import android.annotation.SuppressLint
import android.view.MotionEvent
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInteropFilter
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.home.entity.HomeNoteItemEntity
import com.tct.theme.core.designsystem.component.TclCheckbox
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.Dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.components.HighlightText
import com.tcl.ai.note.home.components.HomeNoteTitleTextStyle
import com.tcl.ai.note.home.components.HomeTitleTextStyle
import com.tcl.ai.note.home.components.notelist.content.CardTypeContent
import com.tcl.ai.note.home.components.preview.HomeNoteItemPreviewParameterProvider
import com.tcl.ai.note.home.utils.rememberScaledCardSize
import com.tcl.ai.note.widget.clickableHover
import com.tcl.ai.note.widget.combinedClickableHover

/**
 * 列表模式下的笔记Item
 */
@Composable
fun NoteListItemListType(
    note: HomeNoteItemEntity,
    modifier: Modifier = Modifier,
    editMode: Boolean = false,
    checked: Boolean = false,
    onItemClick: (String) -> Unit,
    onCheckedChange: ((Boolean) -> Unit)? = null,
    onLongClick: (() -> Unit)? = null
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(Color.White)
            .combinedClickableHover(
                onClick = {
                    onItemClick(note.noteId)
                },
                onLongClick = onLongClick
            )
            .padding(12.dp)
    ) {
        if (editMode) {
            TclCheckbox(
                checked = checked,
                onCheckedChange = onCheckedChange,
                modifier = Modifier.padding(end = 8.dp)
            )
        }
        Column(modifier = Modifier.weight(1f)) {
            // 标题高亮显示
            HighlightText(
                text = note.noteTitle,
                normalStyle = HomeTitleTextStyle,
                highlights = note.highlightInfo?.titleHighlights ?: emptyList()
            )
            Spacer(modifier = Modifier.height(4.dp))
            // 内容摘要高亮显示
            HighlightText(
                text = note.summary ?: "",
                normalStyle = MaterialTheme.typography.bodyMedium,
                highlights = note.highlightInfo?.contentHighlights ?: emptyList(),
                maxLines = 1
            )
        }
    }
}

/**
 * 网格模式下的笔记Item
 */
@Composable
fun HomeNoteItemGridType(
    modifier: Modifier = Modifier,
    note: HomeNoteItemEntity,
    editMode: Boolean = false,
    checked: Boolean = false,
    onItemClick: (String, Boolean) -> Unit = { _, _ -> },
    onCheckedChange: ((Boolean) -> Unit)? = null,
    onLongClick: (() -> Unit)? = null
) {
    val (scaleCardWidth, scaleCardHeight) = rememberScaledCardSize()
    Column(
        modifier = modifier
    ) {
        TopCard(
            modifier = Modifier
                .fillMaxWidth()
                .height(scaleCardHeight.dp),
            editMode,
            onCheckedChange,
            checked,
            onItemClick,
            note,
            onLongClick
        )
        Spacer(modifier = Modifier.height(8.dp))
        ItemTitle(note, scaleCardWidth.dp)
    }
}

@Composable
private fun ItemTitle(note: HomeNoteItemEntity, cardWidth: Dp) {
    // 获取显示标题：优先使用 titleResId，否则使用 title
    val title = note.titleResId?.let { stringResource(it) } ?: note.noteTitle

    // 标题
    HighlightText(
        text = title,
        normalStyle = HomeNoteTitleTextStyle,
        highlights = note.highlightInfo?.titleHighlights ?: emptyList()
    )

    val density = LocalDensity.current

    Spacer(modifier = Modifier.height(4.dp))
    // 为音频图标预留固定空间，避免被文本挤压
    val audioIconSpace = if (note.showAudioIcon) 18.dp else 0.dp // 图标14dp + 间距4dp
    val textMaxWidth = cardWidth - audioIconSpace

    // 日期和音频图标 - 使用固定布局避免挤压
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = note.date,
            fontSize = 12.sp,
            lineHeight = with(density) { 14.dp.toSp() },
            color = colorResource(R.color.text_summary),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.widthIn(max = textMaxWidth)
        )

        if (note.showAudioIcon) {
            Spacer(modifier = Modifier.width(2.dp))
            Icon(
                painter = painterResource(id = R.drawable.ic_note_audio),
                contentDescription = stringResource(R.string.audio_title),
                modifier = Modifier
                    .size(14.dp)
                    .wrapContentSize(), // 确保图标不被压缩
                tint = colorResource(R.color.text_summary)
            )
        }
    }
}

@SuppressLint("DesignSystem")
@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun TopCard(
    modifier: Modifier,
    editMode: Boolean,
    onCheckedChange: ((Boolean) -> Unit)?,
    checked: Boolean,
    onItemClick: (String, Boolean) -> Unit,
    note: HomeNoteItemEntity,
    onLongClick: (() -> Unit)?
) {
    var isPen by remember {
        mutableStateOf(false)
    }

    Card(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
//            .border(
//                width = 0.5.dp, // 边框宽度
//                color = colorResource(R.color.card_border), // 边框颜色
//                shape = RoundedCornerShape(8.dp) // 边框形状（与Card的shape保持一致）
//            )
            .pointerInteropFilter { event ->
                isPen = event.getToolType(0) == MotionEvent.TOOL_TYPE_STYLUS
                false
            }
            .combinedClickableHover(
                onClick = {
                    if (editMode) {
                        onCheckedChange?.invoke(!checked)
                    } else {
                        onItemClick.invoke(note.noteId, isPen)
                    }
                },
                onLongClick = {
                    onLongClick?.invoke()
                }
            ),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = colorResource(R.color.home_note_list_item_bg_color)),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            CardTypeContent(note)
            // 书签图标
            if (note.categoryIcon != null) {
                val (icon, color) = note.categoryIcon
                Icon(
                    painter = painterResource(R.drawable.ic_note_category_flag),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(end = 8.dp)
                        .size(14.dp, 22.dp)
                        .align(Alignment.TopEnd),
                    color?.let { colorResource(it) } ?: Color.Transparent
                )
            }
            if (editMode) {
                Box(
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .padding(8.dp)
                        .size(20.dp)
                        .clip(CircleShape)  // 添加圆形裁剪
                        .clickableHover(
                            onClick = {
                                if (onCheckedChange != null) {
                                    onCheckedChange(!checked)
                                }
                            }
                        )
                ) {
                    Image(
                        painter = painterResource(if (checked) R.drawable.ic_note_checkbox_selected else R.drawable.ic_note_checkbox_normal),
                        contentDescription = null,
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }

    }
}


@Preview(showSystemUi = false, showBackground = true)
@Composable
private fun HomeNoteItemCardParameterizedPreview(
    @PreviewParameter(HomeNoteItemPreviewParameterProvider::class) note: HomeNoteItemEntity
) {
    Box(modifier = Modifier.size(100.dp, 200.dp)) {
        HomeNoteItemGridType(
            note = note,
            editMode = false,
            checked = false,
            onItemClick = { _, _ -> },
            onCheckedChange = {},
            onLongClick = {}
        )

    }
}