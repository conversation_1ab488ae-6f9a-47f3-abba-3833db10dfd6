package com.tcl.ai.note.home.components.notelist.content

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import coil3.compose.AsyncImage
import coil3.gif.GifDecoder
import coil3.imageLoader
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.tcl.ai.note.home.entity.HomeNoteItemEntity
import com.tcl.ai.note.utils.Logger

/**
 * 手绘缩略图显示组件
 * 专门用于优化显示高分辨率手绘缩略图（如1440*2200）
 */
@Composable
fun HandwritingThumbnailDisplay(
    note: HomeNoteItemEntity,
    modifier: Modifier = Modifier,
    contentScale: ContentScale = ContentScale.Crop,
    alignment: Alignment = Alignment.TopCenter,
    showTopPortion: Boolean = true // 是否优先显示顶部内容
) {
    val context = LocalContext.current
    val density = LocalDensity.current

    // 使用状态管理父控件尺寸
    var targetWidth by remember { mutableIntStateOf(0) }
    var targetHeight by remember { mutableIntStateOf(0) }

    // 根据夜间模式选择合适的缩略图
    val handwritingThumbnail = if (isSystemInDarkTheme()) {
        note.handwritingThumbnailDark ?: note.handwritingThumbnail
    } else {
        note.handwritingThumbnail
    }

    if (!handwritingThumbnail.isNullOrEmpty()) {
        val imageLoader = context.imageLoader.newBuilder().components {
            GifDecoder.Factory()
        }.build()

        val imageCacheKey = "$handwritingThumbnail${note.modifyTime}"

        AsyncImage(
            model = ImageRequest.Builder(context)
                .data(handwritingThumbnail)
                .crossfade(true)
                .memoryCacheKey(imageCacheKey)
                .diskCacheKey(imageCacheKey)
                // 动态设置尺寸，基于父控件大小
                .apply {
                    if (targetWidth > 0 && targetHeight > 0) {
                        // 使用实际测量的父控件尺寸
                        val loadWidth = targetWidth
                        val loadHeight = if (showTopPortion) {
                            // 如果要显示顶部内容，增加高度以获取更多内容
                            (targetHeight * 1.5).toInt()
                        } else {
                            targetHeight
                        }
//                        size(loadWidth, loadHeight)
                        Logger.d("HandwritingThumbnail", "Loading with measured size: ${loadWidth}x${loadHeight}")
                    } else {
                        // 默认尺寸，仅作为备选
                        val defaultSize = if (showTopPortion) 600 else 400
//                        size(defaultSize, defaultSize)
                        Logger.d("HandwritingThumbnail", "Loading with default size: ${defaultSize}x${defaultSize}")
                    }
                }
                .build(),
            imageLoader = imageLoader,
            contentDescription = "Handwriting thumbnail",
            contentScale = contentScale,
            alignment = alignment,
            modifier = modifier
                .fillMaxSize()
//                .aspectRatio(1f)
                .onGloballyPositioned { coordinates ->
                    // 获取父控件的像素尺寸
                    val newWidth = coordinates.size.width
                    val newHeight = coordinates.size.height

                    // 只有当尺寸发生变化时才更新
                    if (newWidth != targetWidth || newHeight != targetHeight) {
                        targetWidth = if (newWidth > 0) newWidth else 400
                        targetHeight = if (newHeight > 0) newHeight else 400
                        Logger.d("HandwritingThumbnail", "Parent size updated: ${targetWidth}x${targetHeight}")
                    }
                }
        )
    }
}

/**
 * 混合内容专用的手绘缩略图显示
 * 针对混合模式进行了特殊优化
 */
@Composable
fun MixedContentHandwritingDisplay(
    modifier: Modifier = Modifier,
    note: HomeNoteItemEntity
) {
    HandwritingThumbnailDisplay(
        note = note,
        modifier = modifier,
        contentScale = ContentScale.Fit, // 裁剪模式，确保填满容器
        alignment = Alignment.TopCenter, // 从上往下显示，优先显示顶部内容
        showTopPortion = true // 优先显示顶部内容
    )
}

/**
 * 第一屏模式专用的手绘缩略图显示
 * 针对第一屏显示进行了优化
 */
@Composable
fun FirstScreenHandwritingDisplay(
    modifier: Modifier = Modifier,
    note: HomeNoteItemEntity
) {
    HandwritingThumbnailDisplay(
        note = note,
        modifier = modifier,
        contentScale = ContentScale.Fit, // 适应模式，显示完整内容
        alignment = Alignment.TopCenter, // 居中显示
        showTopPortion = false // 显示完整内容
    )
}
