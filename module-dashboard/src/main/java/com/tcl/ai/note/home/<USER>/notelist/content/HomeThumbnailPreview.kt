package com.tcl.ai.note.home.components.notelist.content

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.*
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.utils.rememberScreenInfo

enum class FillMode {
    FIT,     // 等比例缩放，完全显示内容（可能有空白）
    FILL,    // 等比例缩放，填满容器（可能裁剪）
    STRETCH  // 拉伸填充（不保持比例）
}
@Composable
fun HomeThumbnailPreview(
    modifier: Modifier = Modifier,
    targetWidth: Dp = 300.dp,
    targetHeight: Dp = 500.dp,
    fillMode: FillMode = FillMode.STRETCH, // 添加填充模式选项
    content: @Composable () -> Unit
) {
    //获取屏幕宽高
    val screenInfo = rememberScreenInfo()
    Box(
        modifier = modifier
            .size(targetWidth, targetHeight)
    ) {
        BoxWithConstraints {
            val originalWidth = screenInfo.width.dp
            val originalHeight = screenInfo.height.dp-190.dp
            val scaleX = maxWidth / originalWidth
            val scaleY = maxHeight / originalHeight

            val (finalScaleX, finalScaleY) = when (fillMode) {
                FillMode.FIT -> {
                    val scale = minOf(scaleX, scaleY)
                    scale to scale
                }

                FillMode.FILL -> {
                    val scale = maxOf(scaleX, scaleY)
                    scale to scale
                }

                FillMode.STRETCH -> scaleX to scaleY
            }
            Box(
                modifier = Modifier
                    .requiredSize(originalWidth, originalHeight)
                    .graphicsLayer {
                        val wdPx = originalWidth.toPx()
                        val maxPX = maxWidth.toPx()
                        this.translationX = -(maxPX - wdPx * finalScaleX) / 2-1.dp.toPx()
                        this.scaleX = finalScaleX
                        this.scaleY = finalScaleY
                        transformOrigin = TransformOrigin.Center
                    }
                    .let {
                        if (fillMode == FillMode.FILL) it.clipToBounds() else it
                    },
            ) {
                content()
            }
        }
    }
}