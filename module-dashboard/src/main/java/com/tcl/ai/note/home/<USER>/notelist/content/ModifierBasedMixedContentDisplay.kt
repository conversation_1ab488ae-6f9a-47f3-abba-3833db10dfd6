package com.tcl.ai.note.home.components.notelist.content

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.home.model.HomeNoteItemModel
import com.tcl.ai.note.home.utils.rememberScaledCardSize
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ScreenSize
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.isTabletLandscape
import com.tcl.ai.note.utils.rememberScreenInfo
import com.tcl.ai.note.utils.toPx
import kotlin.math.roundToInt

/**
 * 基于Modifier变换的混合内容显示组件
 * 使用智能的Modifier链来实现精确的缩放和定位
 * 这是最简洁且性能最好的方案
 */
@Composable
fun ModifierBasedMixedContentDisplay(
    note: HomeNoteItemModel,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    val tabletLandscape = isTabletLandscape
    val screenInfo = rememberScreenInfo()
    val  containerSize= rememberScaledCardSize()
    // 计算内容的标准尺寸（基于详情页面的实际尺寸）
    val standardContentSize = remember(density, tabletLandscape, screenInfo) {
        calculateStandardContentSize(density,tabletLandscape,screenInfo)
    }
    val widthDp = standardContentSize.width.dp
    val heightDp = standardContentSize.height.dp
    // 根据容器尺寸动态计算缩放比例
    val scaleRatio = remember(containerSize, standardContentSize,tabletLandscape) {
        if (containerSize != IntSize.Zero) {
            calculateDynamicScaleRatio(containerSize, standardContentSize,tabletLandscape)
        } else {
            val defaultScaleRatio = getDefaultScaleRatio(tabletLandscape)
            Pair(defaultScaleRatio, defaultScaleRatio)
        }
    }

    Logger.d("ModifierBasedMixedContent", "Container: $containerSize, Standard: $standardContentSize, Scale: $scaleRatio")

    val textTransY = if (isTablet) { if (isTabletLandscape) -24.dp.toPx else -0.dp.toPx } else 2.dp.toPx
    val imageOffset = if (isTablet) { if (isTabletLandscape) -24.dp.toPx else -0.dp.toPx } else -2.dp.toPx
    Box(modifier = Modifier.fillMaxSize()) {
        if (isTabletLandscape){
            HomeRichTextPreview(
                note = note,
                modifier = Modifier
                    .requiredSize(heightDp)
                    .contentScale(
                        translationY = textTransY,
                        translationX = -6.dp.toPx,
                        scaleRatioX = scaleRatio.second,
                        scaleRatioY = scaleRatio.second,
                    )
                    .align(Alignment.TopCenter),
                isNormalMode = false
            )
        }else{//竖屏
            HomeRichTextPreview(
                note = note,
                modifier = Modifier
                    .requiredSize(widthDp, heightDp)
                    .contentScale(
                        translationY = textTransY,
                        translationX = -5.dp.toPx,
                        scaleRatioX = scaleRatio.first,
                        scaleRatioY = scaleRatio.second,
                    ),
                isNormalMode = false
            )
        }
        if (isTabletLandscape){
            MixedContentHandwritingDisplay(
                note = note,
                modifier = Modifier.requiredSize(heightDp, heightDp)
                    .contentScale(
                        translationY = textTransY,
                        scaleRatioX = scaleRatio.first,
                        scaleRatioY = scaleRatio.second,
                    )
            )
        }else{
            MixedContentHandwritingDisplay(
                note = note,
                modifier = Modifier.requiredSize(widthDp, heightDp)
                    .contentScale(
                        translationY = imageOffset,
                        scaleRatioX = scaleRatio.first,
                        scaleRatioY = scaleRatio.second,
                    )
            )
        }
    }
}

/**
 * 计算标准内容尺寸（基于详情页面的实际布局）
 */
private fun calculateStandardContentSize(
    density: Density,
    isTabletLandscape: Boolean,
    screenSize: ScreenSize
): IntSize {
    val widthDp = screenSize.width
    val heightDp = screenSize.height-120.dp.toPx.roundToInt()
    Logger.d("ModifierBasedMixedContent", "Screen size: $widthDp x $heightDp")
    return with(density) {
        when {
            isTablet && isTabletLandscape -> IntSize(
                width = widthDp,
                height = heightDp
            )
            isTablet -> IntSize(
                width = widthDp,
                height = heightDp
            )
            else -> IntSize(
                width = widthDp,
                height = heightDp
            )
        }
    }
}

/**
 * 动态计算缩放比例
 */
private fun calculateDynamicScaleRatio(
    containerSize: IntSize,
    standardContentSize: IntSize,
    isTabletLandscape: Boolean
): Pair<Float,Float> {
    var scaleX = containerSize.width.toFloat() / standardContentSize.width
    var scaleY = containerSize.height.toFloat() / standardContentSize.height
    if (isTabletLandscape){
        scaleX= containerSize.height.toFloat() / standardContentSize.width
        scaleY= containerSize.width.toFloat() / standardContentSize.height
    }
    Logger.d("ModifierBasedMixedContent", "Scale X: $scaleX, Scale Y: $scaleY")
    // 选择较小的缩放比例以确保内容完全显示
    return Pair(scaleX, scaleY)
}

/**
 * 获取默认缩放比例
 */
private fun getDefaultScaleRatio(isTabletLandscape: Boolean): Float {
    return when {
        isTablet && isTabletLandscape -> 0.25f
        isTablet -> 0.35f
        else -> 0.45f
    }
}


/**
 * 扩展函数：为任何Composable添加混合内容缩放效果
 */
fun Modifier.contentScale(
    scaleRatioX: Float,
    scaleRatioY: Float=1f,
    translationX: Float = 0f,
    translationY: Float = 0f,
    transformOrigin: TransformOrigin = TransformOrigin.Center
): Modifier = this.graphicsLayer {
    scaleX = scaleRatioX
    scaleY = scaleRatioY
    this.translationX = translationX
    this.translationY = translationY
    this.transformOrigin = transformOrigin
}
