package com.tcl.ai.note.home.components.notelist.content

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import coil3.compose.AsyncImage
import coil3.gif.GifDecoder
import coil3.imageLoader
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.tcl.ai.note.home.entity.HomeNoteItemEntity
import com.tcl.ai.note.utils.Logger

/**
 * 优化版手绘缩略图显示组件
 * 使用 BoxWithConstraints 自动获取父控件尺寸
 */
@Composable
fun OptimizedHandwritingDisplay(
    note: HomeNoteItemEntity,
    modifier: Modifier = Modifier,
    contentScale: ContentScale = ContentScale.Crop,
    alignment: Alignment = Alignment.TopCenter,
    showTopPortion: Boolean = true
) {
    val context = LocalContext.current
    val density = LocalDensity.current
    
    // 根据夜间模式选择合适的缩略图
    val handwritingThumbnail = if (isSystemInDarkTheme()) {
        note.handwritingThumbnailDark ?: note.handwritingThumbnail
    } else {
        note.handwritingThumbnail
    }
    
    if (!handwritingThumbnail.isNullOrEmpty()) {
        // 使用 BoxWithConstraints 获取父控件尺寸
        BoxWithConstraints(modifier = modifier) {
            val imageLoader = context.imageLoader.newBuilder().components {
                GifDecoder.Factory()
            }.build()
            
            val imageCacheKey = "$handwritingThumbnail${note.modifyTime}"
            
            // 将 Dp 转换为像素
            val maxWidthPx = with(density) { maxWidth.toPx().toInt() }
            val maxHeightPx = with(density) { maxHeight.toPx().toInt() }
            
            // 手绘图等比例缩放计算
            // 编辑页: 字体16sp 行高28sp，预览页: 字体11sp 行高15sp
            // 缩放比例 = 11/16 = 0.6875 ≈ 0.69
            val scaleRatio = 11f / 16f // 0.6875

            // 计算加载尺寸，应用缩放比例
            val baseLoadWidth = (maxWidthPx / scaleRatio).toInt().coerceAtLeast(200)
            val baseLoadHeight = (maxHeightPx / scaleRatio).toInt().coerceAtLeast(200)

            val loadWidth = baseLoadWidth
            val loadHeight = if (showTopPortion) {
                // 如果要显示顶部内容，增加高度以获取更多内容
                (baseLoadHeight * 1.5).toInt().coerceAtLeast(300)
            } else {
                baseLoadHeight
            }
            
            Logger.d("OptimizedHandwriting", "Container size: ${maxWidth}x${maxHeight} (${maxWidthPx}x${maxHeightPx}px)")
            Logger.d("OptimizedHandwriting", "Scale ratio: $scaleRatio (${(scaleRatio * 100).toInt()}%)")
            Logger.d("OptimizedHandwriting", "Base load size: ${baseLoadWidth}x${baseLoadHeight}px")
            Logger.d("OptimizedHandwriting", "Final load size: ${loadWidth}x${loadHeight}px")
            
            AsyncImage(
                model = ImageRequest.Builder(context)
                    .data(handwritingThumbnail)
                    .crossfade(true)
                    .memoryCacheKey(imageCacheKey)
                    .diskCacheKey(imageCacheKey)
                    .size(loadWidth, loadHeight) // 使用计算出的尺寸
                    .build(),
                imageLoader = imageLoader,
                contentDescription = "Handwriting thumbnail",
                contentScale = contentScale,
                alignment = alignment,
                modifier = Modifier
                    .fillMaxSize()
                    .aspectRatio(1f)
            )
        }
    }
}

/**
 * 混合内容专用的优化手绘缩略图显示
 */
@Composable
fun OptimizedMixedContentHandwritingDisplay(
    note: HomeNoteItemEntity,
    modifier: Modifier = Modifier
) {
    OptimizedHandwritingDisplay(
        note = note,
        modifier = modifier,
        contentScale = ContentScale.Fit, // 裁剪模式，确保填满容器
        alignment = Alignment.TopCenter, // 从上往下显示，优先显示顶部内容
        showTopPortion = true // 优先显示顶部内容
    )
}

