package com.tcl.ai.note.home.components.notelist.content

import android.content.Context
import android.util.TypedValue
import android.view.Gravity
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.richtext.converter.RichTextStyleEntityToSpanConverter
import com.tcl.ai.note.handwritingtext.richtext.converter.isEmpty
import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.handwritingtext.richtext.utils.DisplayUtils
import com.tcl.ai.note.handwritingtext.richtext.viewholder.RichTextViewHolder
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isDarkMode
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge

/**
 * 精简版富文本显示控件
 * 仅用于显示富文本内容，不支持编辑功能
 * 支持夜间模式
 *
 * @param context 上下文
 * @param maxLines 最大显示行数，默认为预览图推荐值
 * @param isNormalMode 是否为缩放模式，影响字体大小和间距
 */
class RichTextDisplayView(
    context: Context,
    private val maxLines: Int = Int.MAX_VALUE,
    private val isNormalMode: Boolean = true
) : AppCompatTextView(context) {
    
    private val TAG = "RichTextDisplayView"
    
    init {
        initView()
    }
    
    private fun initView() {
        // 设置基本属性 - 根据预览模式调整间距
        val paddingHorizontal = DisplayUtils.dp2px(
            if (isNormalMode) {
                isTablet.judge(PADDING_HORIZONTAL_TABLET_DP, PADDING_HORIZONTAL_PHONE_DP)
            } else {
                isTablet.judge(
                    RichTextViewHolder.PADDING_HORIZONTAL_TABLET_DP,
                    RichTextViewHolder.PADDING_HORIZONTAL_PHONE_DP)  // 编辑模式使用原始间距
            }
        )
        val paddingVertical = DisplayUtils.dp2px(
            if (isNormalMode) {
                isTablet.judge(PADDING_VERTICAL_TABLET_DP, PADDING_VERTICAL_PHONE_DP)
            } else {
                isTablet.judge(RichTextViewHolder.PADDING_VERTICAL_TABLET_DP, RichTextViewHolder.PADDING_VERTICAL_PHONE_DP)  // 编辑模式使用原始间距
            }
        )

        setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical)

        // 设置文本样式 - 根据预览模式调整字体大小
        val fontSize = if (isNormalMode) TEXT_SIZE_SP else RichTextViewHolder.TEXT_SIZE_SP
        val lineHeight = if (isNormalMode) LINE_HEIGHT_SP else RichTextViewHolder.LINE_HEIGHT_SP

//        textSize = fontSize.toFloat()
        setTextSize(TypedValue.COMPLEX_UNIT_DIP, fontSize.toFloat())
        setLineHeight(TypedValue.COMPLEX_UNIT_DIP, lineHeight.toFloat())
        Logger.d(TAG, "initView, fontSize: $fontSize, lineHeight: $lineHeight maxLines $maxLines")

        // 根据夜间模式设置文本颜色
        updateTextColor()

        // 设置为不可编辑
        isFocusable = false
        isFocusableInTouchMode = false
        isClickable = false
        isLongClickable = false

        // 设置文本选择模式（允许复制）
        setTextIsSelectable(false)

        // 针对预览图的优化设置
        setMaxLines(maxLines)  // 使用传入的最大行数
//        ellipsize = android.text.TextUtils.TruncateAt.END  // 超出部分显示省略号
        //设置顶部对齐
        gravity = Gravity.TOP
    }
    
    /**
     * 根据夜间模式更新文本颜色
     */
    private fun updateTextColor() {
        val textColor = if (isDarkMode(context)) {
            ContextCompat.getColor(context, R.color.text_title) // 夜间模式颜色
        } else {
            ContextCompat.getColor(context, R.color.text_title) // 日间模式颜色
        }
        setTextColor(textColor)
    }
    
    /**
     * 设置富文本内容
     * @param content 文本内容
     * @param richTextStyleEntity 富文本样式数据
     */
    fun setRichTextContent(content: String, richTextStyleEntity: RichTextStyleEntity?) {
        try {
            if (content.isBlank()) {
                text = ""
                return
            }

            if (richTextStyleEntity == null || richTextStyleEntity.isEmpty()) {
                // 如果没有富文本样式，直接设置纯文本
                text = content
                return
            }

            // 保存当前的maxLines设置，防止被覆盖
            val currentMaxLines = maxLines

            val todoImageWidth = if (isNormalMode) RichTextStyleEntityToSpanConverter.todoImageWidth_Home else RichTextStyleEntityToSpanConverter.todoImageWidth_Edit
            // 应用富文本样式
            RichTextStyleEntityToSpanConverter.applyToTextView(richTextStyleEntity, this, content,todoImageWidth)

            // 确保maxLines设置没有被覆盖
            if (maxLines != currentMaxLines) {
                setMaxLines(currentMaxLines)
                Logger.d(TAG, "Restored maxLines to: $currentMaxLines")
            }

        } catch (e: Exception) {
            Logger.e(TAG, "Error setting rich text content: ${e.message}")
            // 出错时显示纯文本
            text = content
        }
    }
    
    /**
     * 清空内容
     */
    fun clearContent() {
        text = ""
    }
    
    /**
     * 获取纯文本内容
     */
    fun getPlainText(): String {
        return text?.toString() ?: ""
    }
    
    /**
     * 更新夜间模式
     */
    fun updateDarkMode() {
        updateTextColor()
    }
    
    companion object {
        // 首页预览图专用的紧凑型常量，等比例缩放以适应小尺寸预览
        const val PADDING_HORIZONTAL_TABLET_DP = 0  // 原48缩放到1/3
        const val PADDING_HORIZONTAL_PHONE_DP = 0    // 原24缩放到1/3
        const val PADDING_VERTICAL_TABLET_DP = 0     // 原18缩放到1/3
        const val PADDING_VERTICAL_PHONE_DP = 0      // 原8缩放到约1/3
        const val TEXT_SIZE_SP = 12                  // 原16缩放到约2/3，保持可读性
        const val LINE_HEIGHT_SP = 16               // 原28缩放到约3/5，减少行间距
    }
}