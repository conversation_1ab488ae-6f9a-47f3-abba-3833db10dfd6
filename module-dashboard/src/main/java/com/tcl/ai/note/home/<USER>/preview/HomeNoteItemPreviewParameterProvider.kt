package com.tcl.ai.note.home.components.preview

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.entity.CategoryIcon
import com.tcl.ai.note.home.entity.HighlightInfo
import com.tcl.ai.note.home.entity.HighlightRange
import com.tcl.ai.note.home.entity.HomeNoteItemEntity
import com.tcl.ai.note.home.entity.ThumbnailInfo
import com.tcl.ai.note.home.entity.ThumbnailType

/**
 * 预览参数提供器 预览 HomeNoteItem 示例数据
 */
class HomeNoteItemPreviewParameterProvider : PreviewParameterProvider<HomeNoteItemEntity> {
    override val values = sequenceOf(
        HomeNoteItemEntity(
            noteId = "1",
            noteTitle = "Text Note",
            summary = "This is a sample text note",
            date = "2023-10-01",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.TEXT_CONTENT),
            highlightInfo = HighlightInfo(
                searchKeyword = "Text",
                titleHighlights = arrayListOf(HighlightRange(start = 0, end = 4)),
                contentHighlights = emptyList()
            ),
            categoryIcon = CategoryIcon(R.drawable.ic_category_uncategorised, R.color.category_color_YELLOW)
        ),
        HomeNoteItemEntity(
            noteId = "1",
            noteTitle = "Text Note",
            summary = "This is a sample text note",
            date = "2023-10-01",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN),
            highlightInfo = HighlightInfo(searchKeyword = "Text", titleHighlights = arrayListOf(
                HighlightRange(start = 0, end = 4),
            )  ),
        ),
        HomeNoteItemEntity(
            noteId = "2",
            noteTitle = "Audio Recording",
            summary = "Voice memo from meeting",
            date = "2023年10月20日 14:00",
            thumbnailInfo = ThumbnailInfo(
                type = ThumbnailType.AUDIO_ICON,
                showAudioIcon = true,
                hasAudio = true
            )
        ),
        HomeNoteItemEntity(
            noteId = "3",
            noteTitle = "Screenshot",
            summary = "App interface design",
            date = "2023年10月20日",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
        ),

    )
}