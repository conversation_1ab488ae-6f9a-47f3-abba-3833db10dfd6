package com.tcl.ai.note.home.components.preview

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.tcl.ai.note.base.R
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.home.entity.CategoryIcon
import com.tcl.ai.note.home.entity.HomeCategoryItemEntity
import com.tcl.ai.note.home.entity.HomeNoteItemEntity
import com.tcl.ai.note.home.entity.ThumbnailInfo
import com.tcl.ai.note.home.entity.ThumbnailType
import com.tcl.ai.note.home.data.demoCategories
import com.tcl.ai.note.home.data.getSampleNotes
import com.tcl.ai.note.home.vm.state.HomeCategoryUiState
import com.tcl.ai.note.home.vm.state.HomeContentListNotesUiState
import com.tcl.ai.note.home.vm.state.HomeContentListState
import com.tcl.ai.note.home.vm.state.HomeNoteUiState
import com.tcl.ai.note.home.vm.state.HomeTitleMode

/**
 * 平板预览状态数据类
 */
data class TabletPreviewState(
    val homeNoteUiState: HomeNoteUiState,
    val homeCategoryUiState: HomeCategoryUiState,
    val description: String
)

/**
 * 平板主屏幕预览参数提供器
 * 为 NotesAppTabletPreview 和 NotesAppTabletItemPreview 提供共用的预览数据
 */
class TabletHomeScreenPreviewParameterProvider : PreviewParameterProvider<TabletPreviewState> {
    override val values = sequenceOf(
        // 1. 正常模式 - 网格视图，有数据
        TabletPreviewState(
            homeNoteUiState = HomeNoteUiState(
                selectedCategoryId = "all_notes",
                selectedCategoryName = "全部",
                viewType = DataStoreParam.VIEW_TYPE_GRID,
                titleMode = HomeTitleMode.Normal,
                isClickedSort = false,
                selectedCount = 0,
                isSelectedAll = false,
                isCreateTimeSort = true,
                listNotesUiState = HomeContentListNotesUiState.Success(
                    HomeContentListState(getSampleNotes())
                )
            ),
            homeCategoryUiState = HomeCategoryUiState(
                categories = demoCategories
            ),
            description = "正常模式 - 网格视图"
        ),

        // 2. 正常模式 - 列表视图，有数据
        TabletPreviewState(
            homeNoteUiState = HomeNoteUiState(
                selectedCategoryId = "1",
                selectedCategoryName = "工作笔记",
                viewType = DataStoreParam.VIEW_TYPE_LIST,
                titleMode = HomeTitleMode.Normal,
                isClickedSort = false,
                selectedCount = 0,
                isSelectedAll = false,
                isCreateTimeSort = false,
                listNotesUiState = HomeContentListNotesUiState.Success(
                    HomeContentListState(getSampleNotes().take(5))
                )
            ),
            homeCategoryUiState = HomeCategoryUiState(
                categories = demoCategories
            ),
            description = "正常模式 - 列表视图"
        ),

        // 3. 编辑模式 - 部分选中
        TabletPreviewState(
            homeNoteUiState = HomeNoteUiState(
                selectedCategoryId = "2",
                selectedCategoryName = "学习笔记",
                viewType = DataStoreParam.VIEW_TYPE_GRID,
                titleMode = HomeTitleMode.Edit,
                isClickedSort = false,
                selectedCount = 3,
                isSelectedAll = false,
                isCreateTimeSort = true,
                listNotesUiState = HomeContentListNotesUiState.Success(
                    HomeContentListState(
                        getSampleNotes().mapIndexed { index, note ->
                            note.copy(isChecked = index < 3)
                        }
                    )
                )
            ),
            homeCategoryUiState = HomeCategoryUiState(
                categories = demoCategories
            ),
            description = "编辑模式 - 部分选中"
        ),

        // 4. 编辑模式 - 全选
        TabletPreviewState(
            homeNoteUiState = HomeNoteUiState(
                selectedCategoryId = "3",
                selectedCategoryName = "个人笔记",
                viewType = DataStoreParam.VIEW_TYPE_LIST,
                titleMode = HomeTitleMode.Edit,
                isClickedSort = false,
                selectedCount = 8,
                isSelectedAll = true,
                isCreateTimeSort = true,
                listNotesUiState = HomeContentListNotesUiState.Success(
                    HomeContentListState(
                        getSampleNotes().map { note ->
                            note.copy(isChecked = true)
                        }
                    )
                )
            ),
            homeCategoryUiState = HomeCategoryUiState(
                categories = demoCategories
            ),
            description = "编辑模式 - 全选"
        ),

        // 5. 搜索模式
        TabletPreviewState(
            homeNoteUiState = HomeNoteUiState(
                selectedCategoryId = "search_all",
                selectedCategoryName = "全部",
                viewType = DataStoreParam.VIEW_TYPE_GRID,
                titleMode = HomeTitleMode.Search,
                isClickedSort = false,
                selectedCount = 0,
                isSelectedAll = false,
                searchText = "会议",
                isCreateTimeSort = true,
                listNotesUiState = HomeContentListNotesUiState.Success(
                    HomeContentListState(
                        listOf(
                            HomeNoteItemEntity(
                                noteId = "search1",
                                noteTitle = "会议记录 - 产品规划",
                                summary = "讨论了下季度的产品规划和功能优先级",
                                date = "2024-01-15",
                                thumbnailInfo = ThumbnailInfo(type = ThumbnailType.TEXT_CONTENT)
                            ),
                            HomeNoteItemEntity(
                                noteId = "search2",
                                noteTitle = "团队会议纪要",
                                summary = "周例会讨论项目进度和问题",
                                date = "2024-01-12",
                                thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN)
                            )
                        )
                    )
                )
            ),
            homeCategoryUiState = HomeCategoryUiState(
                categories = demoCategories
            ),
            description = "搜索模式"
        ),

        // 6. 加载状态
        TabletPreviewState(
            homeNoteUiState = HomeNoteUiState(
                selectedCategoryId = "loading_all",
                selectedCategoryName = "全部",
                viewType = DataStoreParam.VIEW_TYPE_GRID,
                titleMode = HomeTitleMode.Normal,
                isClickedSort = false,
                selectedCount = 0,
                isSelectedAll = false,
                isCreateTimeSort = true,
                listNotesUiState = HomeContentListNotesUiState.Loading
            ),
            homeCategoryUiState = HomeCategoryUiState(
                categories = demoCategories
            ),
            description = "加载状态"
        ),

        // 7. 空状态
        TabletPreviewState(
            homeNoteUiState = HomeNoteUiState(
                selectedCategoryId = "4",
                selectedCategoryName = "空分类",
                viewType = DataStoreParam.VIEW_TYPE_GRID,
                titleMode = HomeTitleMode.Normal,
                isClickedSort = false,
                selectedCount = 0,
                isSelectedAll = false,
                isCreateTimeSort = true,
                listNotesUiState = HomeContentListNotesUiState.Empty
            ),
            homeCategoryUiState = HomeCategoryUiState(
                categories = demoCategories
            ),
            description = "空状态"
        ),

        // 8. 错误状态
        TabletPreviewState(
            homeNoteUiState = HomeNoteUiState(
                selectedCategoryId = "error_all",
                selectedCategoryName = "全部",
                viewType = DataStoreParam.VIEW_TYPE_LIST,
                titleMode = HomeTitleMode.Normal,
                isClickedSort = false,
                selectedCount = 0,
                isSelectedAll = false,
                isCreateTimeSort = true,
                listNotesUiState = HomeContentListNotesUiState.Error("网络连接失败")
            ),
            homeCategoryUiState = HomeCategoryUiState(
                categories = emptyList()
            ),
            description = "错误状态"
        ),

        // 9. 排序状态
        TabletPreviewState(
            homeNoteUiState = HomeNoteUiState(
                selectedCategoryId = "sort_all",
                selectedCategoryName = "全部",
                viewType = DataStoreParam.VIEW_TYPE_GRID,
                titleMode = HomeTitleMode.Normal,
                isClickedSort = true,
                selectedCount = 0,
                isSelectedAll = false,
                isCreateTimeSort = false,
                listNotesUiState = HomeContentListNotesUiState.Success(
                    HomeContentListState(getSampleNotes())
                )
            ),
            homeCategoryUiState = HomeCategoryUiState(
                categories = demoCategories
            ),
            description = "排序状态 - 按修改时间"
        ),

        // 10. 长分类名称测试
        TabletPreviewState(
            homeNoteUiState = HomeNoteUiState(
                selectedCategoryId = "long_category",
                selectedCategoryName = "这是一个非常长的分类名称用来测试UI在平板设备上的显示效果",
                viewType = DataStoreParam.VIEW_TYPE_LIST,
                titleMode = HomeTitleMode.Normal,
                isClickedSort = false,
                selectedCount = 0,
                isSelectedAll = false,
                isCreateTimeSort = true,
                listNotesUiState = HomeContentListNotesUiState.Success(
                    HomeContentListState(getSampleNotes().take(3))
                )
            ),
            homeCategoryUiState = HomeCategoryUiState(
                categories = demoCategories + listOf(
                    HomeCategoryItemEntity(
                        id = "long_category",
                        name = "这是一个非常长的分类名称用来测试UI在平板设备上的显示效果",
                        noteCounts = 3,
                        isSelected = true,
                        categoryIcon = CategoryIcon(R.drawable.ic_category_uncategorised)
                    )
                )
            ),
            description = "长分类名称测试"
        )
    )
}
