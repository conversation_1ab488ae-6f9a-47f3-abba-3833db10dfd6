package com.tcl.ai.note.home.vm.state

import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.home.entity.HomeNoteItemEntity



/**
 * 首页UI状态
 */
data class HomeNoteUiState(
    val selectedCategoryId: String = "",
    val selectedCategoryName: String? = null,
    val viewType: String = DataStoreParam.VIEW_TYPE_GRID, // "card" or "list"
    val titleMode: HomeTitleMode = HomeTitleMode.Normal, // 新增：正常、搜索、编辑三种模式
    val isClickedSort: Boolean = false, // 是否点击了排序按钮
    val selectedCount: Int = 0, // 已选中的笔记数量
    val isSelectedAll: Boolean = false,
    val listNotesUiState: HomeContentListNotesUiState = HomeContentListNotesUiState.Loading,
    val isCreateTimeSort: Boolean = true, // 是否按照创建时间排序
    val isShowDeleteDialog: Boolean = false, // 是否 显示删除对话框
    val searchText: String = "", // 搜索文本
    val isSearchInputFocused: Boolean = false, // 搜索框是否获得焦点
    val currentCategoryIndex: Int? =null,
    val isShowBottomNav: Boolean = false, // 是否显示底部导航
    val isCategoryDialogShowing: Boolean = false, //  分类弹窗是否显示了
){
    /**
     * 是否是编辑模式
     */
    val isEditMode: Boolean
        get() = titleMode == HomeTitleMode.Edit
    /**
     * 是否显示操作布局
     */
    val isShowOperateView: Boolean
        get() = isEditMode

    val isButtonOperateEnable: Boolean
        get() = selectedCount>0
    /**
     * 是否是搜索模式
     */
    val isSearchMode: Boolean
        get() = titleMode == HomeTitleMode.Search

    /**
     * 是否是正常模式
     */
    val isNormalMode: Boolean
        get() = titleMode == HomeTitleMode.Normal
}

/**
 * 首页模式
 */
enum class HomeTitleMode {
    Normal, // 正常
    Search, // 搜索
    Edit    // 编辑
}

data class HomeContentListState(
    val notes: List<HomeNoteItemEntity> = emptyList(),
)



sealed class HomeContentListNotesUiState {
    data object Loading : HomeContentListNotesUiState()
    data class Success(val homeContentListState: HomeContentListState) :
        HomeContentListNotesUiState()
    data class Error(val message: String) : HomeContentListNotesUiState()
    data object Empty : HomeContentListNotesUiState()
    data object SearchMode : HomeContentListNotesUiState()
}
