package com.tcl.ai.note.handwritingtext.bean

import com.tcl.ai.note.handwritingtext.utils.toPathBezierCompose
import com.tcl.ai.note.handwritingtext.vo.DrawPathDisplay
import kotlinx.serialization.Serializable

@Serializable
data class DrawStroke(
    // 和DrawPathDisplay的id保存一致
    val id: Long = System.currentTimeMillis(),
    val points: List<DrawPoint> = emptyList(),
    val scaledPoints: List<DrawPoint> = emptyList(),
    val style: StrokeStyle = StrokeStyle(),
) {
    fun toDrawPathDisplay() = DrawPathDisplay(
        id = id,
        path = points.toPathBezierCompose(),
        points = points,
        style = style
    )
}