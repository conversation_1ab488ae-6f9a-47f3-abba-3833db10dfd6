package com.tcl.ai.note.handwritingtext.bean

import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.handwritingtext.R as HR

/**
 * 一级菜单类型枚举
 */
enum class MenuBar {
    NONE,      // 啥也不是
    KEYBOARD,  // 键盘/文本编辑模式，未激活的情况下就是非键盘或非文本编辑模式，即手绘模式
    BRUSH,     // 绘图模式(包含各种笔)
    ERASER,    // 橡皮擦
    LASSO,     // 套索
    RULER,     // 尺子
    HANDWRITING_TO_TEXT,  // 手写转文本
    UNDO,      // 撤销
    REDO,      // 重做
    COLOR,    // 颜色
    AI,        // AI助手
    BEAUTIFY,  // 美化
    PAN_ONLY,  // 平移模式

    // 手机端/二期新增
    TODO,                    // 待办
    TEXT_STYLE,              // 文本样式
    ONE_STROKE_FORMING       // 一笔成型
}

/**
 * 笔刷类型枚举 - 作为BRUSH的二级菜单
 */
enum class BrushType {
    FOUNTAIN_PEN,  // 钢笔
    MARK_PEN,      // 马克笔
    BALL_PEN,      // 圆珠笔
}

/**
 * 菜单栏分组
 */
enum class MenuGroup { 
    MAIN,       // 主要模式切换 (键盘/绘图)
    EDIT,       // 绘图工具 (各种笔/橡皮擦)
    UNDO_REDO,  // 撤销/重做
    COLOR,      // 颜色选择
    TOOL,        // 其他工具 (套索/尺子等)

    // 二期新增
    TEXT,        // 与富文本相关的
}

/**
 * 菜单栏项目基类
 */
sealed class MenuBarItem(
    var id:Int?=null,
    open val menuType: MenuBar,
    val group: MenuGroup,
    var position:Offset = Offset.Zero,
    open val btnSize: Dp = getGlobalDimens().btnSize,
    @DrawableRes var iconRes: Int? = menuType.getImageRes(),
    @StringRes var descriptionRes: Int = menuType.getDescRes(),
    var isChecked: Boolean = false,
    var isEnabled: Boolean = true,
    open var onClick: (MenuBarItem) -> Unit = { },
    open var customContent: (@Composable (modifier: Modifier, menuBarItem:MenuBarItem) -> Unit)? = null
) {
    // 文本编辑模式
    data object Keyboard : MenuBarItem(menuType = MenuBar.KEYBOARD, group =MenuGroup.MAIN)
    
    // 绘图工具 - 各种笔
    data object Brush : MenuBarItem(menuType = MenuBar.BRUSH, group=MenuGroup.EDIT)
    data object Eraser : MenuBarItem(menuType = MenuBar.ERASER,group= MenuGroup.EDIT)
    
    // 特殊绘图工具
    data object Lasso : MenuBarItem(menuType = MenuBar.LASSO, group=MenuGroup.EDIT)
    data object Ruler : MenuBarItem(menuType = MenuBar.RULER, group=MenuGroup.EDIT)
    data object HandwritingToText : MenuBarItem(menuType = MenuBar.HANDWRITING_TO_TEXT, group= MenuGroup.EDIT)
    
    // 撤销/重做
    data object Undo : MenuBarItem(menuType = MenuBar.UNDO, group= MenuGroup.UNDO_REDO)
    data object Redo : MenuBarItem(menuType = MenuBar.REDO, group= MenuGroup.UNDO_REDO)
    
    // 其他工具
    data object Beautify : MenuBarItem(menuType = MenuBar.BEAUTIFY, group= MenuGroup.TOOL)
    data object PanOnly : MenuBarItem(menuType = MenuBar.PAN_ONLY, group= MenuGroup.TOOL)
    data object ShapeRecognize : MenuBarItem(menuType = MenuBar.ONE_STROKE_FORMING, group=MenuGroup.TOOL)

    // 颜色按钮 - 自定义UI
    class BrushColor(
        id: Int,
        menuType: MenuBar,
        override val btnSize: Dp = getGlobalDimens().btnSize,
        @ColorInt val color: Int,
        val isSelected: Boolean,
        onSelected: (colorIdx: Int,item:MenuBarItem) -> Unit,
        override var onClick: (MenuBarItem) -> Unit = {item ->
            item.isChecked = isSelected
            onSelected(id, item)
        },
        override var customContent: (@Composable (modifier: Modifier,menuBarItem:MenuBarItem) -> Unit)? = null
    ) : MenuBarItem(
        id = id,
        menuType = menuType,
        group = MenuGroup.COLOR,
        descriptionRes = R.string.edit_bottom_menu_color_select,
    )



    // AI按钮 - 自定义UI
    class AI(override var customContent: (@Composable (modifier: Modifier,menuBarItem:MenuBarItem) -> Unit)? = null) :
        MenuBarItem(menuType = MenuBar.AI, group = MenuGroup.TOOL, iconRes = MenuBar.AI.getImageRes(), descriptionRes = MenuBar.AI.getDescRes())

    // 二期新增, 菜单栏与富文本工具栏结合了, 以下类型用于富文本
    data object TODO : MenuBarItem(menuType = MenuBar.TODO, group = MenuGroup.EDIT)
    data object TextStyle : MenuBarItem(menuType = MenuBar.TEXT_STYLE, group = MenuGroup.EDIT)

    companion object{
        fun cleanup(){
            MenuBarItem.Keyboard.apply {
                onClick = {}
            }
            MenuBarItem.Brush.apply {
                onClick = {}
            }
            MenuBarItem.Eraser.apply {
                onClick = {}
            }
            MenuBarItem.Undo.apply {
                onClick = {}
            }
            MenuBarItem.Redo.apply {
                onClick = {}
            }
            MenuBarItem.Beautify.apply {
                onClick = {}
            }
            MenuBarItem.HandwritingToText.apply {
                onClick = {}
            }
            MenuBarItem.ShapeRecognize.apply {
                onClick = {}
            }
        }

    }
}

/**
 * 获取菜单图标资源
 */
fun MenuBar.getImageRes(isChecked: Boolean = false): Int? {
    return when (this) {
        MenuBar.KEYBOARD -> HR.drawable.ic_menu_keyboard_nor
        MenuBar.BRUSH -> HR.drawable.ic_tablet_menu_pen_nor
        MenuBar.ERASER -> HR.drawable.ic_tablet_menu_eraser_nor
        MenuBar.LASSO -> HR.drawable.ic_tablet_menu_lasso_nor
        MenuBar.RULER -> HR.drawable.ic_tablet_menu_ruler_nor
        MenuBar.HANDWRITING_TO_TEXT -> HR.drawable.ic_tablet_menu_handwriting_to_text_nor
        MenuBar.UNDO -> if (isChecked) R.drawable.ic_edit_undo_enable else R.drawable.ic_edit_undo
        MenuBar.REDO -> if (isChecked) R.drawable.ic_edit_redo_enable else R.drawable.ic_edit_redo
        MenuBar.COLOR->  null
        MenuBar.AI -> R.drawable.ic_ai_icon
        MenuBar.BEAUTIFY -> HR.drawable.ic_menu_rewrite
        MenuBar.PAN_ONLY -> if(isChecked) HR.drawable.ic_tablet_menu_pan_only_enable else HR.drawable.ic_tablet_menu_pan_only_disable
        MenuBar.TODO -> HR.drawable.ic_richtext_menu_todo
        MenuBar.TEXT_STYLE -> HR.drawable.ic_menu_text_style
        MenuBar.ONE_STROKE_FORMING -> HR.drawable.ic_menu_shape_recognize //  TODO 一笔成型功能还没上，暂时用笔刷代替
        MenuBar.NONE -> null
    }
}

/**
 * 获取笔刷类型图标资源
 */
fun BrushType.getImageRes(isChecked: Boolean = false): Int {
    return when (this) {
        BrushType.FOUNTAIN_PEN -> HR.drawable.ic_tablet_menu_pen_nor
        BrushType.MARK_PEN -> HR.drawable.ic_tablet_menu_markpen_nor
        BrushType.BALL_PEN -> HR.drawable.ic_tablet_menu_ballpen_nor
    }
}

/**
 * 获取菜单描述文本资源
 */
fun MenuBar.getDescRes(isChecked: Boolean = false): Int {
    return when (this) {
        MenuBar.KEYBOARD -> R.string.edit_bottom_menu_keyboard
        MenuBar.BRUSH -> R.string.edit_bottom_tool_brush
        MenuBar.ERASER -> R.string.edit_bottom_tool_eraser
        MenuBar.LASSO -> R.string.edit_menu_lasso
        MenuBar.RULER -> R.string.edit_menu_ruler
        MenuBar.HANDWRITING_TO_TEXT -> R.string.edit_menu_handwriting_to_text
        MenuBar.UNDO -> R.string.edit_top_menu_undo
        MenuBar.REDO -> R.string.edit_top_menu_redo
        MenuBar.COLOR -> R.string.edit_bottom_menu_color_select
        MenuBar.AI -> R.string.edit_bottom_menu_ai_assistant
        MenuBar.BEAUTIFY -> R.string.edit_menu_beautify
        MenuBar.PAN_ONLY -> if (isChecked) R.string.btn_close_hand_drawn_mode else R.string.btn_open_hand_drawn_mode
        MenuBar.TODO -> R.string.menu_todo_button
        MenuBar.TEXT_STYLE -> R.string.menu_text_style_button
        MenuBar.ONE_STROKE_FORMING -> R.string.menu_one_stroke_forming_button
        MenuBar.NONE -> 0
    }
}

/**
 * 获取笔刷类型描述文本资源
 */
fun BrushType.getDescRes(): Int {
    return when (this) {
        BrushType.FOUNTAIN_PEN -> R.string.pen_selector_fountain_pen
        BrushType.MARK_PEN -> R.string.pen_selector_marker_pen
        BrushType.BALL_PEN -> R.string.pen_selector_ball_pen
    }
}

/**
 * 获取菜单描述文本
 */
@Composable
fun MenuBar.getDesc(isChecked: Boolean = false): String {
    return stringResource(this.getDescRes(isChecked))
}

/**
 * 获取笔刷类型描述文本
 */
@Composable
fun BrushType.getDesc(): String {
    return stringResource(this.getDescRes())
}

/**
 * 将BrushType转换为对应的DoodlePen类型
 */
fun BrushType.toDoodlePen(): DoodlePen {
    return when (this) {
        BrushType.FOUNTAIN_PEN -> DoodlePen.FountainPen
        BrushType.MARK_PEN -> DoodlePen.Markpen
        BrushType.BALL_PEN -> DoodlePen.Ballpen
    }
}

/**
 * 将DoodlePen转换为对应的BrushType类型
 */
fun DoodlePen.toBrushType(): BrushType {
    return when (this) {
        is DoodlePen.FountainPen -> BrushType.FOUNTAIN_PEN
        is DoodlePen.Markpen -> BrushType.MARK_PEN
        is DoodlePen.Ballpen -> BrushType.BALL_PEN
    }
}



