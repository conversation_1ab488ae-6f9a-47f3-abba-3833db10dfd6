package com.tcl.ai.note.handwritingtext.bean

import android.text.Layout
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.handwritingtext.R as HR

/**
 * 富文本工具栏类型枚举
 * 定义所有可用的富文本格式化工具
 */
enum class RichTextToolType {
    // 段落格式
    TODO_LIST,          // 待办事项
    BULLET_LIST,        // 无序列表
    NUMBER_LIST,        // 有序列表
    
    // 文本格式
    BOLD,               // 粗体
    ITALIC,             // 斜体
    UNDERLINE,          // 下划线
    STRIKETHROUGH,      // 删除线
    
    // 颜色控制
    TEXT_COLOR,         // 文本颜色
    TEXT_BG_COLOR,      // 文本背景颜色
    
    // 字体大小
    FONT_SIZE,          // 字体大小选择器
    
    // 对齐方式
    ALIGN_LEFT,         // 左对齐
    ALIGN_CENTER,       // 居中对齐
    ALIGN_RIGHT,        // 右对齐
    
    // 缩进控制
    INDENT_DECREASE,    // 减少缩进
    INDENT_INCREASE,    // 增加缩进
    
    // 分割线
    DIVIDER             // 分割线
}

/**
 * 富文本工具栏分组
 * 用于逻辑分组和布局控制
 */
enum class RichTextToolGroup {
    PARAGRAPH,          // 段落格式组 (待办、列表)
    TEXT_FORMAT,        // 文本格式组 (粗体、斜体、下划线、删除线)
    COLOR,              // 颜色控制组
    FONT_SIZE,          // 字体大小组
    ALIGNMENT,          // 对齐方式组
    INDENT              // 缩进控制组
}

/**
 * 富文本工具栏项目基类
 * 采用密封类设计，提供类型安全和扩展性
 */
sealed class RichTextToolBarItem(
    open val id: String,
    open val toolType: RichTextToolType,
    open val group: RichTextToolGroup,
    open val btnSize: Dp = getGlobalDimens().btnSize,
    @DrawableRes open val iconRes: Int? = toolType.getIconRes(),
    @StringRes open val descriptionRes: Int = toolType.getDescRes(),
    open val isEnabled: Boolean = true,
    open val isVisible: Boolean = true,
    open val customContent: (@Composable (modifier: Modifier, item: RichTextToolBarItem) -> Unit)? = null
) {
    
    /**
     * 切换按钮 - 具有选中/未选中状态
     */
    data class ToggleButton(
        override val id: String,
        override val toolType: RichTextToolType,
        override val group: RichTextToolGroup,
        val isChecked: Boolean = false,
        val onToggle: (RichTextToolBarItem) -> Unit = {},
        override val btnSize: Dp = getGlobalDimens().btnSize,
        @DrawableRes override val iconRes: Int? = toolType.getIconRes(),
        @StringRes override val descriptionRes: Int = toolType.getDescRes(),
        override val isEnabled: Boolean = true,
        override val isVisible: Boolean = true
    ) : RichTextToolBarItem(id, toolType, group, btnSize, iconRes, descriptionRes, isEnabled, isVisible)
    
    /**
     * 动作按钮 - 执行特定动作，无状态
     */
    data class ActionButton(
        override val id: String,
        override val toolType: RichTextToolType,
        override val group: RichTextToolGroup,
        val onAction: (RichTextToolBarItem) -> Unit = {},
        override val btnSize: Dp = getGlobalDimens().btnSize,
        @DrawableRes override val iconRes: Int? = toolType.getIconRes(),
        @StringRes override val descriptionRes: Int = toolType.getDescRes(),
        override val isEnabled: Boolean = true,
        override val isVisible: Boolean = true
    ) : RichTextToolBarItem(id, toolType, group, btnSize, iconRes, descriptionRes, isEnabled, isVisible)
    
    /**
     * 下拉选择器 - 带有下拉菜单的选择器
     */
    data class DropdownSelector<T>(
        override val id: String,
        override val toolType: RichTextToolType,
        override val group: RichTextToolGroup,
        val selectedValue: T,
        val options: List<T>,
        val isExpanded: Boolean = false,
        val onSelectionChange: (T, RichTextToolBarItem) -> Unit = { _, _ -> },
        val onToggleExpanded: (RichTextToolBarItem) -> Unit = {},
        val valueToString: (T) -> String = { it.toString() },
        override val btnSize: Dp = getGlobalDimens().btnSize,
        override val descriptionRes: Int = toolType.getDescRes(),
        override val isEnabled: Boolean = true,
        override val isVisible: Boolean = true,
        override val customContent: (@Composable (modifier: Modifier, item: RichTextToolBarItem) -> Unit)? = null
    ) : RichTextToolBarItem(id, toolType, group, btnSize, null, descriptionRes, isEnabled, isVisible, customContent)
    
    /**
     * 颜色选择器 - 专门用于颜色选择
     */
    data class ColorPicker(
        override val id: String,
        override val toolType: RichTextToolType,
        override val group: RichTextToolGroup,
        val selectedColor: Color,
        val isExpanded: Boolean = false,
        val onColorChange: (Color, RichTextToolBarItem) -> Unit = { _, _ -> },
        val onToggleExpanded: (RichTextToolBarItem) -> Unit = {},
        override val btnSize: Dp = getGlobalDimens().btnSize,
        @DrawableRes override val iconRes: Int? = toolType.getIconRes(),
        @StringRes override val descriptionRes: Int = toolType.getDescRes(),
        override val isEnabled: Boolean = true,
        override val isVisible: Boolean = true,
        override val customContent: (@Composable (modifier: Modifier, item: RichTextToolBarItem) -> Unit)? = null
    ) : RichTextToolBarItem(id, toolType, group, btnSize, iconRes, descriptionRes, isEnabled, isVisible, customContent)
    
    /**
     * 分割线 - 用于视觉分组
     */
    data class Divider(
        override val id: String = "divider_${System.currentTimeMillis()}",
        override val group: RichTextToolGroup,
        override val isVisible: Boolean = true
    ) : RichTextToolBarItem(id, RichTextToolType.DIVIDER, group, isVisible = isVisible)
}

/**
 * 富文本工具栏配置
 * 定义工具栏的整体配置和行为
 */
data class RichTextToolBarConfig(
    val items: List<RichTextToolBarItem>,
    val groupSpacing: Dp = 12.dp,
    val itemSpacing: Dp = 8.dp,
    val showGroupDividers: Boolean = true,
    val backgroundColor: Color? = null,
    val isEnabled: Boolean = true
) {
    /**
     * 按组获取工具项
     */
    fun getItemsByGroup(): Map<RichTextToolGroup, List<RichTextToolBarItem>> {
        return items.filter { it.isVisible }
            .groupBy { it.group }
            .toSortedMap(compareBy { it.ordinal })
    }
    
    /**
     * 获取指定类型的工具项
     */
    fun getItemByType(type: RichTextToolType): RichTextToolBarItem? {
        return items.find { it.toolType == type && it.isVisible }
    }
    
    /**
     * 获取指定ID的工具项
     */
    fun getItemById(id: String): RichTextToolBarItem? {
        return items.find { it.id == id && it.isVisible }
    }
}

/**
 * 富文本工具栏状态
 * 管理整个工具栏的状态
 */
data class RichTextToolBarState(
    // 段落格式状态
    val isTodoActive: Boolean = false,
    val isBulletListActive: Boolean = false,
    val isNumberListActive: Boolean = false,
    
    // 文本格式状态
    val isBoldActive: Boolean = false,
    val isItalicActive: Boolean = false,
    val isUnderlineActive: Boolean = false,
    val isStrikethroughActive: Boolean = false,
    val isIndentLeftActive: Boolean = false,
    val isIndentRightActive: Boolean = false,
    
    // 对齐状态 (互斥)
    val alignmentState: Layout.Alignment = Layout.Alignment.ALIGN_NORMAL,
    
    // 颜色状态
    val textColor: Color = Color.Black,
    val textBgColor: Color = Color.Transparent,
    val showTextColorPicker: Boolean = false,
    val showTextBgColorPicker: Boolean = false,
    
    // 字体大小状态
    val selectedFontSize: Int = 16,
    val showFontSizeDropdown: Boolean = false,
    val availableFontSizes: List<Int> = listOf(12, 14, 16, 18, 20, 24, 28, 32),
    
    // 全局状态
    val isEnabled: Boolean = true,
    val isCanUndo:Boolean=false,
    val isCanRedo:Boolean=false
) {
    /**
     * 获取指定工具类型的激活状态
     */
    fun isToolActive(toolType: RichTextToolType): Boolean {
        return when (toolType) {
            RichTextToolType.TODO_LIST -> isTodoActive
            RichTextToolType.BULLET_LIST -> isBulletListActive
            RichTextToolType.NUMBER_LIST -> isNumberListActive
            RichTextToolType.BOLD -> isBoldActive
            RichTextToolType.ITALIC -> isItalicActive
            RichTextToolType.UNDERLINE -> isUnderlineActive
            RichTextToolType.STRIKETHROUGH -> isStrikethroughActive
            RichTextToolType.ALIGN_LEFT -> alignmentState == Layout.Alignment.ALIGN_NORMAL
            RichTextToolType.ALIGN_CENTER -> alignmentState == Layout.Alignment.ALIGN_CENTER
            RichTextToolType.ALIGN_RIGHT -> alignmentState == Layout.Alignment.ALIGN_OPPOSITE
            RichTextToolType.TEXT_COLOR -> showTextColorPicker
            RichTextToolType.TEXT_BG_COLOR -> showTextBgColorPicker
            RichTextToolType.FONT_SIZE -> showFontSizeDropdown
            RichTextToolType.INDENT_DECREASE -> isIndentLeftActive
            RichTextToolType.INDENT_INCREASE -> isIndentRightActive
            else -> false
        }
    }
}

/**
 * 对齐状态枚举
 */
enum class AlignmentState {
    LEFT, CENTER, RIGHT
}

/**
 * 富文本工具栏构建器
 * 提供流式API来构建工具栏配置
 */
class RichTextToolBarBuilder {
    private val items = mutableListOf<RichTextToolBarItem>()
    
    /**
     * 添加段落格式组
     */
    fun addParagraphGroup(
        includeTodo: Boolean = true,
        includeBulletList: Boolean = true,
        includeNumberList: Boolean = true,
        onTodoToggle: (RichTextToolBarItem) -> Unit = {},
        onBulletListToggle: (RichTextToolBarItem) -> Unit = {},
        onNumberListToggle: (RichTextToolBarItem) -> Unit = {}
    ): RichTextToolBarBuilder {
        if (includeTodo) {
            items.add(RichTextToolBarItem.ToggleButton(
                id = "todo_list",
                toolType = RichTextToolType.TODO_LIST,
                group = RichTextToolGroup.PARAGRAPH,
                onToggle = onTodoToggle
            ))
        }
        
        if (includeBulletList) {
            items.add(RichTextToolBarItem.ToggleButton(
                id = "bullet_list",
                toolType = RichTextToolType.BULLET_LIST,
                group = RichTextToolGroup.PARAGRAPH,
                onToggle = onBulletListToggle
            ))
        }
        
        if (includeNumberList) {
            items.add(RichTextToolBarItem.ToggleButton(
                id = "number_list",
                toolType = RichTextToolType.NUMBER_LIST,
                group = RichTextToolGroup.PARAGRAPH,
                onToggle = onNumberListToggle
            ))
        }
        
        return this
    }
    
    /**
     * 添加文本格式组
     */
    fun addTextFormatGroup(
        includeBold: Boolean = true,
        includeItalic: Boolean = true,
        includeUnderline: Boolean = true,
        includeStrikethrough: Boolean = false,
        onBoldToggle: (RichTextToolBarItem) -> Unit = {},
        onItalicToggle: (RichTextToolBarItem) -> Unit = {},
        onUnderlineToggle: (RichTextToolBarItem) -> Unit = {},
        onStrikethroughToggle: (RichTextToolBarItem) -> Unit = {}
    ): RichTextToolBarBuilder {
        if (includeBold) {
            items.add(RichTextToolBarItem.ToggleButton(
                id = "bold",
                toolType = RichTextToolType.BOLD,
                group = RichTextToolGroup.TEXT_FORMAT,
                onToggle = onBoldToggle
            ))
        }

        if (includeUnderline) {
            items.add(RichTextToolBarItem.ToggleButton(
                id = "underline",
                toolType = RichTextToolType.UNDERLINE,
                group = RichTextToolGroup.TEXT_FORMAT,
                onToggle = onUnderlineToggle
            ))
        }
        
        if (includeItalic) {
            items.add(RichTextToolBarItem.ToggleButton(
                id = "italic",
                toolType = RichTextToolType.ITALIC,
                group = RichTextToolGroup.TEXT_FORMAT,
                onToggle = onItalicToggle
            ))
        }
        
        if (includeStrikethrough) {
            items.add(RichTextToolBarItem.ToggleButton(
                id = "strikethrough",
                toolType = RichTextToolType.STRIKETHROUGH,
                group = RichTextToolGroup.TEXT_FORMAT,
                onToggle = onStrikethroughToggle
            ))
        }
        
        return this
    }
    
    /**
     * 添加颜色控制组
     */
    fun addColorGroup(
        includeTextColor: Boolean = true,
        includeTextBgColor: Boolean = true,
        onTextColorChange: (Color, RichTextToolBarItem) -> Unit = { _, _ -> },
        onTextBgColorChange: (Color, RichTextToolBarItem) -> Unit = { _, _ -> },
        onTextColorToggle: (RichTextToolBarItem) -> Unit = {},
        onTextBgColorToggle: (RichTextToolBarItem) -> Unit = {}
    ): RichTextToolBarBuilder {
        if (includeTextColor) {
            items.add(RichTextToolBarItem.ColorPicker(
                id = "text_color",
                toolType = RichTextToolType.TEXT_COLOR,
                group = RichTextToolGroup.COLOR,
                selectedColor = Color.Black,
                onColorChange = onTextColorChange,
                onToggleExpanded = onTextColorToggle
            ))
        }
        
        if (includeTextBgColor) {
            items.add(RichTextToolBarItem.ColorPicker(
                id = "text_bg_color",
                toolType = RichTextToolType.TEXT_BG_COLOR,
                group = RichTextToolGroup.COLOR,
                selectedColor = Color.Transparent,
                onColorChange = onTextBgColorChange,
                onToggleExpanded = onTextBgColorToggle
            ))
        }
        
        return this
    }
    
    /**
     * 添加字体大小选择器
     */
    fun addFontSizeSelector(
        fontSizes: List<Int> = listOf(12, 14, 16, 18, 20, 24, 28, 32),
        defaultSize: Int = 16,
        onSizeChange: (Int, RichTextToolBarItem) -> Unit = { _, _ -> },
        onToggleExpanded: (RichTextToolBarItem) -> Unit = {}
    ): RichTextToolBarBuilder {
        items.add(RichTextToolBarItem.DropdownSelector(
            id = "font_size",
            toolType = RichTextToolType.FONT_SIZE,
            group = RichTextToolGroup.FONT_SIZE,
            selectedValue = defaultSize,
            options = fontSizes,
            onSelectionChange = onSizeChange,
            onToggleExpanded = onToggleExpanded,
            valueToString = { "$it" }
        ))
        
        return this
    }
    
    /**
     * 添加对齐方式组
     */
    fun addAlignmentGroup(
        includeLeft: Boolean = true,
        includeCenter: Boolean = true,
        includeRight: Boolean = true,
        onAlignLeftClick: (RichTextToolBarItem) -> Unit = {},
        onAlignCenterClick: (RichTextToolBarItem) -> Unit = {},
        onAlignRightClick: (RichTextToolBarItem) -> Unit = {}
    ): RichTextToolBarBuilder {
        if (includeLeft) {
            items.add(RichTextToolBarItem.ToggleButton(
                id = "align_left",
                toolType = RichTextToolType.ALIGN_LEFT,
                group = RichTextToolGroup.ALIGNMENT,
                onToggle = onAlignLeftClick
            ))
        }
        
        if (includeCenter) {
            items.add(RichTextToolBarItem.ToggleButton(
                id = "align_center",
                toolType = RichTextToolType.ALIGN_CENTER,
                group = RichTextToolGroup.ALIGNMENT,
                onToggle = onAlignCenterClick
            ))
        }
        
        if (includeRight) {
            items.add(RichTextToolBarItem.ToggleButton(
                id = "align_right",
                toolType = RichTextToolType.ALIGN_RIGHT,
                group = RichTextToolGroup.ALIGNMENT,
                onToggle = onAlignRightClick
            ))
        }
        
        return this
    }
    
    /**
     * 添加缩进控制组
     */
    fun addIndentGroup(
        onDecreaseIndent: (RichTextToolBarItem) -> Unit = {},
        onIncreaseIndent: (RichTextToolBarItem) -> Unit = {}
    ): RichTextToolBarBuilder {
        items.add(RichTextToolBarItem.ActionButton(
            id = "indent_decrease",
            toolType = RichTextToolType.INDENT_DECREASE,
            group = RichTextToolGroup.INDENT,
            onAction = onDecreaseIndent
        ))
        
        items.add(RichTextToolBarItem.ActionButton(
            id = "indent_increase",
            toolType = RichTextToolType.INDENT_INCREASE,
            group = RichTextToolGroup.INDENT,
            onAction = onIncreaseIndent
        ))
        
        return this
    }
    
    /**
     * 添加分割线
     */
    fun addDivider(group: RichTextToolGroup): RichTextToolBarBuilder {
        items.add(RichTextToolBarItem.Divider(group = group))
        return this
    }
    
    /**
     * 添加自定义工具项
     */
    fun addCustomItem(item: RichTextToolBarItem): RichTextToolBarBuilder {
        items.add(item)
        return this
    }
    
    /**
     * 构建配置
     */
    fun build(): RichTextToolBarConfig {
        return RichTextToolBarConfig(items = items.toList())
    }
}

/**
 * 获取工具类型对应的图标资源
 */
fun RichTextToolType.getIconRes(): Int? {
    return when (this) {
        RichTextToolType.TODO_LIST -> HR.drawable.ic_richtext_menu_todo
        RichTextToolType.BULLET_LIST -> HR.drawable.ic_richtext_menu_bulleted
        RichTextToolType.NUMBER_LIST -> HR.drawable.ic_richtext_menu_numbered
        RichTextToolType.BOLD -> HR.drawable.ic_richtext_menu_bold
        RichTextToolType.ITALIC -> HR.drawable.ic_richtext_menu_italic
        RichTextToolType.UNDERLINE -> HR.drawable.ic_richtext_menu_underline
        RichTextToolType.STRIKETHROUGH -> HR.drawable.ic_richtext_menu_delete_line
        RichTextToolType.TEXT_COLOR -> HR.drawable.ic_richtext_menu_text_color
        RichTextToolType.TEXT_BG_COLOR -> HR.drawable.ic_richtext_menu_text_bg
        RichTextToolType.ALIGN_LEFT -> HR.drawable.ic_richtext_menu_align_left
        RichTextToolType.ALIGN_CENTER -> HR.drawable.ic_richtext_menu_align_center
        RichTextToolType.ALIGN_RIGHT -> HR.drawable.ic_richtext_menu_align_right
        RichTextToolType.INDENT_DECREASE -> HR.drawable.ic_richtext_menu_indent_left
        RichTextToolType.INDENT_INCREASE -> HR.drawable.ic_richtext_menu_indent_right
        RichTextToolType.FONT_SIZE -> null // 字体大小使用自定义UI
        RichTextToolType.DIVIDER -> null
    }
}

/**
 * 获取工具类型对应的描述资源
 */
fun RichTextToolType.getDescRes(): Int {
    return when (this) {
        RichTextToolType.TODO_LIST -> R.string.rich_text_todo
        RichTextToolType.BULLET_LIST -> R.string.rich_text_bullet_list
        RichTextToolType.NUMBER_LIST -> R.string.rich_text_number_list
        RichTextToolType.BOLD -> R.string.rich_text_bold
        RichTextToolType.ITALIC -> R.string.rich_text_italic
        RichTextToolType.UNDERLINE -> R.string.rich_text_underline
        RichTextToolType.STRIKETHROUGH -> R.string.rich_text_strikethrough
        RichTextToolType.TEXT_COLOR -> R.string.rich_text_text_color
        RichTextToolType.TEXT_BG_COLOR -> R.string.rich_text_text_bg_color
        RichTextToolType.ALIGN_LEFT -> R.string.rich_text_align_left
        RichTextToolType.ALIGN_CENTER -> R.string.rich_text_align_center
        RichTextToolType.ALIGN_RIGHT -> R.string.rich_text_align_right
        RichTextToolType.INDENT_DECREASE -> R.string.rich_text_indent_decrease
        RichTextToolType.INDENT_INCREASE -> R.string.rich_text_indent_increase
        RichTextToolType.FONT_SIZE -> R.string.rich_text_font_size
        RichTextToolType.DIVIDER -> 0
    }
}