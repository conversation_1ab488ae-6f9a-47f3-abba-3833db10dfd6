package com.tcl.ai.note.handwritingtext.database

import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.utils.CategoryColors
import com.tcl.ai.note.utils.runIO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine

/**
 * 临时创建，用于Category数据库创建的参考
 *
 */
object CategoryRepository {
    private val noteDao = NoteDatabase.getInstance(GlobalContext.instance).noteDao()
    private val categoryDao = NoteDatabase.getInstance(GlobalContext.instance).categoryDao()

    /**
     * 获取所有分类数据
     */
    suspend fun getAllCategories() = runIO {
        try {
            val categories = categoryDao.getAllCategories()
            categories.forEach { category ->
                category.noteCounts = calculateNoteCounts(category.categoryId)
                category.icon = determineIcon(category)
                category.name = determineName(category)
            }
            categories
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * 获取所有分类名称，同时监听分类和笔记总数量的变化
     * 返回 Pair<List<NoteCategory>, Int>
     */
    fun getAllCategoriesList(): Flow<Pair<List<NoteCategory>, Int>> {
        return combine(
            categoryDao.getAllCategoriesFlow(),
            noteDao.getNoteCountFlow() // 监听笔记表的变化
        ) { categories, allNoteCount ->
            // 当分类表或笔记表发生变化时，都会重新计算
            categories.map { category ->
                // 计算每个分类中的Note数量
                category.noteCounts = calculateNoteCounts(category.categoryId)
                // 确定分类icon
                category.icon = determineIcon(category)
                // 确定显示名称
                category.name = determineName(category)
                category
            } to allNoteCount
        }
    }

    // 计算每个分类中的Note数量
    suspend fun calculateNoteCounts(categoryId: Long): Int {
        // 这里是计算 noteCounts 的逻辑，例如从笔记表中统计笔记数量。
        return noteDao.getNotesCountByCategoryId(categoryId)
    }

    // 确定分类icon
    private fun determineIcon(category: NoteCategory): Int {
        // 这里是确定图标的逻辑，可以根据分类的某些属性或预置逻辑设置图标。
        return when (category.colorIndex) {
            CategoryColors.NONE_COLOR -> R.drawable.ic_category_uncategorised // 未分类
            CategoryColors.YELLOW_COLOR -> R.drawable.ic_category_yellow
            CategoryColors.ORANGE_COLOR -> R.drawable.ic_category_orange
            CategoryColors.PINK_COLOR -> R.drawable.ic_category_pink
            CategoryColors.PURPLE_COLOR -> R.drawable.ic_category_purple
            CategoryColors.BLUE_COLOR -> R.drawable.ic_category_blue
            CategoryColors.GREEN_COLOR -> R.drawable.ic_category_green
            else -> R.drawable.ic_all_notes
        }
    }

    // 重新设置显示名称
    private fun determineName(category: NoteCategory): String {
        return when {
            category.isRename -> category.name
            else -> when (category.categoryId) {
                1L -> GlobalContext.instance.getString(R.string.database_preset_category_none)
                2L -> GlobalContext.instance.getString(R.string.database_preset_category_education)
                3L -> GlobalContext.instance.getString(R.string.database_preset_category_work)
                4L -> GlobalContext.instance.getString(R.string.database_preset_category_travel)
                5L -> GlobalContext.instance.getString(R.string.database_preset_shopping_list)
                else -> category.name
            }
        }
    }


    /**
     * 新增category
     */
    suspend fun addCategory(category: NoteCategory): Long = runIO {
        categoryDao.insert(category)
    }

    /**
     * 更Category
     */
    suspend fun updateCategory(category: NoteCategory): Long = runIO {
        categoryDao.update(category)
        category.categoryId
    }

    /**
     * 获取一条Category
     */
    suspend fun getCategory(categoryId: Long): NoteCategory? = runIO {
        val category = categoryDao.getCategoryById(categoryId)?.apply {
            icon = determineIcon(this)
            name = determineName(this)
        }
        return@runIO category
    }

    /**
     * 删除一个Category
     */
    suspend fun deleteCategory(category: NoteCategory): Int = runIO {
        categoryDao.delete(category)
    }
}