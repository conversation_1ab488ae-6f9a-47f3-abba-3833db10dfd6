package com.tcl.ai.note.handwritingtext.database

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class DatabaseModule {

    @Provides
    fun provideNoteRoomDao(noteDatabase: NoteDatabase): NoteDao {
        return noteDatabase.noteDao()
    }

    @Provides
    fun provideContentRoomDao(noteDatabase: NoteDatabase): ContentDao {
        return noteDatabase.contentDao()
    }

    @Provides
    fun provideCategoryRoomDao(noteDatabase: NoteDatabase): CategoryDao {
        return noteDatabase.categoryDao()
    }

    @Provides
    @Singleton
    fun provideNoteDatabase(@ApplicationContext appContext: Context): NoteDatabase {
        return NoteDatabase.getInstance(appContext)
//        return Room.databaseBuilder(
//            appContext,
//            NoteDatabase::class.java,
//            DBConst.DB_NAME
//        ).build()
    }
}