package com.tcl.ai.note.handwritingtext.database.convertor

import android.net.Uri
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.util.fastForEach
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent.AudioBlock
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent.ImageBlock
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent.TextBlock
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent.TodoBlock
import com.tcl.ai.note.handwritingtext.database.entity.ImageBlockScaleMode
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle
import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity
import kotlinx.serialization.Serializable

@Serializable
sealed class NoteContentData {
    @Serializable
    data class Text(
        val text: String,
        val spans: List<ContentSpanStyle>,
        val paragraphStyle: ParagraphStyle
    ) : NoteContentData()

    @Serializable
    data class Image(
        val uri: String,
        val scaleMode: ImageBlockScaleMode,
    ) : NoteContentData()

    @Serializable
    data class Todo(
        val text: String,
        val spans: List<ContentSpanStyle>,
        val isDone: Boolean
    ) : NoteContentData()

    @Serializable
    data class Audio(
        val audioPath: String
    ) : NoteContentData()

    @Serializable
    data class RichTextV2(
        val entity: RichTextStyleEntity
    ) : NoteContentData()

    fun toEditorContent() = when (this) {
        is Text -> TextBlock(
            text = toTextField(spans, text),
            paragraphStyle = paragraphStyle,
        )

        is Todo -> TodoBlock(
            text = toTextField(spans, text),
            isDone = isDone,
        )

        is Audio -> AudioBlock(
            audioPath = audioPath
        )

        is Image -> ImageBlock(
            uri = Uri.parse(uri),
            scaleMode = scaleMode
        )

        is RichTextV2 -> EditorContent.RichTextV2(entity)
    }

    private fun Int.contains(other: TextDecoration) =
        (this or other.mask) == this

    private fun toTextField(spans: List<ContentSpanStyle>, text: String) = TextFieldValue(
        buildAnnotatedString {
            append(text)
            spans.fastForEach { span ->
                addStyle(
                    start = span.start,
                    end = span.end,
                    style = SpanStyle(
                        fontWeight = when (span.fontWeight) {
                            300 -> FontWeight.Light
                            400 -> FontWeight.Normal
                            500 -> FontWeight.Medium
                            700 -> FontWeight.Bold
                            else -> FontWeight.Normal
                        },
                        fontStyle = when (span.fontStyle) {
                            ContentFontStyle.ITALIC -> FontStyle.Italic
                            ContentFontStyle.NORMAL -> FontStyle.Normal
                        },
                        textDecoration = listOf(
                            TextDecoration.Underline,
                            TextDecoration.LineThrough
                        ).fold(TextDecoration.None) { acc, decoration ->
                            if (span.textDecorationMask.contains(decoration))
                                acc + decoration
                            else
                                acc
                        }
                    )
                )
            }
        }
    )
}

@Serializable
data class ContentSpanStyle(
    val start: Int,
    val end: Int,
    val fontWeight: Int,
    val fontStyle: ContentFontStyle,
    val textDecorationMask: Int,
)

// 斜体
enum class ContentFontStyle {
    NORMAL, ITALIC
}

fun EditorContent.toNoteContentData(): NoteContentData = when (this) {
    is TextBlock -> NoteContentData.Text(
        text = this.text.text,
        spans = text.annotatedString.spanStyles.map { span ->
            span.toContentSpanStyle()
        },
        paragraphStyle = this.paragraphStyle,
    )

    is ImageBlock -> NoteContentData.Image(
        uri = uri.toString(),
        scaleMode = scaleMode,
    )

    is TodoBlock -> NoteContentData.Todo(
        text = text.text,
        spans = text.annotatedString.spanStyles.map {
            it.toContentSpanStyle()
        },
        isDone = isDone
    )

    is AudioBlock -> NoteContentData.Audio(
        audioPath = audioPath
    )

    is EditorContent.RichTextV2 -> NoteContentData.RichTextV2(
        entity = richTextStyleEntity,
    )
}

private fun AnnotatedString.Range<SpanStyle>.toContentSpanStyle() =
    ContentSpanStyle(
        start = start,
        end = end,
        fontWeight = item.fontWeight?.weight ?: FontWeight.Normal.weight,
        fontStyle = when (item.fontStyle) {
            FontStyle.Italic -> ContentFontStyle.ITALIC
            else -> ContentFontStyle.NORMAL
        },
        textDecorationMask = item.textDecoration?.mask ?: TextDecoration.None.mask,
    )