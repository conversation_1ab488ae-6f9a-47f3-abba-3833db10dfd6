package com.tcl.ai.note.handwritingtext.intent

import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.database.entity.BgMode
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphType


/**
 * 配置界面意图
 */
sealed class RichTextIntent {

    /**
     * 获取初始化的分类数据
     */
    object GetCategories : RichTextIntent()

    /**
     * 加载回显Note数据
     */
    data class LoadNoteContents(val noteId: Long,val isLandscape:Boolean) : RichTextIntent()

    /**
     * 添加一个分类
     */
    data class AddCategory(val category: NoteCategory, val isPreviewMode: Boolean) : RichTextIntent()

    /**
     * 重命名一个分类
     */
    data class RenameCategory(val category: NoteCategory) : RichTextIntent()

    /**
     * 删除一条Note
     */
    data class DeleteOneNote(val noteId: Long) : RichTextIntent()

    /**
     * 更新单条指定 Note 的 categoryId
     */
    data class UpdateNoteCategoryId(val noteId: Long, val categoryId: Long) : RichTextIntent()

    /**
     * 更新标题
     */
    data class UpdateTitle(val title: String) : RichTextIntent()

    /**
     * 添加内容块
     */
    data class AddContent(val index: Int? = null, val content: EditorContent) : RichTextIntent()

    /**
     * 更新内容块
     */
    data class UpdateContent(val index: Int, val content: EditorContent) : RichTextIntent()

    /**
     * 删除内容块
     */
    data class RemoveContent(val index: Int) : RichTextIntent()

    /**
     * 更新文本样式
     */
    data class UpdateTextStyle(val style: TextStyle) : RichTextIntent()

    /**
     * 解析当前内容块样式
     */
    data class ParseCurrentStyles(val index: Int): RichTextIntent()

    /**
     * 更新文本样式
     */
    data class UpdateTextStyles(val index: Int, val spans: List<AnnotatedString.Range<SpanStyle>>) : RichTextIntent()

    /**
     * 更新段落样式
     */
    data class UpdateParagraphStyle(val style: ParagraphStyle) : RichTextIntent()

    /**
     * 切换段落样式（有序/无序列表）
     */
    data class ToggleParagraphStyle(val index: Int, val targetStyle: ParagraphStyle) : RichTextIntent()

    /**
     * 更新 BottomMenu 类型
     */
    data class UpdateMenuType(val type: MenuBar) : RichTextIntent()


    /**
     * 更新粗体高亮状态
     */
    data class UpdateBoldActive(val isBoldActive:Boolean) : RichTextIntent()

    /**
     * 更新斜体高亮状态
     */
    data class UpdateItalicActive(val isItalicActive:Boolean) : RichTextIntent()

    /**
     * 更新下划线高亮状态
     */
    data class UpdateUnderlineActive(val isUnderlineActive:Boolean) : RichTextIntent()


    /**
     * 更新画笔类型
     */
    data class UpdateBrushMenuType(val type: String) : RichTextIntent()

    /* 数据持久化 */
    /**
     * 请求保存
     */
    object RequestSave : RichTextIntent()

    /**
     * 执行保存
     */
    object PerformSave : RichTextIntent()

    /**
     * 更新焦点索引
     */
    data class UpdateFocusedIndex(val index: Int) : RichTextIntent()

    /**
     * 更新光标在文本块中的位置
     */
    data class UpdateCursorPosition(val position: Int,val isChange:Boolean=false) : RichTextIntent()

    /**
     * 更新光标在标题中的位置
     */
    data class UpdateTitleCursorPosition(val position: Int) : RichTextIntent()

    /**
     * 撤销操作
     */
    object Undo : RichTextIntent()

    /**
     * 重做操作
     */
    object Redo : RichTextIntent()

    /**
     * 清除撤销重做操作记录
     */
    object ResetHistory : RichTextIntent()

    /**
     * 更新标题焦点状态
     */
    data class UpdateTitleFocus(val hasFocus: Boolean) : RichTextIntent()

    /**
     * 更新编辑模式状态
     */
    data class UpdateEditMode(val enabled: Boolean) : RichTextIntent()

    /**
     * 更新保存状态
     */
    data class UpdateSaveState(val canSave: Boolean) : RichTextIntent()

    /**
     * 更新当前焦点数据类型
     */
    data class UpdateCurrentFocusedType(val editorContent: EditorContent?) : RichTextIntent()

    /**
     * 更新当前段落样式
     */
    data class UpdateCurrentParagraphStyle(val currentParagraphStyle: ParagraphStyle) : RichTextIntent()

    /**
     * 更新当前焦点数据类型
     */
    data class UpdateCurrentParagraphType(val currentParagraphType: ParagraphType) : RichTextIntent()

    /**
     * 批量替换内容
     */
    data class ReplaceAllContents(val contents: List<EditorContent>) : RichTextIntent()

    /**
     * 重置手写转文本内容
     */
    object ResetHandwritingToText : RichTextIntent()

    data class UpdateSkinStyle(val bgMode: BgMode,val color:Long) : RichTextIntent()

    data class UpdateAudioPath(val oldAudioPath: String, val newAudioPath: String) : RichTextIntent()
    data class DeleteAudio(val audioPath: String) : RichTextIntent()
}

