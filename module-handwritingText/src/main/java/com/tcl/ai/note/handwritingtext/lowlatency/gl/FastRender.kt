package com.tcl.ai.note.handwritingtext.lowlatency.gl

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PorterDuff
import android.view.MotionEvent
import android.view.SurfaceView
import android.view.View
import androidx.compose.ui.util.fastForEach
import androidx.graphics.lowlatency.CanvasFrontBufferedRenderer
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.handwritingtext.bean.*
import com.tcl.ai.note.handwritingtext.utils.EstimateInitUtils
import com.tcl.ai.note.handwritingtext.utils.forEachBezierPathSegmentWithWidth
import com.tcl.ai.note.handwritingtext.utils.toPathBezier
import com.tcl.ai.note.handwritingtext.vm.DrawBoardViewModel
import com.tcl.ai.note.handwritingtext.vo.DrawPathDisplayAndroid
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getScreenSize
import com.tcl.ai.note.utils.launchIO
import kotlin.math.max


internal class FastRender(
    private var drawBoardViewModel: DrawBoardViewModel,
    private val onNewDrawing: () -> Unit = {},
    private val onTouch: (motionEvent: MotionEvent) -> Unit = {},
) : CanvasFrontBufferedRenderer.Callback<Line> {
    private var isDrawing = false
    private var frontBufferRenderer: CanvasFrontBufferedRenderer<Line>? = null
    private val currentPoints = mutableListOf<DrawPoint>()
    private var lastPoint: DrawPoint? = null
    private val multiPathPoints = mutableListOf<DrawPathDisplayAndroid>()
    private var paint = drawBoardViewModel.strokeStyle.toPaint()
    private var strokeStyle = drawBoardViewModel.strokeStyle
    private val lastBitmap: Bitmap
    private var lastDownTime = System.currentTimeMillis()
    private val reusePath = Path()
    private var lastCenter: DrawPoint? = null
    private var currentLineTag: Long = 0L

    init {
        drawBoardViewModel.viewModelScope.launchIO {
            drawBoardViewModel.fastRenderIntentFlow.collect { intent ->
                when (intent) {
                    Intent.Cancel -> cancel()
                    Intent.Clear -> clear()
                    is Intent.ChangeStrokeStyle -> {
                        Logger.v(TAG, "change stroke style: $intent")
                        paint = intent.strokeStyle.copy(
                            width = intent.strokeStyle.width * drawBoardViewModel.scale,
                        ).toPaint()
                        strokeStyle = intent.strokeStyle
                    }
                }
            }
        }
        val screenSize = getScreenSize()
        val max = max(screenSize.width(), screenSize.height())
        lastBitmap = Bitmap.createBitmap(
            max,
            max,
            Bitmap.Config.ARGB_8888,
        )
    }

    private fun clear() {
        if (multiPathPoints.isNotEmpty()) {
            multiPathPoints.clear()
            frontBufferRenderer?.clear()
        }
    }

    private fun cancel() {
        drawBoardViewModel.updateEraserCirclePoint()
        clearBitmap(lastBitmap)
        frontBufferRenderer?.cancel()
    }

    fun isDrawing() = isDrawing

    fun attachSurfaceView(surfaceView: SurfaceView) {
        frontBufferRenderer = CanvasFrontBufferedRenderer(surfaceView, this)
        EstimateInitUtils.init(surfaceView.context)
    }

    fun release() {
        frontBufferRenderer?.clear()
        frontBufferRenderer?.release(true)
        EstimateInitUtils.release()
    }

    private var isCancelMotionEvent = false
    @SuppressLint("ClickableViewAccessibility")
    val onTouchListener = View.OnTouchListener { _, motionEvent ->
        onTouch.invoke(motionEvent)
        val pressure = motionEvent.pressure
        val x = motionEvent.x
        val y = motionEvent.y
        val point = DrawPoint(x, y, pressure, tooltype = motionEvent.getToolType(0))

        // 处理三方手写笔会让FastRender卡死的问题
        if (motionEvent.action == MotionEvent.ACTION_DOWN){
            isCancelMotionEvent = false
            if (point.pressureScale < 0.2F && point.tooltype == MotionEvent.TOOL_TYPE_STYLUS) {
                Logger.d(TAG, "The pressure is too low, cancel the event! --> pressure: $pressure")
                isCancelMotionEvent = true
            }
        }
        if (isCancelMotionEvent){
            return@OnTouchListener false
        }

        // 处理绘制时双指触摸，保存已绘制内容并切换至滚动
        if (motionEvent.action == MotionEvent.ACTION_CANCEL) {
            if (System.currentTimeMillis() - lastDownTime < SCROLL_TIMEOUT) {
                cancel()
            } else {
                lastPoint?.let {
                    handleCancel(it)
                } ?: cancel()
            }
            return@OnTouchListener true
        }
        if (drawBoardViewModel.panOnly
            && motionEvent.getToolType(0) != MotionEvent.TOOL_TYPE_STYLUS
        ) {
            // 非触控笔时清理并忽略
            clear()
            return@OnTouchListener false
        }
        when (motionEvent.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                isDrawing = true
                // 把之前的笔画清理了
                clear()
                lastDownTime = System.currentTimeMillis()
                lastCenter = null
                lastPoint = null
                currentLineTag = System.currentTimeMillis()
                handleActionDown(point)
                if (PATH_PREDICT_ENABLE) {
                    EstimateInitUtils.doPredict(motionEvent)
                }
            }

            MotionEvent.ACTION_MOVE -> {
                // 移动时先触发基本的绘制
                handleActionMove(point)
                if (PATH_PREDICT_ENABLE
                    && strokeStyle.doodlePen in predictPans
                ) {
                    // 然后触发预测的绘制
                    handlePredict(motionEvent, point, pressure)
                }
            }

            MotionEvent.ACTION_UP -> {
                lastCenter = null
                lastPoint = null
                handleActionUp(point)
                isDrawing = false
            }
        }
        true
    }

    private fun handlePredict(motionEvent: MotionEvent, point: DrawPoint, pressure: Float) {
        EstimateInitUtils.setMaxSpeed(PATH_PREDICT_MAX_SPEED)
        val pointBeans = EstimateInitUtils.doPredict(motionEvent)
        if (pointBeans != null) {
            val predictorX = pointBeans.x
            val predictorY = pointBeans.y
            val pointPredicted =
                DrawPoint(predictorX, predictorY, pressure, tooltype = motionEvent.getToolType(0))
            frontBufferRenderer?.renderFrontBufferedLayer(
                Line(
                    start = point,
                    end = pointPredicted,
                    isPredictor = true,
                    tag = currentLineTag,
                )
            )
        }
    }

    private fun handleActionDown(point: DrawPoint) {
        clearBitmap(lastBitmap)
        when (strokeStyle.drawMode) {
            DrawMode.ERASER -> {
                drawBoardViewModel.updateEraserCirclePoint(point)
                drawBoardViewModel.emitCanvasDrawEvent(
                    DrawMotionEvent.DOWN,
                    point
                )
            }

            DrawMode.BEAUTIFICATION, DrawMode.PEN -> {
                when (strokeStyle.doodlePen) {
                    DoodlePen.Markpen -> {
                        drawBoardViewModel.emitCanvasDrawEvent(
                            DrawMotionEvent.DOWN,
                            point
                        )
                    }

                    DoodlePen.Ballpen, DoodlePen.FountainPen -> {
                        currentPoints.clear()
                        currentPoints.add(point)
                        lastPoint = point
                        frontBufferRenderer?.renderFrontBufferedLayer(
                            Line(
                                start = point,
                                end = point,
                                isPredictor = false,
                                tag = currentLineTag,
                            )
                        )
                    }
                }
            }

            DrawMode.NONE -> {}
        }
    }

    private fun handleActionMove(point: DrawPoint) {
        when (strokeStyle.drawMode) {
            DrawMode.ERASER -> {
                drawBoardViewModel.updateEraserCirclePoint(point)
                drawBoardViewModel.emitCanvasDrawEvent(
                    DrawMotionEvent.MOVE,
                    point
                )
            }

            DrawMode.BEAUTIFICATION, DrawMode.PEN -> {
                when (strokeStyle.doodlePen) {
                    DoodlePen.Markpen -> {
                        drawBoardViewModel.emitCanvasDrawEvent(
                            DrawMotionEvent.MOVE,
                            point
                        )
                    }

                    DoodlePen.Ballpen, DoodlePen.FountainPen -> {
                        lastPoint?.let {
                            currentPoints.add(point)
                            frontBufferRenderer?.renderFrontBufferedLayer(
                                Line(
                                    start = it,
                                    end = point,
                                    isPredictor = false,
                                    tag = currentLineTag,
                                )
                            )
                        }
                        lastPoint = point
                    }
                }
            }

            DrawMode.NONE -> {}
        }
    }

    private fun handleActionUp(point: DrawPoint) {
        when (strokeStyle.drawMode) {
            DrawMode.BEAUTIFICATION, DrawMode.PEN -> {
                when (strokeStyle.doodlePen) {
                    DoodlePen.Markpen -> {
                        drawBoardViewModel.emitCanvasDrawEvent(
                            DrawMotionEvent.UP,
                            point
                        )
                    }

                    DoodlePen.Ballpen, DoodlePen.FountainPen -> {
                        drawBoardViewModel.addPaths(currentPoints, this.strokeStyle.copy())
                        currentPoints.add(point)
                        onNewDrawing.invoke()
                        multiPathPoints.clear()
                        multiPathPoints.add(
                            DrawPathDisplayAndroid(
                                path = currentPoints.toPathBezier(),
                                points = currentPoints.toList(),
                                style = strokeStyle.copy()
                            )
                        )
                        currentPoints.clear()
                        frontBufferRenderer?.commit()
                    }
                }
            }

            DrawMode.ERASER -> {
                drawBoardViewModel.updateEraserCirclePoint()
                drawBoardViewModel.emitCanvasDrawEvent(
                    DrawMotionEvent.UP,
                    point
                )
            }

            DrawMode.NONE -> {}
        }
    }

    private fun handleCancel(point: DrawPoint) {
        when (strokeStyle.drawMode) {
            DrawMode.BEAUTIFICATION, DrawMode.PEN -> {
                when (strokeStyle.doodlePen) {
                    DoodlePen.Markpen -> {
                        drawBoardViewModel.emitCanvasDrawEvent(
                            DrawMotionEvent.UP,
                            point
                        )
                    }

                    DoodlePen.Ballpen, DoodlePen.FountainPen -> {
                        drawBoardViewModel.addPaths(currentPoints, this.strokeStyle.copy())
                        currentPoints.add(point)
                        onNewDrawing.invoke()
                        multiPathPoints.clear()
                        currentPoints.clear()
                        frontBufferRenderer?.cancel()
                    }
                }
            }

            DrawMode.ERASER -> {
                drawBoardViewModel.updateEraserCirclePoint()
                drawBoardViewModel.emitCanvasDrawEvent(
                    DrawMotionEvent.UP,
                    point
                )
            }

            DrawMode.NONE -> {}
        }
    }

    override fun onDrawFrontBufferedLayer(
        canvas: Canvas,
        bufferWidth: Int,
        bufferHeight: Int,
        param: Line
    ) {
        if (currentLineTag != param.tag) {
            return
        }
        val start = param.start
        val end = param.end
        val pressure = (start.pressureScale + end.pressureScale) / 2
        val tmpPaint = when (strokeStyle.doodlePen) {
            DoodlePen.Ballpen, DoodlePen.Markpen -> {
                paint
            }

            DoodlePen.FountainPen -> {
                Paint().apply {
                    color = paint.color
                    strokeWidth = paint.strokeWidth * pressure
                    style = paint.style
                    strokeCap = paint.strokeCap
                    strokeJoin = paint.strokeJoin
                    isAntiAlias = true
                }
            }
        }
        val center = DrawPoint.centerOf(start, end)
        if (param.isPredictor) {
            clearCanvas(canvas)
            canvas.drawBitmap(lastBitmap, 0f, 0f, null)
            canvas.drawLine(start.x, start.y, end.x, end.y, tmpPaint)
        } else {
            reusePath.apply {
                rewind()
                lastCenter?.let {
                    moveTo(it.x, it.y)
                } ?: moveTo(start.x, start.y)
                // reusePath.quadTo(start.x, start.y, center.x, center.y)
                reusePath.lineTo(center.x, center.y)
            }
            lastCenter = center
            Canvas(lastBitmap).drawPath(reusePath, tmpPaint)
            clearCanvas(canvas)
            canvas.drawBitmap(lastBitmap, 0f, 0f, null)
            canvas.drawLine(center.x, center.y, end.x, end.y, tmpPaint)
        }
    }

    override fun onDrawMultiBufferedLayer(
        canvas: Canvas,
        bufferWidth: Int,
        bufferHeight: Int,
        params: Collection<Line>
    ) {
        // 绘制之前保存的所有线条
        multiPathPoints.fastForEach {
            when (it.style.doodlePen) {
                DoodlePen.Ballpen, DoodlePen.Markpen -> {
                    canvas.drawPath(
                        it.path,
                        it.style.toPaint(),
                    )
                }

                DoodlePen.FountainPen -> {
                    val tmpPaint = it.style.toPaint()
                    it.points.forEachBezierPathSegmentWithWidth(
                        path = it.path,
                        baseWidth = it.style.width,
                    ) { path, width ->
                        tmpPaint.strokeWidth = width
                        canvas.drawPath(path, tmpPaint)
                    }
                }
            }
        }
    }

    internal sealed class Intent {
        data object Cancel : Intent()
        data object Clear : Intent()
        data class ChangeStrokeStyle(val strokeStyle: StrokeStyle) : Intent()
    }

    companion object {
        private const val TAG = "FastRender"
        private const val PATH_PREDICT_ENABLE = true
        private const val PATH_PREDICT_MAX_SPEED = 1500
        private const val SCROLL_TIMEOUT = 300L

        private val predictPans = setOf(
            DoodlePen.FountainPen,
            DoodlePen.Ballpen,
        )

        private fun clearCanvas(canvas: Canvas) {
            canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.MULTIPLY)
        }

        private fun clearBitmap(bitmap: Bitmap) {
            Canvas(bitmap).drawColor(Color.TRANSPARENT, PorterDuff.Mode.MULTIPLY)
        }
    }
}

internal data class Line(
    val start: DrawPoint,
    val end: DrawPoint,
    val isPredictor: Boolean,
    val tag: Long,
)
