/*
* Copyright (c) 2022 Google LLC
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*      https://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/
package com.tcl.ai.note.handwritingtext.lowlatency.gl

import android.annotation.SuppressLint
import android.content.Context
import android.view.SurfaceHolder
import android.view.SurfaceView
import com.tcl.ai.note.utils.Logger

@SuppressLint("ViewConstructor")
internal class LowLatencySurfaceView(
    context: Context,
    private val fastRenderer: FastRender
) : SurfaceView(context) {

    init {
        setZOrderOnTop(true)
        holder.addCallback(object : SurfaceHolder.Callback {
            override fun surfaceCreated(holder: SurfaceHolder) {
                // Get the global position on the screen
                Logger.d("LowLatencySurfaceView", "surfaceCreated")
            }

            override fun surfaceChanged(
                holder: SurfaceHolder,
                format: Int,
                width: Int,
                height: Int
            ) {
                // Surface size changed, you can get the width and height here
//                fastRenderer.canvasSize = Offset(width.toFloat(), height.toFloat())
            }

            override fun surfaceDestroyed(holder: SurfaceHolder) {
                // Surface is destroyed
                Logger.d("LowLatencySurfaceView", "surfaceDestroyed")
            }
        })
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        Logger.d("LowLatencySurfaceView", "onAttachedToWindow")
        fastRenderer.attachSurfaceView(this)
    }

    override fun onDetachedFromWindow() {
        Logger.d("LowLatencySurfaceView", "onDetachedFromWindow")
        fastRenderer.release()
        super.onDetachedFromWindow()
    }
}