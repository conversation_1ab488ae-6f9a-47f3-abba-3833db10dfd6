package com.tcl.ai.note.handwritingtext.repo

import android.os.Environment
import android.view.MotionEvent
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.util.fastForEach
import com.sunia.penengine.sdk.operate.touch.PenType
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.bean.DoodlePen
import com.tcl.ai.note.handwritingtext.bean.DrawPoint
import com.tcl.ai.note.handwritingtext.bean.DrawStroke
import com.tcl.ai.note.handwritingtext.bean.EntDataType
import com.tcl.ai.note.handwritingtext.bean.EntPenType
import com.tcl.ai.note.handwritingtext.database.entity.Draw
import com.tcl.ai.note.handwritingtext.richtext.utils.DisplayUtils
import com.tcl.ai.note.handwritingtext.richtext.viewholder.RichTextViewHolder
import com.tcl.ai.note.handwritingtext.utils.RichTextLayoutUtils.horizontalPaddingPx
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.alpha
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.screenSizeMin
import com.tcl.ai.note.utils.toArgbInt
import com.tcl.ff.component.utils.common.GsonUtils
import kotlinx.coroutines.coroutineScope
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.DataInputStream
import java.io.DataOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import kotlin.math.min

object EntRepository {

    suspend fun readEntData(path: String) {
        File(path)
            .inputStream()
            .buffered()
            .use { fis ->
                DataInputStream(fis).use { dis ->
                    // 标记读取位置
                    var idx = 0

                    // FORMATE_HEARDER 校验区
                    var buffer = ByteArray(FORMATE_HEARDER_LEN)
                    dis.read(buffer).assert("FORMATE_HEARDER") {
                        it == FORMATE_HEARDER_LEN
                    }
                    idx += FORMATE_HEARDER_LEN
                    val forMate = String(buffer, Charsets.UTF_8).trim()

                    // HEARD_SIZE 头大小
                    val heardSize = dis.readInt().assert("HEARD_SIZE") {
                        it > INT_LEN
                    }
                    // 头大小包含头长本身，因此读取后续数据时截断
                    buffer = ByteArray(heardSize - INT_LEN)
                    dis.read(buffer).assert("HEARD") {
                        it == heardSize - INT_LEN
                    }
                    idx += heardSize
                    // 读取头部数据
                    var dataVersion = DATA_FORMAT_VERSION
                    var updateTime = System.currentTimeMillis()
                    DataInputStream(ByteArrayInputStream(buffer)).use { stream ->
                        dataVersion = stream.readInt()
                        updateTime = stream.readLong()
                    }

                    // PUBLIC_PRO_SIZE 公开属性区
                    val publicProSize = dis.readInt().assert("PUBLIC_PRO_SIZE") {
                        it >= INT_LEN
                    }
                    buffer = ByteArray(publicProSize - INT_LEN)
                    dis.read(buffer).assert("PUBLIC_PRO") {
                        it == publicProSize - INT_LEN
                    }
                    idx += publicProSize
                    var eachPointSize = 0
                    var itemCurveProSize = 0
                    var itemTableProSize = 0
                    var itemShapeProSize = 0
                    var itemVertexShapeProSize = 0
                    DataInputStream(ByteArrayInputStream(buffer)).use { stream ->
                        eachPointSize = stream.readInt()
                        itemCurveProSize = stream.readInt().assert("ITEM_CURV_PRO_SIZE") {
                            it >= 0
                        }
                        itemTableProSize = stream.readInt()
                        if (dataVersion >= DATA_FORMAT_VERSION_14) {
                            itemShapeProSize = stream.readInt()
                        }
                        if (dataVersion >= DATA_FORMAT_VERSION_15) {
                            itemVertexShapeProSize = stream.readInt()
                        }
                    }

                    // GROUP_HEARD_SIZE 分组信息
                    val groupHeardSize = dis.readInt()
                    buffer = ByteArray(groupHeardSize - INT_LEN)
                    dis.read(buffer).assert("GROUP_HEARD") {
                        it == groupHeardSize - INT_LEN
                    }
                    idx += groupHeardSize
                    var groupCount = 0
                    var groupIdx = 0
                    DataInputStream(ByteArrayInputStream(buffer)).use { stream ->
                        groupCount = stream.readInt()
                        groupIdx = stream.readInt()
                        if (groupIdx > idx) {
                            dis.skipBytes(groupIdx - idx)
                            idx = groupIdx
                        }
                    }

                    // ITEM_HEAD_SIZE
                    val temHeadSize = dis.readInt()
                    buffer = ByteArray(temHeadSize - INT_LEN)
                    dis.read(buffer).assert("ITEM_HEAD") {
                        it == temHeadSize - INT_LEN
                    }
                    idx += temHeadSize
                    DataInputStream(ByteArrayInputStream(buffer)).use { itemHeadStream ->
                        // 元素个数
                        val itemCount = itemHeadStream.readInt()
                        repeat(itemCount) { i ->
                            // 元素起始字节位位置
                            val itemIdx = itemHeadStream.readInt()
                            val id = i + 1
                            // 跳过至元素的起始字节位
                            if (itemIdx > idx) {
                                dis.skipBytes(itemIdx - idx)
                                idx = itemIdx
                            }
                            // 元素大小 ITEM_SIZE
                            val itemSize = dis.readInt()
                            idx += INT_LEN
                            // 元素类型，笔，图片，文本 ITEM_TYPE_INDEX
                            val type = dis.readShort()
                            idx += SHORT_LEN
                            // 子类型 ITEM_SUB_TYPE_INDEX
                            val subType = dis.readShort()
                            idx += SHORT_LEN
                            when {
                                type == EntDataType.CURVE.value -> {
                                    buffer = ByteArray(itemCurveProSize)
                                    // 整个笔迹属性区大小
                                    val curveProLen =
                                        dis.read(buffer).assert("ITEM_CURV_PRO_SIZE") {
                                            it == itemCurveProSize
                                        }
                                    // 跳过笔迹属性区，作为笔迹内容开头下标
                                    var curveIdx = idx + itemCurveProSize
                                    DataInputStream(ByteArrayInputStream(buffer)).use { itemProStream ->
                                        val subTypeIdx2First = itemProStream.readByte()
                                        val shape2 = itemProStream.readByte()
                                        val color = itemProStream.readInt()
                                        val penSize = itemProStream.readFloat()
                                        // 是否为表格数据的一部分 TABLE_FLAG
                                        var tableFlag: Short = 0
                                        if (itemProStream.available() >= SHORT_LEN
                                            && dataVersion >= DATA_FORMAT_VERSION_07
                                        ) {
                                            tableFlag = itemProStream.readShort()
                                        }
                                        if (dataVersion >= DATA_FORMAT_VERSION_08) {
                                            val rotateAngle = itemProStream.readFloat()
                                            val alpha = itemProStream.readInt()
                                        }
                                        if (dataVersion >= DATA_FORMAT_VERSION_09) {
                                            val curveId = itemProStream.readInt()
                                        }
                                        if (dataVersion >= DATA_FORMAT_VERSION_11) {
                                            val colorWheel = itemProStream.readInt()
                                        }
                                    }
                                }

                                else -> {}
                            }
                        }
                    }
                }
            }
    }

    suspend fun writeJsonData(draw: Draw, fileName: String) = coroutineScope {
        val docsDir = Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_DOCUMENTS
        ).apply {
            mkdirs()
        }
        FileOutputStream(File(docsDir, fileName)).buffered().use { fos ->
            DataOutputStream(fos).use { dos ->
                dos.writeChars(GsonUtils.toJson(draw))
            }
        }
    }

    suspend fun writeEntDataToExt(draw: Draw, fileName: String) = coroutineScope {
        val docsDir = Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_DOCUMENTS
        ).apply {
            mkdirs()
        }
        FileOutputStream(File(docsDir, fileName)).buffered()
            .use { fos ->
                val start = System.currentTimeMillis()
                DataOutputStream(fos).use { dos ->
                    // 写入头数据，至分组信息区
                    val idx = writeFileHead(dos)
                    val dataSize = draw.strokes.size
                    ByteArrayOutputStream().use { itemHeadByteStream ->
                        DataOutputStream(itemHeadByteStream).use { itemHeadStream ->
                            // 组元素信息，元素基础信息区
                            // ITEM_HEAD_SIZE
                            val itemHeadSize = 4 + 4 + dataSize * 4
                            itemHeadStream.writeInt(itemHeadSize)
                            // ITEM_COUNT 当前组的元素个数
                            itemHeadStream.writeInt(dataSize)

                            // 每个元素的起始下标
                            var itemIdx = idx + itemHeadSize
                            ByteArrayOutputStream().use { itemByteStream ->
                                DataOutputStream(itemByteStream).use { itemStream ->
                                    repeat(dataSize) { i ->
                                        val stroke = draw.strokes[i]
                                        // ITEM_INDEX_N 写入每个元素起始下标
                                        itemHeadStream.writeInt(itemIdx)
                                        // 笔画信息数据
                                        var itemByteArray: ByteArray
                                        ByteArrayOutputStream().use { itemByteStream ->
                                            DataOutputStream(itemByteStream).use { eachItemStream ->
                                                // 写笔画信息
                                                writeStroke(stroke, i, eachItemStream)
                                            }
                                            itemByteArray = itemByteStream.toByteArray()
                                        }
                                        val itemSize = itemByteArray.size + 4
                                        // ITEM_SIZE 写入元素大小，没有其他信息所以不减
                                        itemStream.writeInt(
                                            itemSize
                                        )
                                        // 写入剩余的笔画信息，从 ITEM_TYPE_INDEX 元素类型开始
                                        itemStream.write(itemByteArray)
                                        itemIdx += itemSize
                                    }
                                }
                                itemHeadByteStream.writeTo(dos)
                                itemByteStream.writeTo(dos)
                            }
                        }
                    }
                }
                Logger.v(
                    TAG,
                    "write ent data success, take: ${System.currentTimeMillis() - start}ms, file name: $fileName"
                )
            }
    }

    suspend fun writeEntData(draw: Draw, path: String, entHelpers: List<EntHelper>) =
        coroutineScope {
            val images = entHelpers.toEntImages()
            val file = File(path)
            if (file.parentFile?.exists() != true) {
                file.parentFile?.mkdirs()
            }
            FileOutputStream(file).buffered()
                .use { fos ->
                    val start = System.currentTimeMillis()
                    DataOutputStream(fos).use { dos ->
                        // 写入头数据，至分组信息区
                        val idx = writeFileHead(dos)
                        val strokeSize = draw.strokes.size
                        val imageSize = images.size
                        ByteArrayOutputStream().use { itemHeadByteStream ->
                            DataOutputStream(itemHeadByteStream).use { itemHeadStream ->
                                // 组元素信息，元素基础信息区
                                // ITEM_HEAD_SIZE
                                val itemHeadSize = 4 + 4 + (strokeSize + imageSize) * 4
                                itemHeadStream.writeInt(itemHeadSize)
                                // ITEM_COUNT 当前组的元素个数
                                itemHeadStream.writeInt(strokeSize + imageSize)

                                // 每个元素的起始下标
                                var itemIdx = idx + itemHeadSize
                                ByteArrayOutputStream().use { itemByteStream ->
                                    DataOutputStream(itemByteStream).use { itemStream ->
                                        repeat(strokeSize) { i ->
                                            val stroke = draw.strokes[i]
                                            // ITEM_INDEX_N 写入每个元素起始下标
                                            itemHeadStream.writeInt(itemIdx)
                                            // 笔画信息数据
                                            var itemByteArray: ByteArray
                                            ByteArrayOutputStream().use { itemByteStream ->
                                                DataOutputStream(itemByteStream).use { eachItemStream ->
                                                    // 写笔画信息
                                                    writeStroke(stroke, i, eachItemStream)
                                                }
                                                itemByteArray = itemByteStream.toByteArray()
                                            }
                                            val itemSize = itemByteArray.size + 4
                                            // ITEM_SIZE 写入元素大小，没有其他信息所以不减
                                            itemStream.writeInt(
                                                itemSize
                                            )
                                            // 写入剩余的笔画信息，从 ITEM_TYPE_INDEX 元素类型开始
                                            itemStream.write(itemByteArray)
                                            itemIdx += itemSize
                                        }
                                        repeat(imageSize) { i ->
                                            val image = images[i]
                                            // ITEM_INDEX_N 写入每个元素起始下标
                                            itemHeadStream.writeInt(itemIdx)
                                            // 图片信息数据
                                            var itemByteArray: ByteArray
                                            ByteArrayOutputStream().use { itemByteStream ->
                                                DataOutputStream(itemByteStream).use { eachItemStream ->
                                                    // 写图片信息
                                                    writeImage(image, eachItemStream)
                                                }
                                                itemByteArray = itemByteStream.toByteArray()
                                            }
                                            val itemSize = itemByteArray.size + 4
                                            // ITEM_SIZE 写入元素大小，没有其他信息所以不减
                                            itemStream.writeInt(
                                                itemSize
                                            )
                                            // 写入剩余的图片信息，从 ITEM_TYPE_INDEX 元素类型开始
                                            itemStream.write(itemByteArray)
                                            itemIdx += itemSize
                                        }
                                    }
                                    itemHeadByteStream.writeTo(dos)
                                    itemByteStream.writeTo(dos)
                                }
                            }
                        }
                    }
                    Logger.v(
                        TAG,
                        "write ent data success, take: ${System.currentTimeMillis() - start}ms, file name: $path"
                    )
                }
        }

    /**
     * 写入文件头，校验区、文件头、公开属性、分组信息
     * @return 头长，即下一个数据写入下标
     */
    private suspend fun writeFileHead(dos: DataOutputStream) = coroutineScope {
        // FORMATE_HEADER
        val formatSize = 12
        val bytes = ByteArray(formatSize)
        val buffer = FORMAT_HEADER.toByteArray()
        System.arraycopy(buffer, 0, bytes, 0, min(buffer.size, bytes.size))
        dos.write(bytes)

        // HEAD_SIZE
        val headSize = 4 + 4 + 4 + 4 + 8
        dos.writeInt(headSize)

        // DATA_VERSION
        dos.writeInt(DATA_FORMAT_VERSION)

        // MODIFY_TIME 最后修改时间
        dos.writeLong(System.currentTimeMillis())

        // WIDTH HEIGHT 宽高，临时值
        dos.writeInt(1000)
        dos.writeInt(1600)

        // PUBLIC_PRO_SIZ 公共属性区，用于存储一些公共属性
        val publicSize = 16 + 4 + 4
        dos.writeInt(publicSize)
        // EACH_POINT_SIZE 每个笔迹点统一的数据大小，以字节为单位
        dos.writeInt(EACH_POINT_SIZE)
        // ITEM_CURV_PRO_SIZE 线属性占用大小
        dos.writeInt(ITEM_PRO_SIZE)
        // ITEM_TABLE_PRO_SIZE 表格中线属性占用大小
        dos.writeInt(ITEM_TABLE_SIZE)
        // ITEM_SHAPE_SIZE 图形属性占用大小
        dos.writeInt(ITEM_RECT_SHAPE_SIZE)
        // ITEM_SHAPE_SIZE 图形属性占用大小
        dos.writeInt(ITEM_VERTEX_SHAPE_SIZE)


        // 写入分组信息，暂未使用
        val groupCount = 1
        val groupHeadSize = 4 + 4 + groupCount * 4
        // GROUP_HEARD_SIZE 元素分组头定义数据大小，以字节为单位
        dos.writeInt(groupHeadSize)
        // GROUP_COUNT 元素分组头定义数据大小，以字节为单位 目前只有一个组
        dos.writeInt(1)
        // GROUP_INDEX 对应的每个组的起始位置，以字节为单位
        val index = formatSize + headSize + publicSize + groupHeadSize
        dos.writeInt(index)
        index
    }

    /**
     * 写入笔画信息，从 ITEM_TYPE_INDEX 元素类型开始
     */
    private suspend fun writeStroke(stroke: DrawStroke, idx: Int, dos: DataOutputStream) =
        coroutineScope {
            val type = EntDataType.CURVE.value
            // ITEM_TYPE_INDEX
            dos.writeShort(type.toInt())
            val subType = when (stroke.style.doodlePen) {
                DoodlePen.Ballpen -> EntPenType.BALL.value
                DoodlePen.FountainPen -> EntPenType.FOUNTAIN.value
                DoodlePen.Markpen -> EntPenType.MARK.value
            }
            // ITEM_SUB_TYPE_INDEX
            dos.writeShort(subType.toInt())
            // ITEM_SUB_TYPE_INDEX_FIRST
            dos.writeByte(
                if (stroke.style.doodlePen == DoodlePen.Markpen) 1
                else 0
            )
            // ITEM_SUB_TYPE_INDEX_END
            dos.writeByte(0)
            // ITEM_COLOR_INDEX
            dos.writeInt(stroke.style.color.toArgbInt())
            // STROK_WIDTH 如果是触控笔类型，需要乘系数
            dos.writeFloat(
                stroke.style.width * if (subType == EntPenType.FOUNTAIN.value
                    && stroke.points.getOrNull(0)?.tooltype == MotionEvent.TOOL_TYPE_STYLUS
                ) DrawPoint.PRESSURE_SCALE
                else 1f
            )
            // TABLE_FLAG
            dos.writeShort(0)
            // ROTATE_ANGLE
            dos.writeFloat(0f)
            // ALPHA 存的0-255
            dos.writeInt(stroke.style.color.alpha())
            // ID
            dos.writeInt(idx + 1)
            // COLOR_WHEEL 暂时不用
            dos.writeInt(stroke.style.color.toArgbInt())
            // SENSITIVITY
            dos.writeFloat(0.5f)
            // TIPSIZE
            dos.writeFloat(0f)
            // ITEM_POSITION_INDEX
            dos.writeInt(1)
            // SHAPE_FLAG
            dos.writeShort(0)
            stroke.points.fastForEach { point ->
                // X
                dos.writeFloat(point.x)
                // Y
                dos.writeFloat(point.y)
                // P
                dos.writeFloat(point.pressureOrigin)
                // A
                dos.writeFloat(0f)
                // TIME_STAMP
                dos.writeLong(point.timestamp)
                // O
                dos.writeFloat(0f)
                // V
                dos.writeFloat(0f)
            }
        }

    /**
     * 写入图片信息，从 ITEM_TYPE_INDEX 元素类型开始
     */
    private suspend fun writeImage(image: EntImage, dos: DataOutputStream) =
        coroutineScope {
            val type = EntDataType.IMAGE.value
            // ITEM_TYPE_INDEX
            dos.writeShort(type.toInt())
            dos.writeShort(PenType.PEN_INK.value)
            // LEFT
            dos.writeFloat(image.left)
            // TOP
            dos.writeFloat(image.top)
            // WIDTH
            dos.writeFloat(image.width)
            // HEIGHT
            dos.writeFloat(image.height)
            val pathByteArray = image.path.toByteArray()
            // PATH_LENGTH
            dos.writeInt(4 + pathByteArray.size)
            // PATH_BUFFER
            dos.write(pathByteArray)
            // ANGLE
            dos.writeFloat(0f)
            // sunia历史遗留错误字段，保留
            dos.writeInt(4 + 0)
        }

    private fun Int.assert(fieldName: String, predicate: (Int) -> Boolean) = this.also {
        if (!predicate.invoke(it)) {
            throw IOException("Invalid $fieldName value: $it")
        }
    }

    private const val TAG = "EntRepository"
    private const val DATA_FORMAT_VERSION_07 = 7
    private const val DATA_FORMAT_VERSION_08 = 8
    private const val DATA_FORMAT_VERSION_09 = 9
    private const val DATA_FORMAT_VERSION_11 = 11
    private const val DATA_FORMAT_VERSION_14 = 14
    private const val DATA_FORMAT_VERSION_15 = 15
    private const val DATA_FORMAT_VERSION = DATA_FORMAT_VERSION_15
    private const val FORMATE_HEARDER_LEN = 12
    private const val INT_LEN = 4
    private const val SHORT_LEN = 2
    private const val FORMAT_HEADER = "HWPENKITDATA"
    private const val EACH_POINT_SIZE = (4 * 4 // x,y,p,t
            + 8 // time
            + 4 // 0
            + 4) // v
    private const val ITEM_PRO_SIZE: Int = (2 // item_sub_type_index_2
            + 4 * 2 // 笔宽和颜色索引
            + 2 // table_flag 元素子分类
            + 4 // 增加旋转角度float 版本8
            + 4 // 增加透明度int  版本8
            + 4 // 增加id,int类型 版本9
            + 4 // 增加万色盘颜色,int类型 版本11
            + 8 // 增加压感灵敏度、笔尖大小
            + 4 // 增加置底置顶 版本13
            + 2) // shape_flag 元素子分类
    private const val ITEM_TABLE_SIZE: Int = 4 + 2
    private const val ITEM_RECT_SHAPE_SIZE = (2 // 图形和 图形使用虚线或者实线
            + 4 * 3 // 填充颜色索引，透明度，万色盘颜色
            + 6 * 4) // 上,下,左,右,X,Y
    private const val ITEM_VERTEX_SHAPE_SIZE: Int = (2 // 图形和 图形使用虚线或者实线
            + 4 * 3 + 2) // 填充颜色索引，透明度，万色盘颜色 顶点个数
    private var cacheScreenMax = 0
}

fun Long.idToEntPath(): String =
    GlobalContext.instance.filesDir.path + "/ent/note_${this}.ent"

sealed class EntHelper {
    data class Text(
        val height: Float,
    ) : EntHelper()

    data class Image(
        val size: IntSize,
        val path: String,
        val height: Float,
    ) : EntHelper()
}

private data class EntImage(
    val path: String,
    val left: Float,
    val top: Float,
    val width: Float,
    val height: Float,
)

private fun List<EntHelper>.toEntImages(): List<EntImage> {
    var startHeight = DisplayUtils.dp2px(
        isTablet.judge(
            RichTextViewHolder.PADDING_VERTICAL_TABLET_DP,
            RichTextViewHolder.PADDING_VERTICAL_PHONE_DP
        )
    ) * entScale
    val entImages = mutableListOf<EntImage>()
    this.fastForEach {
        when (it) {
            is EntHelper.Text -> {
                startHeight += it.height * entScale
            }

            is EntHelper.Image -> {
                entImages.add(
                    EntImage(
                        path = it.path,
                        left = horizontalPaddingPx.toFloat() * entScale,
                        top = startHeight,
                        width = it.size.width.toFloat() * entScale,
                        height = it.size.height.toFloat() * entScale,
                    )
                )
                startHeight += it.height * entScale
            }
        }
    }
    return entImages
}

val entScale = 1000f / screenSizeMin.toFloat()