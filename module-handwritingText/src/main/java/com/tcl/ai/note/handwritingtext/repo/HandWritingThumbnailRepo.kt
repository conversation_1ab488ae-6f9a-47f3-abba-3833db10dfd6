package com.tcl.ai.note.handwritingtext.repo

import android.graphics.Bitmap
import android.graphics.Bitmap.Config
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Point
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.util.fastForEach
import androidx.core.net.toUri
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.bean.DrawPoint
import com.tcl.ai.note.handwritingtext.bean.DrawStroke
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import kotlin.math.max
import kotlin.math.min

/**
 * 获取手绘图的缩略图
 *
 */
object HandWritingThumbnailRepo {
    private const val TAG = "HandWritingThumbnailRepo"

    private fun getFilePath(nodeId: Long, isDark: Boolean = false): String {
        val filesDir = GlobalContext.instance.filesDir
        val saveDir = File(filesDir.absolutePath + "/HandWritingThumbnails/")
        if (!saveDir.exists()) {
            saveDir.mkdir()
        }
        val darkSuffix = if (isDark) "_dark" else ""
        val fileName = "handwriting_thumbnails_noteId_$nodeId$darkSuffix.png"
        return saveDir.absolutePath + "/$fileName"
    }


    suspend fun saveBitmap(nodeId: Long, bitmap: Bitmap, isDark: Boolean = false) = withContext(Dispatchers.IO) {
        try {
            val file = File(getFilePath(nodeId, isDark))
            val outputStream = FileOutputStream(file)
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
            outputStream.flush()
            outputStream.close()
            Logger.d(TAG, "saveBitmap, nodeId: $nodeId, isDark: $isDark, bitmap: $bitmap")
        } catch (e: Exception) {
            Logger.w(TAG, "getIconFromNet failed: ${e.stackTraceToString()}")
        }
    }

    suspend fun deleteHandwritingThumbnail(nodeId: Long) = withContext(Dispatchers.IO) {
        try {
            val file = File(getFilePath(nodeId))
            if (file.exists()) {
                file.delete()
            }
            val fileDark = File(getFilePath(nodeId, true))
            if (fileDark.exists()) {
                fileDark.delete()
            }
        } catch (e: Exception) {
            Logger.w(TAG, "getIconFromNet failed: ${e.stackTraceToString()}")
        }
    }

    fun getBitmapPath(noteId: Long, isDark: Boolean = false): String? {
        val path = getFilePath(noteId, isDark)
        val file = File(path)
        if (file.exists()) {
            return file.toUri().toString()
        }
        return null
    }

    fun getBitmapFromStorage(noteId: Long): Bitmap? {
        val path = getFilePath(noteId)
        var bitmap: Bitmap? = null
        try {
            val file = File(path)
            if (file.exists()) {
                val inputStream = FileInputStream(file)
                bitmap = BitmapFactory.decodeStream(inputStream)
                inputStream.close()
            }
        } catch (e: Exception) {
            Logger.w(
                TAG,
                "getBitmapFromStorage，noteId: $noteId, exception: ${e.stackTraceToString()}"
            )
        }
        return bitmap
    }

    /**
     * 根据数据库中手写笔画生成手绘图, 并截取首页缩略图
     */
    suspend fun saveRealTimeHandwritingThumbnail(noteId: Long) = withContext(Dispatchers.IO) {
        // 获取手绘笔画
        val drawStrokeList = DrawBoardRepository.getDrawByNoteIdBlock(noteId)?.strokes
        if (drawStrokeList.isNullOrEmpty()) {
            deleteHandwritingThumbnail(noteId)
            return@withContext
        }

        // 计算手绘的起始点和尺寸大小
        var leftTopX = drawStrokeList.getOrNull(0)?.points?.getOrNull(0)?.x?.toInt() ?: 0
        var leftTopY = drawStrokeList.getOrNull(0)?.points?.getOrNull(0)?.y?.toInt() ?: 0
        var width = 0
        var height = 0
        drawStrokeList.fastForEach {
            leftTopX = min(leftTopX, it.points.minByOrNull { it.x }?.x?.minus(it.style.width)?.toInt() ?: 0)
            leftTopY = min(leftTopY, it.points.minByOrNull { it.y }?.y?.minus(it.style.width)?.toInt() ?: 0)
            width = max(width, it.points.maxByOrNull { it.x }?.x?.plus(it.style.width)?.toInt() ?: 0)
            height = max(height, it.points.maxByOrNull { it.y }?.y?.plus(it.style.width)?.toInt() ?: 0)
        }
        val leftTopPoint = Point(leftTopX, leftTopY)
        val handwritingSize = IntSize(width - leftTopX, height - leftTopY)

        Logger.d("LowLatencyDrawBoard", "saveHandWritingThumbnail ${leftTopPoint}, ${handwritingSize}")

        // 取最长边，但是最长不能超过屏幕最短边,最短不能小于50
        val screenShortSide = min(GlobalContext.screenWidth, GlobalContext.screenHeight)
        val bitmapSize = max(handwritingSize.width, handwritingSize.height)
                .coerceAtLeast(if(isTablet) 50 else screenShortSide)
                .coerceAtMost(screenShortSide)

        val hwBitmap = Bitmap.createBitmap(bitmapSize, bitmapSize, Config.ARGB_8888)
        val hwBitmapCanvas = Canvas(hwBitmap)
        val newDrawStrokeList = ArrayList<DrawStroke>(drawStrokeList.size)
        drawStrokeList.forEach {
            val newPoints = ArrayList<DrawPoint>(it.points.size)
            it.points.forEach {
                if(isTablet) {
                    newPoints.add(DrawPoint(it.x - leftTopPoint.x, it.y - leftTopPoint.y))
                } else {
                    newPoints.add(DrawPoint(it.x, it.y))
                }
            }
            newDrawStrokeList.add(it.copy(points = newPoints))
        }
        newDrawStrokeList.forEach { it.toDrawPathDisplay().draw(hwBitmapCanvas) }
        // 保存缩略图
        saveBitmap(noteId, hwBitmap)
    }

}