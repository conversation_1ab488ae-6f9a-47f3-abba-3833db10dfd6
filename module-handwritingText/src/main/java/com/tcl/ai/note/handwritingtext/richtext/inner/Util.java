package com.tcl.ai.note.handwritingtext.richtext.inner;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ContentUris;
import android.content.Context;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.PixelFormat;
import android.graphics.Point;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.DocumentsContract;
import android.provider.MediaStore;
import android.text.Editable;
import android.text.Layout;
import android.text.Selection;
import android.text.Spannable;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.StringBuilderPrinter;
import android.view.Display;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.Toast;

import com.tcl.ai.note.handwritingtext.richtext.spans.AreListSpan;
import com.tcl.ai.note.utils.Logger;

import java.util.ArrayList;
import java.util.Collections;

/**
 * All Rights Reserved.
 *
 * <AUTHOR> Liu
 */
public class Util {

    /**
     * Toast message.
     */
    public static void toast(Context context, String msg) {
        Toast.makeText(context, msg, Toast.LENGTH_LONG).show();
    }

    /**
     * @param s
     */
    public static void log(String s) {
        Logger.d(s);
    }

    /**
     * Returns the line number of current cursor.
     *
     * @param editText
     * @return
     */
    public static int getCurrentCursorLine(EditText editText) {
        int selectionStart = Selection.getSelectionStart(editText.getText());
        Layout layout = editText.getLayout();

        if (null == layout) {
            return -1;
        }
        if (selectionStart != -1) {
            return layout.getLineForOffset(selectionStart);
        }

        return -1;
    }

    public static int getLineBySelection(EditText editText, int selection) {
        Layout layout = editText.getLayout();
        if (layout == null) {
            return -1;
        }
        return layout.getLineForOffset(selection);
    }

    /**
     * 处理不带序号的Span
     * 如果全部段落都选中，那么就全部取消
     * 如果有段落未选中，那么所有的段落都加上
     */
    public static <E extends AreListSpan> boolean makeOrUnMarkMultiLineListSpanWithNoNum(Editable editable,
                                                                                         int selectionStart, int selectionEnd, Class<E> spanClazz) {
        int start = TextUtils.lastIndexOf(editable, '\n', selectionStart) + 1;
        int end = TextUtils.indexOf(editable, '\n', selectionEnd);
        if (end == -1) {
            end = editable.length() - 1;
        }
        ArrayList<E> existedSpanList = new ArrayList<>();
        boolean setNewSpan = false;
        while (start <= end) {
            E span;
            try {
                span = spanClazz.newInstance();
            } catch (Exception e) { throw new RuntimeException(e); }
            int nextStart;
            if ((nextStart = TextUtils.indexOf(editable, '\n', start)) != -1) {
                E[] currentLineSpans = editable.getSpans(start, nextStart, spanClazz);
                if (currentLineSpans == null || currentLineSpans.length == 0) {
                    if (start == nextStart) {//单独的换行符插入一个零宽字符占位，保证能保存到HTML里面
                        editable.insert(nextStart++, Constants.ZERO_WIDTH_SPACE_STR);
                        end++;
                    }
                    editable.setSpan(span, start, nextStart, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                    setNewSpan = true;
                } else {
                    Collections.addAll(existedSpanList, currentLineSpans);
                }
            } else {//reach end
                E[] existSpans = editable.getSpans(start, end, spanClazz);
                if (existSpans == null || existSpans.length == 0) {
                    editable.setSpan(span, start, editable.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                    setNewSpan = true;
                } else {
                    Collections.addAll(existedSpanList, existSpans);
                }
                break;
            }
            start = nextStart + 1;
        }
        if (!setNewSpan) {
            for (E span : existedSpanList) {
                editable.removeSpan(span);
            }
        }
        return setNewSpan;
    }

    /**
     * Returns the selected area line numbers.
     *
     * @param editText
     * @return
     */
    public static int[] getCurrentSelectionLines(EditText editText) {
        Editable editable = editText.getText();
        int selectionStart = Selection.getSelectionStart(editable);
        int selectionEnd = Selection.getSelectionEnd(editable);
        Layout layout = editText.getLayout();

        int[] lines = new int[2];
        if (selectionStart != -1) {
            int startLine = layout.getLineForOffset(selectionStart);
            lines[0] = startLine;
        }

        if (selectionEnd != -1) {
            int endLine = layout.getLineForOffset(selectionEnd);
            lines[1] = endLine;
        }

        return lines;
    }

    /**
     * Returns the line start position of the current line (which cursor is focusing now).
     *
     * @param editText
     * @return
     */
    public static int getThisLineStart(EditText editText, int currentLine) {
        Layout layout = editText.getLayout();
        int start = 0;
        if (currentLine > 0) {
            start = layout.getLineStart(currentLine);
            if (start > 0) {
                Editable text = editText.getText();
                char lastChar = text.charAt(start - 1);
                while (lastChar != '\n') {
                    if (currentLine > 0) {
                        currentLine--;
                        start = layout.getLineStart(currentLine);
                        if (start > 1) {
                            start--;
                            lastChar = text.charAt(start);
                            if (lastChar == '\n') start++;
                        } else {
                            break;
                        }
                    }
                }
            }
        }
        return start;
    }

    /**
     * Returns the line end position of the current line (which cursor is focusing now).
     *
     * @param editText
     * @return
     */
    public static int getThisLineEnd(EditText editText, int currentLine) {
        Layout layout = editText.getLayout();
        if (-1 != currentLine) {
//            return layout.getLineEnd(currentLine);
            int lineEnd = layout.getLineStart(currentLine);
            int newLineEnd = TextUtils.indexOf(editText.getText(), '\n', lineEnd);
            return newLineEnd == -1 ? editText.length() : newLineEnd;
        }
        return -1;
    }

    /**
     * Gets the pixels by the given number of dp.
     *
     * @param context
     * @param dp
     * @return
     */
    public static int getPixelByDp(Context context, int dp) {
        int pixels = dp;
        DisplayMetrics displayMetrics = new DisplayMetrics();
        ((Activity) context).getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        pixels = (int) (displayMetrics.density * dp + 0.5);
        return pixels;
    }


    /**
     * Returns the color in string format.
     *
     * @param intColor
     * @param containsAlphaChannel
     * @param removeAlphaFromResult
     * @return
     */
    public static String colorToString(int intColor, boolean containsAlphaChannel, boolean removeAlphaFromResult) {
        String strColor = String.format("#%06X", 0xFFFFFF & intColor);
        if (containsAlphaChannel) {
            strColor = String.format("#%06X", 0xFFFFFFFF & intColor);
            if (removeAlphaFromResult) {
                StringBuffer buffer = new StringBuffer(strColor);
                buffer.delete(1, 3);
                strColor = buffer.toString();
            }
        }

        return strColor;
    }

    public static Bitmap scaleBitmapToFitWidth(Bitmap bitmap, int maxWidth) {
        int w = bitmap.getWidth();
        int h = bitmap.getHeight();
        int newWidth = maxWidth;
        int newHeight = maxWidth * h / w;
        Matrix matrix = new Matrix();
        float scaleWidth = ((float) newWidth / w);
        float scaleHeight = ((float) newHeight / h);
        if (w < maxWidth * 0.2) {
            return bitmap;
        }
        matrix.postScale(scaleWidth, scaleHeight);
        return Bitmap.createBitmap(bitmap, 0, 0, w, h, matrix, true);
    }

    public static Bitmap mergeBitmaps(Bitmap background, Bitmap foreground) {
        if( background == null ) {
            return null;
        }

        int bgWidth = background.getWidth();
        int bgHeight = background.getHeight();

        //create the new blank bitmap
        Bitmap newBitmap = Bitmap.createBitmap(bgWidth, bgHeight, Bitmap.Config.ARGB_8888);
        Canvas cv = new Canvas(newBitmap);
        //draw bg into
        cv.drawBitmap(background, 0, 0, null);

        int fgWidth = foreground.getWidth();
        int fgHeight = foreground.getHeight();
        int fgLeft = (bgWidth - fgWidth) / 2;
        int fgTop = (bgHeight - fgHeight) / 2;

        //draw fg into
        cv.drawBitmap(foreground, fgLeft, fgTop, null);
        //save all clip
        cv.save();
        //store
        cv.restore();
        return newBitmap;
    }

    public static void hideKeyboard(View view, Context context) {
        if (view != null && context != null) {
            InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
            }
        }
    }

    public static void showKeyboard(View view, Context context) {
        if (view != null && context != null) {
            InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
            }
        }
    }

    public static Bitmap drawableToBitmap(Drawable drawable) {
        int w = drawable.getBounds().width();
        int h = drawable.getBounds().height();
        Bitmap bitmap = Bitmap.createBitmap(
                w,
                h,
                drawable.getOpacity() != PixelFormat.OPAQUE ? Bitmap.Config.ARGB_8888
                        : Bitmap.Config.RGB_565);

        Canvas canvas = new Canvas(bitmap);
        //canvas.setBitmap(bitmap);
        drawable.setBounds(0, 0, w, h);
        drawable.draw(canvas);
        return bitmap;
    }

//    public static void updateCheckStatus(IARE_Style areStyle, boolean checked) {
//        areStyle.setChecked(checked);
//        View imageView = areStyle.getImageView();
////        int color = checked ? Constants.CHECKED_COLOR : Constants.UNCHECKED_COLOR;
////        imageView.setBackgroundColor(color);
//        imageView.setEnabled(checked);
//    }

    public static void logSpan(Spanned spanned) {
        if (true) {
            StringBuilder builder = new StringBuilder();
            StringBuilderPrinter printer = new StringBuilderPrinter(builder);
            TextUtils.dumpSpans(spanned, printer, "");
            char[] tmp = spanned.toString().toCharArray();
            for (int i=0; i< tmp.length;i++){
                char c = tmp[i];
                Log.i("span", " index=" + i + " detail: " + c + " int=" + (int)c);
            }
            Log.i("span", " length=" + spanned.length() + " detail: " + builder.toString());
        }
    }

}
