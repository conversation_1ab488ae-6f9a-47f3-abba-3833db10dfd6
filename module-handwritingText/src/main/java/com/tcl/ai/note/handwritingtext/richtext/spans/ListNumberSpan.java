package com.tcl.ai.note.handwritingtext.richtext.spans;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.text.Layout;
import android.text.Spanned;

import com.tcl.ai.note.handwritingtext.richtext.utils.DisplayUtils;


public class ListNumberSpan implements AreListSpan {
    private static final String TAG = "ListNumberSpan";
    private int mNumber;

    public ListNumberSpan(int number) {
        mNumber = number;
    }

    public void setNumber(int number) {
        this.mNumber = number;
    }

    public int getNumber() {
        return this.mNumber;
    }

    public int getLeadingMargin(boolean first) {
        if (mNumber > 99) {
            return DisplayUtils.dp2px(30);
        } else {
            return DisplayUtils.dp2px(28);
        }
    }


    @Override
    public void drawLeadingMargin(Canvas c, Paint p, int x, int dir, int top,
                                  int baseline, int bottom, CharSequence text, int start, int end,
                                  boolean first, Layout l) {
        if (((Spanned) text).getSpanStart(this) == start) {
            Paint.Style style = p.getStyle();
            p.setStyle(Paint.Style.FILL);

            float oneNumberWidth = p.measureText(String.valueOf(1));

            float xPosition;

            String drawText;
            if (dir > 0) {
                xPosition = x + (mNumber > 9 ? 0f : oneNumberWidth);
                drawText = mNumber + ".";
            } else {
                xPosition = x - p.measureText(String.valueOf(mNumber)) - oneNumberWidth;
                drawText = "." + mNumber;
            }
            if (mNumber != -1) {
                c.drawText(drawText, xPosition, baseline, p);
            }

            p.setStyle(style);
        }
    }
}