package com.tcl.ai.note.handwritingtext.richtext.styles;

import android.content.Context;
import android.text.Editable;
import android.widget.EditText;

import com.tcl.ai.note.handwritingtext.richtext.listener.StyleStatusListener;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;


public abstract class ARE_ABS_FreeStyle implements IARE_Style {

	protected Context mContext;//不赋值为null
	protected AREditText mEditText; // 子类需要用到时，setEditText来传递,没有赋值为null

	public ARE_ABS_FreeStyle(Context context) {
		this.mContext = context;
	}

	@Override
	public void setEditText(AREditText editText) {
		this.mEditText = editText;
	}

	@Override
	public EditText getEditText() {
		return mEditText;
	}

	// 可选的伪方法实现
	@Override
	public boolean getIsChecked() {
		return false;
	}
	protected StyleStatusListener mStyleStatusListener;

	@Override
	public void setStyleStatusListener(StyleStatusListener listener) {
		this.mStyleStatusListener = listener;
	}

	@Override
	public void removeStyle(Editable editable, int start, int end) {

	}
}
