package com.tcl.ai.note.handwritingtext.richtext.styles;


import android.text.Editable;
import android.text.style.BackgroundColorSpan;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.EditText;
import android.widget.ImageView;

import com.tcl.ai.note.handwritingtext.richtext.spans.AreBackgroundColorSpan;
import com.tcl.ai.note.handwritingtext.richtext.styles.windows.BackgroundColorChangeListener;
import com.tcl.ai.note.handwritingtext.richtext.utils.ARE_Helper;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;


public class ARE_BackgroundColor extends ARE_ABS_Dynamic_Style<AreBackgroundColorSpan> implements BackgroundColorChangeListener {

    private ImageView mBackgroundImageView;

    private boolean mBackgroundChecked = false;

    private int mColor;

    private boolean mBackgroundValid = false;



    public ARE_BackgroundColor() {
        super(AreBackgroundColorSpan.class);
    }

    /**
     * @param backgroundImage
     */
    public ARE_BackgroundColor(ImageView backgroundImage, int backgroundColor) {
        super(AreBackgroundColorSpan.class);
        this.mBackgroundImageView = backgroundImage;
        this.mColor = backgroundColor;
        setListenerForImageView(this.mBackgroundImageView);
    }

    public void setEditText(AREditText editText) {
        this.mEditText = editText;
    }

    @Override
    public EditText getEditText() {
        return mEditText;
    }

    @Override
    public void updateCheckStatus(boolean checked) {
              this.mBackgroundChecked = checked;
    }


    @Override
    public void setListenerForImageView(final ImageView imageView) {
        imageView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
               /* mBackgroundChecked = !mBackgroundChecked;
                ARE_Helper.updateCheckStatus(ARE_BackgroundColor.this, mBackgroundChecked);
                if (null != mEditText) {
                    applyStyle(mEditText.getEditableText(), mEditText.getSelectionStart(), mEditText.getSelectionEnd(), true);
                }*/
            }
        });
    }




    @Override
    public AreBackgroundColorSpan newSpan() {
        return  new AreBackgroundColorSpan(this.mColor);
    }


    /**
     * 设置背景颜色
     * @param color
     */
    public void setBackgroundColor(int color) {
        mColor = color;
        mBackgroundChecked = true;
        ARE_Helper.updateCheckStatus(ARE_BackgroundColor.this, true);
        if (null != mEditText) {
            Editable editable = mEditText.getEditableText();
            int start = mEditText.getSelectionStart();
            int end = mEditText.getSelectionEnd();

            if (end >= start) {
                applyNewStyle(editable, start, end, mColor);
            }
        }
    }


    //判断切换颜色后，内部颜色是否一致，不一致，需要重新应用新的style
    @Override
    protected void changeSpanInsideStyle(Editable editable, int start, int end, AreBackgroundColorSpan existingSpan) {
        int currentColor = existingSpan.getBackgroundColor();
        if(currentColor != mColor){
            applyNewStyle(editable,start,end,mColor);
        }
    }

    @Override
    public ImageView getImageView() {
        return this.mBackgroundImageView;
    }

    @Override
    public void setChecked(boolean isChecked) {
        this.mBackgroundChecked = isChecked;
    }

    @Override
    public boolean getIsChecked() {
        return this.mBackgroundChecked;
    }

    @Override
    public void setisValid(boolean isValid) {
        mBackgroundValid = isValid;
    }

    @Override
    public boolean getIsValid() {
        return mBackgroundValid;
    }

    @Override
    protected void featureChangedHook(int lastSpanColor) {
        mColor = lastSpanColor;


    }

    @Override
    protected AreBackgroundColorSpan newSpan(int color) {
        return new AreBackgroundColorSpan(color);
    }

    @Override
    public void onBackgroundColorChanged(int color) {
        setBackgroundColor(color);
    }


}
