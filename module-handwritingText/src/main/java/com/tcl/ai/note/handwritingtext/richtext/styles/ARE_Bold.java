package com.tcl.ai.note.handwritingtext.richtext.styles;

import android.widget.EditText;
import android.widget.ImageView;

import com.tcl.ai.note.handwritingtext.richtext.spans.AreBoldSpan;
import com.tcl.ai.note.utils.Logger;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;


public class ARE_Bold extends ARE_ABS_Style<AreBoldSpan> {

    private ImageView mBoldImageView;

    private boolean mBoldChecked;
    private boolean mBoldValid = false;

    public ARE_Bold() {
        super(AreBoldSpan.class);
    }

    @Override
    public void setisValid(boolean isValid) {
        mBoldValid = isValid;
    }

    @Override
    public boolean getIsValid() {
        return mBoldValid;
    }

    @Override
    public void setEditText(AREditText editText) {
        this.mEditText = editText;
    }

    @Override
    public EditText getEditText() {
        return this.mEditText;
    }

    @Override
    public void setListenerForImageView(final ImageView imageView) {
    }

    public void setBold() {
        if(!mBoldValid) {
            return;
        }
        mBoldChecked = !mBoldChecked;
        updateCheckStatus(mBoldChecked);

        if (null != mEditText) {
            Logger.d("datahub, text_bold_click editor : " + mEditText.hashCode());
            int start = mEditText.getSelectionStart();
            int end = mEditText.getSelectionEnd();
            //如果是选中文本，则mIsRecordToHistory要为true
            mIsRecordToHistory = start != end;
            applyStyle(mEditText.getEditableText(),
                    start,
                    end, mIsRecordToHistory);
            triggerSaveContent(mEditText);

        }
    }

    @Override
    public void updateCheckStatus(boolean checked) {
        setChecked(checked);
    }

    @Override
    public ImageView getImageView() {
        return this.mBoldImageView;
    }

    @Override
    public void setChecked(boolean isChecked) {
        this.mBoldChecked = isChecked;
    }

    @Override
    public boolean getIsChecked() {
        return this.mBoldChecked;
    }

    @Override
    public AreBoldSpan newSpan() {
        return new AreBoldSpan();
    }
}
