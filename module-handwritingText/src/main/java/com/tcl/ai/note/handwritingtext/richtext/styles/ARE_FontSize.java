package com.tcl.ai.note.handwritingtext.richtext.styles;

import android.text.Editable;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.EditText;
import android.widget.ImageView;

import com.tcl.ai.note.handwritingtext.richtext.inner.Constants;
import com.tcl.ai.note.handwritingtext.richtext.spans.AreFontSizeSpan;
import com.tcl.ai.note.handwritingtext.richtext.styles.windows.FontSizeChangeListener;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;


public class ARE_FontSize extends ARE_ABS_Dynamic_Style<AreFontSizeSpan> implements FontSizeChangeListener {

//	public static boolean S_CHECKED = true;

	private ImageView mFontsizeImageView;


	private boolean mFontSizeValid = false;

	private int mSize = Constants.DEFAULT_FONT_SIZE;

	private static final int DEFAULT_FONT_SIZE = 18;

	private boolean mIsChecked;

	public ARE_FontSize() {
		super(AreFontSizeSpan.class);
	}

	/**
	 * @param editText
	 */
	public void setEditText(AREditText editText) {
		this.mEditText = editText;
	}

	@Override
	public EditText getEditText() {
		return mEditText;
	}

	@Override
	public void updateCheckStatus(boolean checked) {
		setChecked(checked);
	}

	@Override
	public void setListenerForImageView(final ImageView imageView) {
		imageView.setOnClickListener(new OnClickListener() {
			@Override
			public void onClick(View v) {
				// 弹出 PopupMenu
//				FontSizePopup.showFontSizePopup(v.getContext(), v, ARE_FontSize.this);
			}
		});
	}

	public void setFontSize(int fontSize){
		mIsChecked = true;
		mSize = fontSize;
		if (null != mEditText) {
			Editable editable = mEditText.getEditableText();
			int start = mEditText.getSelectionStart();
			int end = mEditText.getSelectionEnd();

			if (end >= start) {
				applyNewStyle(editable, start, end, mSize);
			}
		}
	}

	@Override
	protected void changeSpanInsideStyle(Editable editable, int start, int end, AreFontSizeSpan existingSpan) {
		int currentSize = existingSpan.getSize();
		if (currentSize != mSize) {
			applyNewStyle(editable, start, end, mSize);
		}
	}

	@Override
	public AreFontSizeSpan newSpan() {
		return new AreFontSizeSpan(mSize);
	}

	@Override
	public ImageView getImageView() {
		return this.mFontsizeImageView;
	}

	@Override
	public void setChecked(boolean isChecked) {
		// Do nothing.
		this.mIsChecked = isChecked;
	}

	@Override
	public boolean getIsChecked() {
		return mIsChecked;
	}

	@Override
	public void setisValid(boolean isValid) {
		mFontSizeValid = isValid;
	}

	@Override
	public boolean getIsValid() {
		return mFontSizeValid;
	}

	@Override
	public void onFontSizeChange(int fontSize) {
		mIsChecked = true;
		mSize = fontSize;
		if (null != mEditText) {
			Editable editable = mEditText.getEditableText();
			int start = mEditText.getSelectionStart();
			int end = mEditText.getSelectionEnd();

			if (end > start) {
				applyNewStyle(editable, start, end, mSize);
			}
		}
	}

	@Override
	protected void featureChangedHook(int lastSpanFontSize) {
		mSize = lastSpanFontSize;
	}

	@Override
	protected AreFontSizeSpan newSpan(int size) {
		return new AreFontSizeSpan(size);
	}
}
