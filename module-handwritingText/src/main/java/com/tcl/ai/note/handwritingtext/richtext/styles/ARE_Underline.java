package com.tcl.ai.note.handwritingtext.richtext.styles;

import android.widget.EditText;
import android.widget.ImageView;

import com.tcl.ai.note.handwritingtext.richtext.spans.AreUnderlineSpan;
import com.tcl.ai.note.utils.Logger;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;


public class ARE_Underline extends ARE_ABS_Style<AreUnderlineSpan> {


    private boolean mUnderlineChecked;
    private boolean ismUnderlineValided = false;

    /**
     */
    public ARE_Underline() {
        super(AreUnderlineSpan.class);
    }

    @Override
    public void setisValid(boolean isValid) {
        ismUnderlineValided = isValid;
    }

    @Override
    public boolean getIsValid() {
        return ismUnderlineValided;
    }

    /**
     * @param editText
     */
    public void setEditText(AREditText editText) {
        this.mEditText = editText;
    }

    @Override
    public EditText getEditText() {
        return this.mEditText;
    }

    @Override
    public void setListenerForImageView(final ImageView imageView) {

    }

    public void setUnderLine() {
        if (!ismUnderlineValided) {
            return;
        }
        Logger.d("datahub, text_underline_click");
        mUnderlineChecked = !mUnderlineChecked;
        updateCheckStatus(mUnderlineChecked);
        if (null != mEditText) {
            int start = mEditText.getSelectionStart();
            int end = mEditText.getSelectionEnd();
            mIsRecordToHistory = start != end;
            applyStyle(mEditText.getEditableText(),
                    start,
                    end, mIsRecordToHistory);
            triggerSaveContent(mEditText);
        }
    }

    @Override
    public void updateCheckStatus(boolean checked) {
        setChecked(checked);
    }

    @Override
    public ImageView getImageView() {
        return null;
    }

    @Override
    public void setChecked(boolean isChecked) {
        this.mUnderlineChecked = isChecked;
    }

    @Override
    public boolean getIsChecked() {
        return this.mUnderlineChecked;
    }

    @Override
    public AreUnderlineSpan newSpan() {
        return new AreUnderlineSpan();
    }
}
