package com.tcl.ai.note.handwritingtext.richtext.utils

import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.state.RichTextState
import com.tcl.ai.note.handwritingtext.ui.richtext.RichTextController
import com.tcl.ai.note.handwritingtext.vm.state.RichTextDataState
import com.tcl.ai.note.utils.Logger

object NoteContentUtil {

    /**
     * 判断Note中富文本内容是否为空
     */
    fun isContentEmpty(state: RichTextDataState):Boolean{
        return state.title.isBlank() &&
                state.content.replace(Regex("[\\n\\r]"), "").isBlank() &&
                state.images.isEmpty() &&
                state.audios.isEmpty()
    }
    /**
     * 判断Note内容是否为空（为空则可以删除）
     */
    fun canNoteDelete(state: RichTextState, condition: () -> Boolean): Boolean {
        // 1. 标题必须为空
        if (state.title.isNotEmpty()) return false

        // 2. 所有内容必须都是“无有效内容”的
        val hasContent = state.contents.any { content ->
            when (content) {
                is EditorContent.TextBlock -> content.text.text.isNotBlank()
                is EditorContent.TodoBlock -> content.text.text.isNotBlank()
                is EditorContent.ImageBlock -> true        // 有图片必然有内容
                is EditorContent.AudioBlock -> {
                    // 已在外层判断
                    false
                    // 只要音频文件真实存在即视为有内容
//                    content.audioPath.isNotEmpty() && File(content.audioPath).exists()
                }

                is EditorContent.RichTextV2 -> false
            }
        }
        if (hasContent) return false
        // 3. Note已有且数据库中的title和content也均为空
        return state.note?.let {
            val noteTitleIsEmpty = it.title.isEmpty()
            // 把换行符去掉后看content是否还有内容
            val pureContent = it.content.replace(Regex("[\\n\\r]"), "")
            val noteContentIsEmpty = pureContent.isEmpty()
            it.noteId > 0 &&
                    noteTitleIsEmpty &&
                    noteContentIsEmpty &&
                    condition()
        } == true
    }

    /**
     * 判断插入内容后是否超出长度限制
     */
    fun isInsertContentLengthValid(originContent: String,needAddContent: String):Boolean{
        val isCanInsert =
            originContent.length + needAddContent.length <= RichTextController.MAX_CONTENT_LENGTH
        Logger.d("isInsertContentLengthValid","isCanInsert:$isCanInsert originContent.length:${originContent.length} needAddContent.length:${needAddContent.length}")
        return isCanInsert
    }
    /**
     *笔记转文本，给AI使用,
     * 主要处理 待办事项的断句问题
     */
    fun dealWithNoteContentForAI(note: Note?): String {
        // 如果 contents 列表不为空，则从中获取内容并在每个 item 后添加句号
        if (note?.contents?.isNotEmpty() == true) {
//            val isHasTodo = note.contents.any { it is EditorContent.TodoBlock }
            val content = buildString {
                note.contents.forEach { content ->
                    when (content) {
                        is EditorContent.TextBlock -> {
//                            if (isHasTodo) {
//                                append("[text] ")
//                            }
                            append(content.text.text)
                        }

                        is EditorContent.TodoBlock -> {
//                            // 对待办事项添加特殊标记，使用逗号标记
//                            val status = if (content.isDone) "[todolist] " else "[todolist] "
//                            append(status)
                            append(content.text.text).append("。")
                        }
                        // 图片和音频内容不添加文本
                        else -> {}
                    }
                }
            }
            if (content.isEmpty()){//一期数据，为空则是二期数据
                return note.content
            }
        }

        // 如果 contents 为空，则使用原来的 content 字段
        return note?.content ?: ""
    }
}