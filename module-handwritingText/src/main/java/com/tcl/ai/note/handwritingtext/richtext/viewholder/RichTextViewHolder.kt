package com.tcl.ai.note.handwritingtext.richtext.viewholder

import android.content.Context
import android.content.ContextWrapper
import android.util.TypedValue
import android.view.View
import android.view.View.OnAttachStateChangeListener
import android.view.View.OnClickListener
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.richtext.converter.toRichTextStyleEntity
import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.handwritingtext.richtext.listener.StyleStatusListener
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Alignment
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_BackgroundColor
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Bold
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_FontColor
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_FontSize
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_IndentLeft
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_IndentRight
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Italic
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_ListBullet
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_ListNumber
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Strikethrough
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Underline
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Upcoming
import com.tcl.ai.note.handwritingtext.richtext.utils.DisplayUtils
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText
import com.tcl.ai.note.handwritingtext.vm.event.RichTextEventManager
import com.tcl.ai.note.handwritingtext.vm.event.RichTextOperateEvent
import com.tcl.ai.note.handwritingtext.vm.event.RichTextStyleActionEvent
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * 富文本 ViewHolder
 */
class RichTextViewHolder(
    private val context: Context,
    private val areEditText: AREditText,
    private val onRichTextChanged: ((content: String, style: RichTextStyleEntity) -> Unit)? = null
) : OnClickListener , StyleStatusListener by StyleStatusDelegate(){

    private var areBold: ARE_Bold? = null
    private var areItalic: ARE_Italic? = null
    private var areUnderline: ARE_Underline? = null
    private var areStrikethrough: ARE_Strikethrough? = null
    private var areAlignment: ARE_Alignment? = null
    private var areIndentRight: ARE_IndentRight? = null
    private var areIndentLeft: ARE_IndentLeft? = null
    private var areListNumber: ARE_ListNumber? = null
    private var areListBullet: ARE_ListBullet? = null
    private var areFontSize: ARE_FontSize? = null
    private var areFontColor: ARE_FontColor? = null
    private var areBackgroundColor: ARE_BackgroundColor? = null
    private var areTodo: ARE_Upcoming? = null
    private val TAG = "RichTextViewHolder"
    private val lifecycleOwner: LifecycleOwner? = when (context) {
        is LifecycleOwner -> context
        is ContextWrapper -> (context as? ContextWrapper)?.baseContext as? LifecycleOwner
        else -> null
    }
    private val lifecycleScope: CoroutineScope? = lifecycleOwner?.lifecycleScope

    private var isReleased = false
    private var eventJob: Job? = null

    init {
        initEditView()
        initStyles()
        observeEvents()
        initListeners()
    }

    private fun initEditView() {
        val paddingHorizontal = DisplayUtils.dp2px(
            isTablet.judge(PADDING_HORIZONTAL_TABLET_DP, PADDING_HORIZONTAL_PHONE_DP)
        )
        val paddingVertical = DisplayUtils.dp2px(
            isTablet.judge(PADDING_VERTICAL_TABLET_DP, PADDING_VERTICAL_PHONE_DP)
        )
        areEditText.setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, 0)
        areEditText.isFocusable = true
        areEditText.setFocusableInTouchMode(true)
        areEditText.setTextColor(ContextCompat.getColor(context, R.color.text_title))
        areEditText.stopAllMonitor()
        areEditText.textSize = TEXT_SIZE_SP.toFloat()
        areEditText.setLineHeight(TypedValue.COMPLEX_UNIT_SP, LINE_HEIGHT_SP.toFloat())

        //初次加载完才监听，不然会调用apply style 创建了无效的undo redo记录
        areEditText.startAllMonitor()
    }

    private fun initListeners() {
        areEditText.setOnClickListener(this)
        areEditText.setStyleStatusListener(this)
        areEditText.addOnAttachStateChangeListener(object : OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(v: View) {
                registerTodoMessage()
            }

            override fun onViewDetachedFromWindow(v: View) {
                unregisterTodoMessage()
                RichTextEventManager.restoreToolBarStyleState()
            }
        })
    }

    private fun initStyles() {
        // 初始化各种样式对象
        areBold = ARE_Bold().apply {
            areEditText.setInStylesList(this)
        }
        areItalic = ARE_Italic().apply {
            areEditText.setInStylesList(this)
        }
        areUnderline = ARE_Underline().apply {
            areEditText.setInStylesList(this)
        }
        areListNumber = ARE_ListNumber().apply {
            areEditText.setInStylesList(this)
        }

        areListBullet = ARE_ListBullet().apply {
            areEditText.setInStylesList(this)
        }

        areTodo = ARE_Upcoming(context).apply {
            areEditText.setInStylesList(this)
            this.setOnCheckStateListener {
                notifyRichTextChange()
            }
        }
        // TODO 暂时去掉不支持功能，对待办功能有影响
//        areStrikethrough = ARE_Strikethrough().apply {
//            areEditText.setInStylesList(this)
//        }
//        areIndentRight = ARE_IndentRight(areEditText).apply {
//            areEditText.setInStylesList(this)
//        }
//        areIndentLeft = ARE_IndentLeft(areEditText).apply {
//            areEditText.setInStylesList(this)
//        }
//
//        areFontSize = ARE_FontSize().apply {
//            areEditText.setInStylesList(this)
//        }
//        areFontColor = ARE_FontColor().apply {
//            areEditText.setInStylesList(this)
//        }
//        areBackgroundColor = ARE_BackgroundColor().apply {
//            areEditText.setInStylesList(this)
//        }
//
//        areAlignment = ARE_Alignment().apply {
//            areEditText.setInStylesList(this)
//        }
    }

    /**
     * 观察事件
     */
    private fun observeEvents() {
        // 取消之前的Job
        eventJob?.cancel()
        eventJob = lifecycleScope?.launch {
            RichTextEventManager.richTextStyleActionEvent.collect { event ->
                event?.let {
                    if (areEditText.isAttachedToWindow && !isReleased) {
                        handleStyleEvent(it)
                    }
//                    RichTextEventManager.clearStyleEvent() // 处理完事件后清除
                }
            }
        }
    }

    /**
     * 处理富文本操作事件，与RichTextOperateEventManager事件交互
     */
    private fun handleOperateEvent(operateEvent: RichTextOperateEvent) {
        when (operateEvent) {

            is RichTextOperateEvent.Undo -> {
                areEditText.undo()
            }

            is RichTextOperateEvent.Redo -> {
                areEditText.redo()
            }

            else -> {}
        }
    }

    /**
     * 处理富文本“样式/类型”事件（富文本面板的所有按钮事件最终都会到这里）
     * 每执行一次操作，都会调用[notifyRichTextChange]进行保存回调
     */
    private fun handleStyleEvent(event: RichTextStyleActionEvent) {
        when (event) {
            is RichTextStyleActionEvent.TodoToggled -> {
                // 切换待办事项
                areTodo?.setTodo()
            }

            is RichTextStyleActionEvent.BoldToggled -> {
                // 切换粗体
                areBold?.setBold()
            }

            is RichTextStyleActionEvent.ItalicToggled -> {
                // 切换斜体
                areItalic?.setItalic()
            }

            is RichTextStyleActionEvent.UnderlineToggled -> {
                // 切换下划线
                areUnderline?.setUnderLine()
            }

            is RichTextStyleActionEvent.StrikethroughToggled -> {
                // 切换删除线
                areStrikethrough?.setStrikethrough()
            }

            is RichTextStyleActionEvent.AlignmentApplied -> {
                // 应用对齐方式
                areAlignment?.setAlignment(event.alignment)
            }

            is RichTextStyleActionEvent.IndentLeftApplied -> {
                // 增加左缩进
                areIndentLeft?.setIndentLeft()
            }

            is RichTextStyleActionEvent.IndentRightApplied -> {
                // 增加右缩进
                areIndentRight?.setIndentRight(null, true)
            }

            is RichTextStyleActionEvent.NumberedListToggled -> {
                // 切换有序列表
                areListNumber?.setListNumber()
            }

            is RichTextStyleActionEvent.BulletedListToggled -> {
                // 切换无序列表
                areListBullet?.setListBullet()
            }

            is RichTextStyleActionEvent.FontSizeApplied -> {
                // 设置字体大小
                areFontSize?.setFontSize(event.size)
            }

            is RichTextStyleActionEvent.FontColorApplied -> {
                // 设置字体颜色
                areFontColor?.setFontColor(event.color)
            }

            is RichTextStyleActionEvent.BackgroundColorApplied -> {
                // 设置字体背景
                areBackgroundColor?.setBackgroundColor(event.color)
            }
        }
        notifyRichTextChange()
        // 操作底部功能，都需要弹出键盘
        areEditText.requestFocusAndShowKeyboard()
    }

    /**
     * 统一回调内容
     */
    private fun notifyRichTextChange() {
        val content = areEditText.text.toString()
        val style = areEditText.toRichTextStyleEntity()
        // 通知外部回调（ViewModel），进行数据持久化保存
        onRichTextChanged?.invoke(content, style)
    }

    override fun onClick(v: View?) {

    }


    private fun registerTodoMessage() {
        areTodo?.registerUpcomingMessage()
    }

    private fun unregisterTodoMessage() {
        areTodo?.unRegisterUpcomingMessage()
    }

    /**
     * 完整清理资源
     */
    fun onRelease() {
        if (isReleased) return
        isReleased = true

        Logger.d(TAG, "Releasing RichTextViewHolder for AREditText: ${areEditText.hashCode()}")

        // 取消协程
        eventJob?.cancel()
        eventJob = null

        // 清理消息监听
        unregisterTodoMessage()

        // 清理样式对象
        areTodo?.unRegisterUpcomingMessage()

        // 清理AREditText（作为备用清理，通常在RichTextController中已经调用了）
        if (!areEditText.isDestroyed) {
            areEditText.destroy()
        }

        // 清理其他引用
        areBold = null
        areItalic = null
        areUnderline = null
        areStrikethrough = null
        areAlignment = null
        areIndentRight = null
        areIndentLeft = null
        areListNumber = null
        areListBullet = null
        areFontSize = null
        areFontColor = null
        areBackgroundColor = null
        areTodo = null
    }

    companion object {
        const val PADDING_HORIZONTAL_TABLET_DP = 48
        const val PADDING_HORIZONTAL_PHONE_DP = 24
        const val PADDING_VERTICAL_TABLET_DP = 18
        const val PADDING_VERTICAL_PHONE_DP = 8
        const val TEXT_SIZE_SP = 16
        const val LINE_HEIGHT_SP = 28
    }
}