package com.tcl.ai.note.handwritingtext.state

import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.database.entity.BgMode
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphType
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.vm.RichTextViewModel.Operation

/**
 * 笔记分类列表数据状态
 */
sealed class ListNoteCategoryState {
    object Loading : ListNoteCategoryState()
    data class Success(val items: List<NoteCategory>) : ListNoteCategoryState()
    data class Error(val message: String) : ListNoteCategoryState()
}

/**
 * 笔记分类数据状态
 */
sealed class NoteCategoryState {
    object Loading : NoteCategoryState()
    data class Success(val item: NoteCategory) : NoteCategoryState()
    data class Error(val message: String) : NoteCategoryState()
}

/**
 * 富文本状态
 */
data class RichTextState(
    val title: String = "", // 标题内容
    val note: Note? = null, // 当前笔记
    val contents: List<EditorContent> = emptyList(), // 内容块列表
    val undoStack:ArrayDeque<Operation> = ArrayDeque<Operation>(50), // 撤销栈
    val newCategory: NoteCategory? = null, // 预览新建的分类
    val editMode:Boolean = true, // 编辑模式状态(默认可编辑状态)
    val focusedIndex: Int = -1, // 焦点索引
    val cursorPosition: Int = -1, // 光标位置
    val titleCursorPosition: Int = -1, // 标题光标位置
    val currentFocusedType: EditorContent? = null, // 当前焦点数据类型（TextBlock/ImageBlock/TodoBlock/AudioBlock）
    val currentParagraphStyle: ParagraphStyle = ParagraphStyle.NONE,
    val currentParagraphType: ParagraphType = ParagraphType.TEXT, // 当前焦点数据类型(TEXT, NUMBERED_LIST, BULLETED_LIST, TODO_ITEM)
    val isBoldActive: Boolean = false,
    val isItalicActive: Boolean = false,
    val isUnderlineActive: Boolean = false,
    val canUndo:Boolean = false, // 撤销按钮状态
    val canRedo:Boolean = false, // 重做按钮状态
    val canSave:Boolean = false, // 是否可保存状态
    val bottomMenuType: MenuBar = MenuBar.NONE, // 当前点击的BottomMenu类型
    val brushMenuType: String = "", // 当前点击的手绘菜单栏类型
    val aiReplaceText:String = "", // AI润色替换文本
    val aiInsertText:String = "", // 手写插入文本
    var bgMode: BgMode = BgMode.none,
    var bgColor: Long = Skin.defColor,
    val isNavigatingWithinApp:Boolean = false, // 导航状态(是否应用内页面跳转)
    val isSaving: Boolean = false, // 是否正在保存
){
    fun getNoteId() = note?.noteId?:-1L


}






