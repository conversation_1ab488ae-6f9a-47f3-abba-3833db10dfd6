package com.tcl.ai.note.handwritingtext.ui

import android.widget.Toast
import androidx.activity.compose.BackHandler
import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.richtext.utils.NoteContentUtil
import com.tcl.ai.note.handwritingtext.ui.popup.ShowDataSavingPopup
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.LocalScaffoldPadding
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.px2dp
import com.tcl.ai.note.voicetotext.audio.ShowAudioPlayer
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun EditScreen2(
    noteId: Long?,
    modifier: Modifier = Modifier,
    isPen: Boolean = false,
) {
    val navigationBarsHeight = WindowInsets.navigationBars.getBottom(LocalDensity.current).px2dp.dp
    EditScreenBackHandler()
    Scaffold(
        topBar = {
            TableEditScreenTopBar()
        },
        containerColor = TclTheme.colorScheme.primaryBackground,
        modifier = modifier
            .fillMaxSize()
            .onSizeChanged{
                Logger.d("EditScreen2", "EditScreen2, onSizeChanged: $it")
            }
            .consumeWindowInsets(PaddingValues(0.dp, 0.dp, 0.dp, navigationBarsHeight))
    ) { innerPadding ->
        CompositionLocalProvider(
            LocalScaffoldPadding provides innerPadding
        ) {
            EditContent(
                noteId = noteId,
                isPen = isPen,
                modifier = Modifier
                    .padding(
                        PaddingValues(
                            innerPadding.calculateLeftPadding(LocalLayoutDirection.current),
                            innerPadding.calculateTopPadding(),
                            innerPadding.calculateRightPadding(LocalLayoutDirection.current),
                            navigationBarsHeight,
                        )
                    )
                    .clipToBounds()
            )
        }
    }
}

@Composable
fun EditScreenBackHandler(
    richTextViewModel: RichTextViewModel2 = hiltViewModel(),
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel(),
) {
    var backHandled by remember { mutableStateOf(false) }
    val uiState by richTextViewModel.uiState.collectAsState()
    var isProcessing by remember { mutableStateOf(false) }  // 防止重复操作

    val tip = stringResource(R.string.edit_note_saved)
    val context = LocalContext.current
    val activity = LocalActivity.current
    BackHandler(enabled = !backHandled) {
        backHandled = true
    }
    if (backHandled) {
        ShowDataSavingPopup {
            if (isProcessing) return@ShowDataSavingPopup // 避免重复
            isProcessing = true

            // 返回时暂停录音播放
            if (ShowAudioPlayer.isCurrentlyPlaying()) {
                ShowAudioPlayer.stopPlay()
            }

            // 判断Note内容是否为空，为空则删除再返回
            if(NoteContentUtil.isContentEmpty(uiState)
                && suniaDrawViewModel.drawStokeVisibleRectState.value.isEmpty
                && uiState.noteId != null
                && uiState.noteId != 0L){
                // 执行删除，必须同步/异步完成后才能返回
                richTextViewModel.viewModelScope.launch {
                    richTextViewModel.deleteOneNote(uiState.noteId!!)
                    delay(150)
                    isProcessing = false
                    //完成后再返回
                    activity?.finish()
                }
            }else{
                // 内容非空，直接返回
                if(uiState.isSaved){
                    // 执行过保存操作则提示已保存
                    Toast.makeText(context, tip, Toast.LENGTH_SHORT).show()
                }
                isProcessing = false
                activity?.finish()
            }
            backHandled = false
        }
    }
}

