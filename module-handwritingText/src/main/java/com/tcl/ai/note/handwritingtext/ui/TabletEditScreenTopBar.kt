package com.tcl.ai.note.handwritingtext.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.ui.edit.container.TopBarPopupContainer
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.TopMenuBar
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.EditableTitleNavigationBar
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.events.createPopupActionEvents
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.handler.rememberAudioHandler
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.handler.rememberImageHandlers
import com.tcl.ai.note.handwritingtext.ui.other.MaskForTitleEditOnTitle
import com.tcl.ai.note.handwritingtext.ui.richtext.state.EditableTitleNavigationBarState
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.TopAudioBar
import com.tcl.ai.note.handwritingtext.vm.TitleEditViewModel
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel

/**
 * 平板顶部编辑栏
 * 支持水平滚动，在手机上可以滚动查看所有工具项
 */
@Composable
fun TableEditScreenTopBar(
    modifier: Modifier = Modifier,
    titleEditViewModel: TitleEditViewModel = hiltViewModel()
) {
    // 从Activity级别获取titleEditViewModel，这样和EditContent共享同一个实例
    val noteTitle by titleEditViewModel.noteTitle
    val defaultTitle = stringResource(R.string.title)

    val richTextViewModel: RichTextViewModel2 = hiltViewModel()
    val suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel()
    val audioToTextViewModel: AudioToTextViewModel = hiltViewModel()

    // 监听 RichTextViewModel2 的状态变化，同步标题到 TitleEditViewModel
    val uiState by richTextViewModel.uiState.collectAsState()
    val enableFingerDrawing by suniaDrawViewModel.enableFingerDrawingState.collectAsState()

    // 添加导航栏状态管理
    var navigationBarState by remember {
        mutableStateOf(
            EditableTitleNavigationBarState(
                title = noteTitle.ifEmpty { defaultTitle },
                hasUserEditedTitle = noteTitle.isNotEmpty() && noteTitle != defaultTitle
            )
        )
    }

    // 获取必要的UI参数
    val density = LocalDensity.current

    // 准备弹框事件处理器
    val imageHandlers = rememberImageHandlers()
    val audioHandler = rememberAudioHandler(richTextViewModel, uiState)
    
    val popupActionEvents = createPopupActionEvents(
        imageHandlers = imageHandlers,
        audioHandler = audioHandler,
        suniaDrawViewModel = suniaDrawViewModel,
        enableFingerDrawing = enableFingerDrawing,
        onDeleteNote = {} // 删除逻辑现在 PopupContainer 中处理
    )

    LaunchedEffect(uiState.title) {
        titleEditViewModel.initTitle(uiState.title)
    }

    // 设置默认hint文本
    LaunchedEffect(Unit) {
        titleEditViewModel.setDefaultHintText(defaultTitle)
    }

    // 设置标题变化监听器
    LaunchedEffect(Unit) {
        titleEditViewModel.setOnTitleChangedListener { newTitle ->
            richTextViewModel.onTitleChanged(newTitle)
        }
    }

    Box(modifier = modifier.fillMaxWidth().statusBarsPadding()) {
        Column (
            modifier = Modifier.fillMaxWidth()
        ) {
            EditableTitleNavigationBar(
                title = noteTitle,
                onTitleChanged = { newTitle ->
                    titleEditViewModel.updateTitle(newTitle)
                },
                onTitleEditingStateChanged = { isEditing ->
                    titleEditViewModel.setEditingState(isEditing)
                },
                onUserStartedEditing = {
                    // 当用户真正开始输入时，标记为已编辑状态
                    titleEditViewModel.markUserHasEditedTitle()
                },
                navigationBarState = navigationBarState,
                onNavigationBarStateChanged = { newState ->
                    navigationBarState = newState
                }
            )
            TopAudioBar()
            TopMenuBar()
        }

        // TopMenuBar区域的遮罩层，与内容区遮罩保持一致
        // 放在Box内部并使用align对齐到底部
        Box(modifier = Modifier.align(Alignment.BottomCenter)) {
            MaskForTitleEditOnTitle(titleEditViewModel)
        }

        // 弹框容器 - 统一存放TopBar里要弹出的所有弹框
        TopBarPopupContainer(
            navigationBarState = navigationBarState,
            onNavigationBarStateChanged = { newState ->
                navigationBarState = newState
            },
            popupActionEvents = popupActionEvents,
            noteId = uiState.noteId,
            categoryId = uiState.categoryId,
            enableFingerDrawing = enableFingerDrawing,
            richTextViewModel = richTextViewModel,
            audioToTextViewModel = audioToTextViewModel,
            audios = uiState.audios,
            density = density
        )
    }
}


