package com.tcl.ai.note.handwritingtext.ui.categorydialog

import android.annotation.SuppressLint
import android.widget.Toast
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.LifecycleResumeEffect
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.utils.isExistCategory
import com.tcl.ai.note.theme.CursorColor
import com.tcl.ai.note.utils.CategoryColors
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.globalDialogWidth
import com.tcl.ai.note.utils.getCategoryColor
import com.tcl.ai.note.utils.getCategoryColorArray
import com.tcl.ai.note.utils.getCategoryIconColor
import com.tcl.ai.note.utils.getNavigationBarHeight
import com.tcl.ai.note.utils.isButtonNavigation
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.utils.toComposeColor
import com.tcl.ai.note.utils.tryToRequestFocus
import com.tcl.ai.note.widget.HoverProofIconButton
import com.tct.theme.core.designsystem.component.TclDialog
import com.tct.theme.core.designsystem.component.TclTextButton
import com.tct.theme.core.designsystem.theme.TclTheme
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged


/**
 * 主要的弹窗管理组件 - 内部封装ViewModel能力
 *
 * 使用方法:
 * 1. 创建一个dialogController = rememberCategoryDialogController()
 * 2. 在需要显示弹窗的地方调用dialogController的方法
 * 3. 将dialogController.state.value传递给CategoryDialogManager
 *
 * Toast提示
 * 首页：
 * 1.移动
 * 1.1 单个：已将 所选1条笔记移动至 ...("..."为分类名，超过两行后...)
 * 1.2 多个：已将 所选n条笔记移动至 ...("..."为分类名，超过两行后...)
 * 2.重命名：已重命名为 ...（“...”为分类名，超过两行后...）
 * 3.新建：无需Toast
 *
 * 编辑页：
 * 1.移动：已将 所选1条笔记移动至 ...("..."为分类名，超过两行后...)
 * 2.新建后移动：已将 所选1条笔记移动至 ...("..."为分类名，超过两行后...)
 */
@SuppressLint("StateFlowValueCalledInComposition")
@Composable
fun CategoryDialogScreenManager(
    categoryViewModel: CategoryDialogViewModel = hiltViewModel(),
    screenKey: String, // 添加一个唯一标识符参数
    onSuccess: ((String) -> Unit)? = null // 成功回调，可以传递成功消息
) {
    // 在组件进入时重置状态 解决首页和编辑页 重复弹出移动分类弹窗问题
    LifecycleResumeEffect(screenKey) {
        // 当组件进入时，确保弹窗状态是关闭的
        categoryViewModel.resetDialogState()
        categoryViewModel.observeCategoryListAndDialogState()
        onPauseOrDispose {
            categoryViewModel.resetDialogState()
        }
    }
    // 获取分类列表
    val dialogController = categoryViewModel.categoryDialogController
    val dialogState by categoryViewModel.categoryDialogState.collectAsState()
    val dialogCategory = categoryViewModel.categoryDialogState.value.editingDialogCategory
    val notesToMove = dialogState.notesToMove
    val noteCategories = dialogState.noteCategories
    val selectedCategoryId = dialogState.selectedCategoryId
    val isPreviewMode = dialogState.isPreviewMode

    //这里的文案提示需要区分，新建分类是公用的，如果是从选择分类弹窗中触发的新建，新建完 移动note到当前新建，此时Toast提示“移动至分类成功”
    //如果是侧边栏新建，Toast提示“新建分类成功”
    val toastFail = stringResource(R.string.category_add_fail)
    val context = LocalContext.current

    Logger.i(TAG, "dialogState:$dialogState")

    if (dialogState.isVisible) {
        when (dialogState.type) {
            CategoryDialogType.NEW_CATEGORY -> {
                NewCategoryDialog(
                    dialogCategory = null,
                    dialogState = dialogState,
                    isNewCategory = true,
                    onConfirm = { name, colorIndex ->
                        // Add new category
                        //限制最多创建分类个数为50个(不管是移动新建，还是侧边栏新建)
                        Logger.i(
                            TAG,
                            "noteCategories.size:${noteCategories.size}, notesToMove.size:${dialogState.notesToMove.size}"
                        )
                        if (noteCategories.size >= 50) {
                            ToastUtils.makeWithCancel(toastFail, Toast.LENGTH_SHORT)
                            dialogController.dismiss()
                            return@NewCategoryDialog
                        }
                        val category = NoteCategory(
                            name = name,
                            colorIndex = colorIndex,
                            createTime = System.currentTimeMillis(),
                            modifyTime = System.currentTimeMillis()
                        )
                        val noteIds = dialogState.notesToMove.map { it.id }
                        categoryViewModel.addCategory(
                            category,
                            noteIds,
                            dialogState.isMoveToNewCategory
                        )
                        if (dialogState.isMoveToNewCategory) {
                            val toastSuccess = context.getString(
                                R.string.category_moveto_success_new,
                                dialogState.notesToMove.size.toString(),
                                name
                            )
                            ToastUtils.makeWithCancel(toastSuccess, Toast.LENGTH_SHORT)
                        }

                        dialogController.dismiss()
                    },
                    onDismiss = { dialogController.dismiss() }
                )
            }

            CategoryDialogType.EDIT_CATEGORY -> {
                NewCategoryDialog(
                    dialogCategory = dialogCategory,
                    dialogState = dialogState,
                    isNewCategory = false,
                    onConfirm = { name, index ->
//                            Rename category
                        Logger.i(
                            TAG,
                            "Rename, original category, dialogCategory:$dialogCategory, name:$name, index:$index"
                        )
                        dialogCategory?.categoryId?.let { categoryId ->
                            val newCategory = NoteCategory(
                                categoryId = categoryId,
                                name = name,
                                colorIndex = index,
                            )
                            newCategory.modifyTime = System.currentTimeMillis()
                            newCategory.name = name
                            newCategory.colorIndex = index
                            newCategory.isRename = dialogCategory.name != name
                            categoryViewModel.renameCategory(newCategory)
                            val toastMsg = context.getString(R.string.category_rename, name)
                            ToastUtils.makeWithCancel(toastMsg, Toast.LENGTH_SHORT)
                        }
                        dialogController.dismiss()
                    },
                    onDismiss = {
                        dialogController.dismiss()
                        categoryViewModel.onCategoryDialogDismiss()
                    }
                )
            }

            CategoryDialogType.MOVE_NOTE_TO_CATEGORY -> {
                Logger.i(
                    TAG,
                    "Move，selectedCategoryId:$selectedCategoryId, noteCategories:$noteCategories"
                )
                //如果selectedCategoryId是空，说明选中的是全部便签
                val result = inSameCategory(notesToMove)
                val inSameCategory = result.first
                MoveNoteToCategoryDialog(
                    dialogController = dialogController,
                    notesToMove = dialogState.notesToMove,
                    inSameCategory = inSameCategory,
                    categories = noteCategories,
                    categoryId = result.second,
                    onMoveToCategory = { categoryId ->
                        //移动之后，取消选中模式，并自动切换到选中的分类
                        if (dialogState.notesToMove.isNotEmpty()) {
                            val noteIds = dialogState.notesToMove.map { it.id }
                            categoryViewModel.updateNotesCategoryId(
                                noteIds,
                                categoryId,
                                isPreviewMode
                            )
                        }

                        dialogController.dismiss()
                    },
                    onDismiss = {
                        dialogController.dismiss()
                        categoryViewModel.onCategoryDialogDismiss()
                    }
                )
            }
        }
    }
}

// 新建分类和重命名弹窗
@SuppressLint("UnusedBoxWithConstraintsScope", "DesignSystem")
@Composable
private fun NewCategoryDialog(
    dialogCategory: DialogCategory?,
    isNewCategory: Boolean,
    dialogState: CategoryDialogState,
    onConfirm: (String, Int) -> Unit,
    onDismiss: () -> Unit
) {
    // 分类列表数据
    val items by remember(dialogState) {
        derivedStateOf { dialogState.noteCategories }
    }

    val name = if (isNewCategory || dialogCategory == null) "" else dialogCategory.name
    val categoryId =
        if (isNewCategory || dialogCategory == null) 1L else dialogCategory.categoryId
    val currentColorIndex =
        if (isNewCategory || dialogCategory == null) 0 else dialogCategory.colorIndex
    Logger.i(
        TAG,
        "NewCategoryDialog, dialogCategory:$dialogCategory, name:$name, categoryId:$categoryId, currentColorIndex:$currentColorIndex"
    )

    val focusManager = LocalFocusManager.current

    var categoryName by remember { mutableStateOf(name) }

    val mainFocus = remember { FocusRequester() }
    val dummyFocus = remember { FocusRequester() }

    // 键盘状态监听（精确到像素）
    val isKeyboardOpen by rememberKeyboardState()
    // 键盘状态监听
    var isKeyboardReallyOpen by rememberSaveable {
        mutableStateOf(false)
    }
    // 首次弹窗标识
    var isInitialShow by remember { mutableStateOf(true) }

    var nameFieldValue by remember {
        mutableStateOf(
            TextFieldValue(
                text = categoryName,
                selection = TextRange(categoryName.length)
            )
        )
    }

    //重命名时移动光标位置至最后（仅在初始化时执行）
    LaunchedEffect(name) {
        nameFieldValue = TextFieldValue(
            text = categoryName,
            selection = TextRange(categoryName.length)
        )
    }

    // 请求焦点
    LaunchedEffect(Unit) {
        if (nameFieldValue.text.isEmpty()) {
            mainFocus.tryToRequestFocus()
            isKeyboardReallyOpen=true
        }
        isInitialShow = true
        Logger.v(TAG, "LaunchedEffect, tryToRequestFocus ")
    }

    val context = LocalContext.current
    val density = LocalDensity.current


    val initialColorIndex = if (!isNewCategory) {
        currentColorIndex
    } else {
//        findLeastUsedColor(items) { it.colorIndex } ?: CategoryColors.PINK_COLOR
        CategoryColors.PINK_COLOR
    }
    var selectedColorIndex by remember { mutableIntStateOf(initialColorIndex) }

    val originalColorIndex by remember { mutableIntStateOf(currentColorIndex) }

    Logger.v(
        TAG,
        "NewCategoryDialog, items.size:${items.size}, originalColorIndex:$originalColorIndex, selectedColorIndex:$selectedColorIndex"
    )

    // Compute dialog states for validation
    val clearCategory = categoryName.trim()
    val isNotEmpty = clearCategory.isNotEmpty()
    val isExistCategory =
        isExistCategory(items, clearCategory, categoryId, selectedColorIndex, isNewCategory)
    val isRepeated = isExistCategory && isNotEmpty

    var isOnChanged by remember { mutableStateOf(false) }

    var isMaximumCharacter by remember { mutableStateOf(false) }

    val isDiffColor = selectedColorIndex != originalColorIndex
    val isEnabled = if (isNewCategory) {
        !isExistCategory && isNotEmpty && !isMaximumCharacter
    } else {
        (isDiffColor || !isExistCategory) && isNotEmpty && !isMaximumCharacter
    }
    Logger.v(
        TAG, "NewCategoryDialog, isNotEmpty:$isNotEmpty, isDiffColor:$isDiffColor, " +
                "isExistCategory:$isExistCategory,isMaximumCharacter:$isMaximumCharacter, isEnabled:$isEnabled"
    )

    val dividerColor = colorResource(R.color.text_field_border)
    var divider by remember { mutableStateOf(dividerColor) }

    var hasFocus by remember { mutableStateOf(false) }

    Logger.v(TAG, "isKeyboardOpen:$isKeyboardOpen, isInitialShow:$isInitialShow")

    LaunchedEffect(isKeyboardReallyOpen) {
        Logger.d(TAG,"isKeyboardReallyOpen:$isKeyboardOpen")
        if (!isKeyboardReallyOpen) {
            focusManager.clearFocus()
        }
    }
    // 智能焦点转移逻辑
    LaunchedEffect(isKeyboardOpen) {
        if (!isKeyboardOpen && !isInitialShow) {
            delay(50)
            divider =
                if (isRepeated && isOnChanged) context.getColor(R.color.text_field_border_error)
                    .toComposeColor() else context.getColor(R.color.text_field_no_focus)
                    .toComposeColor()
//            focusManager.clearFocus()
//            dummyFocus.requestFocus()
        } else {
            divider = context.getColor(R.color.text_field_border).toComposeColor()
        }
        isInitialShow = false
    }

    var isShowColorPicker by remember { mutableStateOf(false) }
    var isExpandedTip by remember { mutableStateOf(false) }

    var errorTip = stringResource(R.string.category_name_exists)

    TclTheme(dynamicColor = false) {
        TclDialog(
            show = true,
            dialogMaxWidth = globalDialogWidth(),
            title = {
                Text(
                    text = if (isNewCategory)
                        stringResource(R.string.dialog_category_name_title)
                    else
                        stringResource(R.string.dialog_category_rename_title),
                )
            },
            onDismissRequest = onDismiss,
            content = {

//                Box {
//                    BasicTextField(
//                        value = "",
//                        onValueChange = { },
//                        modifier = Modifier
//                            .size(1.dp) // 1像素大小
//                            .alpha(0f) // 完全透明
//                            .align(Alignment.TopStart) // 放置在左上角
//                            .focusRequester(dummyFocus)
//                            .onFocusChanged { hasFocus ->
//                                Logger.i(
//                                    TAG,
//                                    "1 size TextField onFocusChanged, hasFocus:${hasFocus.isFocused}"
//                                )
//                            }
//                            .focusable()
//                    )
//
//                }

                Column(
                    modifier = Modifier.animateContentSize(
//                    animationSpec = tween(200, easing = FastOutLinearInEasing)
                        animationSpec = spring(
                            dampingRatio = Spring.DampingRatioLowBouncy,
                            stiffness = Spring.StiffnessLow
                        )
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(48.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            modifier = Modifier
                                .semantics {
                                    contentDescription = context.getString(
                                        isShowColorPicker.judge(
                                            R.string.dialog_category_close_colour_swatches,
                                            R.string.dialog_category_expand_colour_swatches
                                        )
                                    )
                                    role = Role.Button
                                },
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_category_pink),
                                contentDescription = null,
                                modifier = Modifier
                                    .width(24.dp)
                                    .height(24.dp),
                                tint = colorResource(getCategoryColor(selectedColorIndex))
                            )
                            val icon = if (isShowColorPicker) {
                                R.drawable.ic_arrow_drop_up
                            } else {
                                R.drawable.ic_arrow_drop_down
                            }

                            HoverProofIconButton(
                                modifier = Modifier.size(24.dp),
                                onClick = { isShowColorPicker = !isShowColorPicker }
                            ) {
                                Icon(
                                    painter = painterResource(id = icon),
                                    contentDescription = isShowColorPicker.judge(
                                        R.string.dialog_category_close_colour_swatches,
                                        R.string.dialog_category_expand_colour_swatches
                                    ).stringRes(),
                                    modifier = Modifier.align(alignment = Alignment.CenterVertically)
                                )
                            }

                            Spacer(modifier = Modifier.width(16.dp))
                        }
                        Row(modifier = Modifier.align(alignment = Alignment.CenterVertically)) {
                            BasicTextField(
                                value = nameFieldValue,
                                onValueChange = { newText ->
                                    // 限制最大输入长度为50个字符
                                    //切换语言时，会导致这里变为true 会导致误判
                                    Logger.i(TAG, "onValueChange, content change")
                                    isOnChanged = true
                                    categoryName = newText.text
                                    nameFieldValue = newText
                                    isMaximumCharacter = newText.text.length > 50
                                },
                                singleLine = true,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .align(Alignment.CenterVertically)
                                    .focusRequester(mainFocus)
                                    .onFocusChanged { focusState ->
                                        // 监听焦点
                                        if (hasFocus && !focusState.isFocused) {
                                            // 失去焦点，键盘即将收回
                                            isKeyboardReallyOpen=false
                                        }
                                        hasFocus = focusState.isFocused
                                    },
                                decorationBox = { innerTextField ->
                                    Column(modifier = Modifier.align(Alignment.CenterVertically)) {
                                        Box(
                                            modifier = Modifier.fillMaxWidth()// 为下划线留出空间
                                        ) {
                                            if (categoryName.isEmpty()) {
                                                Text(
                                                    text = stringResource(R.string.category_name),
                                                    color = colorResource(R.color.text_category_placeholder),
                                                    fontSize = 16.sp,
                                                    lineHeight = with(density) { 22.dp.toSp() },
                                                    overflow = TextOverflow.Ellipsis,
                                                )
                                            }
                                            innerTextField()
                                        }
                                    }
                                },
                                cursorBrush = SolidColor(CursorColor),
                                textStyle = LocalTextStyle.current.copy(
                                    color = colorResource(R.color.text_title),
                                    fontSize = 16.sp,
                                    lineHeight = with(density) { 22.dp.toSp() },
                                ),
                                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                                keyboardActions = KeyboardActions(
                                    onDone = {
                                        focusManager.clearFocus()
                                        isKeyboardReallyOpen=false
                                    }
                                ),
                            )
                        }
                    }

                    HorizontalDivider(
                        color = if (isRepeated && isOnChanged) colorResource(R.color.text_field_border_error) else divider,
                        thickness = 2.dp,
                        modifier = Modifier.padding(start = 64.dp)
                    )

                    Spacer(Modifier.height(1.dp))

                    Column(modifier = Modifier.fillMaxWidth()) {
                        // 判断是否需要显示错误提示
                        val showErrorTip = (isRepeated && isOnChanged) || isMaximumCharacter

                        // 更新 isExpandedTip 状态
                        isExpandedTip = showErrorTip

                        if (isMaximumCharacter) {
                            errorTip = context.getString(R.string.category_name_maximum_character)
                        }

                        // 显示错误提示
                        if (showErrorTip) {
                            Text(
                                text = errorTip,
                                color = colorResource(R.color.text_field_border_error),
                                fontSize = 14.sp,
                                lineHeight = with(density) { 16.dp.toSp() },
                                modifier = Modifier.padding(start = 64.dp, top = 6.dp)
                            )
                        }

                        if (isShowColorPicker) {
                            ColorPicker(
                                selectedIndex = selectedColorIndex,
                                onColorSelectedIndex = { index ->
                                    Logger.i(TAG, "onColorSelectedIndex, index:$index")
                                    isOnChanged = true
                                    selectedColorIndex = index
                                },
                            )
                        }
                    }
                }
            },
            actions = {
                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ){
                    TclTextButton(
                        onClick = onDismiss,
                        contentColor = colorResource(id = R.color.home_title_color),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = stringResource(R.string.category_cancel),
                            color = colorResource(R.color.btn_new_category_cancel),
                            fontSize = 16.sp
                        )
                    }

                    Spacer(Modifier.width(8.dp))

                    TclTextButton(
                        enabled = isEnabled,
                        onClick = {onConfirm(clearCategory, selectedColorIndex)},
                        contentColor = colorResource(id = R.color.home_title_color),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = stringResource(
                                if (isNewCategory) R.string.category_add else R.string.confirm
                            ),
                            color = if (isEnabled)
                                colorResource(R.color.btn_new_category_cancel)
                            else
                                colorResource(R.color.btn_new_category_create),
                            fontSize = 16.sp
                        )
                    }
                }
            },
        )
    }

}

//@Composable
//fun rememberKeyboardState(): State<Boolean> {
//    val isOpen = rememberSaveable { mutableStateOf(false) }
//    // 根据设备类型和键盘状态计算距离底部的间距
//    val bottomPadding = WindowInsets.ime.asPaddingValues().calculateBottomPadding()
//
//    LaunchedEffect(bottomPadding) {
//        isOpen.value = bottomPadding > 0.dp
//    }
//    Logger.v(TAG, "rememberKeyboardState, bottomPadding:$bottomPadding, isOpen:$isOpen")
//    return isOpen
//}


@Composable
fun rememberKeyboardState(): State<Boolean> {
    val isKeyboardOpen = rememberSaveable { mutableStateOf(false) }
    val imeInsets = WindowInsets.ime
    val density = LocalDensity.current
    val view = LocalView.current
    // 用 snapshotFlow 监听 insets 变化
    LaunchedEffect(imeInsets, density) {
        snapshotFlow {
            // 直接取 IME 的 bottom 距离
            with(density) {
                imeInsets.getBottom(density).toDp()
            }
        }
            .distinctUntilChanged()
            .collectLatest { bottom ->
                isKeyboardOpen.value = bottom > 0.dp
                Logger.v(TAG, "rememberKeyboardState, bottom:$bottom, isKeyboardOpen:${isKeyboardOpen.value}")
            }
    }
    return isKeyboardOpen
}

// 移动笔记到分类弹窗
@SuppressLint("DesignSystem", "UnusedBoxWithConstraintsScope")
@Composable
private fun MoveNoteToCategoryDialog(
    notesToMove: List<DialogNote>,
    dialogController: CategoryDialogController,
    inSameCategory: Boolean, //选中的note是否处于同一个分类
    categoryId: Long,
    categories: List<DialogCategory>,
    onMoveToCategory: (Long) -> Unit,
    onDismiss: () -> Unit
) {
    val density = LocalDensity.current

    val configuration = LocalConfiguration.current.screenHeightDp
    val maxHeight = configuration - 120
    val maxBoxHeight = maxHeight - 88
    Logger.v(TAG, "MoveNoteToCategoryDialog, configuration:$configuration")
    val hasNavigationBar = isButtonNavigation()
    val navigationBarHeight = getNavigationBarHeight()

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        ),
    ) {
        BoxWithConstraints(
            modifier = Modifier
                .fillMaxSize()
                .clickable { onDismiss() } // 点击背景关闭对话框
                .padding(
                    bottom = if (hasNavigationBar) navigationBarHeight + 16.dp else 16.dp
                )
        ) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .width(globalDialogWidth())
                    .heightIn(188.dp, maxHeight.dp)
                    .clip(RoundedCornerShape(20.dp))
                    .background(colorResource(R.color.bg_dialog))
                    .pointerInput(Unit) {
                        detectTapGestures { /* 空实现，阻止事件传播 */ }
                    }
                    .padding(start = 12.dp, end = 12.dp, bottom = 16.dp, top = 24.dp)
            ) {

                Column {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .heightIn(min = 0.dp, max = maxBoxHeight.dp)
                            .padding(bottom = 16.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.choose_category),
                            fontSize = with(density) { 20.dp.toSp() },
                            fontWeight = FontWeight.Medium,
                            color = colorResource(R.color.dialog_title),
                            modifier = Modifier.padding(start = 12.dp)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        val context = LocalContext.current
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f, fill = false) // 填满剩余空间、能滚动
                        ) {
                            items(categories) { category ->
                                var tip = stringResource(R.string.category_moveto_already)
                                if (category.categoryId != categoryId) {
                                    tip = context.getString(
                                        R.string.category_moveto_success_new,
                                        notesToMove.size.toString(),
                                        category.name
                                    )
                                }
                                CategoryItem(
                                    category = category,
                                    categoryId = categoryId,
                                    inSameCategory = inSameCategory,
                                    onClick = {
                                        if (category.categoryId != categoryId) {
                                            onMoveToCategory(category.categoryId)
                                        }
                                        ToastUtils.makeWithCancel(tip, Toast.LENGTH_SHORT)
                                    }
                                )
                            }
                        }

                    }
                    CategoryBottom(onClick = {
                        dialogController.showNewCategoryDialog(true)
                    })
                }
            }
        }
    }
}


@OptIn(ExperimentalFoundationApi::class)
@Composable
fun CategoryItem(
    category: DialogCategory,
    categoryId: Long,
    inSameCategory: Boolean,
    onClick: () -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(48.dp)
                .clip(RoundedCornerShape(8.dp))
                .combinedClickable(
                    role = Role.Button,
                    onClick = {
                        onClick()
                    }
                )
                .padding(horizontal = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {

            Icon(
                painter = painterResource(category.icon),
                contentDescription = category.name,
                modifier = Modifier
                    .size(24.dp),
                tint = getCategoryIconColor(category.colorIndex).colorRes()
            )

            Spacer(modifier = Modifier.width(12.dp))

            var textColor = colorResource(R.color.text_title)
            if (inSameCategory && category.categoryId == categoryId) {
                textColor = colorResource(R.color.text_category_list_selected)
            }

            Text(
                text = category.name,
                fontSize = 14.sp,
                color = textColor,
                modifier = Modifier.weight(1f),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Spacer(modifier = Modifier.width(19.dp))
            Text(
                text = category.noteCounts.toString(),
                fontSize = 14.sp,
                color = colorResource(R.color.text_summary)
            )
        }
    }
}


@Composable
private fun CategoryBottom(onClick: () -> Unit) {
    Row(
        modifier = Modifier
            .padding(horizontal = 12.dp)
            .fillMaxWidth()
            .height(44.dp)
            .clip(RoundedCornerShape(30.dp))
            .clickable(role = Role.Button) {
                onClick()
            }
            .background(
                colorResource(id = R.color.home_category_add_btn_bg)
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Text(
            text = stringResource(R.string.dialog_category_name_title),
            color = Color(0xFFFF9800),
            style = MaterialTheme.typography.bodyLarge
        )
    }
}


/**
 * 颜色选择器
 */
@SuppressLint("DesignSystem")
@Composable
internal fun ColorPicker(
    selectedIndex: Int,
    onColorSelectedIndex: (Int) -> Unit,
) {
    //获取颜色列表
    val colors = getCategoryColorArray()
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 1.dp, vertical = 15.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Logger.d("NewCategoryDialog", "selectedIndex:$selectedIndex")
        colors.forEachIndexed { index, color ->
            val curColor = colorResource(color)
            val contentDescription = when (index) {
                0 -> R.string.dialog_category_colour_pink.stringRes()
                1 -> R.string.dialog_category_colour_purple.stringRes()
                2 -> R.string.dialog_category_colour_bluish.stringRes()
                3 -> R.string.dialog_category_colour_green.stringRes()
                4 -> R.string.dialog_category_colour_yellow.stringRes()
                5 -> R.string.dialog_category_colour_orange.stringRes()
                else -> ""
            }

            CustomRadioButton(
                selected = selectedIndex == index,
                onClick = {
                    onColorSelectedIndex(index)
                    Logger.d(
                        "NewCategoryDialog",
                        "onClick(), index:$index, color:$color, curColor:$curColor"
                    )
                },
                color = curColor,
                modifier = Modifier
                    .size(24.dp)
                    .semantics {
                        contentDescription
                    }
            )
        }
    }
}

/**
 * 选中的Note是否属于同一个分类
 */
fun inSameCategory(noteCategories: List<DialogNote>): Pair<Boolean, Long> {
    // 获取第一个元素的categoryId作为基准
    val firstCategoryId = noteCategories.first().categoryId

    // 处理空列表和单元素列表的情况
    if (noteCategories.size <= 1) return Pair(true, firstCategoryId)

    // 检查后续所有元素的categoryId是否与基准一致
    return Pair(noteCategories.all { it.categoryId == firstCategoryId }, firstCategoryId)
}

private const val TAG = "CategoryDialogScreenManager"