package com.tcl.ai.note.handwritingtext.ui.edit

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.menu.EraserViewModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.px2dp
import com.tcl.ai.note.utils.toPx
import kotlin.math.roundToInt


/**
 * 橡皮擦按压指示器
 * 通过监听 SuniaDrawViewModel 的触摸位置状态实现实时跟踪
 */
@Composable
fun EraserPressIndicator(
    eraserViewModel: EraserViewModel = hiltViewModel(),
    menuBarViewModel: MenuBarViewModel = hiltViewModel(),
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel()
) {
    val density = LocalDensity.current

    // 获取菜单栏状态
    val menuBarState by menuBarViewModel.menuBarState.collectAsState()
    // 获取显示用的橡皮擦属性状态（不受缩放影响，用于弹框预览和按压指示器）
    val displayEraserPropState by suniaDrawViewModel.displayEraserPropState.collectAsState()
    // 获取当前触摸位置
    val currentTouchPosition by suniaDrawViewModel.currentTouchPositionState.collectAsState()

    // 判断是否处于橡皮擦模式且正在触摸
    // isPassiveEraserActive 按下按键触发的擦除
    val shouldShowIndicator = menuBarState.currentMenuType == MenuBar.BRUSH &&
            (menuBarState.isEraserActive || menuBarState.isPassiveEraserActive) && currentTouchPosition != null

    if (shouldShowIndicator) {
        currentTouchPosition?.let { touchPos ->
            // 现在直接显示sdk里的半径了，因为传参给sdk的地方时size就除以2了
            val indicatorRadius = displayEraserPropState.eraserRadius
            // 将指示器半径从dp转换为px（用于偏移计算）
            val indicatorRadiusInDp = indicatorRadius.toInt().px2dp.dp
            val indicatorRadiusInPx = (indicatorRadiusInDp).toPx

            Box(
                modifier = Modifier
                    .size(indicatorRadiusInDp * 2) // 大小=半径*2
                    .offset {
                        // 将触摸位置转换为偏移量，使指示器中心对齐触摸点
                        // 偏移量 = 触摸位置 - 半径（都是px单位）
                        IntOffset(
                            x = (touchPos.x - indicatorRadiusInPx).roundToInt(),
                            y = (touchPos.y - indicatorRadiusInPx).roundToInt()
                        )
                    }
                    .background(
                        color = TclTheme.colorScheme.eraserIndicatorBackground,
                        shape = CircleShape
                    )
                    .border(
                        width = 1.dp,
                        color = TclTheme.colorScheme.eraserIndicatorBorder,
                        shape = CircleShape
                    )
            )
        }
    }
}