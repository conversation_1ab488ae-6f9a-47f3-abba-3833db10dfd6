package com.tcl.ai.note.handwritingtext.ui.edit.container

import androidx.activity.compose.LocalActivity
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Density
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.events.PopupActionEvents
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.popup.ImportPopupComponent
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.popup.MorePopupComponent
import com.tcl.ai.note.handwritingtext.ui.richtext.state.EditableTitleNavigationBarState
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.DeleteDataDialog
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel
import com.tcl.ai.note.voicetotext.vm.RecordingViewModel
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogScreenManager
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.popup.RichTextBgSettingPopup
import com.tcl.ai.note.utils.toComposeColor

/**
 * 编辑页面弹框容器
 * 统一管理所有弹框的显示逻辑
 */
@Composable
fun TopBarPopupContainer(
    navigationBarState: EditableTitleNavigationBarState,
    onNavigationBarStateChanged: (EditableTitleNavigationBarState) -> Unit,
    popupActionEvents: PopupActionEvents,
    noteId: Long?,
    categoryId: Long,
    enableFingerDrawing: Boolean,
    richTextViewModel: RichTextViewModel2,
    audioToTextViewModel: AudioToTextViewModel,
    audios: List<EditorContent>,
    density: Density = LocalDensity.current
) {
    // 删除对话框状态
    var isDeleteDialogShow by remember { mutableStateOf(false) }

    var isShowBgSettingPopup by remember { mutableStateOf(false) }

    // 获取录音状态
    val recordingViewModel: RecordingViewModel = hiltViewModel()
    val recordingState by recordingViewModel.recordState.collectAsState()

    // 创建修改后的 popupActionEvents，包含删除处理
    val enhancedPopupActionEvents = popupActionEvents.copy(
        onDelete = {
            isDeleteDialogShow = true
        }
    )

    val richTextState by richTextViewModel.uiState.collectAsState()
    
    // 导入弹框
    ImportPopupComponent(
        isVisible = navigationBarState.showImportPopup,
        density = density,
        onDismiss = {
            onNavigationBarStateChanged(navigationBarState.copy(showImportPopup = false))
        },
        onRecordAudio = enhancedPopupActionEvents.onRecordAudio,
        onChoosePhoto = enhancedPopupActionEvents.onChoosePhoto,
        onTakePhoto = enhancedPopupActionEvents.onTakePhoto,
        isRecording = recordingState.isRecording
    )
    
    // 更多选项弹框
    MorePopupComponent(
        isVisible = navigationBarState.showMorePopup,
        noteId = noteId ?: 0L,
        categoryId = categoryId,
        enableFingerDrawing = enableFingerDrawing,
        density = density,
        onDismiss = {
            onNavigationBarStateChanged(navigationBarState.copy(showMorePopup = false))
        },
        onDelete = enhancedPopupActionEvents.onDelete,
        onPageSettings = {
            isShowBgSettingPopup = true
        },
        onFingerDrawing = enhancedPopupActionEvents.onFingerDrawing
    )

    val activity = LocalActivity.current

    // 删除确认对话框
    if (isDeleteDialogShow) {
        DeleteDataDialog(
            text = stringResource(R.string.dialog_title_delete_one_items),
            onDelete = {
                if (noteId != null && noteId > 0) {
                    isDeleteDialogShow = false
                    richTextViewModel.viewModelScope.launchIO {
                        audios.filterIsInstance<EditorContent.AudioBlock>().forEach { audioBlock ->
                            audioToTextViewModel.deleteAudioFile(audioBlock.audioPath)
                            richTextViewModel.onDeleteAudio(audioBlock.audioPath)
                        }
                        richTextViewModel.deleteOneNote(noteId)
                    }
                    activity?.finish()
                }
            },
            onDismiss = {
                isDeleteDialogShow = false
            }
        )
    }
    if(isShowBgSettingPopup){
        RichTextBgSettingPopup(
            selBgMode = richTextState.bgMode,
            selBgColor = richTextState.bgColor.toComposeColor(),
            onChange = { skin ->
                richTextViewModel.onSkinStyleChanaged(skin.bgMode,skin.color)
            },
            onDismiss = {
                isShowBgSettingPopup = false
            }
        )
    }
    
    // 分类对话框管理器
    CategoryDialogScreenManager(screenKey = "EditScreen")
}