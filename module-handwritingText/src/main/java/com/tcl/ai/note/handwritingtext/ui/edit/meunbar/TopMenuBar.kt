package com.tcl.ai.note.handwritingtext.ui.edit.meunbar

import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.toArgb
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.group.TableMenuUndoRedoGroup
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.group.TabletMenuColorGroup
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.group.TabletMenuToolGroupV1
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.handwritingtext.vm.TextAndDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.menu.EraserViewModel
import com.tcl.ai.note.handwritingtext.vm.menu.syncToSdk
import com.tcl.ai.note.handwritingtext.vm.pen.PenToolbarViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextToolBarViewModel
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.widget.HorizontalLine


/**
 * 平板编辑页的菜单栏（位于页面顶部）
 */
@Composable
fun TopMenuBar(
    modifier: Modifier = Modifier,
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel(),
    menuBarViewModel: MenuBarViewModel = hiltViewModel(),
    penToolbarViewModel: PenToolbarViewModel = hiltViewModel(),
    textAndDrawViewModel: TextAndDrawViewModel = hiltViewModel(),
    richTextViewModel: RichTextToolBarViewModel = hiltViewModel(),
    eraserViewModel: EraserViewModel = hiltViewModel()
) {
    val scrollState = rememberScrollState()
    val dimens = getGlobalDimens()
    val menuBarState by menuBarViewModel.menuBarState.collectAsState()
    val textToolState by richTextViewModel.toolBarState.collectAsState()
    val editMode by textAndDrawViewModel.editModeState.collectAsState()
    val canDrawUndo by textAndDrawViewModel.canUndoState.collectAsState()
    val canDrawRedo by textAndDrawViewModel.canRedoState.collectAsState()
    val enableFingerDrawing by suniaDrawViewModel.enableFingerDrawingState.collectAsState()
    var popupContent: (@Composable () -> Unit)? by remember { mutableStateOf(null) }


    // 清理 popupContent，避免内存泄露
    DisposableEffect(Unit) {
        onDispose {
            popupContent = null
            MenuBarItem.cleanup()
        }
    }


    if(isTablet) {
        Box {
            HorizontalLine(modifier = Modifier.align(alignment = Alignment.TopCenter))
            Row(
                modifier = modifier
                    .fillMaxWidth()
                    .height(dimens.menuBarHeight)
                    .horizontalScroll(scrollState),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                TabletMenuToolGroupV1(
                    eraserViewModel = eraserViewModel,
                    menuBarState= menuBarState,
                    setPopupComposable= { newPopupComposable ->
                        popupContent =newPopupComposable
                    },
                    onOpenEraser = {
                        Logger.d("TopMenuBar","onOpenEraser")
                        menuBarViewModel.onEraserClick()
                        textAndDrawViewModel.editMode = EditMode.DRAW
                        // 点击时要同步橡皮擦设置到SDK，这样按压指示器才能使用最新值显示
                        eraserViewModel.syncToSdk(suniaDrawViewModel)
                    },
                    onOpenKeyboard = {
                        Logger.d("TopMenuBar","onOpenKeyboard")
                        menuBarViewModel.switchToTextEditMode()
                        textAndDrawViewModel.editMode = EditMode.TEXT
                    },
                    onOpenBrush = {
                        Logger.d("TopMenuBar","onOpenBrush")
                        menuBarViewModel.switchToDrawingMode()
                        textAndDrawViewModel.editMode = EditMode.DRAW
                        suniaDrawViewModel.switchBrush(suniaDrawViewModel.penPropState.value)
                    }
                )

                TableMenuUndoRedoGroup(
                    menuBarState = menuBarState.copy(
                        canUndo = canDrawUndo,
                        canRedo = canDrawRedo,
                    ),
                    onUndoClick = {
                        textAndDrawViewModel.undo()
                    },
                    onRedoClick = {
                        textAndDrawViewModel.redo()
                    }
                )

                TabletMenuColorGroup(
                    isDrawMode = editMode == EditMode.DRAW,
                    setPopupComposable= { newPopupComposable ->
                        popupContent =newPopupComposable
                    },
                    onConfirm ={ penColor ->
                        suniaDrawViewModel.changePenColor(penColor.color.toArgb(),penColor.alpha)
                        penToolbarViewModel.updateSelectedPen(color = penColor.color, alpha = penColor.alpha)
                    },
                    onChangeDrawMode = {
                        menuBarViewModel.switchToDrawingMode()
                        textAndDrawViewModel.editMode = EditMode.DRAW
                    }
                )

                /*EnableFingerDrawing(enableFingerDrawing){
                    suniaDrawViewModel.enableFingerDrawing = !enableFingerDrawing
                }*/
                //TableMenuAiGroup(menuBarState= menuBarState)
            }
            HorizontalLine(modifier = Modifier.align(alignment = Alignment.BottomCenter))
        }

        popupContent?.let { it() }
    }
}