package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.handler

import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.graphics.toArgb
import com.sunia.penengine.sdk.operate.touch.PenProp
import com.tcl.ai.note.handwritingtext.bean.ColorSource
import com.tcl.ai.note.handwritingtext.bean.toPenType
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.bean.PenColor
import com.tcl.ai.note.handwritingtext.state.MenuEvent
import com.tcl.ai.note.handwritingtext.state.MenuPopupState
import com.tcl.ai.note.handwritingtext.vm.MenuBarUiState
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.menu.ColorGroupViewModel
import com.tcl.ai.note.handwritingtext.vm.menu.EraserViewModel
import com.tcl.ai.note.handwritingtext.vm.pen.PenToolbarViewModel



/**
 * 平板菜单工具事件处理器
 * 
 * 将用户事件转换为状态变化和副作用执行
 */
class TabletMenuEventHandler(
    // ViewModels - 业务逻辑处理
    private val penToolbarViewModel: PenToolbarViewModel,
    private val suniaDrawViewModel: SuniaDrawViewModel,
    private val colorGroupViewModel: ColorGroupViewModel,
    private val menuBarViewModel: MenuBarViewModel,
    private val eraserViewModel: EraserViewModel,
    
    // UI依赖
    private val focusManager: FocusManager,
    
    // 回调函数 - 通知上层组件
    private val callbacks: MenuCallbacks
) {

    companion object {
        // 防重点击间隔半秒钟
        private const val POPUP_DEBOUNCE_TIME = 200L
    }

    /**
     * 事件处理主入口
     * 
     * @param event 用户触发的事件
     * @param currentState 当前弹窗状态
     * @param menuBarState 菜单栏状态
     * @param lastDismissTime 上次关闭弹窗的时间
     * @return 新的弹窗状态
     */
    fun handleEvent(
        event: MenuEvent,
        currentState: MenuPopupState,
        menuBarState: MenuBarUiState,
        lastDismissTime: Long
    ): MenuPopupState = when (event) {
        MenuEvent.KeyboardClick -> handleKeyboardClick()
        is MenuEvent.EraserClick -> handleEraserClick(event.menuBarItem, currentState, menuBarState, lastDismissTime)
        is MenuEvent.BrushClick -> handleBrushClick(event.menuBarItem, currentState, menuBarState, lastDismissTime)
        MenuEvent.OpenColorPicker -> handleColorPickerOpen(currentState)
        MenuEvent.DismissPopup -> MenuPopupState.None
    }


    // ==================== 笔刷相关操作 ====================

    /**
     * 初始化默认笔刷设置
     * 根据用户偏好设置默认笔刷属性
     */
    fun initializeDefaultBrush() {
        val selectedPen = penToolbarViewModel.selectedPen
        if (penToolbarViewModel.isInit) {
            val penProp = PenProp().apply {
                penType = selectedPen.toPenType().value
                penColor = selectedPen.color.toArgb()
                penSize = selectedPen.width.toFloat()
                penAlpha = selectedPen.alpha
            }
            suniaDrawViewModel.switchBrush(penProp)
        }
    }

    /**
     * 切换笔刷类型
     */
    fun switchBrush(penProp: com.sunia.penengine.sdk.operate.touch.PenProp) {
        suniaDrawViewModel.switchBrush(penProp)
        colorGroupViewModel.cancelSelectColor()
    }

    /**
     * 改变笔刷大小
     */
    fun changeBrushSize(brushSize: Float) {
        suniaDrawViewModel.changePenSize(brushSize)
    }

    /**
     * 改变笔刷颜色
     */
    fun changePenColor(penColor: PenColor) {
        suniaDrawViewModel.changePenColor(penColor.color.toArgb(), penColor.alpha)
        colorGroupViewModel.cancelSelectColor()
    }

    /**
     * 确认颜色选择 - 保存到用户偏好
     */
    fun confirmColorSelection(penColor: PenColor) {
        // 应用颜色到绘图引擎
        suniaDrawViewModel.changePenColor(penColor.color.toArgb(), penColor.alpha)
        
        // 保存用户颜色偏好
        with(penToolbarViewModel) {
            collectColor(penColor)
            updateSelectedPen(
                color = penColor.color,
                alpha = penColor.alpha,
            )
        }
        
        // 取消当前颜色选择状态
        colorGroupViewModel.cancelSelectColor()
    }

    // ==================== 以下方法不开放，外部不用关注 ====================

    /**
     * 处理键盘点击 - 切换到文本编辑模式
     */
    private fun handleKeyboardClick(): MenuPopupState {
        menuBarViewModel.switchToTextEditMode()
        callbacks.onOpenKeyboard()
        return MenuPopupState.None
    }

    /**
     * 处理橡皮擦点击
     */
    private fun handleEraserClick(
        menuBarItem: MenuBarItem,
        currentState: MenuPopupState,
        menuBarState: MenuBarUiState,
        lastDismissTime: Long
    ): MenuPopupState {
        val isEraserActive = menuBarState.isEraserActive && menuBarState.currentMenuType == MenuBar.BRUSH

        return when {
            isEraserActive -> showPopupIfAllowed(
                currentState = currentState,
                targetState = MenuPopupState.EraserTool(menuBarItem),
                lastDismissTime = lastDismissTime
            )
            else -> {
                activateEraserTool()
                MenuPopupState.None
            }
        }
    }

    /**
     * 处理笔刷点击
     */
    private fun handleBrushClick(
        menuBarItem: MenuBarItem,
        currentState: MenuPopupState,
        menuBarState: MenuBarUiState,
        lastDismissTime: Long
    ): MenuPopupState {
        val isBrushActive = with(menuBarState) {
            isBrushActive && currentMenuType == MenuBar.BRUSH && !isEraserActive
        }

        return when {
            isBrushActive -> showPopupIfAllowed(
                currentState = currentState,
                targetState = MenuPopupState.PenTool(menuBarItem),
                lastDismissTime = lastDismissTime
            )
            else -> {
                activateBrushTool()
                MenuPopupState.None
            }
        }
    }

    /**
     * 处理颜色选择器打开
     */
    private fun handleColorPickerOpen(currentState: MenuPopupState): MenuPopupState =
        if (currentState is MenuPopupState.PenTool) {
            MenuPopupState.ColorPalette(currentState.menuBarItem)
        } else {
            currentState
        }

    // ==================== 工具激活方法 ====================

    /**
     * 激活橡皮擦工具
     */
    private fun activateEraserTool() {
        focusManager.clearFocus()
        menuBarViewModel.onEraserClick()

        // 应用用户之前的橡皮擦设置
        with(eraserViewModel) {
            suniaDrawViewModel.switchEraser(currentEraserMode, eraserSize.toFloat())
        }

        callbacks.onOpenEraser()
    }

    /**
     * 激活笔刷工具
     */
    fun activateBrushTool() {
        menuBarViewModel.onBrushClick()
        callbacks.onOpenBrush()
    }

    // ==================== 弹窗控制辅助方法 ====================

    /**
     * 根据防抖逻辑决定是否显示弹窗
     */
    private fun showPopupIfAllowed(
        currentState: MenuPopupState,
        targetState: MenuPopupState,
        lastDismissTime: Long
    ): MenuPopupState = when {
        shouldShowPopup(lastDismissTime) && !currentState.isSameType(targetState) -> targetState
        else -> MenuPopupState.None
    }

    /**
     * 防抖检查 - 避免频繁开关弹窗
     */
    private fun shouldShowPopup(lastDismissTime: Long): Boolean =
        System.currentTimeMillis() - lastDismissTime > POPUP_DEBOUNCE_TIME

    /**
     * 检查两个状态是否为同一类型
     */
    private fun MenuPopupState.isSameType(other: MenuPopupState): Boolean = when {
        this is MenuPopupState.EraserTool && other is MenuPopupState.EraserTool -> true
        this is MenuPopupState.PenTool && other is MenuPopupState.PenTool -> true
        this is MenuPopupState.ColorPalette && other is MenuPopupState.ColorPalette -> true
        else -> false
    }

} 