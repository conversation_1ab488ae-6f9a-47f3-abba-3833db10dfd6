package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.provider

import com.tcl.ai.note.base.R
import com.tcl.ai.note.controller.isAIServiceEnable
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.bean.menHullIcon
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.data.MenuBarDependencies
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.handler.MenuBarEventHandler
import com.tcl.ai.note.handwritingtext.R as HR

/**
 * 菜单栏数据提供者
 * 负责根据当前状态生成菜单项数据源
 *
 * 优化后使用依赖组合，减少参数数量
 */
open class MenuBarDataProvider(
    private val eventHandler: MenuBarEventHandler,
    private val dependencies: MenuBarDependencies
) {

    /**
     * 获取绘图模式下的菜单项
     */
    private fun getDrawModeMenuItems(
        menuBarState: MenuBarUiState,
        toolBarState: RichTextToolBarState,
        canDrawUndo: Boolean,
        canDrawRedo: Boolean
    ): List<MenuBarItem> {
        val selectedPen = dependencies.penToolbarViewModel.selectedPen

        return listOf(
            // 键盘切换icon（切换到TEXT模式）
            MenuBarItem.Keyboard.apply {
                isChecked = false
                onClick = { _ -> eventHandler.handleKeyboardSwitchFromDraw() }
                iconRes = HR.drawable.ic_menu_keyboard_nor
                descriptionRes = R.string.edit_bottom_menu_keyboard
            },
            // 笔刷
            MenuBarItem.Brush.apply {
                onClick = eventHandler::handleBrushClick
                isChecked =
                    menuBarState.currentMenuType == MenuBar.BRUSH && menuBarState.isBrushActive
                iconRes = selectedPen.menHullIcon()
            },
            // 笔迹美化
            MenuBarItem.Beautify.apply {
                onClick = { _ -> eventHandler.handleBeautifyClick() }
            },
            // 橡皮擦
            MenuBarItem.Eraser.apply {
                isChecked = menuBarState.isEraserActive
                onClick = eventHandler::handleEraserClick
            },
            // 撤销
            MenuBarItem.Undo.apply {
                isEnabled = canDrawUndo
                onClick = { _ -> eventHandler.handleDrawUndo() }
            },
            // 重做
            MenuBarItem.Redo.apply {
                isEnabled = canDrawRedo
                onClick = { _ -> eventHandler.handleDrawRedo() }
            }
        )
    }

    /**
     * 获取文本模式下的菜单项
     */
    private fun getTextModeMenuItems(
        toolBarState: RichTextToolBarState
    ): List<MenuBarItem> {
        return listOfNotNull(
            // 画笔切换icon（切换到DRAW模式）
            MenuBarItem.Keyboard.apply {
                isChecked = false
                onClick = { _ -> eventHandler.handleBrushSwitchFromText() }
                iconRes = HR.drawable.ic_menu_handwriting
                descriptionRes = R.string.edit_bottom_menu_handwriting
            },
            // 待办
            MenuBarItem.TODO.apply {
                isChecked = toolBarState.isTodoActive
                onClick = { _ -> eventHandler.handleTodoClick() }
            },
            // 文本样式
            MenuBarItem.TextStyle.apply {
                onClick = { menuBarItem -> eventHandler.handleTextStyleClick(menuBarItem) }
            },
            // 撤销
            MenuBarItem.Undo.apply {
                isEnabled = toolBarState.isCanUndo
                onClick = { _ -> eventHandler.handleTextUndo() }
            },
            // 重做
            MenuBarItem.Redo.apply {
                isEnabled = toolBarState.isCanRedo
                onClick = { _ -> eventHandler.handleTextRedo() }
            },
            if (isAIServiceEnable){
                // AI工具 可用才显示
                MenuBarItem.AI().apply {
                    iconRes = HR.drawable.ic_menu_ai
                    onClick = { _ -> eventHandler.handleAiClick() }
                }
            }else{
                null
            }
        )
    }

    /**
     * 根据当前编辑模式获取对应的菜单项
     */
    fun getMenuItems(
        editMode: EditMode,
        menuBarState: MenuBarUiState,
        toolBarState: RichTextToolBarState,
        canDrawUndo: Boolean,
        canDrawRedo: Boolean
    ): List<MenuBarItem> {
        return when (editMode) {
            EditMode.DRAW -> getDrawModeMenuItems(
                menuBarState,
                toolBarState,
                canDrawUndo,
                canDrawRedo
            )

            else -> getTextModeMenuItems(toolBarState) // 注意这里还包含了 Preview 的情况
        }
    }
}

/**
 * 菜单栏状态数据类（简化版，只包含UI相关的状态）
 */
data class MenuBarUiState(
    val currentMenuType: MenuBar = MenuBar.KEYBOARD,
    val isKeyboardActive: Boolean = false,
    val isBrushActive: Boolean = false,
    val isEraserActive: Boolean = false
)

/**
 * 富文本工具栏状态数据类（简化版，只包含UI相关的状态）
 */
data class RichTextToolBarState(
    val isTodoActive: Boolean = false,
    val isCanUndo: Boolean = false,
    val isCanRedo: Boolean = false
)
