package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.renderer

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.bean.PenStyle
import com.tcl.ai.note.handwritingtext.bean.menHullIcon
import com.tcl.ai.note.handwritingtext.bean.menNibIcon
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.ui.pen.PenTurnColorButton
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.widget.DelayedBackgroundIconButton
import com.tcl.ai.note.widget.DelayedBackgroundImageButton
import com.tcl.ai.note.widget.HorizontalLine
import com.tcl.ai.note.widget.VerticalLine


// 绘制具体的菜单栏UI（手机端）
@Composable
fun MenuBarUIRenderer(
    modifier: Modifier,
    menuToolItems: List<MenuBarItem>,
    selectedPen: PenStyle,
    editMode: EditMode
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .height(TclTheme.dimens.menuBarHeight)
            .background(TclTheme.colorScheme.secondaryBackground)
            .clickable(
                interactionSource = null,
                indication = null,  // 不显示点击效果
                onClick = { /* 拦截点击但不做任何处理 */ }
            )
    ) {
        // 只在 DRAW 模式下显示顶部线，因为其他模式下 AdaptiveToolbarContainer 已经提供了
        if (editMode == EditMode.DRAW) {
            HorizontalLine()
        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            menuToolItems.forEachIndexed { index, item ->
                val modifierPos = Modifier.onGloballyPositioned { layoutCoordinates ->
                    val position = layoutCoordinates.localToWindow(Offset.Zero)
                    item.position = position
                }

                when (item.menuType) {
                    MenuBar.BRUSH -> {
                        // 笔刷icon样式会变
                        PenTurnColorButton(
                            modifier = modifierPos,
                            btnSize = item.btnSize,
                            iconSize = TclTheme.dimens.iconSize,
                            isChecked = item.isChecked,
                            onClick = { item.onClick.invoke(item) },
                            hullIconRes = selectedPen.menHullIcon(),
                            nibIconRes = selectedPen.menNibIcon(),
                            contentDescription = stringResource(item.descriptionRes),
                            penColor = selectedPen.color,
                        )
                    }
                    MenuBar.AI -> {
                        // AI按钮需要保持图片颜色
                        DelayedBackgroundImageButton(
                            modifier = modifierPos,
                            btnSize = item.btnSize,
                            iconSize = TclTheme.dimens.iconSize,
                            painter = painterResource(item.iconRes!!),
                            isChecked = item.isChecked,
                            enabled = item.isEnabled,
                            contentDescription = stringResource(item.descriptionRes),
                            onClick = { item.onClick.invoke(item) }
                        )
                    }
                    else -> {
                        DelayedBackgroundIconButton(
                            modifier = modifierPos,
                            btnSize = item.btnSize,
                            iconSize = TclTheme.dimens.iconSize,
                            painter = painterResource(item.iconRes!!),
                            isChecked = item.isChecked,
                            enabled = item.isEnabled,
                            contentDescription = stringResource(item.descriptionRes),
                            onClick = { item.onClick.invoke(item) }
                        )
                    }
                }

                if (item.menuType == MenuBar.KEYBOARD) {
                    VerticalLine(modifier = Modifier.height(20.dp))
                }
            }
        }
        HorizontalLine()
    }
}

// 绘制具体的菜单栏UI（手机端）
@Composable
fun JournalMenuBarUIRenderer(
    modifier: Modifier,
    menuToolItems: List<MenuBarItem>,
    selectedPen: PenStyle,
    editMode: EditMode,
    darkTheme: Boolean = isSystemInDarkTheme()
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .height(TclTheme.dimens.menuBarHeight)
            .background(TclTheme.colorScheme.secondaryBackground)
            .clickable(
                interactionSource = null,
                indication = null,  // 不显示点击效果
                onClick = { /* 拦截点击但不做任何处理 */ }
            )
    ) {
        // 只在 DRAW 模式下显示顶部线，因为其他模式下 AdaptiveToolbarContainer 已经提供了
        if (editMode == EditMode.DRAW) {
            HorizontalLine(
                color = darkTheme.judge(
                    Color(0x1AFFFFFF),
                    Color(0x0D000000)
                )
            )
        }
        if (DataStoreParam.IS_IFA) {
            JournalMenuBarContentForIFA(
                menuToolItems = menuToolItems,
                selectedPen = selectedPen,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            )
        } else {
            JournalMenuBarContent(
                menuToolItems = menuToolItems,
                selectedPen = selectedPen,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
            )
        }
        HorizontalLine(
            color = darkTheme.judge(
                Color(0x1AFFFFFF),
                Color(0x0D000000)
            )
        )
    }
}

@Composable
fun JournalMenuBarContentForIFA(
    menuToolItems: List<MenuBarItem>,
    selectedPen: PenStyle,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceAround
    ) {
        menuToolItems.forEachIndexed { _, item ->
            val modifierPos = Modifier.onGloballyPositioned { layoutCoordinates ->
                val position = layoutCoordinates.localToWindow(Offset.Zero)
                item.position = position
            }

            when (item.menuType) {
                MenuBar.BRUSH -> {
                    // 笔刷icon样式会变
                    PenTurnColorButton(
                        modifier = modifierPos,
                        isDarkTheme = false,
                        btnSize = item.btnSize,
                        iconSize = TclTheme.dimens.iconSize,
                        isChecked = item.isChecked,
                        onClick = { item.onClick.invoke(item) },
                        hullIconRes = selectedPen.menHullIcon(),
                        nibIconRes = selectedPen.menNibIcon(),
                        contentDescription = stringResource(item.descriptionRes),
                        penColor = selectedPen.color,
                    )
                }

                MenuBar.AI -> {
                    // AI按钮需要保持图片颜色
                    DelayedBackgroundImageButton(
                        modifier = modifierPos,
                        btnSize = item.btnSize,
                        iconSize = TclTheme.dimens.iconSize,
                        painter = painterResource(item.iconRes!!),
                        isChecked = item.isChecked,
                        enabled = item.isEnabled,
                        contentDescription = stringResource(item.descriptionRes),
                        onClick = { item.onClick.invoke(item) }
                    )
                }

                else -> {
                    DelayedBackgroundIconButton(
                        modifier = modifierPos,
                        btnSize = item.btnSize,
                        iconSize = TclTheme.dimens.iconSize,
                        painter = painterResource(item.iconRes!!),
                        isChecked = item.isChecked,
                        enabled = item.isEnabled,
                        contentDescription = stringResource(item.descriptionRes),
                        onClick = { item.onClick.invoke(item) }
                    )
                }
            }
        }
    }
}

@Composable
fun JournalMenuBarContent(
    menuToolItems: List<MenuBarItem>,
    selectedPen: PenStyle,
    modifier: Modifier = Modifier,
    darkTheme: Boolean = isSystemInDarkTheme()
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        val menuToolItemsKeyboard = menuToolItems.filter { it.menuType == MenuBar.KEYBOARD }
        if (menuToolItemsKeyboard.isNotEmpty()) {
            Row(
                modifier = Modifier
                    .padding(horizontal = 16.dp),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                menuToolItemsKeyboard.forEachIndexed { _, item ->
                    val modifierPos = Modifier.onGloballyPositioned { layoutCoordinates ->
                        val position = layoutCoordinates.localToWindow(Offset.Zero)
                        item.position = position
                    }

                    DelayedBackgroundIconButton(
                        modifier = modifierPos,
                        btnSize = item.btnSize,
                        iconSize = TclTheme.dimens.iconSize,
                        painter = painterResource(item.iconRes!!),
                        isChecked = item.isChecked,
                        enabled = item.isEnabled,
                        contentDescription = stringResource(item.descriptionRes),
                        onClick = { item.onClick.invoke(item) }
                    )
                }
            }
            VerticalLine(
                modifier = Modifier.height(20.dp),
                color = darkTheme.judge(
                    Color(0x1AFFFFFF),
                    Color(0x1A000000)
                )
            )
        }

        Row(
            modifier = Modifier.fillMaxWidth()
                .weight(1f)
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            menuToolItems.filter { it.menuType != MenuBar.KEYBOARD }.forEachIndexed { _, item ->
                val modifierPos = Modifier.onGloballyPositioned { layoutCoordinates ->
                    val position = layoutCoordinates.localToWindow(Offset.Zero)
                    item.position = position
                }

                when (item.menuType) {
                    MenuBar.BRUSH -> {
                        // 笔刷icon样式会变
                        PenTurnColorButton(
                            modifier = modifierPos,
                            btnSize = item.btnSize,
                            iconSize = TclTheme.dimens.iconSize,
                            isChecked = item.isChecked,
                            onClick = { item.onClick.invoke(item) },
                            hullIconRes = selectedPen.menHullIcon(),
                            nibIconRes = selectedPen.menNibIcon(),
                            contentDescription = stringResource(item.descriptionRes),
                            penColor = selectedPen.color,
                        )
                    }

                    MenuBar.AI -> {
                        // AI按钮需要保持图片颜色
                        DelayedBackgroundImageButton(
                            modifier = modifierPos,
                            btnSize = item.btnSize,
                            iconSize = TclTheme.dimens.iconSize,
                            painter = painterResource(item.iconRes!!),
                            isChecked = item.isChecked,
                            enabled = item.isEnabled,
                            contentDescription = stringResource(item.descriptionRes),
                            onClick = { item.onClick.invoke(item) }
                        )
                    }

                    else -> {
                        DelayedBackgroundIconButton(
                            modifier = modifierPos,
                            btnSize = item.btnSize,
                            iconSize = TclTheme.dimens.iconSize,
                            painter = painterResource(item.iconRes!!),
                            isChecked = item.isChecked,
                            enabled = item.isEnabled,
                            contentDescription = stringResource(item.descriptionRes),
                            onClick = { item.onClick.invoke(item) }
                        )
                    }
                }
            }
        }
    }
}