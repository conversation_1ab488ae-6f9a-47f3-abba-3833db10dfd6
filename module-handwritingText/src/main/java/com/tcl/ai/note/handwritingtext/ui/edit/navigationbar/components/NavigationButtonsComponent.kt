package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.ui.richtext.state.EditableTitleNavigationBarState
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.ImportButton
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.MoreButton
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.voicetotext.states.RecordSpecialState
import com.tcl.ai.note.voicetotext.util.AudioToTextConstant
import com.tcl.ai.note.voicetotext.view.widget.RecordingBarCrossfadeAnim
import com.tcl.ai.note.voicetotext.view.widget.rememberAudioPermissionHelper
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel
import com.tcl.ai.note.voicetotext.vm.RecordingViewModel

@Composable
fun NavigationButtonsComponent(
    state: EditableTitleNavigationBarState,
    onStateChanged: (EditableTitleNavigationBarState) -> Unit,
    modifier: Modifier = Modifier
) {
    val richTextViewModel: RichTextViewModel2 = hiltViewModel()
    val recordingViewModel: RecordingViewModel = hiltViewModel()
    val audioToTextViewModel: AudioToTextViewModel = hiltViewModel()
    
    val uiState by richTextViewModel.uiState.collectAsState()
    val toastText = stringResource(R.string.record_completed)
    
    var showAudioPanel by remember { mutableStateOf(audioToTextViewModel.topAudioVisibleState.value) }
    val importAudioHelper = rememberAudioPermissionHelper(uiState.noteId?: 0L) { path ->
        val newList = uiState.audios.toMutableList()
        // 添加到列表中，不需要指定位置，保存后会按创建时间排序
        newList.add(EditorContent.AudioBlock(audioPath = path))
        richTextViewModel.onAudiosChanged(newList)
    }
    val recordingState by recordingViewModel.recordState.collectAsState()
    var audioPath by remember(recordingState) { mutableStateOf(
        if (recordingState.isRecording) {
            recordingState.audioPath
        } else if (recordingState.audioPath != null &&
            (recordingState.specialState is RecordSpecialState.RecordingError ||
                    recordingState.specialState is RecordSpecialState.MaxDurationReached ||
                    (recordingState.recordDuration > 0 && recordingState.recordDuration < AudioToTextConstant.MIN_RECORD_DURATION))) {
            // 保持 audioPath 以便错误处理或自动保存处理
            recordingState.audioPath
        } else {
            ""
        }) }

    val context = LocalContext.current
    val endPadding = if (context.resources.displayMetrics.density > 3.0f) 3.dp else 20.dp
    
    Row(
        horizontalArrangement = Arrangement.End,
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier.padding(end = endPadding)
    ) {
        if (!state.isEditing) {
            RecordingBarCrossfadeAnim(
                noteId = uiState.noteId ?: 0L,
                audioPath = audioPath,
                hasAudio = uiState.audios.isNotEmpty(),
                showAudioPanel = showAudioPanel,
                onStopAudioClick = {
                    audioPath?.let { path ->
                        // 检查是否为"保存并开始新录音"操作，如果是则不处理
                        if (recordingState.specialState is RecordSpecialState.SaveAndStartNew) {
                            // 对于"保存并开始新录音"操作，不在这里处理录音添加
                            // 录音添加会在saveAndStartNewAudio函数中处理
                            audioPath = ""
                            return@let
                        }

                        if ((recordingState.recordDuration > 0
                                    && recordingState.recordDuration > AudioToTextConstant.MIN_RECORD_DURATION))
                            audioPath?.let {
                                ToastUtils.makeWithCancel(toastText)
                            }
                        // 录音完成后，检查录音是否已存在，避免重复添加
                        val existingAudio = uiState.audios.find { it.audioPath == path }
                        if (existingAudio == null) {
                            // 录音不存在，添加到笔记中
                            val newList = uiState.audios.toMutableList()
                            newList.add(0, EditorContent.AudioBlock(audioPath = path))
                            richTextViewModel.onAudiosChanged(newList)
                        }

                        // 录音完成后自动显示录音条
                        showAudioPanel = true
                        audioToTextViewModel.updateTopAudioBarState(visible = true)
                    }
                    audioPath = ""
                },
                onRecordingError = { path ->
                    path?.let {
                        richTextViewModel.onDeleteAudio(it)
                    }
                    audioPath = ""
                },
                onAddAudio = { importAudioHelper.invoke { } },
                onAudioPanelVisibleClick = {
                    showAudioPanel = !showAudioPanel
                    audioToTextViewModel.updateTopAudioBarState(
                        visible = showAudioPanel
                    )
                },
                recordingViewModel = recordingViewModel,
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            ImportButton(
                onClick = {
                    onStateChanged(state.copy(
                        showImportPopup = !state.showImportPopup,
                        showMorePopup = false
                    ))
                },
                btnSize = TclTheme.dimens.btnSize,
                iconSize = TclTheme.dimens.iconSize
            )

            Spacer(modifier = Modifier.width(8.dp))

            MoreButton(
                onClick = {
                    onStateChanged(state.copy(
                        showMorePopup = !state.showMorePopup,
                        showImportPopup = false
                    ))
                },
                btnSize = TclTheme.dimens.btnSize,
                iconSize = TclTheme.dimens.iconSize
            )
        }
    }
}