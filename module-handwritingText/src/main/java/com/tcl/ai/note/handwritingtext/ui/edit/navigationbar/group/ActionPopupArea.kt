package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.group

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Density
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.components.NavigationButtonsComponent
import com.tcl.ai.note.handwritingtext.ui.richtext.state.EditableTitleNavigationBarState

@Composable
fun NavigationActionArea(
    state: EditableTitleNavigationBarState,
    onStateChanged: (EditableTitleNavigationBarState) -> Unit,
    density:  Density,
    modifier: Modifier = Modifier,
    onDeleteNote: () -> Unit
) {
    // 只保留按钮组件，移除弹框相关逻辑
    NavigationButtonsComponent(
        state = state,
        onStateChanged = onStateChanged,
        modifier = modifier
    )
}


