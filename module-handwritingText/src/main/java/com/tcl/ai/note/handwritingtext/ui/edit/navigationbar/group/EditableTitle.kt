package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.group

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.handwritingtext.ui.richtext.state.EditableTitleNavigationBarState

@Composable
fun EditableTitle(
    state: EditableTitleNavigationBarState,
    defaultTitle: String,
    onTitleChanged: (String) -> Unit,
    onStateChanged: (EditableTitleNavigationBarState) -> Unit,
    onSave: () -> Unit,
    focusRequester: FocusRequester,
    keyboardController: SoftwareKeyboardController?,
    focusManager: androidx.compose.ui.focus.FocusManager,
    modifier: Modifier = Modifier,
    onUserStartedEditing: (() -> Unit)? = null
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.CenterStart
    ) {
        EditableTitleField(
            title = state.title,
            onTitleChanged = { newTitle ->
                onTitleChanged(newTitle)
                onStateChanged(state.copy(
                    title = newTitle,
                    hasUserEditedTitle = newTitle.isNotEmpty() && newTitle != defaultTitle
                ))
            },
            isEditing = state.isEditing,
            onEditingChanged = { isFocused ->
                if (isFocused && !state.isEditing) {
                    onStateChanged(state.copy(
                        isEditing = true,
                        hasUserEditedTitle = true
                    ))
                } else if (!isFocused && state.isEditing) {
                    onSave()
                }
            },
            focusRequester = focusRequester,
            keyboardController = keyboardController,
            modifier = Modifier.padding(
                start = 12.dp, 
                end = if (LocalContext.current.resources.displayMetrics.density > 3.0f) 3.dp else 16.dp
            ), // 因为左边的返回icon使用了HoverProofIconButton自带padding，高density时减少end padding给标题更多空间
            onUserStartedEditing = onUserStartedEditing
        )
    }
}
