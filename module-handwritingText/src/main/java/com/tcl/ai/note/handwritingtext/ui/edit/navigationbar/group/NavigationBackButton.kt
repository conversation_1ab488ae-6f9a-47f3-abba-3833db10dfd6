package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.group

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.BackButton


@Composable
fun NavigationBackButton(
    onBackClick: () -> Unit,
    btnSize: Dp,
    iconSize: Dp,
    modifier: Modifier = Modifier
) {
    // 因为左边的返回icon使用了HoverProofIconButton自带padding 24 -> 20
    Spacer(Modifier.width(20.dp))
    BackButton(
        onClick = onBackClick,
        iconSize = iconSize,
        btnSize = btnSize,
        modifier = modifier
    )
}