package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.handler

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.utils.ImageInsertHelper
import com.tcl.ai.note.handwritingtext.ui.image.rememberPickImageHelper
import com.tcl.ai.note.handwritingtext.ui.image.rememberTakePhotoHelper
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.state.RichTextDataState
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.voicetotext.view.widget.rememberAudioPermissionHelper

/**
 * 无他，唯simple code尔
 */
data class ImageHandlers(
    val pickImage: (() -> Unit) -> Unit,
    val takePhoto: (() -> Unit) -> Unit
)

@Composable
fun rememberImageHandlers(): ImageHandlers {
    val context = LocalContext.current
    val suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel()
    val richTextViewModel: RichTextViewModel2 = hiltViewModel()
    
    val drawRectF by remember { mutableStateOf(suniaDrawViewModel.showRectState) }
    val maxImageLimitTip = stringResource(R.string.max_image_limit)
    val imageCountState by suniaDrawViewModel.imageCountState.collectAsState()
    
    val imagePickHelper = rememberPickImageHelper(context, imageCountState, maxImageLimitTip) { uri ->
        ImageInsertHelper.insertImageToDrawBoard(context, uri, drawRectF.value, suniaDrawViewModel, richTextViewModel)
    }
    
    val takePhotoHelper = rememberTakePhotoHelper(context, imageCountState, maxImageLimitTip) { uri ->
        ImageInsertHelper.insertImageToDrawBoard(context, uri, drawRectF.value, suniaDrawViewModel, richTextViewModel)
    }
    
    return ImageHandlers(
        pickImage = imagePickHelper,
        takePhoto = takePhotoHelper
    )
}


/**
 * 创建音频处理器
 * 封装音频导入的逻辑，保持代码整洁
 */
@Composable
internal fun rememberAudioHandler(
    richTextViewModel: RichTextViewModel2,
    uiState: RichTextDataState
): (() -> Unit) -> Unit {
    return rememberAudioPermissionHelper(uiState.noteId ?: 0L) { path ->
        val newList = uiState.audios.toMutableList()
        // 添加到列表中，不需要指定位置，保存后会按创建时间排序
        newList.add(EditorContent.AudioBlock(audioPath = path))
        richTextViewModel.onAudiosChanged(newList)
    }
}
