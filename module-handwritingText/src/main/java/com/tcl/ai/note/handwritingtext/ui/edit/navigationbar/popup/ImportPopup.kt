package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.popup

import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.unit.Density
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.utils.PopupOffsetUtils
import com.tcl.ai.note.handwritingtext.ui.popup.BounceScalePopup
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.ImportActionContent
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge

@Composable
fun ImportPopupComponent(
    isVisible: Boolean,
    density: Density,
    onDismiss: () -> Unit,
    onRecordAudio: () -> Unit,
    onChoosePhoto: () -> Unit,
    onTakePhoto: () -> Unit,
    isRecording: Boolean = false
) {
    if (!isVisible) return

    val importPopupOffset = PopupOffsetUtils.calculateImportPopupOffset(TclTheme.dimens, density)

    BounceScalePopup(
        onDismissRequest = onDismiss,
        offset = importPopupOffset,
        alignment =  Alignment.TopEnd,
        enterTransformOrigin = TransformOrigin(0.65f, 0f),
        exitTransformOrigin = TransformOrigin(0.65f, 0f),
    ) { closePopup ->
        ImportActionContent(
            onRecordAudio = {
                closePopup()
                onRecordAudio()
            },
            onChoosePhoto = {
                closePopup()
                onChoosePhoto()
            },
            onTakePhoto = {
                closePopup()
                onTakePhoto()
            },
            isRecording = isRecording
        )
    }
}