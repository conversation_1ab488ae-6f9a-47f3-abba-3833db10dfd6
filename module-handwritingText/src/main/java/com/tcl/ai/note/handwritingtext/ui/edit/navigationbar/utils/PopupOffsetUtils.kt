package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.utils

import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.theme.GlobalDimens

object PopupOffsetUtils {
    
    fun calculateImportPopupOffset(dimens: GlobalDimens, density: Density): IntOffset {
        return with(density) {
            IntOffset(
                x = ((-24).dp).toPx().toInt(),
                y = (dimens.navigationBarHeight).toPx().toInt()
            )
        }
    }
    
    fun calculateMorePopupOffset(dimens: GlobalDimens, density: Density): IntOffset {
        return with(density) {
            IntOffset(
                x = ((-24).dp).toPx().toInt(),
                y = (dimens.navigationBarHeight).toPx().toInt()
            )
        }
    }
}