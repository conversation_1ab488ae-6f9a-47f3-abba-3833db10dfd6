package com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.group

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarItem
import com.tcl.ai.note.widget.DelayedBackgroundIconButton

/**
 * 渲染动作按钮
 * 执行特定动作的按钮，如撤销、重做等
 *
 * @param item 动作按钮项
 * @param isEnabled 是否启用
 */
@Composable
internal fun RenderActionButton(
    item: RichTextToolBarItem.ActionButton,
    isEnabled: Boolean
) {
    // 使用自定义内容或默认按钮
    if (item.customContent != null) {
        item.customContent.invoke(Modifier, item)
    } else {
        item.iconRes?.let { iconRes ->
            DelayedBackgroundIconButton(
                btnSize = item.btnSize,
                iconSize = item.btnSize,
                painter = painterResource(id = iconRes),
                isChecked = false, // 动作按钮没有选中状态
                contentDescription = stringResource(item.descriptionRes),
                enabled = isEnabled,
                onClick = { item.onAction(item) }
            )
        }
    }
}
