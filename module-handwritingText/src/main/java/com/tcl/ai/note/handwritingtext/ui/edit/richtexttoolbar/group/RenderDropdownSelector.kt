package com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.group

import android.annotation.SuppressLint
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.window.Popup
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarItem
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarState
import com.tcl.ai.note.handwritingtext.bean.RichTextToolType
import com.tcl.ai.note.handwritingtext.ui.richtext.component.FontSizeDropdown
import com.tcl.ai.note.widget.FontSizeSelector

/**
 * 渲染下拉选择器
 * 处理不同类型的下拉选择器，如字体大小选择器
 *
 * @param item 下拉选择器项
 * @param state 工具栏状态
 * @param isEnabled 是否启用
 */
@Composable
internal fun RenderDropdownSelector(
    item: RichTextToolBarItem.DropdownSelector<*>,
    state: RichTextToolBarState,
    isEnabled: Boolean,
    offsetYPx: Int
) {
    when (item.toolType) {
        RichTextToolType.FONT_SIZE -> {
            RenderFontSizeSelector(item, state, isEnabled, offsetYPx)
        }
        else -> {
            // 使用自定义内容或跳过
            item.customContent?.invoke(Modifier, item)
        }
    }
}


/**
 * 渲染字体大小选择器
 * 显示字体大小选择按钮和下拉菜单
 *
 * @param item 字体大小选择器项
 * @param state 工具栏状态
 * @param isEnabled 是否启用
 * @param offsetYPx 弹出框Y轴偏移量（像素）
 */
@SuppressLint("DesignSystem")
@Composable
private fun RenderFontSizeSelector(
    item: RichTextToolBarItem.DropdownSelector<*>,
    state: RichTextToolBarState,
    isEnabled: Boolean,
    offsetYPx: Int
) {
    // 安全的类型检查和转换
    if (item.selectedValue is Int && item.options.all { it is Int }) {
        // 渲染选择器按钮
        if (item.customContent != null) {
            item.customContent.invoke(Modifier, item)
        } else {
            FontSizeSelector(
                btnSize = item.btnSize,
                fontSize = state.selectedFontSize,
                isExpanded = state.showFontSizeDropdown,
                onClick = { item.onToggleExpanded(item) }
            )
        }
    } else {
        // 如果类型不匹配，使用自定义内容或跳过
        item.customContent?.invoke(Modifier, item)
    }

    // 渲染字体大小下拉菜单
    RenderFontSizeDropdown(item, state, offsetYPx)
}


/**
 * 渲染字体大小下拉菜单
 *
 * @param item 字体大小选择器项
 * @param state 工具栏状态
 * @param offsetYPx 弹出框Y轴偏移量（像素）
 */
@Composable
private fun RenderFontSizeDropdown(
    item: RichTextToolBarItem.DropdownSelector<*>,
    state: RichTextToolBarState,
    offsetYPx: Int
) {
    if (item.selectedValue is Int && item.options.all { it is Int }) {
        @Suppress("UNCHECKED_CAST")
        val fontSizeItem = item as RichTextToolBarItem.DropdownSelector<Int>

        if (state.showFontSizeDropdown) {
            // -offsetYPx 不管用, 暂时写死 top padding
            Popup(
                alignment = Alignment.TopCenter,
                offset = IntOffset(0, -288),
                onDismissRequest = {
                    // 确保调用onToggleExpanded来关闭弹框
                    fontSizeItem.onToggleExpanded(fontSizeItem)
                }
            ) {
                FontSizeDropdown(
                    expanded = true,
                    onDismissRequest = {
                        // 确保调用onToggleExpanded来关闭弹框
                        fontSizeItem.onToggleExpanded(fontSizeItem)
                    },
                    selectedFontSize = state.selectedFontSize,
                    fontSizes = fontSizeItem.options.map { it },
                    onFontSizeSelected = { size ->
                        fontSizeItem.onSelectionChange(size, fontSizeItem)
                        // 确保在选择后关闭弹框
                        fontSizeItem.onToggleExpanded(fontSizeItem)
                    }
                )
            }
        }
    }
}

