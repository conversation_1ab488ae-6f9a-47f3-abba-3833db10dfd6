package com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.group

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarItem
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarState
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.widget.DelayedBackgroundIconButton

/**
 * 渲染切换按钮
 * 可切换状态的按钮，如粗体、斜体等
 *
 * @param item 切换按钮项
 * @param state 工具栏状态
 * @param isEnabled 是否启用
 */
@Composable
internal fun RenderToggleButton(
    item: RichTextToolBarItem.ToggleButton,
    state: RichTextToolBarState,
    isEnabled: Boolean
) {
    // 使用自定义内容或默认按钮
    if (item.customContent != null) {
        item.customContent.invoke(Modifier, item)
    } else {
        item.iconRes?.let { iconRes ->
            DelayedBackgroundIconButton(
                btnSize = item.btnSize,
                iconSize = TclTheme.dimens.iconSize,
                painter = painterResource(id = iconRes),
                isChecked = state.isToolActive(item.toolType),
                contentDescription = stringResource(item.descriptionRes),
                enabled = isEnabled,
                onClick = { item.onToggle(item) }
            )
        }
    }
}