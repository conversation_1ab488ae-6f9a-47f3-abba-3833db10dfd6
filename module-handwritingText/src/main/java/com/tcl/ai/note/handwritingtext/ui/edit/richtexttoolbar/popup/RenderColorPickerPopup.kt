package com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.popup

import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.window.Popup
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarItem
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarState
import com.tcl.ai.note.handwritingtext.bean.RichTextToolType
import com.tcl.ai.note.handwritingtext.ui.richtext.component.BackgroundColorPicker
import com.tcl.ai.note.handwritingtext.ui.richtext.component.TextColorPicker


/**
 * 渲染颜色选择器弹出框
 * 根据工具类型显示相应的颜色选择器弹窗
 *
 * @param item 颜色选择器项
 * @param state 工具栏状态
 * @param offsetYPx 弹出框Y轴偏移量（像素）
 */
@Composable
internal fun RenderColorPickerPopup(
    item: RichTextToolBarItem.ColorPicker,
    state: RichTextToolBarState,
    offsetYPx: Int
) {
    when (item.toolType) {
        RichTextToolType.TEXT_COLOR -> {
            if (state.showTextColorPicker) {
                Popup(
                    alignment = Alignment.TopCenter,
                    offset = IntOffset(0, -offsetYPx)
                ) {
                    TextColorPicker(
                        selectedColor = state.textColor,
                        onColorSelected = { color -> item.onColorChange(color, item) }
                    )
                }
            }
        }
        RichTextToolType.TEXT_BG_COLOR -> {
            if (state.showTextBgColorPicker) {
                Popup(
                    alignment = Alignment.TopCenter,
                    offset = IntOffset(0, -offsetYPx)
                ) {
                    BackgroundColorPicker(
                        selectedColor = state.textBgColor,
                        onColorSelected = { color -> item.onColorChange(color, item) }
                    )
                }
            }
        }
        else -> {
            // 其他类型不处理
        }
    }
}