package com.tcl.ai.note.handwritingtext.ui.image

import android.graphics.RectF
import com.tcl.ai.note.handwritingtext.richtext.utils.DisplayUtils

object InsertImage {

    const val MAX_IMAGE_LIMIT = 50

    const val INSERT_IMAGE_OFFSET_X = 24
    const val INSERT_IMAGE_OFFSET_Y = 78



    fun getInsertImageRect(width: Int, height: Int, showRectF: RectF): RectF {
        val defaultOffsetX = DisplayUtils.dp2px(INSERT_IMAGE_OFFSET_X)
        val defaultOffsetY = DisplayUtils.dp2px(INSERT_IMAGE_OFFSET_Y)
        val widthScale = 1.0f * (showRectF.width() - defaultOffsetX * 2) / width
        val heightScale = 1.0f * (showRectF.height() - defaultOffsetY) / height
        val scale = minOf(widthScale, heightScale)
        if (scale >= 1.0f) {
            val offsetX = showRectF.left + (showRectF.width() - width) / 2
            val offsetY = defaultOffsetY
            return RectF(offsetX.toFloat(), offsetY.toFloat(), offsetX.toFloat() + width.toFloat(), offsetY.toFloat() + height.toFloat())
        } else {
            val newWidth = (width * scale).toInt()
            val newHeight = (height * scale).toInt()
            var offsetX = showRectF.left + (showRectF.width() - newWidth) / 2
            val offsetY = defaultOffsetY
            return RectF(offsetX.toFloat(), offsetY.toFloat(), offsetX.toFloat() + newWidth.toFloat(), offsetY.toFloat() + newHeight.toFloat())

        }
    }
}