package com.tcl.ai.note.handwritingtext.ui.other

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.handwritingtext.vm.TitleEditViewModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.isTablet


/**
 * 遮罩层，用于标题编辑时的TopMenuBar区域遮罩
 * 点击遮罩层会退出标题编辑状态
 */
@Composable
fun MaskForTitleEditOnTitle(titleEditViewModel: TitleEditViewModel) {
    val isEditingTitle by titleEditViewModel.isEditingTitle

    if (isTablet && isEditingTitle) {
        val dimens = getGlobalDimens()
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(dimens.menuBarHeight + 0.dp) // TopMenuBar高度 + HorizontalLine高度
                .background(TclTheme.colorScheme.maskBackground)
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null
                ) {
                    titleEditViewModel.forceExitEditing()
                }
        )
    }
}

/**
 * 遮罩层，用于标题编辑时的背景遮罩
 * 点击遮罩层会退出标题编辑状态
 */
@Composable
fun MaskForTitleEditOnContent(titleEditViewModel: TitleEditViewModel) {
    val isEditingTitle by titleEditViewModel.isEditingTitle

    if (isEditingTitle) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(TclTheme.colorScheme.maskBackground)
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null
                ) {
                    titleEditViewModel.forceExitEditing()
                }
        )
    }
}
