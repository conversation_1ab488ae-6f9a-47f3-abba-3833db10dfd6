package com.tcl.ai.note.handwritingtext.ui.popup

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.window.Popup
import kotlinx.coroutines.delay

/**
 * 带有弹性缩放动画效果的弹出组件
 * 从小到大弹出
 */
@Composable
fun BounceScalePopup(
    onDismissRequest: () -> Unit,
    offset: IntOffset,
    durationMillis:Int = 200,
    alignment: Alignment = Alignment.TopStart,
    enterTransformOrigin: TransformOrigin = TransformOrigin(0.5f, 0f),
    exitTransformOrigin: TransformOrigin = TransformOrigin(0.5f, 0f),
    content: @Composable (closePupup:() ->Unit) -> Unit
) {
    var showContent by remember { mutableStateOf(false) }

    LaunchedEffect(showContent) {
        if (!showContent) {
            delay(durationMillis.toLong())
            onDismissRequest()
        }
    }

    Popup(
        alignment = alignment,
        onDismissRequest = {
            showContent = false
        },
        offset = offset
    ) {

        DisposableEffect(Unit) {
            showContent = true
            onDispose {}
        }


        AnimatedVisibility(
            visible = showContent,
            enter = scaleIn(
                initialScale = 0.1f,
                animationSpec = spring(
                    stiffness = Spring.StiffnessLow,
                    dampingRatio = Spring.DampingRatioLowBouncy
                ),
                transformOrigin = enterTransformOrigin
            ),
            exit = scaleOut(
                targetScale = 0f,
                animationSpec = tween(
                    durationMillis = durationMillis,
                    easing = FastOutSlowInEasing
                ),
                transformOrigin = exitTransformOrigin
            )
        ) {

            content{
                showContent = false
            }
        }
    }
}

    val BounceScaleCenter = TransformOrigin(0.5f, 0.5f)
    val BounceScaleRightTop = TransformOrigin(1f, 0f)
