package com.tcl.ai.note.handwritingtext.ui.popup

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.bean.PenColor
import com.tcl.ai.note.handwritingtext.ui.swatches.AlphaSliderWithControls
import com.tcl.ai.note.handwritingtext.ui.swatches.BottomButton
import com.tcl.ai.note.handwritingtext.ui.swatches.ColorSwatchGrid
import com.tcl.ai.note.handwritingtext.utils.ColorUtils.inverseColor
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.getStatusBarHeight
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.utils.toPx


/**
 * 色盘popup--笔刷颜色选择
 */
@SuppressLint("DesignSystem")
@Composable
internal fun PenColorPalettePopup(
    isDarkTheme:Boolean = isSystemInDarkTheme(),
    curPenColor: PenColor,
    popupWidth:Dp = 328.dp,
    onSizeChanged: (IntSize) -> Unit ={},
    onConfirm:(penColor:PenColor) ->Unit,
    onClosePopup: () -> Unit) {
    var selectedColor by remember (curPenColor.color) {
        mutableStateOf(curPenColor.color)
    }
    var alpha  by remember(curPenColor.alpha) {
        mutableIntStateOf(curPenColor.alpha)
    }


    Box(
        modifier = Modifier
            .background(
                color = Color.Transparent,
                shape = RoundedCornerShape(20.dp)
            )
            .width(popupWidth)
            .defShadow(20.dp)
            .onSizeChanged { size ->
                onSizeChanged(size)
            }

    ) {
        Column(
            modifier = Modifier
                .width(popupWidth)
                .wrapContentHeight()
                .background(color = TclTheme.colorScheme.tertiaryBackground),
            horizontalAlignment = Alignment.CenterHorizontally
        ){
            Spacer(modifier = Modifier.height(24.dp))
            Text(
                modifier = Modifier.fillMaxWidth().padding(horizontal = 24.dp),
                fontSize = 20.sp,
                lineHeight = 24.sp,
                textAlign = TextAlign.Start,
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Medium,
                color = com.tcl.ai.note.handwritingtext.R.color.popup_title.colorRes(),
                text = R.string.swatches_popup_tool.stringRes(),
            )
            Spacer(modifier = Modifier.height(20.dp))
            ColorSwatchGrid(
                selColor = selectedColor
            ) { color, ->
                selectedColor = color

            }

            Spacer(modifier = Modifier.height(4.dp))
            AlphaSliderWithControls(
                modifier= Modifier.fillMaxWidth()
                    .height(48.dp)
                    .padding(start = 20.dp, end = 24.dp),
                baseColor = selectedColor,
                alphaValue =alpha,
                onAlphaChanged = { value ->
                    alpha = value

                }
            )

            BottomButton(
                modifier = Modifier.fillMaxWidth().padding(horizontal = 20.dp)
            ) { isOk ->
                if(isOk){
                    val penColor = PenColor(color = isDarkTheme.judge(
                        selectedColor.inverseColor(),
                        selectedColor
                    ) , alpha = alpha)
                    onConfirm(penColor)
                }
                onClosePopup()
            }
            Spacer(modifier = Modifier.height(16.dp))

        }
    }

}
@Composable
fun TabletPenColorPalettePopup(
    isDarkTheme:Boolean = isSystemInDarkTheme(),
    menuBarItem: MenuBarItem,
    curPenColor: PenColor,
    onConfirm:(penColor:PenColor) ->Unit,
    onDismissRequest: () -> Unit
){
    val dimens = getGlobalDimens()
    val popupWidth = 328.dp
    val space = 23.dp
    val density = LocalDensity.current
    val layoutDirection = LocalLayoutDirection.current
    val screenWidthDp = LocalConfiguration.current.screenWidthDp
    val position: Offset = menuBarItem.position
    val xOffset = with(density) {
        if (layoutDirection == LayoutDirection.Rtl) {
            (screenWidthDp.dp.toPx() - position.x - dimens.btnSize.toPx / 2 - popupWidth.toPx() / 2).toInt()
        } else {
            (position.x + dimens.btnSize.toPx / 2 - popupWidth.toPx() / 2).toInt()
        }
    }
    val yOffset =(space.toPx+position.y).toInt()

    BounceScalePopup(
        onDismissRequest ={
            onDismissRequest()
        } ,
        offset =IntOffset(xOffset,yOffset),
    ) { closePopup ->
        PenColorPalettePopup(
            curPenColor =isDarkTheme.judge(
                curPenColor.copy(color = curPenColor.color.inverseColor()),
                curPenColor
            ),
            popupWidth = popupWidth,
            onConfirm = onConfirm,
            onClosePopup = closePopup
        )
    }


}

@Composable
fun PhonePenColorPalettePopup(
    isDarkTheme:Boolean = isSystemInDarkTheme(),
    areaHeight: Int,
    curPenColor: PenColor,
    onConfirm:(penColor:PenColor) ->Unit,
    onDismissRequest: () -> Unit
){
    val popupWidth = 328.dp
    val space = 8.dp
    val density = LocalDensity.current


    val screenWidthDp = LocalConfiguration.current.screenWidthDp


    val xOffset = with(density) {
        (screenWidthDp.dp.toPx() - popupWidth.toPx())/2
    }
    var popupHeight by remember { mutableIntStateOf(0) }

    val yOffset =areaHeight - popupHeight - space.toPx
    SlideFromBottomPopup(
        onDismissRequest = onDismissRequest,
        offset = IntOffset(xOffset.toInt(),yOffset.toInt()),
    ) { closePopup ->
        PenColorPalettePopup(
            curPenColor =isDarkTheme.judge(
                curPenColor.copy(color = curPenColor.color.inverseColor()),
                curPenColor
            ) ,
            popupWidth = popupWidth, onSizeChanged = { size ->
                popupHeight = size.height
            },
            onConfirm = onConfirm,
            onClosePopup = closePopup
        )
    }

}


