package com.tcl.ai.note.handwritingtext.ui.popup

import androidx.compose.runtime.*
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.window.DialogProperties
import com.tcl.ai.note.base.R
import com.tcl.ai.note.utils.Logger
import com.tct.theme.core.designsystem.component.TclLoadingDialog
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun ShowDataLoadingPopup() {
    val coroutineScope = rememberCoroutineScope()
    var isShowLoadingDialog by remember { mutableStateOf(false) }

    DisposableEffect(Unit) {
        Logger.d("ShowDataLoadingPopup", "create")
        coroutineScope.launch {
            // 延时500ms显示
            delay(500)
            isShowLoadingDialog = true
            Logger.d("ShowDataLoadingPopup", "show")
        }
        onDispose {
            coroutineScope.cancel()
            isShowLoadingDialog = false
            Logger.d("ShowDataLoadingPopup", "destroy")
        }
    }

    TclLoadingDialog(
        onDismissRequest = { isShowLoadingDialog = false },
        show = isShowLoadingDialog,
        text = stringResource(com.tct.theme.core.designsystem.R.string.loading),
        // TODO 存在bug，一直在保存， dismissOnBackPress暂改为true
        properties = DialogProperties(dismissOnBackPress = true, dismissOnClickOutside = false)
    )
}