package com.tcl.ai.note.handwritingtext.ui.popup

import androidx.compose.runtime.*
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.window.DialogProperties
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.controller.NoteDataSaveController
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.utils.Logger
import com.tct.theme.core.designsystem.component.TclLoadingDialog
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun ShowDataSavingPopup(
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel(),
    onSaveFinish: () -> Unit = {},
) {
    val coroutineScope = rememberCoroutineScope()
    var isShowLoadingDialog by remember { mutableStateOf(false) }

    DisposableEffect(Unit) {
        Logger.d("ShowDataSavingPopup", "create")

        coroutineScope.launch {
            // 返回时保存缩略图
            suniaDrawViewModel.saveThumbnail()
            // 强制保存, 并等待结束
            NoteDataSaveController.forceSave().await()
            if (isShowLoadingDialog) {
                // 延长保存时间，避免返回太快，视觉上弹窗闪现
                delay(500)
            }
            isShowLoadingDialog = false
            onSaveFinish()
            Logger.d("ShowDataSavingPopup", "save finish")
        }

        coroutineScope.launch {
            // 延时500ms显示
            delay(500)
            isShowLoadingDialog = true
            Logger.d("ShowDataSavingPopup", "save timeout, show Loading")
        }

        onDispose {
            Logger.d("ShowDataSavingPopup", "destroy")
            coroutineScope.cancel()
            isShowLoadingDialog = false
        }
    }

    TclLoadingDialog(
        onDismissRequest = { isShowLoadingDialog = false },
        show = isShowLoadingDialog,
        text = stringResource(R.string.saving),
        properties = DialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false)
    )
}