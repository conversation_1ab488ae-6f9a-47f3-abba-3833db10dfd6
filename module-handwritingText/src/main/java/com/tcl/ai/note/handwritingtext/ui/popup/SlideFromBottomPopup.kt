package com.tcl.ai.note.handwritingtext.ui.popup

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import com.tcl.ai.note.handwritingtext.utils.defShadow
import kotlinx.coroutines.delay

@Composable
fun SlideFromBottomPopup(
    onDismissRequest: () -> Unit,
    offset: IntOffset,
    content: @Composable (closePupup:() ->Unit) -> Unit
) {
    var showContent by remember { mutableStateOf(false) }

    LaunchedEffect(showContent) {
        if (!showContent) {
            delay(200)
            onDismissRequest()
        }
    }

    Popup(
        onDismissRequest = {
            showContent = false
        },
        offset = offset
    ) {

        DisposableEffect(Unit) {
            showContent = true
            onDispose {}
        }

        AnimatedVisibility(
            visible = showContent,
            enter = slideInVertically(
                initialOffsetY = { fullHeight -> fullHeight },
                animationSpec = spring(
                    stiffness = Spring.StiffnessMediumLow,
                    dampingRatio = Spring.DampingRatioNoBouncy
                ),
            ) ,
            exit = slideOutVertically(
                targetOffsetY = { fullHeight -> fullHeight },
                animationSpec = tween(
                    durationMillis = 200,
                    easing = FastOutSlowInEasing
                )
            )
        ) {
            content{
                showContent = false
            }

        }
    }
}