package com.tcl.ai.note.handwritingtext.ui.richtext

import android.content.Context
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.controller.BottomRoute
import com.tcl.ai.note.controller.LoginErrorResult
import com.tcl.ai.note.controller.rememberLoginHandler
import com.tcl.ai.note.handwritingtext.vm.text.RichTextToolBarViewModel
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.utils.startAIHelpWriting
import com.tcl.ai.note.utils.startAIPolish
import com.tcl.ai.note.utils.startAISummary
import com.tcl.ai.note.voicetotext.vm.RecordingViewModel
import com.tcl.ai.note.widget.components.AIServiceDialog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * AI登录状态处理
  */
data class AIRouteState(
    val context: Context,
    val coroutineScope: CoroutineScope,
    val loginHandler: (action: () -> Unit) -> Unit,
    val viewModel: RichTextToolBarViewModel,
    val recordingViewModel: RecordingViewModel
) {

    fun handleRoute(content: String?, noteId: Long?, route: BottomRoute) {
        if (viewModel.isOffline.value){
            Toast.makeText(context, R.string.no_connection, Toast.LENGTH_SHORT).show()
            return
        }
        recordingViewModel._isShowBottomAIPop.value = true
        val plainContent= content?.replace("\n","")//去掉富文本中的 \n 无效换行符 不然文字长度计算不准
        when (route) {
            is BottomRoute.Summary -> {
                GlobalContext.applicationScope.launchIO {
                    // 上报登录状态
                    TclAnalytics.reportAiSummaryLoginState(AccountController.getLoginState())
                }
                toAIRoute(plainContent) {
                    viewModel.startNavigation()
                    context.startAISummary(noteId ?: 0)
                }
            }
            is BottomRoute.HelpWrite -> {
                GlobalContext.applicationScope.launchIO {
                    // 上报登录状态
                    TclAnalytics.reportAiWriteLoginState(AccountController.getLoginState())
                }
                //没有写文字时，也要跳转到帮写页面
                toAICheckLoginState {
                    viewModel.startNavigation()
                    context.startAIHelpWriting(noteId?:0)
                }
            }
            is BottomRoute.Polish -> {
                GlobalContext.applicationScope.launchIO {
                    // 上报登录状态
                    TclAnalytics.reportAiRewriteLoginState(AccountController.getLoginState())
                }
                toAIRoute(plainContent) {
                    viewModel.startNavigation()
                    context.startAIPolish(noteId ?: 0)
                }
            }
        }
    }

    private fun toAIRoute(content: String?, onRoute: () -> Unit) {
        // 在使用时动态获取字符串资源
        val tooLongTips = context.getString(R.string.tips_content_too_long)
        val tooShortTips = context.getString(R.string.tips_content_too_short)
        val noContentTips = context.getString(R.string.not_content_text_function_unavailable)
        if (content!=null){
            content.let { note ->
                Logger.d("toAIRoute","content.length:${content.length}")
                when (content.length) {
                    in 20..10000 -> {
                        toAICheckLoginState(onRoute)
                    }

                    0 -> {
                        //发现富文本输入完，马上点击AI功能，出现Note的内容为空的情况，是富文本输入完文字之后做了1秒的延时防抖功能
                        showToastAndClosePop(noContentTips)
                    }

                    in 0 until 20 -> {
                        showToastAndClosePop(tooShortTips)
                    }

                    else ->{
                        showToastAndClosePop(tooLongTips)
                    }
                }
            }
        }else{
            showToastAndClosePop(noContentTips)
        }
    }

    private fun toAICheckLoginState(
        toRoute: () -> Unit,
    ) = coroutineScope.launch {
        if (AccountController.getLoginState()) {
            viewModel.operateBottomAIPop()
            toRoute()
        } else {
            loginHandler {
                viewModel.operateBottomAIPop()
                toRoute()
            }
        }
    }

    private fun showToastAndClosePop(noContentTips: String) {
        Toast.makeText(context, noContentTips, Toast.LENGTH_SHORT).show()
        viewModel.operateBottomAIPop()
    }
}

/*
 * AIRouteState 的状态
 */
@Composable
fun rememberAIRouteState(
    viewModel: RichTextToolBarViewModel
): AIRouteState {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val showAiServiceDialog = remember { mutableStateOf(false) }
    val loginHandler = rememberLoginHandler(onFailure = {
        if (it == LoginErrorResult.LunchError) {
            showAiServiceDialog.value = true
            Logger.d("AIRouteState","loginHandler onFailure")
        }
    })
    val recordingViewModel: RecordingViewModel = hiltViewModel()
    AIServiceDialog(
        isShow = showAiServiceDialog.value,
        title = context.getString(R.string.edit_bottom_menu_ai_assistant),
        onDismiss = { showAiServiceDialog.value = false },
        onGoToSettings = {
            showAiServiceDialog.value = false
        }
    )

    return remember(viewModel, loginHandler) {
        AIRouteState(
            context = context,
            coroutineScope = coroutineScope,
            loginHandler = loginHandler,
            viewModel = viewModel,
            recordingViewModel = recordingViewModel,
        )
    }
}