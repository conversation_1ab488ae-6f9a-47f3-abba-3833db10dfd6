package com.tcl.ai.note.handwritingtext.ui.richtext

import android.app.ActivityManager
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.FlingBehavior
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.ScrollScope
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.calculatePan
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.rememberScrollableState
import androidx.compose.foundation.gestures.scrollable
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.AppBarDefaults
import androidx.compose.material3.adaptive.currentWindowSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.runtime.withFrameNanos
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.PointerEventPass
import androidx.compose.ui.input.pointer.PointerType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.coerceAtLeast
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.toSize
import androidx.compose.ui.util.fastForEach
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.handwritingtext.bean.BrushMenu
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.intent.RichTextIntent
import com.tcl.ai.note.handwritingtext.state.RichTextState
import com.tcl.ai.note.handwritingtext.ui.richtext.component.TitleBlock
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.MeshStyle
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.handwritingtext.vm.DrawBoardViewModel
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.handwritingtext.vm.RichTextViewModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.LocalScaffoldPadding
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isLandScapeByScreenSize
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.utils.screenSizeMax
import com.tcl.ai.note.utils.screenSizeMin
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.utils.tryToRequestFocus
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch

/**
 * 页面内容区
 */
@Composable
fun ContentContainer(
    title: String,
    onTitleChange: (String) -> Unit,
    onFocusChanged: (Boolean) -> Unit,
    onAddContent: (EditorContent, Int?) -> Unit, // 添加新内容
    onUpdateContent: (Int, EditorContent) -> Unit, // 更新内容
    onRemoveContent: (Int) -> Unit, // 删除内容
    modifier: Modifier = Modifier,
    viewModel: RichTextViewModel = hiltViewModel(),
    drawBoardViewModel: DrawBoardViewModel = hiltViewModel(),
    menuBarViewModel: MenuBarViewModel = hiltViewModel(),
) {
    val scope = rememberCoroutineScope()
    val state by viewModel.state.collectAsState()
    val onDraw = state.bottomMenuType == MenuBar.BRUSH && state.editMode
    val onText  = state.bottomMenuType != MenuBar.BRUSH && state.editMode
    val layoutDirection = LocalLayoutDirection.current
    val innerPadding = LocalScaffoldPadding.current
    val context = LocalContext.current
    val verticalScrollState = rememberScrollableState { delta ->
        with(drawBoardViewModel) {
            updateTransByDelta(y = -delta)
        }
        delta
    }
    val horizontalScrollState = rememberScrollableState { delta ->
        with(drawBoardViewModel) {
            // 横屏下不允许横向移动
            if (isLandScapeByScreenSize) {
                return@with
            }
            // 竖屏下仅允许移动长边-短边
            val currentTrans = drawBoardViewModel.translation
            val x = (currentTrans.x - delta).coerceIn((screenSizeMin - screenSizeMax).toFloat(), 0f)
            updateTrans(x = x, y = currentTrans.y)
        }
        delta
    }
    if (isTablet) {
        val windowSize = currentWindowSize().toSize()
        LaunchedEffect(windowSize) {
            Logger.v(TAG, "windowSize change, reset x translation")
            val currentTrans = drawBoardViewModel.translation
            drawBoardViewModel.updateTrans(x = 0f, y = currentTrans.y)
        }
    }

    LaunchedEffect(state.brushMenuType, state.bottomMenuType, state.editMode, isLandScapeByScreenSize) {
        drawBoardViewModel.clearFastRender()
    }

    LaunchedEffect(state.editMode) {
        if (!state.editMode) {
            drawBoardViewModel.panOnly = false
        }
    }
    val filterEnabled = state.editMode && drawBoardViewModel.panOnly && !onDraw
    val insets = AppBarDefaults.bottomAppBarWindowInsets
    val density = LocalDensity.current
    val bottomInsetPx = insets.getBottom(LocalDensity.current)
    val bottomInsetDp = with(density) { bottomInsetPx.toDp() }

    val menuHeight = getGlobalDimens().let { it.menuBarHeight+it.richTextToolBarHeight }
    val bottom = (innerPadding.calculateBottomPadding() -
            if (!isTablet ) {
                if(menuBarViewModel.showExpandButton){
                    getGlobalDimens().menuBarHeight
                }else{
                    if(innerPadding.calculateBottomPadding()>menuHeight){
                        innerPadding.calculateBottomPadding() -menuHeight-bottomInsetDp
                    }else{
                        0.dp
                    }
                }
            } else 0.dp).coerceAtLeast(0.dp)

    // 光标滚动到最后
    fun cursorToEnd(state: RichTextState) {
        val contents = state.contents
        val lastItem = contents.lastOrNull() ?: return
        val position = when (lastItem) {
            is EditorContent.TextBlock -> lastItem.text.text.length
            is EditorContent.TodoBlock -> lastItem.text.text.length
            else -> 0
        }
        viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(contents.lastIndex))
        scope.launch {
            // 等一帧
            repeat(1) { withFrameNanos {} }
            // 设置position为0，重置一下状态，否则状态相同不会改变光标
            viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(0))
            viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(position))
        }
    }

    Box(
        modifier = modifier
            .padding(
                top = innerPadding.calculateTopPadding(),
                bottom = bottom,
            )
            .fillMaxSize()
            .background(TclTheme.colorScheme.tctGlobalBgColor)
            .then(
                if (onDraw) {
                    Modifier.pointerInput(Unit) {
                        awaitEachGesture {
                            // 每次点击时触发，移动时不触发
                            awaitFirstDown(requireUnconsumed = false)
                            do {
                                // 点击时触发，移动时循环
                                val event = awaitPointerEvent()
                                // 双指滚动放大，仅笔时单指
                                if (event.changes.size == 2
                                    || event.changes.size == 1
                                    && drawBoardViewModel.panOnly
                                    && event.changes[0].type != PointerType.Stylus
                                ) {
                                    val pan = event.calculatePan()
                                    with(drawBoardViewModel) {
                                        // 仅平板的竖屏支持横向移动
                                        val x = if (isTablet && !isLandScapeByScreenSize) {
                                            (translation.x + pan.x).coerceIn(
                                                (screenSizeMin - screenSizeMax).toFloat(),
                                                0f
                                            )
                                        } else translation.x
                                        val y = (translation.y + pan.y).coerceAtMost(0f)
                                        updateTrans(x, y)
                                    }
                                    event.changes.fastForEach {
                                        it.consume()
                                    }
                                }
                            } while (event.changes.any { it.pressed })
                        }
                    }
                } else {
                    val flingBehavior = object : FlingBehavior {
                        // 加速度0
                        override suspend fun ScrollScope.performFling(initialVelocity: Float) =
                            0f
                    }
                    Modifier
                        // 文本状态如果开启了防误触，则拦截手指事件
                        .pointerInput(filterEnabled) {
                            if (!filterEnabled) return@pointerInput
                            awaitPointerEventScope {
                                while (true) {
                                    // 需要在initial拦截，main无法拦截点击文本
                                    val event = awaitPointerEvent(PointerEventPass.Initial)
                                    event.changes.fastForEach {
                                        if (it.type == PointerType.Touch) {
                                            drawBoardViewModel.goneDrawer()
                                            it.consume()
                                        }
                                    }
                                }
                            }
                        }
                        .scrollable(
                            state = verticalScrollState,
                            orientation = Orientation.Vertical,
                            reverseDirection = true,
                            flingBehavior = flingBehavior,
                        )
                        .then(
                            if (isTablet) {
                                Modifier.scrollable(
                                    state = horizontalScrollState,
                                    orientation = Orientation.Horizontal,
                                    reverseDirection = true,
                                    flingBehavior = flingBehavior,
                                )
                            } else Modifier
                        )
                }
            )
            .pointerInput(state.editMode) {
                if (state.editMode) return@pointerInput
                try {
                    awaitEachGesture {
                        val down = awaitFirstDown()
                        Logger.v(TAG, "awaitEachGesture, down: $down")
                        val downTime = System.currentTimeMillis()
                        var tapTimeout = viewConfiguration.longPressTimeoutMillis
                        var touchSlop = viewConfiguration.touchSlop
                        if (down.type == PointerType.Stylus) {
                            // 扩大触控笔有效范围
                            tapTimeout *= 3
                            touchSlop *= 3
                        }
                        val tapPosition = down.position
                        do {
                            val event = awaitPointerEvent()
                            val currentTime = System.currentTimeMillis()
                            if (event.changes.size != 1) break
                            if (currentTime - downTime > tapTimeout) break
                            val change = event.changes[0]
                            val distance = (change.position - tapPosition).getDistance()
                            if (distance > touchSlop) break
                            if (change.id == down.id && !change.pressed) {
                                val tapType = change.type
                                when (tapType) {
                                    PointerType.Stylus -> {
                                        viewModel.handleIntent(
                                            RichTextIntent.UpdateMenuType(
                                                MenuBar.BRUSH
                                            )
                                        )
                                        viewModel.handleIntent(
                                            RichTextIntent.UpdateBrushMenuType(
                                                BrushMenu.PEN
                                            )
                                        )
                                        viewModel.handleIntent(
                                            RichTextIntent.UpdateEditMode(
                                                true
                                            )
                                        )
                                    }

                                    else -> {
                                        if (state.bottomMenuType == MenuBar.BRUSH) {
                                            viewModel.handleIntent(
                                                RichTextIntent.UpdateMenuType(
                                                    MenuBar.NONE
                                                )
                                            )
                                        }
                                        viewModel.handleIntent(
                                            RichTextIntent.UpdateEditMode(
                                                true
                                            )
                                        )
                                        if (state.bottomMenuType != MenuBar.BRUSH) {
                                            cursorToEnd(state)
                                        }
                                    }
                                }
                            }
                        } while (event.changes.any { it.id == down.id && it.pressed })
                    }
                } catch (ex: Exception) {
                    Logger.e(TAG, "awaitTapEventJob error: ${ex.message}")
                }
            }
            .pointerInput(onText) {
                if (!onText) return@pointerInput
                // 需求，点击底部空白处。光标移动到最后一个可编辑位置
                detectTapGestures(onTap = { cursorToEnd(state) })
            },
    ) {
        val verticalScrollProvider = { drawBoardViewModel.translation.y }
        val horizontalScrollProvider = { drawBoardViewModel.translation.x }
        val fixScrollState = rememberScrollState()
        val dimens = getGlobalDimens()

        var titleHeight by remember { mutableIntStateOf(0) }

        // 锁定scroll滚动量，临时实现，避免更新内容导致自动滚动，画板错位
        LaunchedEffect(fixScrollState) {
            snapshotFlow { fixScrollState.value }
                .distinctUntilChanged()
                .collect {
                    scope.launchIO {
                        with(drawBoardViewModel) {
                            updateTransByDelta(y = -it.toFloat())
                        }
                        fixScrollState.scrollTo(0)
                    }
                }
        }

//        if(state.bottomMenuType == MenuTypes.BRUSH){
//            MeshStyle(
//                getTranslationY = verticalScrollProvider,
//                bgMode = bgMode,
//                bgColor = bgColor
//            )
//        }


        val contentFocus = remember { FocusRequester() }

        if (!ActivityManager.isUserAMonkey()) {
            Column(
                modifier = Modifier
                    // 利用scrollable触发全数据渲染，内存与性能存在较高开销，后续优化
                    .verticalScroll(fixScrollState, enabled = false)
                    .graphicsLayer {
                        if (isTablet) {
                            translationX = horizontalScrollProvider.invoke()
                        }
                        translationY = verticalScrollProvider.invoke()
                    }
                    .onGloballyPositioned {
                        drawBoardViewModel.updateRichTextSize(it.size)
                    }
            ) {

                // 标题栏
                TitleBlock(
                    darkTheme = (isSystemInDarkTheme() && state.bgColor == Skin.defColor),
                    onTitleChange = { str ->
                        onTitleChange.invoke(str)
                        // 标题修改时回到顶部
                        drawBoardViewModel.updateTrans(x = 0f, y = 0f)
                    },
                    onFocusChanged = onFocusChanged,
                    title = title,
                    onEnterPress = {
                        contentFocus.tryToRequestFocus()
                    },
                    modifier = Modifier
                        .padding(
                            start = 24.dp,
                            top = dimens.titleTopMargin,
                            bottom = dimens.titleBottomMargin
                        )
                        .onSizeChanged { size -> titleHeight = size.height }

                )


                // 富文本编辑框
                RichTextEditor(
                    viewModel = viewModel,
                    onAddContent = onAddContent,
                    onUpdateContent = onUpdateContent,
                    onRemoveContent = onRemoveContent,
                    modifier = Modifier.focusRequester(contentFocus)
                )
            }
        }
        // 拦截底部长按选择事件
        if (onDraw) {
            // 再加一层Box用于无障碍播报，规避子Box因为enabled = false 播报已停用
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .semantics {
                        contentDescription =
                            context.getString(com.tcl.ai.note.base.R.string.draw_panel)
                    }
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .clickable(
                            enabled = false
                        ) { }
                        .invisibleSemantics()
                ) { }
            }

        }
//        LowLatencyDrawBoard(
//            noteId = state.note?.noteId ?: -1L,
//            enabled = onDraw,
//            onNewDrawing = {
//                viewModel.handleIntent(RichTextIntent.UpdateSaveState(canSave = true))
//                viewModel.handleIntent(RichTextIntent.RequestSave)
//            },
//            onTouch = {
//                // viewModel.handleIntent(RichTextIntent.UpdateBrushMenuType(""))
//                drawBoardViewModel.goneDrawer()
//            },
//            drawBoardViewModel = drawBoardViewModel,
//            modifier = Modifier.fillMaxSize(),
//        )

        //TextAndDrawBoard()
    }
}

private const val TAG = "EditContentContainer"