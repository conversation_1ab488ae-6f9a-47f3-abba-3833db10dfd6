package com.tcl.ai.note.handwritingtext.ui.richtext

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.util.fastForEachIndexed
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.BuildConfig
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.ImageBlockScaleMode
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.intent.RichTextIntent
import com.tcl.ai.note.handwritingtext.ui.richtext.component.ImageBlock
import com.tcl.ai.note.handwritingtext.ui.richtext.component.TextBlock
import com.tcl.ai.note.handwritingtext.ui.richtext.component.TodoBlock
import com.tcl.ai.note.handwritingtext.vm.DrawBoardViewModel
import com.tcl.ai.note.handwritingtext.vm.RichTextViewModel
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.tryToRequestFocus
import com.tcl.ai.note.voicetotext.view.widget.AudioBlockComponent
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 富文本编辑区域
 */
@Composable
fun RichTextEditor(
    viewModel: RichTextViewModel = hiltViewModel(),
    drawBoardViewModel: DrawBoardViewModel = hiltViewModel(),
    darkTheme: Boolean = (isSystemInDarkTheme() && viewModel.state.value.bgColor == Skin.defColor),
    onAddContent: (EditorContent, Int?) -> Unit,
    onUpdateContent: (Int, EditorContent) -> Unit,
    onRemoveContent: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    val currentFocusedType = remember { mutableStateOf<EditorContent?>(null) }
    val focusRequesters = remember { mutableMapOf<Int, FocusRequester>() }
    val state by viewModel.state.collectAsState()
    var brushMenuType = state.brushMenuType
    // 当前点击的BottomMenu类型
    var bottomMenuType = state.bottomMenuType
    // 在样式更新时使用 derivedStateOf 避免重组
    val currentTextStyle by remember {
        derivedStateOf { viewModel.currentTextStyle.value }
    }
    // 滚动控制器
    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope() // 获取当前可组合作用域的协程上下文

    // 动态同步焦点请求器与内容列表
    LaunchedEffect(state.contents.size) {
        // 添加延迟，确保在状态变化后有足够的时间初始化
        delay(100)
        try {
            state.contents.forEachIndexed { index, _ ->
                focusRequesters.getOrPut(index) { FocusRequester() }
            }
            Logger.d(TAG, "Focus requesters synchronized with content list, size: ${focusRequesters.size}")
        } catch (e: Exception) {
            Logger.e(TAG, "Error synchronizing focus requesters: ${e.message}")
        }
    }


    val onDraw = state.bottomMenuType == MenuBar.BRUSH
    var wasTitleFocused by remember { mutableStateOf(false) }
    LaunchedEffect(onDraw) {
        if (!onDraw && state.editMode) {
            // 恢复上次编辑位置
            drawBoardViewModel.resumeLastTextTrans()
            try {
                // 添加延迟，确保在从手绘模式切换回来后有足够的时间初始化
                delay(200)
                Logger.d(TAG, "try to focus on: ${state.focusedIndex}")
                // 直接利用map聚焦，因在构建时才往map存入，能确保焦点能成功focus
                focusRequesters[state.focusedIndex]?.tryToRequestFocus()
            } catch (ex: Exception) {
                Logger.e(TAG, "resume edit cursor error: ${ex.message}")
            }
            if (wasTitleFocused) {
                val actualLen = state.title.length
                val curPos = state.titleCursorPosition
                val safePos = if (curPos < 0) actualLen else curPos.coerceAtMost(actualLen)
                if (curPos != safePos) {
                    viewModel.handleIntent(RichTextIntent.UpdateTitleCursorPosition(safePos))
                }
                viewModel.handleIntent(RichTextIntent.UpdateTitleFocus(true))
                wasTitleFocused = false
            }
        }else if(onDraw && state.editMode){
            // 手绘模式
            wasTitleFocused = state.titleCursorPosition >= 0
        }
    }

    // 显示编辑器内容
    Column(modifier = modifier.fillMaxSize()) {
            state.contents.fastForEachIndexed { index, item ->
                when (item) {
                    is EditorContent.TextBlock -> {
                        val focusRequester = focusRequesters.getOrPut(index) { FocusRequester() }
                        TextBlock(
                            viewModel,
                            state.contents.size,
                            block = item,
                            focusRequester = focusRequester,
                            shouldFocus = state.focusedIndex == index,
                            index = index,
                            darkTheme = (isSystemInDarkTheme() && viewModel.state.value.bgColor == Skin.defColor),
                            onAddContent = { textBlock, index  ->
                                onAddContent(textBlock, index)
                                if (index != null) {
                                    focusRequesters.getOrPut(index) { FocusRequester() }.tryToRequestFocus()
                                }
                            },
                            onUpdateContent = { idx, updated ->
                                onUpdateContent(idx, updated)
                                // 确保焦点器与内容同步
                                if (idx >= focusRequesters.size) {
                                    focusRequesters.getOrPut(idx) { FocusRequester() }
                                }
                                // 滚动到视图区域，确保更新可见
                                coroutineScope.launch {
                                    scrollToListItem(listState, idx) // 滚动到更新项
                                }
                            },
                            onRemoveContent = {
                                // 增加索引有效性检查
                                if (index !in state.contents.indices) return@TextBlock

                                if (state.contents.size > 1) {
                                    val newIndex = (index - 1).coerceAtLeast(0)
                                    val item = state.contents.getOrNull(newIndex)
                                    if (item != null && item is EditorContent.AudioBlock) {
                                        onRemoveContent(index)
                                    } else {
                                        onRemoveContent(index) // 删除当前内容
                                        // 更新焦点到上一个或下一个
                                        //val newIndex = (index - 1).coerceAtLeast(0)
                                        currentFocusedType.value = state.contents.getOrNull(newIndex)
                                    }
                                } else {
                                    // 如果只剩下一个待办事项，则清空内容而不是删除
                                    val updatedBlock = EditorContent.TextBlock(
                                        text = TextFieldValue("")
                                    )
                                    onUpdateContent(index, updatedBlock)
                                }

                                // 精确设置焦点
                                val newIndex = (index - 1).coerceAtLeast(0)
                                focusRequesters[newIndex]?.tryToRequestFocus()
                                currentFocusedType.value = state.contents.getOrNull(newIndex)
                            },
                            onCursorChange = { cursorPosition,isChange ->
                                if(index!=state.focusedIndex){
                                    viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(index))
                                    viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(cursorPosition,true))
                                }else{
                                    viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(cursorPosition,isChange))
                                }
                                currentFocusedType.value = item // 聚焦到文本块
                                coroutineScope.launch {
                                    scrollToListItem(listState, index) // 滚动到焦点
                                }
                                drawBoardViewModel.saveLastTextTrans()
                            },
                            currentTextStyle = currentTextStyle.copy( // 继承当前样式
                                color = colorResource(R.color.text_edit_color) // 强制使用标题色
                            )
                        )
                    }

                    is EditorContent.ImageBlock -> {
                        val focusRequester = focusRequesters.getOrPut(index) { FocusRequester() }
                        ImageBlock(
                            viewModel,
                            item,
                            focusRequester = focusRequester,
                            shouldFocus = state.focusedIndex == index,
                            onDelete = {
                                onRemoveContent(index) // 删除内容
                                // 更新焦点到上一个或下一个待办事项
                                val newIndex = (index - 1).coerceAtLeast(0)
                                currentFocusedType.value = state.contents.getOrNull(newIndex)
                            },
                            onEnterKeyPressed = {
                                val newBlock = EditorContent.TextBlock(
                                    text = TextFieldValue("").copy(
                                        selection = TextRange(0) // 明确设置光标位置
                                    )
                                )
                                onAddContent(newBlock, index + 1)
                                currentFocusedType.value = newBlock
                                viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(0))
                                coroutineScope.launch {
                                    scrollToListItem(listState, index + 1) // 滚动到新项
                                }
                            },
                            onLargeScale = {
                                viewModel.handleIntent(RichTextIntent.UpdateContent(
                                    index,
                                    item.copy(scaleMode = ImageBlockScaleMode.Large)
                                ))
                            },
                            onOriginScale = {
                                viewModel.handleIntent(RichTextIntent.UpdateContent(
                                    index,
                                    item.copy(scaleMode = ImageBlockScaleMode.Origin)
                                ))
                            },
                            onSmallScale = {
                                viewModel.handleIntent(RichTextIntent.UpdateContent(
                                    index,
                                    item.copy(scaleMode = ImageBlockScaleMode.Small)
                                ))
                            },
                        )
                    }

                    is EditorContent.TodoBlock -> {
                        val focusRequester = focusRequesters.getOrPut(index) { FocusRequester() }
                        TodoBlock(
                            viewModel,
                            block = item,
                            focusRequester = focusRequester,
                            shouldFocus = state.focusedIndex == index,
                            index = index,
                            darkTheme = darkTheme,
                            currentTextStyle = currentTextStyle.copy(
                               /* color = if (item.isDone) colorResource(R.color.btn_new_category_create)
                                else colorResource(R.color.text_edit_color)*/
                                color =  colorResource(darkTheme.judge(R.color.white, R.color.text_edit_color))
                            ),
                            onAddContent = { todoBlock, index  ->
                                onAddContent(todoBlock, index)
                                if (index != null) {
                                    focusRequesters.getOrPut(index) { FocusRequester() }.tryToRequestFocus()
                                }
                            },
                            onUpdate = { updatedBlock ->
                                onUpdateContent(index, updatedBlock)
                            },
                            onCursorChange = { cursorPosition,isChange ->
                                if(index!=state.focusedIndex){
                                    viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(index))
                                    viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(cursorPosition,true))
                                }else{
                                    viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(cursorPosition,isChange))
                                }
                                currentFocusedType.value = item // 聚焦待办事项块
                                coroutineScope.launch {
                                    scrollToListItem(listState, index) // 滚动到焦点
                                }
                                drawBoardViewModel.saveLastTextTrans()
                            },
                            onEnterKeyPressed = {
                                // 添加新块到当前块后面
                                val newTodoBlock = EditorContent.TodoBlock(
                                    text = TextFieldValue("").copy(
                                        selection = TextRange(0) // 明确设置光标位置
                                    ),
                                    isDone = false
                                )
                                onAddContent(newTodoBlock, index + 1) // 添加新内容块
                                currentFocusedType.value = newTodoBlock
                                viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(0))
                                coroutineScope.launch {
                                    scrollToListItem(listState, index + 1) // 滚动到新项
                                }
                            },
                            onDeleteBlock = {
                                // 如果列表还有其他项，删除当前项
                                if (state.contents.size > 1) {
                                    onRemoveContent(index) // 删除当前内容
                                    // 更新焦点到上一个或下一个待办事项
                                    val newIndex = (index - 1).coerceAtLeast(0)
                                    currentFocusedType.value = state.contents.getOrNull(newIndex)
                                } else {
                                    // 如果只剩下一个待办事项，则转换待办事项为文本块
                                    val updatedBlock = EditorContent.TextBlock(
                                        text = TextFieldValue(""),
                                        paragraphStyle = ParagraphStyle.NONE
                                    )
                                    onUpdateContent(index, updatedBlock)
                                    if(index!=state.focusedIndex){
                                        viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(index))
                                    }
                                }
                            }
                        )
                    }
                    is EditorContent.AudioBlock -> {
                        if (!isTablet) {
                            val focusRequester = focusRequesters.getOrPut(index) { FocusRequester() }
                            AudioBlockComponent(
                                enabled =   bottomMenuType != MenuBar.BRUSH,
                                darkTheme = darkTheme,
                                focusRequester = focusRequester,
                                audioPath = item.audioPath,
                                onStopRecordClick = { audioPath ->

                                },
                                onMoreClick = {},
                                onAudioToTextClick = {
                                    viewModel.startNavigation()
                                },
                                onDeleteClick = { audioPath ->
                                    if (audioPath == item.audioPath) {
                                        onRemoveContent(index) // 删除内容
                                    }
                                },
                                onEnterKeyPressed = {
                                    // 添加新块到当前块后面
                                    val newTextBlock = EditorContent.TextBlock(
                                        text = TextFieldValue("").copy(
                                            selection = TextRange(0) // 明确设置光标位置
                                        )
                                    )
                                    onAddContent(newTextBlock, index + 1) // 添加新内容块

                                    currentFocusedType.value = newTextBlock
                                    coroutineScope.launch {
                                        scrollToListItem(listState, index + 1) // 滚动到新项
                                    }
                                },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(
                                        start = if (BuildConfig.IS_PHONE) 20.dp else 24.dp,
                                        end = if (BuildConfig.IS_PHONE) 20.dp else 24.dp
                                    )
                            )
                        }
                    }

                    is EditorContent.RichTextV2 ->{}
                }
            }
    }

    // 监控富文本区焦点，更新图标显示
    LaunchedEffect(currentFocusedType.value) {
        viewModel.handleIntent(RichTextIntent.UpdateCurrentFocusedType(currentFocusedType.value))
    }
}

suspend fun scrollToListItem(listState: LazyListState, index: Int) {
    // try {
    //     // 检查索引范围并执行滚动
    //     if (index in 0 until listState.layoutInfo.totalItemsCount) {
    //         listState.animateScrollToItem(index)
    //     }
    // } catch (e: Exception) {
    //     e.printStackTrace()
    //     // 处理错误，比如索引越界等 -- 可忽略或打印日志
    // }
}

private const val TAG = "RichTextEditor"