package com.tcl.ai.note.handwritingtext.ui.richtext.base

import android.annotation.SuppressLint
import android.content.ClipboardManager
import android.content.Context
import android.graphics.*
import android.icu.text.BreakIterator
import android.os.Bundle
import android.text.Editable
import android.text.Layout
import android.text.method.PasswordTransformationMethod
import android.util.AttributeSet
import android.util.Size
import android.view.*
import android.view.accessibility.AccessibilityNodeInfo
import android.view.autofill.AutofillManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatEditText
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.marginBottom
import androidx.core.view.marginLeft
import androidx.core.view.marginRight
import androidx.core.view.marginTop
import com.tcl.ai.note.handwritingtext.ui.utils.scale.MatrixInfo
import com.tcl.ai.note.utils.*
import kotlinx.coroutines.Runnable
import java.lang.reflect.Field


open class BaseRichTextEditView : AppCompatEditText {
    //观察文本的变动
    private var isNeedMonitoring = true
    // 插入水滴
    private val insertCursorHandle = EditCursorView(
        this,
        EditCursorView.CursorType.INSERT
    )
    // 左选区水滴
    private val leftSelectionCursorHandle = EditCursorView(
        this,
        EditCursorView.CursorType.LEFT_SELECTION
    )
    // 右选区水滴
    private val rightSelectionCursorHandle = EditCursorView(
        this,
        EditCursorView.CursorType.RIGHT_SELECTION
    )
    // view的实际宽度
    var viewWidth = 0
        private set
    // view的实际高度
    var viewHeight = 0
        private set
    // 插入光标的菜单栏
    var insertionActionMode: ActionMode? = null
        private set

    private val fakeText = """
        豫章故郡，洪都新府。星分翼轸，地接衡庐。襟三江而带五湖，控蛮荆而引瓯越。物华天宝，龙光射牛斗之墟；人杰地灵，徐孺下陈蕃之榻。雄州雾列，俊采星驰。台隍枕夷夏之交，宾主尽东南之美。都督阎公之雅望，棨戟遥临；宇文新州之懿范，襜帷暂驻。十旬休假，胜友如云；千里逢迎，高朋满座。腾蛟起凤，孟学士之词宗；紫电青霜，王将军之武库。家君作宰，路出名区；童子何知，躬逢胜饯。

        时维九月，序属三秋。潦水尽而寒潭清，烟光凝而暮山紫。俨骖騑于上路，访风景于崇阿；临帝子之长洲，得天人之旧馆。层峦耸翠，上出重霄；飞阁流丹，下临无地。鹤汀凫渚，穷岛屿之萦回；桂殿兰宫，即冈峦之体势。

        披绣闼，俯雕甍，山原旷其盈视，川泽纡其骇瞩。闾阎扑地，钟鸣鼎食之家；舸舰弥津，青雀黄龙之舳。云销雨霁，彩彻区明。落霞与孤鹜齐飞，秋水共长天一色。渔舟唱晚，响穷彭蠡之滨；雁阵惊寒，声断衡阳之浦。

        遥襟甫畅，逸兴遄飞。爽籁发而清风生，纤歌凝而白云遏。睢园绿竹，气凌彭泽之樽；邺水朱华，光照临川之笔。四美具，二难并。穷睇眄于中天，极娱游于暇日。天高地迥，觉宇宙之无穷；兴尽悲来，识盈虚之有数。望长安于日下，目吴会于云间。地势极而南溟深，天柱高而北辰远。关山难越，谁悲失路之人？萍水相逢，尽是他乡之客。怀帝阍而不见，奉宣室以何年？

        嗟乎！时运不齐，命途多舛。冯唐易老，李广难封。屈贾谊于长沙，非无圣主；窜梁鸿于海曲，岂乏明时？所赖君子见机，达人知命。老当益壮，宁移白首之心？穷且益坚，不坠青云之志。酌贪泉而觉爽，处涸辙以犹欢。北海虽赊，扶摇可接；东隅已逝，桑榆非晚。孟尝高洁，空余报国之情；阮籍猖狂，岂效穷途之哭！

        勃，三尺微命，一介书生。无路请缨，等终军之弱冠；有怀投笔，慕宗悫之长风。舍簪笏于百龄，奉晨昏于万里。非谢家之宝树，接孟氏之芳邻。他日趋庭，叨陪鲤对；今兹捧袂，喜托龙门。杨意不逢，抚凌云而自惜；钟期既遇，奏流水以何惭？

        呜乎！胜地不常，盛筵难再；兰亭已矣，梓泽丘墟。临别赠言，幸承恩于伟饯；登高作赋，是所望于群公。敢竭鄙怀，恭疏短引；一言均赋，四韵俱成。请洒潘江，各倾陆海云尔：

        滕王高阁临江渚，佩玉鸣鸾罢歌舞。

        画栋朝飞南浦云，珠帘暮卷西山雨。

        闲云潭影日悠悠，物换星移几度秋。

        阁中帝子今何在？槛外长江空自流。
    """.trimIndent() + "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n"


    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    init {
        layoutParams = ViewGroup.LayoutParams(
            screenSizeMin,
            ViewGroup.LayoutParams.MATCH_PARENT,
        )
        setTextColor(Color.BLACK)
        isFocusable = true
        isFocusableInTouchMode = true
        // 设置游标颜色
        try {
            val cursorColorInt = ContextCompat.getColor(context, com.tcl.ai.note.base.R.color.text_field_border)
            highlightColor = cursorColorInt
            textCursorDrawable?.setTint(cursorColorInt)

            textSelectHandleLeft?.setTint(cursorColorInt)
            textSelectHandleRight?.setTint(cursorColorInt)
            textSelectHandle?.setTint(cursorColorInt)
        } catch (e: Exception) {
            Logger.e(TAG, "Failed to set cursor color: ${e.message}")
        }

        customInsertionActionModeCallback = object : ActionMode.Callback2() {
            override fun onGetContentRect(mode: ActionMode?, view: View?, outRect: Rect?) {
                super.onGetContentRect(mode, view, outRect)
                if (view == null) return
                val cursorPointF = getCursorPointF(index = selectionStart, includeScale = false)
                // 插入柄的菜单栏放置的位置
                outRect?.set(
                    cursorPointF.x.toInt(),
                    cursorPointF.y.toInt(),
                    cursorPointF.x.toInt(),
                    cursorPointF.y.toInt()
                )
                Logger.d(TAG, "customInsertionActionModeCallback onGetContentRect: view: $view, outRect: $outRect")
            }

            override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                mode?.menu?.clear()
                Logger.d(TAG, "customInsertionActionModeCallback onCreateActionMode")
                val systemContext = context.createPackageContext("android", 0)
                val selectAllStr = systemContext.getString(android.R.string.selectAll)
                val pasteStr = systemContext.getString(android.R.string.paste)
                val autofillStr = systemContext.getString(android.R.string.autofill)
                if (canPaste()) {
                    // 添加粘贴 Paste
                    menu?.add(0, android.R.id.paste, 0, pasteStr)
                }
                // 添加全选 Select All
                menu?.add(0, android.R.id.selectAll, 1, selectAllStr)
                if (canRequestAutofill()) {
                    // 添加Auto Fill
                    menu?.add(0, android.R.id.autofill, 2, autofillStr)
                }
                insertCursorHandle.showOrUpdate()
                return true
            }
            override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                updateCursorHandle()
                Logger.d(TAG, "customInsertionActionModeCallback onPrepareActionMode")
                return true
            }

            override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                when (item?.itemId) {
                    android.R.id.selectAll -> {
                        val bundle = Bundle().apply {
                            putInt(AccessibilityNodeInfo.ACTION_ARGUMENT_SELECTION_START_INT, 0)
                            putInt(AccessibilityNodeInfo.ACTION_ARGUMENT_SELECTION_END_INT, text?.length ?: 0)
                        }
                        performAccessibilityAction(AccessibilityNodeInfo.ACTION_SET_SELECTION, bundle)
                    }

                    android.R.id.paste -> {
                        onTextContextMenuItem(item.itemId)
                        post {
                            // 原生逻辑，粘贴后。水滴拖动柄会消失
                            hideInsertCursorHandle()
                        }
                    }

                    android.R.id.autofill -> {
                        onTextContextMenuItem(item.itemId)
                    }
                }
                return true
            }
            override fun onDestroyActionMode(mode: ActionMode?) {
                // 需要自己调用finish。不然无法隐藏
            }
        }
        customSelectionActionModeCallback = object : ActionMode.Callback {
            override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                Logger.d(TAG, "customSelectionActionModeCallback onCreateActionMode")
                showSelectionCursor()
                return true
            }
            override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                updateCursorHandle()
                Logger.d(TAG, "customSelectionActionModeCallback onPrepareActionMode")
                return true
            }
            override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean = false
            override fun onDestroyActionMode(mode: ActionMode?) {
                leftSelectionCursorHandle?.dismiss()
                rightSelectionCursorHandle?.dismiss()
            }
        }
    }

    // 用来封装富文本，避免因为无限富文本原因，底部无法设置padding
    inner class RichTextWrapper(context: Context) : FrameLayout(context) {
        val leftMargin get() = layoutParams?.leftMargin ?: 0
        val topMargin get() = layoutParams?.topMargin ?: 0
        val rightMargin get() = layoutParams?.rightMargin ?: 0
        val bottomMargin get() = layoutParams?.bottomMargin ?: 0

        init {
            layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
        }

        private val refreshCursorHandleMeasureRunnbale = Runnable {
            leftSelectionCursorHandle?.refreshParentMeasure()
            rightSelectionCursorHandle?.refreshParentMeasure()
            insertCursorHandle?.refreshParentMeasure()
            updateCursorHandle()
        }

        override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec)
            // 这里才是view的真正大小。不受无限富文本影响
            viewWidth = measuredWidth
            viewHeight = measuredHeight
            // 通知拖动柄，父布局大小变化。
            removeCallbacks(refreshCursorHandleMeasureRunnbale)
            post(refreshCursorHandleMeasureRunnbale)
            Logger.d(TAG, "RichTextWrapper onMeasure: width: $measuredWidth, height: $measuredHeight")
        }

        override fun getLayoutParams(): LayoutParams? = super.getLayoutParams() as? LayoutParams
    }

    // 给自己封装一层ViewGroup，避免因为无限富文本原因，底部无法设置padding
    private val richTextWrapper = RichTextWrapper(context)
    private fun wrapMyself() {
        if (parent is RichTextWrapper) {
            // 已经封装过了
            return
        }
        val parentView = parent as? ViewGroup ?: return
        val index = parentView.indexOfChild(this)
        // 1. 移除自己
        parentView.removeViewAt(index)
        // 2. 创建要包裹的新父View, 并把RichText加进去
        richTextWrapper.removeAllViews()
        richTextWrapper.addView(this)
        // 3. 新 wrapper 放回原parent
        parentView.addView(richTextWrapper, index)
    }

    /**
     * 调整margin值
     */
    fun setMargins(
        left: Int = richTextWrapper.leftMargin,
        top: Int = richTextWrapper.topMargin,
        right: Int = richTextWrapper.rightMargin,
        bottom: Int = richTextWrapper.bottomMargin,
    ) {
        Logger.d(TAG, "setMargins, left: $left, top: $top, right: $right, bottom: $bottom")
        richTextWrapper.layoutParams?.apply {
            setMargins(left, top, right, bottom)
            requestLayout()
            Logger.d(TAG, "setMargins requestLayout(), $marginLeft, $marginTop, $marginRight, $marginBottom")
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        // 给自己包一层，作用：
        // 1. 添加margin效果;
        // 2. 处理系统拖动柄的逻辑
        wrapMyself()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        hideCursorHandle()
    }

    override fun onWindowFocusChanged(hasWindowFocus: Boolean) {
        super.onWindowFocusChanged(hasWindowFocus)
        Logger.d(TAG, "onWindowFocusChanged $hasWindowFocus")
    }

    override fun onFocusChanged(focused: Boolean, direction: Int, previouslyFocusedRect: Rect?) {
        super.onFocusChanged(focused, direction, previouslyFocusedRect)
        Logger.d(TAG, "onFocusChanged $focused")
        if (!focused) {
            // 没有焦点，隐藏光标
            hideCursorHandle()
            // 没有焦点，移除键盘
            hideKeyboard(this)
        }
    }

    override fun performLongClick() = try {
            // 避免内层调用startDragAndDrop发生异常
            // IllegalStateException: Drag shadow dimensions must be positive
            super.performLongClick()
        } catch (ex: Exception){
            Logger.e(TAG, "performLongClick error: ${ex.javaClass}, ${ex.message}")
            false
        }

    fun showKeyboard() {
        val imm = context.getSystemService(InputMethodManager::class.java)
        imm?.showSoftInput(this, InputMethodManager.SHOW_IMPLICIT)
    }

    private fun hideKeyboard(view: View) {
        val imm = context.getSystemService(InputMethodManager::class.java)
        imm?.hideSoftInputFromWindow(view.windowToken, 0)
    }

    // 监听光标回到屏幕的变化
    private var onCursorToScreenListener: ((translationX: Float, translationY: Float) -> Unit)? = null
    fun setOnCursorToScreenListener(onCursorToScreenListener: ((translationX: Float, translationY: Float) -> Unit)?) {
        this.onCursorToScreenListener = onCursorToScreenListener
    }

    /**
     * 显示选区光标拖动柄
     *
     * 受customSelectionActionModeCallback的显示影响
     */
    fun showSelectionCursor() {
        hideInsertCursorHandle()
        leftSelectionCursorHandle?.showOrUpdate()
        rightSelectionCursorHandle?.showOrUpdate()
    }

    /**
     * 显示插入光标拖动柄
     *
     * 延时4s隐藏插入光标，系统逻辑
     */
    private val insertCursorDelayHideRunnable = Runnable { insertCursorHandle.dismiss() }
    fun showInsertCursorHandle() = post {
        leftSelectionCursorHandle?.dismiss()
        rightSelectionCursorHandle?.dismiss()
        if (layout != null && isFocused) {
            insertCursorHandle?.showOrUpdate()
            // 系统的插入光标会自动隐藏，所以这里也做个相同处理
            removeCallbacks(insertCursorDelayHideRunnable)
//            postDelayed(insertCursorDelayHideRunnable, 4000)
        }
    }

    fun hideInsertCursorHandle() {
        removeCallbacks(insertCursorDelayHideRunnable)
        insertCursorHandle?.dismiss()
    }

    // 显示插入光标拖动柄的菜单栏, 也就是粘贴，全选功能。
    fun showInsertionActionMode() {
        if (insertionActionMode != null) {
            updateInsertionActionMode()
            return
        }
        insertionActionMode = startActionMode(customInsertionActionModeCallback, ActionMode.TYPE_FLOATING)
    }

    // 更新插入光标拖动柄的菜单栏的位置
    fun updateInsertionActionMode() {
        insertionActionMode?.invalidateContentRect()
    }

    // 隐藏插入光标拖动柄的菜单栏, 也就是粘贴，全选功能。
    // 因为插入拖动柄是自己执行了startActionMode，所以要自己隐藏
    fun hideInsertionActionMode() {
        insertionActionMode?.finish()
        insertionActionMode = null
    }

    // 只更新手柄位置。不负责是否显示
    private fun updateCursorHandle() {
        if (selectionStart == selectionEnd) {
            // 显示插入光标拖动柄，拦截原生拖动事件
            leftSelectionCursorHandle?.dismiss()
            rightSelectionCursorHandle?.dismiss()
            // customInsertionActionModeCallback是弹出菜单栏时才会触发的，所以这里每次都要showOrUpdate
            insertCursorHandle?.updatePosition()
        } else {
            // 显示插入光标拖动柄，拦截原生拖动事件
            hideInsertCursorHandle()
            // 通过customSelectionActionModeCallback显示了，这里只需要更新坐标即可
            leftSelectionCursorHandle?.updatePosition()
            rightSelectionCursorHandle?.updatePosition()
        }
    }

    // 隐藏手柄
    private fun hideCursorHandle() {
        hideInsertCursorHandle()
        leftSelectionCursorHandle?.dismiss()
        rightSelectionCursorHandle?.dismiss()
    }

    // 光标回到屏幕内
    fun editRichCursorToScreen(
        index: Int = selectionStart,
        horizontalPadding: Float = getCharWidth(),
        verticalPadding: Float = 0f,
    ): Pair<Float, Float> {
        return editRichCursorToScreen(
            index = index,
            horizontalPadding = horizontalPadding,
            verticalPadding = verticalPadding,
            onCursorToScreenListener = onCursorToScreenListener
        )
    }

    // text的选区变化
    private var onSelectionChanged: ((selStart: Int, selEnd: Int) -> Unit)? = null
    fun setonSelectionChanged(onSelectionChanged: ((selStart: Int, selEnd: Int) -> Unit)? = null) {
        this.onSelectionChanged = onSelectionChanged
    }

    override fun onSelectionChanged(selStart: Int, selEnd: Int) {
        super.onSelectionChanged(selStart, selEnd)
        Logger.d(TAG, "onSelectionChanged: selStart = $selStart, selEnd = $selEnd")
        if (layout != null) {
            // 更新光标位置
            updateCursorHandle()
            // 光标位置改变，也要屏蔽菜单栏
            hideInsertionActionMode()
        }
        onSelectionChanged?.invoke(selStart, selEnd)
    }

    /**
     * 追加富文本时，不移动光标
     */
    override fun append(text: CharSequence?, start: Int, end: Int) {
        setMonitoring(false)
        super.append(text, start, end)
        Logger.d(TAG, "appendText Finish")
        setMonitoring(true)
    }

    override fun onTextChanged(text: CharSequence?, start: Int, lengthBefore: Int, lengthAfter: Int) {
        super.onTextChanged(text, start, lengthBefore, lengthAfter)
        if (layout != null && (layout.width > width || layout.height > height)) {
            requestLayout()
        }
        Logger.d(TAG, "onTextChanged, isNeedMonitoring: $isNeedMonitoring")
        // 文本变化后，把光标移动回屏幕内
        if (isNeedMonitoring) {
            editRichCursorToScreen()
        }
    }

    fun setMonitoring(isNeedMonitoring: Boolean) {
        this.isNeedMonitoring = isNeedMonitoring
    }
    override fun scrollTo(x: Int, y: Int) {
        // 关闭滚动
        super.scrollTo(0, 0)
    }

    // text的layout变化，用来限制滚动量的
    private var onMeasureChanged: ((width: Int, height: Int) -> Unit)? = null
    fun setOnMeasureChanged(onMeasureChanged: ((width: Int, height: Int) -> Unit)? = null) {
        this.onMeasureChanged = onMeasureChanged
    }
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        Logger.d(TAG, "onMeasure, width: $measuredWidth, height: $measuredHeight")
        // 无限富文本
        setMeasuredDimension(measuredWidth, layout.height + lineHeight)
        Logger.d(TAG, "onMeasure, unlimited, width: $measuredWidth, height: $measuredHeight")
        // 关闭系统光标，自定义光标。需求变更，暂时注释
        // 谷歌的系统插入光标拖动柄显示无法感知，自定义插入光标拖动柄处理
        closeSystemCursorHandle(isHideSelectionHandle = false)
        onMeasureChanged?.invoke(measuredWidth, measuredHeight)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (w != oldw) {
            // 屏幕旋转，隐藏拖动柄
            hideCursorHandle()
        }
    }

    // 键盘弹出时移动光标到键盘上方
    // 单击必定会弹出键盘，系统逻辑
    private fun insertCursorToScreenWhenShowIme() {
        // 显示光标时，键盘肯定会弹出来，此时要把光标移动到屏幕内
        if (selectionStart == selectionEnd) {
            if (getImeHeight() == 0) {
                // 等待键盘弹出后，把光标移动到屏幕内
                // 注：光标没有正常移动到屏幕内，可能是awaitShowIme的postDelay太快了
                awaitShowIme {
                    // 移动光标到屏幕内，并获取的偏移量
                    val (offsetX, offsetY) = editRichCursorToScreen()
                    Logger.d(TAG, "awaitShowIme finish, imeHeight： $it, offsetX： $offsetX, offsetY： $offsetY")
                    // 等待键盘隐藏
                    if (getImeHeight() != 0) {
                        restoreCursorWhenHideIme()
                    }
                }
            } else {
                // 键盘已经弹出，所以不需要等待
                post {
                    editRichCursorToScreen()
                }
            }
        }
    }

    // 等待键盘消失，还原上次的偏移量
    private fun restoreCursorWhenHideIme() {
        if (selectionStart == selectionEnd) {
            awaitHideIme { imeHeight ->
                // 还原键盘高度
                val maxTranslationY = (measuredHeight) * scaleY + getNavigateBarHeight() - richTextWrapper.height - imeHeight
                Logger.d(TAG, "awaitHideIme finish, translationY: ${-translationY}, maxTranslationY: ${(maxTranslationY)}, measuredHeight: ${measuredHeight}, visibleHeight: ${richTextWrapper.height}, NavigateBarHeight: ${getNavigateBarHeight()}, imeHeight: $imeHeight")
                // 1. 滑到了布局底部，收起键盘时，要减去键盘的高度
                // 和Samsung逻辑保持一致
                if (-translationY >= maxTranslationY) {
                    onCursorToScreenListener?.invoke(translationX, -maxTranslationY)
                }
            }
        }
    }

    /**
     * 获取缩放位移后，view的实际位置
     */
    fun getViewRealRectInParent(): RectF {
        return RectF(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat())
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (event == null) return super.onTouchEvent(event)
        Logger.d(TAG, "onTouchEvent, event: $event")

        // 判断是否在view的点击范围中
        val touchableRect = getViewRealRectInParent()
        // 手指事件需要乘以缩放位移比例
        matrix.mapRect(touchableRect)
        val isValidTouchEvent = touchableRect.contains(event.x, event.y)
        if (!isValidTouchEvent) {
            // 不再点击范围内，不处理事件
            val cancelEvent = MotionEvent.obtain(event).apply { action = MotionEvent.ACTION_CANCEL }
            val isInterrupted = super.onTouchEvent(cancelEvent)
            cancelEvent.recycle()
            return isInterrupted
        }

        // 还原缩放前的Matrix, 否则触摸事件和实际点击的不一样
        val newEvent = MotionEvent.obtain(event)
        // 手指事件需要乘以缩放位移比例
        val invertScaleMatrix = Matrix()
        matrix.invert(invertScaleMatrix)
        newEvent.transform(invertScaleMatrix)
        try {
            when (newEvent.actionMasked) {
                MotionEvent.ACTION_DOWN -> {
                    return super.onTouchEvent(newEvent)
                }

                MotionEvent.ACTION_UP -> {
                    // 每次点击事件，都要重新显示插入光标
                    // 因为插入光标不受customInsertionActionModeCallback控制
                    showInsertCursorHandle()
                    // 因为父布局拦截的原因，这里永远都是单击事件
                    // 单击必定会弹出键盘，系统逻辑
                    insertCursorToScreenWhenShowIme()
                    return super.onTouchEvent(newEvent)
                }
            }
        } finally {
            newEvent.recycle()
        }
        return super.onTouchEvent(event)
    }

    /**
     * 富文本缩放位移
     */
    fun changeScale(matrixInfo: MatrixInfo) {
        Logger.d(TAG, "changeScale: ${matrixInfo}")
        if (isAttachedToWindow) {
            translationX = matrixInfo.offsetX
            translationY = matrixInfo.offsetY
            pivotX = matrixInfo.scaleCenterX
            pivotY = matrixInfo.scaleCenterY
            scaleX = matrixInfo.scale
            scaleY = matrixInfo.scale
        }
        // 缩放位移后修正光标位置
        updateCursorHandle()
        // 更新插入拖动柄菜单栏的位置。很吃性能
        updateInsertionActionMode()
    }

    companion object {
        private const val TAG = "BaseRichTextEditView"
    }
}

/**
 * 禁用系统光标移除，选取功能。
 * 自定义自己的光标
 */
private var editorField: Field? = null
private var insertionControllerField: Field? = null
private var selectionControllerField: Field? = null
@SuppressLint("DiscouragedPrivateApi")
private fun TextView.closeSystemCursorHandle(isHideInsertHandle: Boolean = true, isHideSelectionHandle: Boolean = true) {
    try {
        editorField = editorField ?: TextView::class.java.getDeclaredField("mEditor")
        editorField?.isAccessible = true
        val editor = editorField?.get(this)
        editor?.let {
            if (isHideInsertHandle) {
                // 1. 禁用插入柄
                insertionControllerField = insertionControllerField ?: it.javaClass.getDeclaredField("mInsertionControllerEnabled")
                insertionControllerField?.isAccessible = true
                insertionControllerField?.setBoolean(it, false)
            }
            if (isHideSelectionHandle) {
                //2. 禁用选区柄
                selectionControllerField = selectionControllerField ?: it.javaClass.getDeclaredField("mSelectionControllerEnabled")
                selectionControllerField?.isAccessible = true
                selectionControllerField?.setBoolean(it, false)
            }
        }
    } catch (e: Exception) {
        // 反射失败，兼容性问题
    }
}

/**
 * 获取单词宽度
 */
fun EditText.getWordBoundary(index: Int): IntRange {
    val finder = BreakIterator.getWordInstance()
    finder.setText(text)
    val start = finder.preceding(index)
    val end = finder.following(index)
    return IntRange(start.coerceAtLeast(0), end.coerceAtMost(text.length - 1))
}
/**
 * 编辑光标回到屏幕内
 *
 * 返回偏移量
 */
fun EditText.editRichCursorToScreen(
    index: Int = selectionStart,
    includeIME: Boolean = false,
    horizontalPadding : Float = getCharWidth(index),
    verticalPadding : Float = 0f,
    onCursorToScreenListener: ((translationX: Float, translationY: Float) -> Unit)? = null,
): Pair<Float, Float> {
    if (layout != null) {
        val overRectF = getCursorToParentRectF(index = index ,includeIME = includeIME)

        val oldTranslationX = this.translationX
        var translationX = this.translationX
        if (overRectF.left < 0f) {
            // 超出屏幕左侧，translationX为负数，overRectF.left为超出屏幕为负数，所以减去偏移量
            translationX = translationX - overRectF.left + horizontalPadding
        } else {
            // 在输入文字时，保持左侧一个horizontalPadding距离
            translationX = if (overRectF.left < horizontalPadding) translationX - overRectF.left + horizontalPadding else translationX
        }
        if (overRectF.right < 0f) {
            // 超出屏幕右侧，translationX为正数，overRectF.right为超出屏幕为负数，所以加上偏移量
            translationX = translationX + overRectF.right - horizontalPadding
        } else {
            // 在输入文字时，保持右侧一个horizontalPadding距离
            translationX = if (overRectF.right < horizontalPadding) translationX - (horizontalPadding - overRectF.right) else translationX
        }

        val oldTranslationY = this.translationY
        var translationY = this.translationY
        if (overRectF.top < 0f) {
            translationY = translationY - overRectF.top + verticalPadding
        } else {
            // 在输入文字时，保持顶部一个verticalPadding距离
            translationY = if (overRectF.top < verticalPadding) translationY - overRectF.top + verticalPadding else translationY
        }
        if (overRectF.bottom < 0f) {
            // 位移距离 + 1，避免精度问题，导致光标底层的气泡和键盘重叠
            translationY = translationY + overRectF.bottom - verticalPadding + 1
        } else {
            // 在输入文字时，保持底部一个verticalPadding距离
            translationY = if (overRectF.bottom < verticalPadding) translationY + overRectF.bottom - verticalPadding + 1 else translationY
        }
        Logger.d("EditText", "editRichCursorToScreen, translationX: $translationX, translationY: $translationY")

        // 通知缩放位移变化。这里的回调方法才是实际光标位移的地方
        onCursorToScreenListener?.invoke(translationX, translationY)
        return Pair(translationX - oldTranslationX, translationY - oldTranslationY)
    }
    return Pair(0f, 0f)
}

/**
 * 获取当前光标位置的行首索引
 */
fun EditText.getLineStartAtCursorIndex(cursorIndex: Int): Int {
    val layout: Layout? = getLayout()
    if (layout != null) {
        val line = layout.getLineForOffset(cursorIndex)
        return layout.getLineStart(line)
    }
    return cursorIndex
}

/**
 * 获取当前光标位置的行首索引
 */
fun EditText.getLineEndAtCursorIndex(cursorIndex: Int): Int {
    val layout: Layout? = getLayout()
    if (layout != null) {
        val line = layout.getLineForOffset(cursorIndex)
        return layout.getLineEnd(line)
    }
    return cursorIndex
}

/**
 * 获取当前字宽
 */
fun EditText.getCharWidth(index: Int = this.selectionStart): Float {
    val text = text ?: return 0f
    if (index < 0 || index >= text.length) return 0f
    val char = text[index].toString()
    return paint.measureText(char) * scaleX
}

/**
 * 滚动到屏幕内
 *
 * @param includeIme 是否包含键盘高度
 *
 * @return 获取光标到屏幕上下左右的距离，超出屏幕为负数，在屏幕内为正数
 *
 */
fun EditText.getCursorToParentRectF(
    index: Int = selectionStart,
    includeIME: Boolean = false
): RectF {
    var imeHeight = 0
    if (includeIME) {
        val insets = ViewCompat.getRootWindowInsets(this)
        val imeInsets = insets?.getInsets(WindowInsetsCompat.Type.ime())
        imeHeight = imeInsets?.bottom ?: 0
    }

    val cursorPointF = getCursorPointF(index)
    val parentViewSize = getParentViewSize()
    val cursorHeight = getCursorHeight(index)
    val overRectF = RectF(
        cursorPointF.x,
        cursorPointF.y,
        parentViewSize.width - cursorPointF.x,
        parentViewSize.height - cursorPointF.y - cursorHeight - imeHeight,
    )
    Logger.d("EditText", "getCursorToParentRectF, overRectF: $overRectF, cursorPointF: $cursorPointF, parentViewSize: $parentViewSize, imeHeight: $imeHeight")
//    translationX = translationX - overRectF.left - overRectF.right
//    translationY = translationY - overRectF.top - overRectF.bottom
    return overRectF
}

/**
 * 获取光标宽度
 */
fun EditText.getCursorWidth(): Float {
    // 获取不了光标实际宽度，等待有缘人解决。这里设置成1dp
    val cursorWidth = 1 * resources.displayMetrics.density
    return cursorWidth * scaleX
}
/**
 * 获取光标高度
 */
fun EditText.getCursorHeight(index: Int = selectionStart, includeLineSpacing: Boolean = false): Float {
    var cursorHeight = 0
    val layout: Layout? = getLayout()
    if (layout != null) {
        val line = layout.getLineForOffset(index)
        cursorHeight = layout.getLineBottom(line, includeLineSpacing) - layout.getLineTop(line)
    }
    return cursorHeight * scaleY
}

/**
 * 获取父布局大小
 */
fun EditText.getParentViewSize(): Size {
    if (parent is View) {
        val parentView = parent as View
        // 现在 parentView 就是父布局啦，可以获取宽高等属性
        val parentWidth = parentView.width
        val parentHeight = parentView.height
        return Size(parentWidth, parentHeight)
    }
    return Size(0, 0)
}

/**
 * 获取相对于父布局的坐标
 */
fun EditText?.getCursorPointF(index: Int = this?.selectionStart ?: 0, includeScale: Boolean = true): PointF {
    if (this == null || layout == null) {
        return PointF(0f, 0f)
    }
    // 1. 获取布局Layout对象
    val layout: Layout = layout
    // 2. 获取光标插入的位置索引
    val offset: Int = index
    // 3. 获取该位置所在行
    val line: Int = layout.getLineForOffset(offset)
    // 4. 获取光标X（相对EditText内容区左边缘）
    val cursorX: Float = layout.getPrimaryHorizontal(offset)
    // 5. 获取光标Y（相对EditText内容区上边缘=该行top或者基线+ascent）
    val baseline: Int = layout.getLineBaseline(line)
    val ascent: Int = layout.getLineAscent(line)
    val cursorY = (baseline + ascent).toFloat()
    // 6. 若需要“相对整个View左上角（含padding）”，加上padding
    val viewX: Float = cursorX + totalPaddingStart
    val viewY: Float = cursorY + totalPaddingTop

    if (!includeScale) {
        return PointF(viewX, viewY)
    }
    // 7. 计算缩放偏移量
    val scaleViewX: Float = (viewX - pivotX) * scaleX + pivotX + translationX
    val scaleViewY: Float = (viewY - pivotY) * scaleY + pivotY + translationY

    return PointF(scaleViewX, scaleViewY)
}

fun EditText.canSelectAllText(): Boolean {
    return text.isNotEmpty() && transformationMethod !is PasswordTransformationMethod
            && !(selectionStart == 0 && selectionEnd == text.length)
}

fun EditText.canPaste(): Boolean {
    return (text is Editable
            && selectionStart >= 0
            && selectionEnd >= 0
            && context.getSystemService(ClipboardManager::class.java)?.hasPrimaryClip() == true)
}

fun EditText.canRequestAutofill(): Boolean {
    if (autofillType != View.AUTOFILL_TYPE_NONE) {
        return false
    }
    val afm: AutofillManager? = context.getSystemService(AutofillManager::class.java)
    return afm?.isEnabled() == true
}

