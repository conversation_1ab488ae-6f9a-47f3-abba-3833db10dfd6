package com.tcl.ai.note.handwritingtext.ui.richtext.base

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.PointF
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.Rect
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.os.SystemClock
import android.view.GestureDetector
import android.view.Gravity
import android.view.HapticFeedbackConstants
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.PopupWindow
import androidx.core.content.ContextCompat
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.dp2px


/**
 * 1. 如果是选区，只拦截事件，不显示自定义光标水滴
 * 2. 如果是插入光标，直接替换。
 */
@SuppressLint("ResourceType", "ClickableViewAccessibility")
class EditCursorView(
    private val editText: BaseRichTextEditView,
    private val cursorType: CursorType,
    private val alpha: Float = if (cursorType == CursorType.INSERT) 1F else 0F,
    private var isDraggable: Boolean = false,
) : PopupWindow() {
    enum class CursorType { LEFT_SELECTION, INSERT, RIGHT_SELECTION }

    private val cursorHandleDrawable = getCursorDrawable(editText.context, cursorType)
    private val realWidth get() = cursorHandleDrawable?.intrinsicWidth ?: 0
    private val realHeight get() = cursorHandleDrawable?.intrinsicHeight ?: 0
    private var paddingHorizontal = 0
    private var paddingBottom = 0

    // 获取父view的大小，避免光标超出屏幕
    private var displayRect = Rect().also {
        (editText.parent as? View)?.getWindowVisibleDisplayFrame(it)
    }
    // 获取富文本的父View在屏幕的左上角
    private var editTextLocation = IntArray(2).also {
        (editText.parent as? View)?.getLocationInWindow(it)
    }
    private val editTextLeft get() = editTextLocation[0]
    private val editTextTop get() = editTextLocation[1]
    private val editTextRight get() = editTextLocation[0] + editText.viewWidth
    private val editTextBottom get() = editTextLocation[1] + editText.viewHeight

    init {
        isClippingEnabled = false
        isFocusable = false
        isOutsideTouchable = false

        val cursorHandle = ImageView(editText.context).apply {
            setImageDrawable(cursorHandleDrawable)
            this.alpha = <EMAIL>
            val cursorColorInt = ContextCompat.getColor(context, com.tcl.ai.note.base.R.color.text_field_border)
            colorFilter = PorterDuffColorFilter(cursorColorInt, PorterDuff.Mode.SRC_IN)
        }
        val linearLayout: LinearLayout = LinearLayout(editText.context).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT,
            )
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
            addView(cursorHandle)
        }

        // WindowManager.LayoutParams.TYPE_APPLICATION_ABOVE_SUB_PANEL = 1005
        // 原生拖动柄层级是WindowManager.LayoutParams.TYPE_APPLICATION_SUB_PANEL = 1002
        // 这样才能盖在原生拖动柄上方拦截事件
        windowLayoutType = if (cursorType == CursorType.INSERT) {
            WindowManager.LayoutParams.TYPE_APPLICATION_SUB_PANEL
        } else {
            1005
        }
        // 根据布局观察得出来的值
        width = 47.dp2px
        height = 42.dp2px
        setContentView(linearLayout)
        // +2px 让选区能覆盖原cursor的选区
        paddingHorizontal = (width - realWidth) / 2 + 2
        // top没有padding
        paddingBottom = height - realHeight
        contentView.setOnTouchListener { v, e -> dragEventListener.onTouch(v, e) }
    }

    private val gestureListener = object : GestureDetector.SimpleOnGestureListener() {
        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            if (cursorType == CursorType.INSERT) {
                // 弹出粘贴栏
                editText.showInsertionActionMode()
                Logger.d(TAG, "onSingleTapConfirmed, startActionMode: customInsertionActionModeCallback")
            }
            return true
        }
    }
    private val gestureDetector = GestureDetector(editText.context, gestureListener)

    // 处理拖动事件
    private val dragEventListener by lazy {
        object : View.OnTouchListener {
            // 系统刷新率hz
            private val frameRate = contentView.context.display.refreshRate / 1000
            // 用节流（throttle）机制减少 update 调用，比如每 16ms 一次
            private var lastUpdateTime = 0L
            // 上次边界滚动行的时间
            private var lastBoundaryRollingTime = 0L
            // 是否在触摸
            var isTouching = false
                private set

            private var downRawX = 0f
            private var downRawY = 0f
            private var initialX = 0
            private var initialY = 0
            // activityWindowLocation为了解决分屏
            private val activityWindowLocation = IntArray(2)
            // popupViewLocation是获取拖动柄相对于富文本的位置
            private val popupViewLocation = IntArray(2)
            override fun onTouch(v: View, event: MotionEvent): Boolean {
                gestureDetector.onTouchEvent(event)
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        isTouching = true

                        editText.rootView.getLocationOnScreen(activityWindowLocation)
                        contentView.getLocationOnScreen(popupViewLocation)
                        initialX = popupViewLocation[0] - activityWindowLocation[0]
                        initialY = popupViewLocation[1] - activityWindowLocation[1]

                        downRawX = event.rawX
                        downRawY = event.rawY
                        Logger.d(TAG, "onTouch ACTION_DOWN, activityWindowLocation: ${activityWindowLocation.contentToString()}, popupViewLocation: ${popupViewLocation.contentToString()}, downRawX: $downRawX, downRawY: $downRawY")
                        return true
                    }

                    MotionEvent.ACTION_MOVE -> {
                        val offsetX = event.rawX - downRawX
                        val offsetY = event.rawY - downRawY

                        var newX = initialX + offsetX
                        var newY = initialY + offsetY

                        // 防止超出屏幕
                        newX = newX.coerceIn(
                            (displayRect.left - activityWindowLocation[0] - width / 2).toFloat(),
                            (displayRect.right + width / 2).toFloat()
                        )
                        newY = newY.coerceIn(
                            (displayRect.top - activityWindowLocation[1] + editTextTop - realHeight).toFloat(),
                            (displayRect.bottom).toFloat()
                        )

                        if (isDraggable && SystemClock.elapsedRealtime() - lastUpdateTime > frameRate) {
                            // 每帧更新一次，性能优化，减缓光标不跟手
                            update(newX.toInt(), newY.toInt(), -1, -1, true)
                            lastUpdateTime = SystemClock.elapsedRealtime()
                        }

                        val logicX = (newX - editText.translationX + width / 2) / editText.scaleX
                        val logicY = (newY - editText.translationY - editTextTop) / editText.scaleY + realHeight - editText.lineHeight
                        var cursorPosition = editText.getOffsetForPosition(logicX, logicY)
                        cursorPosition = when (cursorType) {
                            // 左选区不能超过右选区
                            CursorType.LEFT_SELECTION -> cursorPosition.coerceAtMost(editText.selectionEnd - 1)
                            CursorType.INSERT -> cursorPosition
                            // 右选区不能小于左选区
                            CursorType.RIGHT_SELECTION -> cursorPosition.coerceAtLeast(editText.selectionStart + 1)
                        }
                        Logger.d(TAG, "onTouch ACTION_MOVE, logicX: $logicX, logicY: $logicY, cursorPosition: $cursorPosition")
                        // 修改光标位置
                        moveCursorPosition(cursorPosition)
                        if (SystemClock.elapsedRealtime() - lastBoundaryRollingTime > 300) {
                            // 让光标回到屏幕内,500ms滚动一行，不然会快速回到顶部
                            editText.editRichCursorToScreen(
                                index = cursorPosition
                                    .coerceAtLeast(
                                        editText.getLineStartAtCursorIndex(cursorPosition)
                                    )
                                    .coerceAtMost(
                                        editText.getLineEndAtCursorIndex(cursorPosition)
                                    ),
                                verticalPadding = editText.getCursorHeight(
                                    index = cursorPosition,
                                    includeLineSpacing = true
                                ),
                                horizontalPadding = editText.getCharWidth(cursorPosition) * 2
                            )
                            lastBoundaryRollingTime = SystemClock.elapsedRealtime()
                        }
                        return true
                    }

                    MotionEvent.ACTION_UP -> {
                        val cursorPosition = if (cursorType == CursorType.RIGHT_SELECTION) {
                            editText.selectionEnd
                        } else {
                            editText.selectionStart
                        }
                        // up事件复位拖动柄，避免错位
                        updatePosition(cursorPosition)
                        isTouching = false
                        return true
                    }

                    MotionEvent.ACTION_CANCEL -> {
                        isTouching = false
                        return false
                    }
                }
                return false
            }
        }
    }

    /**
     * 移动光标
     */
    private fun moveCursorPosition(cursorPosition: Int) {
        when (cursorType) {
            CursorType.LEFT_SELECTION -> {
                // 左选区扩展
                if (cursorPosition != editText.selectionStart && cursorPosition < editText.selectionEnd) {
                    editText.setSelection(cursorPosition, editText.selectionEnd)
                    vibrate()
                }
            }

            CursorType.RIGHT_SELECTION -> {
                // 右选区扩展
                if (cursorPosition != editText.selectionEnd && cursorPosition > editText.selectionStart) {
                    editText.setSelection(editText.selectionStart, cursorPosition)
                    vibrate()
                }
            }

            CursorType.INSERT -> {
                // 移动插入光标
                if (cursorPosition != editText.selectionStart || editText.selectionStart != editText.selectionEnd) {
                    editText.setSelection(cursorPosition)
                    vibrate()
                }
            }
        }
    }

    /**
     * 修正光标拖动柄（水滴）位置，获取光标正下方位置(左对齐)
     */
    private fun calculateCursorPoint(cursorIndex: Int): PointF {
        val cursorPoint = editText.getCursorPointF(cursorIndex)
        val cursorHeight = editText.getCursorHeight(cursorIndex)

        val xOffset = when (cursorType) {
            CursorType.LEFT_SELECTION -> -width.toFloat() + paddingHorizontal
            CursorType.INSERT -> -width / 2 + editText.getCursorWidth() / 2
            CursorType.RIGHT_SELECTION -> 0f - paddingHorizontal
        }
        val revertLogicX = cursorPoint.x + xOffset
        val revertLogicY = cursorPoint.y + editTextTop + cursorHeight
        Logger.d(TAG, "calculateCursorPoint, cursorPoint: $revertLogicX, $revertLogicY, cursorHeight: $cursorHeight")
        return PointF(revertLogicX, revertLogicY)
    }

    /**
     * 根据光标位置，放置水滴view
     */
    fun showOrUpdate(
        cursorIndex: Int = if (cursorType == CursorType.RIGHT_SELECTION) editText.selectionEnd else editText.selectionStart,
        gravity: Int = Gravity.TOP or Gravity.START,
        onlyUpdate: Boolean = false,
    ) {
        if (editText?.isAttachedToWindow != true) {
            Logger.w(TAG, "show fail! view isn't attached to window")
            return
        }
        // 获取光标正下方位置（左对齐）
        val point = calculateCursorPoint(cursorIndex)

        // 微调水滴的位置，让icon居中。现在icon是左对齐
        var x = point.x.toInt()
        var y = point.y.toInt()
        val xOffset = when (cursorType) {
            CursorType.LEFT_SELECTION -> -width
            CursorType.INSERT -> -width / 2
            CursorType.RIGHT_SELECTION -> 0
        }

        // 如果是触摸事件，不能消失。
        if (!dragEventListener.isTouching
            && (x < editTextLeft + xOffset
                    || y < editTextTop
                    || x > editTextRight + xOffset
                    || y > editTextBottom
                    )
        ) {
            // 放置到屏幕外，起到隐藏作用。避免拦截事件
            update(-width, -height, -1, -1)
            return
        }
        if (onlyUpdate || isShowing) {
            update(x, y, -1, -1, true)
        } else {
            // showAsDropDown需要特殊处理一下y值, 创建光标
            y = y + height
            super.showAsDropDown(editText.rootView, x, y, gravity)
        }
    }

    /**
     * 只更新光标位置，不创建光标
     */
    fun updatePosition(
        cursorIndex: Int = if (cursorType == CursorType.RIGHT_SELECTION) editText.selectionEnd else editText.selectionStart,
        gravity: Int = Gravity.TOP or Gravity.START,
    ) {
        showOrUpdate(cursorIndex, gravity, true)
    }

    fun refreshParentMeasure() {
        displayRect = Rect().also {
            (editText.parent as? View)?.getWindowVisibleDisplayFrame(it)
        }
        editTextLocation = IntArray(2).also {
            (editText.parent as? View)?.getLocationInWindow(it)
        }
        Logger.d(TAG, "refreshParentMeasure, displayRect: $displayRect, editTextLocation: ${editTextLocation.get(0)}, ${editTextLocation.get(1)}")
    }

    override fun dismiss() {
        super.dismiss()
        // 因为插入拖动柄是自己执行了startActionMode，所以要自己隐藏
        if (cursorType == CursorType.INSERT) {
            editText.hideInsertionActionMode()
        }
    }
    /**
     * 使用函数[showOrUpdate]代替
     */
    @Deprecated("请使用showOrUpdate方法替换")
    override fun showAsDropDown(anchor: View?, xoff: Int, yoff: Int, gravity: Int) {
        throw IllegalAccessException("请使用showOrUpdate(cursorIndex: Int, gravity: Int)方法替换")
    }

    // 添加震动效果
    private fun vibrate() {
        editText.performHapticFeedback(HapticFeedbackConstants.TEXT_HANDLE_MOVE)
    }

    companion object {
        private const val TAG = "EditCursorView"
        private val cursorResMap = HashMap<Int, Drawable?>(3)

        // 获取系统光标资源
        private fun getCursorDrawable(context: Context, cursorType: CursorType) = when (cursorType) {
            CursorType.LEFT_SELECTION -> {
                cursorResMap.getOrPut(android.R.attr.textSelectHandleLeft) {
                    context.obtainStyledAttributes(intArrayOf(android.R.attr.textSelectHandleLeft)).use {
                        trimDrawableTransparent(it.getDrawable(0), context)
                    }
                }
            }

            CursorType.INSERT -> {
                cursorResMap.getOrPut(android.R.attr.textSelectHandle) {
                    context.obtainStyledAttributes(intArrayOf(android.R.attr.textSelectHandle)).use {
                        trimDrawableTransparent(it.getDrawable(0), context)
                    }
                }
            }

            CursorType.RIGHT_SELECTION -> {
                cursorResMap.getOrPut(android.R.attr.textSelectHandleRight) {
                    context.obtainStyledAttributes(intArrayOf(android.R.attr.textSelectHandleRight)).use {
                        trimDrawableTransparent(it.getDrawable(0), context)
                    }
                }
            }
        }

        // 裁剪透明区域
        private fun trimDrawableTransparent(drawable: Drawable?, context: Context): Drawable? {
            if (drawable == null) return drawable
            Logger.d(TAG, "trimDrawableTransparent, raw, width: ${drawable.intrinsicWidth}, height: ${drawable.intrinsicHeight}")
            val bmp = Bitmap.createBitmap(
                drawable.intrinsicWidth,
                drawable.intrinsicHeight,
                Bitmap.Config.ARGB_8888
            )
            val canvas = Canvas(bmp)
            drawable.setBounds(0, 0, canvas.width, canvas.height)
            drawable.draw(canvas)

            val w = bmp.width
            val h = bmp.height
            val pixels = IntArray(w * h)
            bmp.getPixels(pixels, 0, w, 0, 0, w, h)

            var left = w;
            var right = 0;
            var top = h;
            var bottom = 0
            for (y in 0 until h) {
                for (x in 0 until w) {
                    if ((pixels[y * w + x] ushr 24) > 0) {
                        if (x < left) left = x
                        if (x > right) right = x
                        if (y < top) top = y
                        if (y > bottom) bottom = y
                    }
                }
            }
            // 防止图片全透明
            if (right < left || bottom < top) return drawable

            val outBmp = Bitmap.createBitmap(bmp, left, top, right - left + 1, bottom - top + 1)
            Logger.d(TAG, "trimDrawableTransparent, new, width: ${outBmp.width}, height: ${outBmp.height}")
            return BitmapDrawable(context.resources, outBmp)
        }
    }
}