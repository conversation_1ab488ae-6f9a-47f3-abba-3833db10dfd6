package com.tcl.ai.note.handwritingtext.ui.richtext.component

import android.annotation.SuppressLint
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Slider
import androidx.compose.material.SliderDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.DoodlePen
import com.tcl.ai.note.handwritingtext.bean.progressToDp
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.handwritingtext.vm.BrushSliderModel
import com.tcl.ai.note.handwritingtext.vm.DrawBoardViewModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.toComposeColor


/**
 * 笔刷宽度类型控制
 */
@SuppressLint("DesignSystem")
@Composable
internal fun BrushControl(
    drawBoardViewModel: DrawBoardViewModel = hiltViewModel(),
    brushSliderModel: BrushSliderModel = hiltViewModel(),
    selDoodlePen: DoodlePen,
    sliderMin:Float =0.15f,
    sliderMax:Float =1f,
    onPanChange:(doodlePen: DoodlePen) -> Unit
) {

    val sliderPosition = brushSliderModel.sliderPositionState.value
    val density = LocalDensity.current
    val ratio = (sliderPosition - sliderMin) / (sliderMax - sliderMin)
    val dimens = getGlobalDimens()
    val context = LocalContext.current

    Column(
        modifier = Modifier
            .height(dimensionResource(com.tcl.ai.note.handwritingtext.R.dimen.bottom_menu_float_board_brush_height))
            .fillMaxWidth()
    ) {
        LineIcon(
            modifier = Modifier
                .padding(horizontal = 16.dp),
            height = selDoodlePen.progressToDp(sliderPosition),
            ratio = ratio
        )
        Spacer(modifier = Modifier.height(7.dp))
        Box(
            modifier = Modifier
                .height(dimensionResource(com.tcl.ai.note.handwritingtext.R.dimen.bottom_menu_brush_slider_height))
                .fillMaxWidth()
        ){
            Slider(
                value = sliderPosition,
                onValueChange = { progress ->
                    brushSliderModel.updateSliderPosition(progress,selDoodlePen)
                    brushSliderModel.sendDrawBoardIntent(
                        drawBoardViewModel =drawBoardViewModel,
                        pxValue = with(density) {selDoodlePen.progressToDp (progress).toPx() },
                        color =  selDoodlePen.color.toComposeColor(),
                        selDoodlePen = selDoodlePen
                    )
                },
                modifier = Modifier
                    .clearAndSetSemantics {
                        contentDescription= String.format(context.getString(R.string.brush_slider_status),"${(ratio*100).toInt()}%")
                    }
                    .padding(horizontal = 18.dp),
                valueRange = sliderMin..sliderMax,
                colors = SliderDefaults.colors(
                    thumbColor = colorResource(R.color.edit_slider_indicator_color),// 滑块手柄的颜色
                    activeTrackColor = colorResource(R.color.edit_slider_phone_track_active_color),// 激活状态下滑块轨道的颜色
                    inactiveTrackColor = colorResource(R.color.edit_slider_phone_track_inactive_color) // 未激活状态下滑块轨道的颜色
                )
            )
        }
        Spacer(modifier = Modifier.height(7.dp))
        // 选择画笔类型
        Box(
            modifier = Modifier
                .padding(horizontal = 24.dp)
                .clickable {  }
                .fillMaxWidth()
                .height(dimens.penOptionHeight)
                .background(
                    color = TclTheme.colorScheme.reWriteExpandBg,
                    shape = RoundedCornerShape(8.dp),
                )
                .padding(horizontal = 18.dp)

        ){
            PenSelector(selDoodlePen =selDoodlePen){ pen ->
                brushSliderModel.saveLastDoodlePen(pen)
                onPanChange(pen)
            }
        }

    }
}



/**
 *  画笔线条粗细进度条上面的线条图标（需跟随进度条手柄滑动而滑动）
 */
@Composable
private fun LineIcon(
    height: Dp,
    ratio:Float,
    modifier: Modifier = Modifier,
) {

    val configuration = LocalConfiguration.current
    val screenWidthDp = configuration.screenWidthDp // 屏幕宽度
    val slidingWidth = screenWidthDp - 48 - 20 // 线条图标可移动宽度：为屏幕宽度减去两边的padding值再减去进度条手柄默认宽度（20dp)

    Box(
        modifier = modifier
            .invisibleSemantics()
            .size(dimensionResource(com.tcl.ai.note.handwritingtext.R.dimen.bottom_menu_line_icon_size))
            .offset(x = (ratio * slidingWidth).dp)
            .background(
                colorResource(R.color.edit_slider_group_color),
                shape = RoundedCornerShape(3.dp)
            ),
        contentAlignment = Alignment.Center
    ) {
        val color = colorResource(R.color.edit_slider_thumb_color)
        Canvas(
            modifier = Modifier
                .width(12.dp)
                .height(height)
        ) {
            drawLine(
                color = color,
                start = Offset(0f, size.height / 2),
                end = Offset(size.width, size.height / 2),
                strokeWidth = height.toPx(), // 静态线条宽度
                cap = StrokeCap.Round,
            )
        }
    }
}

