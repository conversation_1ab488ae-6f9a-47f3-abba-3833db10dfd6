package com.tcl.ai.note.handwritingtext.ui.richtext.component

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.material3.Icon
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.R as HandwritingTextR

/**
 * 带有动态颜色条的颜色选择器图标
 * 色条会显示当前选中的颜色
 * 
 * @param iconResId 图标资源ID (不包含底部色块)
 * @param currentColor 当前选中的颜色，会显示在底部色块中
 * @param onColorChanged 当颜色变化时的回调，用于双向绑定
 * @param modifier 修饰符
 * @param contentDescription 图标内容描述
 */
@Composable
fun ColorPickerIcon(
    iconResId: Int,
    currentColor: Color,
    onColorChanged: (Color) -> Unit = {},
    modifier: Modifier = Modifier,
    contentDescription: String? = null
) {
    // 使用记忆状态保存上次选择的颜色
    var lastColor by remember { mutableStateOf(currentColor) }
    
    // 当外部颜色变化时更新内部状态
    LaunchedEffect(currentColor) {
        if (currentColor != lastColor) {
            lastColor = currentColor
            // 通知颜色变化
            onColorChanged(currentColor)
        }
    }
    
    Box(
        modifier = modifier.size(28.dp),
        contentAlignment = Alignment.Center
    ) {
        // 绘制主图标
        Icon(
            painter = painterResource(id = iconResId),
            contentDescription = contentDescription,
            tint = Color.Unspecified, // 不要给整个图标着色
            modifier = Modifier.size(28.dp)
        )
        
        // 在底部绘制当前颜色的色条
        Canvas(modifier = Modifier.size(28.dp)) {
            val colorBarWidth = 14.dp.toPx()
            val colorBarHeight = 2.dp.toPx()
            val startX = center.x - (colorBarWidth / 2)
            val startY = center.y + 6.dp.toPx()
            
            if (lastColor == Color.Transparent) {
                // 透明颜色特殊处理 - 只绘制一个中间镂空的矩形轮廓
                drawRect(
                    color = Color.Gray,
                    topLeft = Offset(startX, startY),
                    size = androidx.compose.ui.geometry.Size(
                        width = colorBarWidth,
                        height = colorBarHeight
                    ),
                    style = Stroke(width = 0.5f)
                )
            } else {
                // 普通颜色 - 绘制实心矩形
                drawRect(
                    color = lastColor,
                    topLeft = Offset(startX, startY),
                    size = androidx.compose.ui.geometry.Size(
                        width = colorBarWidth,
                        height = colorBarHeight
                    )
                )
            }
        }
    }
}

/**
 * 文本颜色选择器图标
 * 
 * @param currentColor 当前选中的颜色
 * @param onColorChanged 当颜色变化时的回调，用于双向绑定
 * @param modifier 修饰符
 */
@Composable
fun TextColorIcon(
    currentColor: Color,
    onColorChanged: (Color) -> Unit = {},
    modifier: Modifier = Modifier
) {
    ColorPickerIcon(
        iconResId = HandwritingTextR.drawable.ic_richtext_menu_text_color,
        currentColor = currentColor,
        onColorChanged = onColorChanged,
        modifier = modifier,
        contentDescription = stringResource(R.string.rich_text_text_color)
    )
}

/**
 * 文本背景颜色选择器图标
 * 
 * @param currentColor 当前选中的颜色
 * @param onColorChanged 当颜色变化时的回调，用于双向绑定
 * @param modifier 修饰符
 */
@Composable
fun TextBackgroundColorIcon(
    currentColor: Color,
    onColorChanged: (Color) -> Unit = {},
    modifier: Modifier = Modifier
) {
    ColorPickerIcon(
        iconResId = HandwritingTextR.drawable.ic_richtext_menu_text_bg,
        currentColor = currentColor,
        onColorChanged = onColorChanged,
        modifier = modifier,
        contentDescription = stringResource(R.string.rich_text_text_bg_color)
    )
} 