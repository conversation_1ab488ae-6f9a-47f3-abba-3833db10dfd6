package com.tcl.ai.note.handwritingtext.ui.richtext.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

/**
 * 颜色选择器的预览组件
 */
@Preview(showBackground = true)
@Composable
fun ColorPickerPreview() {
    var selectedTextColor by remember { mutableStateOf(Color.Black) }
    var selectedBgColor by remember { mutableStateOf(Color.Transparent) }
    
    Column(modifier = Modifier.padding(16.dp)) {
        // 文本颜色选择器
        TextColorPicker(
            selectedColor = selectedTextColor,
            onColorSelected = { selectedTextColor = it }
        )
        
        Spacer(modifier = Modifier.height(10.dp))
        
        // 背景颜色选择器
        BackgroundColorPicker(
            selectedColor = selectedBgColor,
            onColorSelected = { selectedBgColor = it }
        )
    }
} 