package com.tcl.ai.note.handwritingtext.ui.richtext.component

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Divider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.handwritingtext.utils.ColorUtils
import com.tcl.ai.note.theme.TclTheme

/**
 * 颜色选择器类型
 */
enum class ColorPickerType {
    TEXT_COLOR,      // 文本颜色选择器（不含透明色）
    BACKGROUND_COLOR // 背景颜色选择器（含透明色）
}

/**
 * 可复用的颜色选择器行组件
 * 
 * @param colors 颜色列表
 * @param selectedColor 当前选中的颜色
 * @param type 选择器类型（文本颜色或背景颜色）
 * @param onColorSelected 颜色选择回调
 * @param modifier 修饰符
 */
@Composable
fun ColorPickerRow(
    colors: List<Color> = ColorUtils.TEXT_COLORS,
    selectedColor: Color = Color.Black,
    type: ColorPickerType = ColorPickerType.TEXT_COLOR,
    onColorSelected: (Color) -> Unit = {},
    modifier: Modifier = Modifier
) {
    val shape = RoundedCornerShape(12.dp)
    val colorSize = 18.dp
    val colorSpacing = 10.dp
    
    Box(
        modifier = modifier
            .shadow(
                elevation = 4.dp,
                shape = shape,
                ambientColor = Color(0x26000000), // 15% 透明度黑色阴影
                spotColor = Color(0x26000000)     // 保持一致的阴影颜色
            )
            .clip(shape)
            .background(Color.White)
            .border(
                width = 0.5.dp,
                color = Color.Gray.copy(alpha = 0.1f),
                shape = shape
            )
            .padding(horizontal = 12.dp, vertical = 10.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 仅背景颜色选择器包含透明选项
            if (type == ColorPickerType.BACKGROUND_COLOR) {
                // 透明色按钮
                TransparentColorButton(
                    isSelected = selectedColor == Color.Transparent,
                    size = colorSize,
                    onClick = { onColorSelected(Color.Transparent) }
                )
                
                // 透明按钮与分割线之间的距离
                Spacer(modifier = Modifier.width(10.dp))
                
                // 分割线，高度为16dp，宽度为1dp
                Divider(
                    modifier = Modifier
                        .height(16.dp)
                        .width(1.dp),
                    color = Color.Black.copy(alpha = 0.1f)
                )
                
                // 分割线与黑色按钮之间的距离
                Spacer(modifier = Modifier.width(10.dp))
            }
            
            // 渲染颜色选项
            colors.forEachIndexed { index, color ->
                ColorButton(
                    color = color,
                    isSelected = color == selectedColor,
                    size = colorSize,
                    onClick = { onColorSelected(color) }
                )
                
                // 在颜色之间添加间距，但不在最后一个颜色后添加
                if (index < colors.size - 1) {
                    Spacer(modifier = Modifier.width(colorSpacing))
                }
            }
        }
    }
}

/**
 * 单个颜色按钮
 */
@Composable
private fun ColorButton(
    color: Color,
    isSelected: Boolean,
    size: androidx.compose.ui.unit.Dp,
    onClick: () -> Unit
) {
    if (isSelected) {
        // 选中状态：外环和内圆都是颜色本身，中间是白色环
        Box(
            modifier = Modifier
                .size(size)
                .shadow(
                    elevation = 2.dp, 
                    shape = CircleShape,
                    ambientColor = Color(0x26000000),
                    spotColor = Color(0x26000000)
                )
                .clip(CircleShape)
                .background(color)
                .clickable(onClick = onClick),
            contentAlignment = Alignment.Center
        ) {
            // 白色环
            Box(
                modifier = Modifier
                    .size(size - 4.dp)
                    .clip(CircleShape)
                    .background(Color.White)
            ) {
                // 内部颜色圆
                Box(
                    modifier = Modifier
                        .size(size - 8.dp)
                        .clip(CircleShape)
                        .background(color)
                        .align(Alignment.Center)
                )
            }
        }
    } else {
        // 未选中状态：简单的颜色圆
        Box(
            modifier = Modifier
                .size(size)
                .clip(CircleShape)
                .background(color)
                .clickable(onClick = onClick)
        )
    }
}

/**
 * 透明颜色按钮（用于背景色选择）
 */
@Composable
private fun TransparentColorButton(
    isSelected: Boolean,
    size: androidx.compose.ui.unit.Dp,
    onClick: () -> Unit
) {
    // 透明色按钮无论是否选中，都保持相同的视觉效果
    Box(
        modifier = Modifier
            .size(size)
            .clip(CircleShape)
            .background(Color.White)
            .border(
                width = 0.5.dp,
                color = Color.Gray.copy(alpha = 0.3f),
                shape = CircleShape
            )
            .clickable(onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        TransparentColorIcon(
            modifier = Modifier.size(size * 0.8f),
            cellSize = 4f
        )
    }
}

/**
 * 预览用的固定尺寸颜色选择器
 */
@Composable
fun TextColorPicker(
    colors: List<Color> = ColorUtils.TEXT_COLORS,
    selectedColor: Color = Color.Black,
    onColorSelected: (Color) -> Unit = {}
) {
    ColorPickerRow(
        colors = colors,
        selectedColor = selectedColor,
        type = ColorPickerType.TEXT_COLOR,
        onColorSelected = onColorSelected,
        modifier = Modifier.width(210.dp).height(38.dp)
    )
}

/**
 * 预览用的固定尺寸背景颜色选择器
 */
@Composable
fun BackgroundColorPicker(
    colors: List<Color> = ColorUtils.TEXT_COLORS,
    selectedColor: Color = Color.Transparent,
    onColorSelected: (Color) -> Unit = {}
) {
    ColorPickerRow(
        colors = colors,
        selectedColor = selectedColor,
        type = ColorPickerType.BACKGROUND_COLOR,
        onColorSelected = onColorSelected,
        modifier = Modifier.width(257.dp).height(38.dp)
    )
} 