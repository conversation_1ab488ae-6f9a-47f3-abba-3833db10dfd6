package com.tcl.ai.note.handwritingtext.ui.richtext.component

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Slider
import androidx.compose.material.SliderDefaults
import androidx.compose.material.TextButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.state.DrawBoardIntent
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.handwritingtext.vm.DrawBoardViewModel
import com.tcl.ai.note.handwritingtext.vm.EraserSliderModel
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.dp2px
import com.tcl.ai.note.utils.px2dp
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.widget.AutoScrollText


/***
 * 橡皮擦宽带滑块
 */
@SuppressLint("DesignSystem")
@Composable
fun EraserWidthSlider(
    drawBoardViewModel: DrawBoardViewModel = hiltViewModel(),
    eraserSliderModel: EraserSliderModel = hiltViewModel(),
    sliderMin:Float =0.26f,//参考旧版设置
    sliderMax:Float =1f,////参考旧版设置
){
    val context = LocalContext.current
    val sliderPosition = eraserSliderModel.sliderPositionState.value
    val density = LocalDensity.current
    var btnClearAllWidth by remember { mutableIntStateOf(0) }

    LaunchedEffect(Unit) {
        //把drawMode调整为橡皮擦，且恢复上次宽
        eraserSliderModel.sendSliderProgressIntent(drawBoardViewModel,sliderPosition)
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .height(dimensionResource(com.tcl.ai.note.handwritingtext.R.dimen.bottom_menu_float_board_eraser_height))
            .padding(
                start = dimensionResource(com.tcl.ai.note.handwritingtext.R.dimen.bottom_menu_brush_option_padding_horizontal)
            ),
        verticalArrangement = Arrangement.Center
    ){
        EraserIcon(
            progress =sliderPosition,
            sliderMin = sliderMin,
            sliderMax = sliderMax,
            marginRight = btnClearAllWidth
        )
        Spacer(modifier = Modifier.height(5.dp))
        Row(
            modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ){
            Box(
                modifier = Modifier.height(dimensionResource(com.tcl.ai.note.handwritingtext.R.dimen.bottom_menu_brush_slider_height))
                    .weight(1f)
            ){
                Slider(
                    value = sliderPosition,
                    onValueChange = { progress ->
                        eraserSliderModel.updateSliderPosition(progress)
                        eraserSliderModel.sendSliderProgressIntent(drawBoardViewModel,progress)
                    },
                    modifier = Modifier
                        .clearAndSetSemantics {
                            val desc =String.format(context.getString(R.string.eraser_slider_status),"${(sliderPosition*100).toInt()}%")
                            contentDescription= desc
                        }
                        .padding(horizontal = 0.dp),
                    valueRange = sliderMin..sliderMax,
                    colors = SliderDefaults.colors(
                        thumbColor = colorResource(R.color.edit_slider_indicator_color),// 滑块手柄的颜色
                        activeTrackColor = colorResource(R.color.edit_slider_phone_track_active_color),// 激活状态下滑块轨道的颜色
                        inactiveTrackColor = colorResource(R.color.edit_slider_phone_track_inactive_color) // 未激活状态下滑块轨道的颜色
                    )
                )
            }

            Row (
                modifier = Modifier
                    .wrapContentWidth()
                    .onSizeChanged { size ->
                        btnClearAllWidth = size.width
                    },
                verticalAlignment = Alignment.CenterVertically
            ){
                Spacer(modifier = Modifier.width(16.dp))
                TextButton(
                    contentPadding = PaddingValues(0.dp),
                    onClick = {
                        drawBoardViewModel.sendIntent(DrawBoardIntent.ClearAll())
                    },
                ) {

                    AutoScrollText(
                        text = stringResource(id = R.string.clear_all),
                        fontSize = 14.sp,
                        color = colorResource(R.color.btn_clear_all),
                        modifier = Modifier
                            .padding(start = 8.dp)
                            .widthIn(max = 100.dp)
                    )
                }
                Spacer(modifier = Modifier.width(16.dp))
            }

        }


    }
}
/**
 *  橡皮擦线条粗细进度条上面的线条图标（需跟随进度条手柄滑动而滑动）
 */
@Composable
private fun EraserIcon(
    progress: Float,
    sliderMin:Float,
    sliderMax:Float,
    marginRight:Int
) {
    val configuration = LocalConfiguration.current
    val screenWidthDp = configuration.screenWidthDp.dp2px // 屏幕宽度
    val marginLeft = dimensionResource(com.tcl.ai.note.handwritingtext.R.dimen.bottom_menu_brush_option_padding_horizontal).toPx
    val ionSize = dimensionResource(com.tcl.ai.note.handwritingtext.R.dimen.bottom_menu_line_icon_size).toPx
    val slidingWidth = (screenWidthDp - marginLeft -marginRight -ionSize).toInt().px2dp
    val ratio = (progress - sliderMin) / (sliderMax - sliderMin)
    val height =10 * progress
    Box(
        modifier = Modifier
            .invisibleSemantics()
            .size(dimensionResource(com.tcl.ai.note.handwritingtext.R.dimen.bottom_menu_line_icon_size))
            .fillMaxHeight()
            .offset(x = (ratio * slidingWidth).dp)
            .background(
                colorResource(R.color.edit_slider_group_color),
                shape = RoundedCornerShape(3.dp)
            ),
        contentAlignment = Alignment.Center
    ) {
        Box(
            modifier = Modifier
                .size((height*2).dp)
                .background(colorResource(R.color.edit_slider_indicator_color), shape = CircleShape)
        )
    }
}
