package com.tcl.ai.note.handwritingtext.ui.richtext.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.R as HandwritingTextR
import kotlinx.coroutines.launch
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.lazy.items

/**
 * 自定义字体大小下拉选择器
 * 
 * @param expanded 是否展开下拉菜单，由父组件控制
 * @param onDismissRequest 关闭下拉菜单的回调
 * @param selectedFontSize 当前选中的字体大小
 * @param fontSizes 可选字体大小列表，支持1-64范围，20以上间隔为2
 * @param onFontSizeSelected 字体大小选择回调
 */
@Composable
fun FontSizeDropdown(
    expanded: Boolean,
    onDismissRequest: () -> Unit,
    selectedFontSize: Int,
    fontSizes: List<Int>,
    onFontSizeSelected: (Int) -> Unit
) {
    if (!expanded) return
    
    // 计算单个项的高度
    val itemHeight = 28.dp
    // 最多显示4项，计算最大高度
    val maxHeight = itemHeight * 4
    // 创建LazyListState
    val listState = rememberLazyListState()
    // 计算选中项的索引
    val selectedIndex = fontSizes.indexOf(selectedFontSize).coerceAtLeast(0)
    
    // 在展开时执行滚动
    LaunchedEffect(expanded) {
        if (expanded) {
            // 等待布局完成
            kotlinx.coroutines.delay(16) // 一帧的时间
            // 计算目标位置，让选中项居中显示
            val visibleItems = 4 // 可见项数量
            val centerOffset = visibleItems / 2 // 居中偏移量
            val targetIndex = (selectedIndex - centerOffset).coerceAtLeast(0)
            listState.animateScrollToItem(targetIndex)
        }
    }
    
    Surface(
        modifier = Modifier
            .width(99.dp)
            .shadow(
                elevation = 4.dp,
                shape = RoundedCornerShape(20.dp)
            ),
        shape = RoundedCornerShape(20.dp),
        color = Color.White
    ) {
        LazyColumn(
            modifier = Modifier
                .padding(vertical = 10.dp)
                .heightIn(max = maxHeight),
            state = listState
        ) {
            items(fontSizes) { fontSize ->
                FontSizeItem(
                    fontSize = fontSize,
                    isSelected = fontSize == selectedFontSize,
                    onClick = { 
                        onFontSizeSelected(fontSize)
                        onDismissRequest()
                    }
                )
            }
        }
    }
}

/**
 * 字体大小选项项
 * 
 * @param fontSize 字体大小
 * @param isSelected 是否选中
 * @param onClick 点击回调
 */
@Composable
private fun FontSizeItem(
    fontSize: Int,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(28.dp)
            .clickable { onClick() }
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = fontSize.toString(),
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
                color = if (isSelected) Color(0xFF155BF0) else Color.Black.copy(alpha = 0.9f),
                textAlign = TextAlign.Center,
                lineHeight = if (isSelected) 16.sp else 18.8.sp
            )
        )
        
        if (isSelected) {
            Icon(
                painter = painterResource(id = HandwritingTextR.drawable.ic_richitext_menu_textsize_selected),
                contentDescription = stringResource(R.string.state_selected),
                tint = Color(0xFF155BF0),
                modifier = Modifier.size(16.dp)
            )
        }
    }
} 