package com.tcl.ai.note.handwritingtext.ui.richtext.component

import android.annotation.SuppressLint
import android.graphics.BitmapFactory
import android.graphics.BitmapFactory.Options
import android.widget.Toast
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.key.*
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.max
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.hilt.navigation.compose.hiltViewModel
import coil.ImageLoader
import coil.compose.AsyncImage
import coil.decode.GifDecoder
import coil.request.ImageRequest
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.BuildConfig
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.ImageBlockScaleMode
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.DeleteDataDialog
import com.tcl.ai.note.handwritingtext.utils.FileUtils
import com.tcl.ai.note.handwritingtext.vm.RichTextViewModel
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.px2dp
import com.tcl.ai.note.utils.tryToRequestFocus
import kotlinx.coroutines.delay
val imageLoader = ImageLoader.Builder(GlobalContext.instance)
    .components {
        add(GifDecoder.Factory()) // Add Gif support
    }
    .build()
/**
 * 图片内容块
 */
@OptIn(ExperimentalFoundationApi::class)
@SuppressLint("DesignSystem")
@Composable
fun ImageBlock(
    viewModel: RichTextViewModel = hiltViewModel(),
    block: EditorContent.ImageBlock,
    focusRequester: FocusRequester,
    shouldFocus: Boolean,
    onEnterKeyPressed: () -> Unit, // 监听换行事件的回调
    onDelete: () -> Unit,
    onLargeScale: (() -> Unit)? = null,
    onOriginScale: (() -> Unit)? = null,
    onSmallScale: (() -> Unit)? = null,
) {
    var text by remember { mutableStateOf("") }
    var isDeleteDialogVisible by remember { mutableStateOf(false) } // 控制对话框显示状态
    val state by viewModel.state.collectAsState()
    var imageHeight by remember { mutableStateOf(0.dp) }
    val density = LocalDensity.current

    // 自动聚焦逻辑
    LaunchedEffect(shouldFocus) {
        if (shouldFocus && state.editMode) {
            delay(50)
            focusRequester.tryToRequestFocus()
        }
    }
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .pointerInput(Unit){ detectTapGestures() }
            .padding(start = 24.dp, end = 20.dp, bottom = 8.dp)
    ) {
        Row(modifier = Modifier.fillMaxHeight()) {
            Box(
                modifier = Modifier.weight(1f)
            ) {
                var showPopupWithOffset by remember { mutableStateOf<IntOffset?>(null) }
                if (isTablet && showPopupWithOffset != null) {
                    val offset = showPopupWithOffset ?: IntOffset(0,0)
                    showImageMenu(
                        offset = offset,
                        selectedScaleMode = block.scaleMode,
                        onDismissRequest = { showPopupWithOffset = null },
                        onLargeScale = { onLargeScale?.invoke() },
                        onOriginScale = { onOriginScale?.invoke() },
                        onSmallScale = { onSmallScale?.invoke() },
                        onDelete = { onDelete() }
                    )
                }

                val options = Options()
                val path = FileUtils.getRealPathFromUri(GlobalContext.instance, block.uri)
                options.inJustDecodeBounds = true
                BitmapFactory.decodeFile(path,options)
                val measuredWidth = options.outWidth
                val measuredHeight = options.outHeight
                val shortEdge = kotlin.math.max(GlobalContext.screenWidth, GlobalContext.screenHeight)
                val scaleModifier = when (block.scaleMode) {
                    ImageBlockScaleMode.Large -> Modifier.fillMaxWidth()
                    ImageBlockScaleMode.Origin -> Modifier.width(measuredWidth.px2dp.dp)
                    ImageBlockScaleMode.Small -> {
                        val widthPx = when {
                            measuredWidth > shortEdge -> shortEdge * 0.4f
                            measuredWidth > shortEdge / 2 -> measuredWidth * 0.4f
                            else -> measuredWidth * 0.6f
                        }
                        Modifier.width(widthPx.toInt().px2dp.dp)
                    }
                }
                Logger.d("ImageBlock", "ImageBlockScaleMode: ${block.scaleMode}, measuredWidth: $measuredWidth, measuredWidth: $measuredHeight")

                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(block.uri)
                        .crossfade(true)
                        .build(),
                    imageLoader = imageLoader,
                    contentDescription = null,
                    contentScale = ContentScale.FillWidth,
                    modifier = Modifier
                        .then(scaleModifier)
                        .pointerInput(Unit) {
                            detectTapGestures(
                                onLongPress = { offset ->
                                    showPopupWithOffset = IntOffset(offset.x.toInt(), offset.y.toInt())
                                }
                            )
                        }
                )

                // 第一期暂时隐藏图片删除功能
//                if(state.editMode){
//                    IconButton(
//                        onClick = {
//                            isDeleteDialogVisible = true // 显示对话框
//                        },
//                        modifier = Modifier
//                            .align(Alignment.TopEnd)
//                    ) {
//                        Image(
//                            painter = painterResource(id = R.drawable.ic_image_delete),
//                            contentDescription = null
//                        )
//                    }
//                }
            }
            TextField(
                value = text,
                onValueChange = {newValue ->
                    val oldText = text
                    // 获取新增的换行符数量
                    val newLineCount = newValue.count { it == '\n' }
                    val oldLineCount = oldText.count { it == '\n' }
                    if (newLineCount > oldLineCount) {
                        onEnterKeyPressed()
                    }else{
                        text = newValue
                    }
                },
                colors = TextFieldDefaults.textFieldColors(
                    textColor = colorResource(R.color.text_title),//设置字体颜色,
                    cursorColor = colorResource(R.color.text_field_border), // 设置光标颜色
                    focusedIndicatorColor = Color.Transparent, // 有焦点时的底部边线颜色
                    unfocusedIndicatorColor = Color.Transparent, // 无焦点时的底部边线颜色
                    backgroundColor = Color.Transparent // 设置TextField的背景颜色
                ),
                modifier = Modifier
                    .focusRequester(focusRequester) // 使用 block 自己的焦点控制器
                    .width(4.dp)
                    .height(imageHeight)
                    .background(Color.Transparent)
                    .onPreviewKeyEvent { keyEvent -> // 使用 onPreviewKeyEvent 捕获事件
                        if (keyEvent.type == KeyEventType.KeyDown) {
                            when (keyEvent.key) {
                                Key.Backspace -> {
                                    if (text.isEmpty()) {
                                        isDeleteDialogVisible = true // 显示对话框
                                        true // 拦截事件，停止冒泡
                                    } else {
                                        false // 不删除，交给默认行为
                                    }
                                }

                                Key.Enter -> {
                                    onEnterKeyPressed()
                                    true
                                }

                                else -> {
                                    false
                                }
                            }
                        } else {
                            false
                        }
                    },
            )
        }

    }

    // 对话框显示确认提示
    if (isDeleteDialogVisible) {
        DeleteDataDialog(
            text = stringResource(R.string.dialog_title_delete_photo),
            onDelete = {
                onDelete() // 删除当前图片
                isDeleteDialogVisible = false // 隐藏对话框
            },
            onDismiss = {
                isDeleteDialogVisible = false // 隐藏对话框
            }
        )
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun showImageMenu(
    selectedScaleMode: ImageBlockScaleMode,
    offset: IntOffset = IntOffset(0, 0),
    onDismissRequest: (() -> Unit)? = null,
    onLargeScale: (() -> Unit)? = null,
    onOriginScale: (() -> Unit)? = null,
    onSmallScale: (() -> Unit)? = null,
    onDelete: (() -> Unit)? = null,
) {
    Popup(offset = offset, onDismissRequest = onDismissRequest) {
        Column(
            modifier = Modifier
                .width(132.dp)
                .height(192.dp)
                .clip(RoundedCornerShape(20.dp))
                .shadow(20.dp, shape = RoundedCornerShape(20.dp))
                .background(color = colorResource(R.color.bg_dialog))
        ) {
            Row(modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
                .combinedClickable(onClick = {
                    onLargeScale?.invoke()
                    onDismissRequest?.invoke()
                }),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Spacer(modifier = Modifier.padding(start = 16.dp))
                Text(
                    text =  stringResource(id = R.string.image_large),
                    fontSize = 13.sp,
                    color = if (selectedScaleMode == ImageBlockScaleMode.Large) colorResource(R.color.image_menu_scale_selected) else colorResource(R.color.text_title),
                )
                Spacer(modifier = Modifier.weight(1f))
                if (selectedScaleMode == ImageBlockScaleMode.Large) {
                    Image(
                        painter = painterResource(id = R.drawable.menu_selected),
                        contentDescription = null
                    )
                }
                Spacer(modifier = Modifier.padding(end = 16.dp))
            }
            Row(modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
                .combinedClickable(onClick = {
                    onOriginScale?.invoke()
                    onDismissRequest?.invoke()
                }),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Spacer(modifier = Modifier.padding(start = 16.dp))
                Text(
                    text = stringResource(id = R.string.image_original),
                    fontSize = 13.sp,
                    color = if (selectedScaleMode == ImageBlockScaleMode.Origin) colorResource(R.color.image_menu_scale_selected) else colorResource(R.color.text_title),
                )
                Spacer(modifier = Modifier.weight(1f))
                if (selectedScaleMode == ImageBlockScaleMode.Origin) {
                    Image(
                        painter = painterResource(id = R.drawable.menu_selected),
                        contentDescription = null
                    )
                }
                Spacer(modifier = Modifier.padding(end = 16.dp))
            }
            Row(modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
                .combinedClickable(onClick = {
                    onSmallScale?.invoke()
                    onDismissRequest?.invoke()
                }),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Spacer(modifier = Modifier.padding(start = 16.dp))
                Text(
                    text = stringResource(id = R.string.image_small),
                    fontSize = 13.sp,
                    color = if (selectedScaleMode == ImageBlockScaleMode.Small) colorResource(R.color.image_menu_scale_selected) else colorResource(R.color.text_title),
                )
                Spacer(modifier = Modifier.weight(1f))
                if (selectedScaleMode == ImageBlockScaleMode.Small) {
                    Image(
                        painter = painterResource(id = R.drawable.menu_selected),
                        contentDescription = null
                    )
                }
                Spacer(modifier = Modifier.padding(end = 16.dp))
            }
            Divider(color = colorResource(R.color.bg_outline_10), thickness = 1.dp)
            Row(modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
                .combinedClickable(onClick = {
                    onDelete?.invoke()
                    onDismissRequest?.invoke()
                }),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Spacer(modifier = Modifier.padding(start = 16.dp))
                Text(
                    text = stringResource(id = R.string.delete),
                    fontSize = 13.sp,
                    color = colorResource(R.color.image_menu_delete),
                )
                Spacer(modifier = Modifier.padding(end = 16.dp))
            }
        }
    }
}