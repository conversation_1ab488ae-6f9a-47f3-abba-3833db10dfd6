package com.tcl.ai.note.handwritingtext.ui.richtext.component

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Slider
import androidx.compose.material.SliderDefaults
import androidx.compose.material.TextButton
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.state.DrawBoardIntent
import com.tcl.ai.note.handwritingtext.vm.DrawBoardViewModel

/**
 * 画笔线条粗细控制器
 * @param isEraser 是否是橡皮擦线条选择器（画笔（默认）/橡皮擦）
 */
@Composable
fun LineThicknessProgressBar(
    isEraser: Boolean,
    drawBoardViewModel: DrawBoardViewModel = hiltViewModel(),
) {
    var progress by remember { mutableFloatStateOf(0f) }
    val density = LocalDensity.current
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surfaceContainer),
        verticalArrangement = Arrangement.Center
    ) {
        Box(
            modifier = Modifier
                .height(32.dp)
                .padding(horizontal = 16.dp)
        ) {
            if (isEraser) {
                EraserIcon(progress)
            } else {
                LineIcon(progress)
            }

        }
        Spacer(modifier = Modifier.height(5.dp))
        if(isEraser){
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 24.dp, end = 16.dp)
            ) {
                Slider(
                    value = progress,
                    onValueChange = {
                        progress = it
                        val pxValue = with(density){(progress*10).dp.toPx()}

                    },
                    modifier = Modifier
                        .weight(1f)
                        .background(MaterialTheme.colorScheme.surfaceContainer),
                    valueRange = 0f..1f,
                    colors = SliderDefaults.colors(
                        thumbColor = colorResource(R.color.edit_slider_thumb_color),// 滑块手柄的颜色
                        activeTrackColor = colorResource(R.color.edit_slider_track_color),// 激活状态下滑块轨道的颜色
                        inactiveTrackColor = colorResource(R.color.edit_slider_track_color) // 未激活状态下滑块轨道的颜色
                    )
                )
                // Clear All Button
                TextButton(
                    contentPadding = PaddingValues(0.dp),
                    onClick = {
                        drawBoardViewModel.sendIntent(DrawBoardIntent.ClearAll())
                    },
                ) {
                    Text(
                        text = stringResource(id = R.string.clear_all),
                        fontSize = 14.sp,
                        color = colorResource(R.color.btn_clear_all),
                        modifier = Modifier
                            .padding(start = 8.dp)
                    )
                }
            }
        }else{
            Slider(
                value = progress,
                onValueChange = { progress = it },
                modifier = Modifier
                    .padding(horizontal = 24.dp)
                    .background(MaterialTheme.colorScheme.surfaceContainer),
                valueRange = 0f..1f,
                colors = SliderDefaults.colors(
                    thumbColor = colorResource(R.color.edit_slider_thumb_color),// 滑块手柄的颜色
                    activeTrackColor = colorResource(R.color.edit_slider_track_color),// 激活状态下滑块轨道的颜色
                    inactiveTrackColor = colorResource(R.color.edit_slider_track_color) // 未激活状态下滑块轨道的颜色
                )
            )
        }

    }
}

/**
 *  画笔线条粗细进度条上面的线条图标（需跟随进度条手柄滑动而滑动）
 */
@Composable
private fun LineIcon(progress: Float, modifier: Modifier = Modifier) {
    val configuration = LocalConfiguration.current
    val screenWidthDp = configuration.screenWidthDp // 屏幕宽度
    val slidingWidth = screenWidthDp - 48 - 20 // 线条图标可移动宽度：为屏幕宽度减去两边的padding值再减去进度条手柄默认宽度（20dp)
    var height = 2f;
    if (progress > 0.1) {
        height = 10 * progress
    }
    Box(
        modifier = modifier
            .size(32.dp)
            .offset(x = (progress * slidingWidth).dp)
            .background(
                colorResource(R.color.edit_slider_group_color),
                shape = RoundedCornerShape(3.dp)
            ),
        contentAlignment = Alignment.Center
    ) {
        val color = colorResource(R.color.edit_slider_thumb_color)
        Canvas(
            modifier = Modifier
                .width(12.dp)
                .height(height.dp)
        ) {
            drawLine(
                color = color,
                start = Offset(0f, size.height / 2),
                end = Offset(size.width, size.height / 2),
                strokeWidth = height.dp.toPx(), // 静态线条宽度
                cap = StrokeCap.Round,
            )
        }
    }
}

/**
 *  橡皮擦线条粗细进度条上面的线条图标（需跟随进度条手柄滑动而滑动）
 */
@Composable
private fun EraserIcon(progress: Float) {
    val configuration = LocalConfiguration.current
    val screenWidthDp = configuration.screenWidthDp // 屏幕宽度
    val slidingWidth = screenWidthDp - 48 - 20 - 76 // 线条图标可移动宽度：为屏幕宽度减去两边的padding值再减去进度条手柄默认宽度（20dp)再减去Clear ALL内容宽度76dp左右
    var height = 2f;
    if (progress > 0.1) {
        height = 10 * progress
    }
    Box(
        modifier = Modifier
            .size(32.dp)
            .fillMaxHeight()
            .offset(x = (progress * slidingWidth).dp)
            .background(
                colorResource(R.color.edit_slider_group_color),
                shape = RoundedCornerShape(3.dp)
            ),
        contentAlignment = Alignment.Center
    ) {
        Box(
            modifier = Modifier
                .size((height * 2).dp)
                .background(colorResource(R.color.edit_slider_thumb_color), shape = CircleShape)
        )
    }
}