package com.tcl.ai.note.handwritingtext.ui.richtext.component

import androidx.annotation.DrawableRes
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import com.tcl.ai.note.handwritingtext.bean.DoodlePen
import com.tcl.ai.note.handwritingtext.bean.resId
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.widget.IconThemeSwitcher
import com.tcl.ai.note.base.R
import com.tcl.ai.note.utils.stringRes

@Composable
internal fun PenSelector(
    modifier: Modifier = Modifier,
    selDoodlePen: DoodlePen,
    onPanChange: (doodlePen: Doodle<PERSON>en) -> Unit
) {
    val dimens = getGlobalDimens()
    Row(
        horizontalArrangement = Arrangement.SpaceBetween, //  设置间距相等
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .height(dimens.penOptionHeight)
            .fillMaxWidth(),
    ) {
        // 钢笔
        PenItem(
            id = DoodlePen.FountainPen.resId(selDoodlePen == DoodlePen.FountainPen),
            contentDescription = R.string.pen_selector_fountain_pen.stringRes()
        ) {
            onPanChange(DoodlePen.FountainPen)
        }

        // 马克笔
        PenItem(
            id = DoodlePen.Markpen.resId(selDoodlePen == DoodlePen.Markpen),
            contentDescription = R.string.pen_selector_marker_pen.stringRes()
        ) {
            onPanChange(DoodlePen.Markpen)
        }
        // 画笔(圆珠笔)
        PenItem(
            id = DoodlePen.Ballpen.resId(selDoodlePen == DoodlePen.Ballpen),
            contentDescription = R.string.pen_selector_ball_pen.stringRes()
        ) {
            onPanChange(DoodlePen.Ballpen)
        }
    }

}
@Composable
internal fun PenItem(@DrawableRes id: Int, contentDescription: String?,onPanChange: () -> Unit){
    val dimens = getGlobalDimens()
    IconThemeSwitcher(
        btnSize = dimens.btnSize,
        iconSize = dimens.iconSize,
        onClick = onPanChange,
        painter = painterResource(id = id),
        contentDescription = contentDescription
    )


}
