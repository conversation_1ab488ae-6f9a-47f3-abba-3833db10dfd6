package com.tcl.ai.note.handwritingtext.ui.richtext.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.state.ListNoteCategoryState
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.CategoryScreen
import com.tcl.ai.note.handwritingtext.vm.RichTextViewModel
import com.tcl.ai.note.widget.AutoScrollText

/**
 * 选择分类
 */
@Composable
internal fun SelectCategory(
    showPopup:Boolean,
    onDismiss: () -> Unit,
    currentCategoryId:String,
    onSelected: (NoteCategory) -> Unit,
    viewModel: RichTextViewModel = hiltViewModel(),
    topAppBarHeight: Int,
    modifier: Modifier = Modifier
){
    val density = LocalDensity.current
    val screenWidthPx = with(density) {
        LocalConfiguration.current.screenWidthDp.dp.roundToPx()
    }
    val popupWidthPx = with(density) { 226.dp.roundToPx() }
    val horizontalPaddingPx = with(density) { 16.dp.roundToPx() }

    // 当前分类名称
    val listNoteCategoryState by viewModel.listNoteCategoryState.collectAsState()
    var showNewCategory by remember { mutableStateOf(false) }
    val tmpCategoryState = listNoteCategoryState
    if(showPopup && tmpCategoryState is ListNoteCategoryState.Success){
        Popup(
            alignment = Alignment.TopEnd,
            onDismissRequest = { onDismiss() },
            offset = IntOffset(
                x = screenWidthPx - popupWidthPx - horizontalPaddingPx,
                y = topAppBarHeight + with(density) { 8.dp.roundToPx() }
            ),
            properties = PopupProperties(
                focusable = true,
                excludeFromSystemGesture = true,
                dismissOnBackPress = true
            )
        ) {
            Surface(
                elevation = 8.dp,
                shape = RoundedCornerShape(8.dp),
                modifier = modifier.width(226.dp)
            ) {
                Box(modifier = Modifier
                    .width(226.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(
                        colorResource(R.color.bg_dialog)
                    )) {
                    Column(modifier = Modifier) {
                        //Category list
                        LazyColumn(
                            modifier = Modifier
                                .heightIn(max = 292.dp)// 限制弹出菜单高度，使其可以滚动
                                .padding(16.dp)
                        ) {
                            items(tmpCategoryState.items) { item ->
                                if(item.categoryId!=-1L){
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 8.dp)
                                            .clickable {
                                                // 更改当前note分类
                                                onSelected(item)
                                                onDismiss()
                                            }
                                    ) {
                                        Image(
                                            painter = painterResource(id = item.icon),
                                            contentDescription = null,
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        var  textColor = colorResource(R.color.text_title)
                                        if(item.categoryId == currentCategoryId.toLongOrNull()){
                                            textColor = colorResource(R.color.text_category_list_selected)
                                        }else if(currentCategoryId.isEmpty() && item.categoryId ==1L){
                                            textColor = colorResource(R.color.text_category_list_selected)
                                        }
                                        AutoScrollText(
                                            text = item.name+"(${item.noteCounts})",
                                            color = textColor,
                                            modifier = Modifier
                                                .weight(1f, fill = false)
                                                .wrapContentWidth()
                                        )
                                    }
                                }
                            }
                        }

                        Divider(color = colorResource(R.color.bg_outline_10), thickness = 1.dp)

                        //New Category
                        Box(
                            modifier = Modifier
                                .padding(16.dp)
                                .fillMaxWidth()
                                .clickable {
                                    onDismiss()
                                    showNewCategory = true
                                }
                        ) {
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Image(
                                    painter = painterResource(id = R.drawable.ic_category_add),
                                    contentDescription = null,
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = stringResource(R.string.dialog_category_name_title),
                                    color = colorResource(R.color.text_title),
                                    fontSize = 14.sp
                                )
                            }
                        }
                    }
                }

            }
        }
    }

    if(showNewCategory){
        CategoryScreen(
            isPreviewMode = true,
            isAddCategoryMode = true,
            onDismissRequest = {
                showNewCategory = false
            }
        )
    }

}