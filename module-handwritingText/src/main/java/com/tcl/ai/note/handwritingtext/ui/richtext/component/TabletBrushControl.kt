package com.tcl.ai.note.handwritingtext.ui.richtext.component

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Slider
import androidx.compose.material.SliderDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.DoodlePen
import com.tcl.ai.note.handwritingtext.bean.DrawMode
import com.tcl.ai.note.handwritingtext.bean.progressToDp
import com.tcl.ai.note.handwritingtext.state.DrawBoardIntent
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.handwritingtext.vm.BrushSliderModel
import com.tcl.ai.note.handwritingtext.vm.DrawBoardViewModel
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.toArgbLong
import com.tcl.ai.note.utils.toComposeColor


/**
 * 笔刷宽度类型控制
 */
@SuppressLint("DesignSystem")
@Composable
internal fun TabletBrushControl(
    menuBarViewModel: MenuBarViewModel = hiltViewModel(),
    drawBoardViewModel: DrawBoardViewModel = hiltViewModel(),
    brushSliderModel: BrushSliderModel = hiltViewModel(),
    selDoodlePen: DoodlePen,
    sliderMin:Float =0.15f,
    sliderMax:Float =1f,
    onPanChange:(doodlePen: DoodlePen) -> Unit
) {

    val sliderPosition = brushSliderModel.sliderPositionState.value
    val density = LocalDensity.current
    val ratio = (sliderPosition - sliderMin) / (sliderMax - sliderMin)
    val context = LocalContext.current

    Column(
        modifier = Modifier.fillMaxWidth()
            .background(color = TclTheme.colorScheme.reWriteExpandBg)
            .wrapContentHeight()
            .padding(horizontal = 17.dp)
            .padding(bottom = 10.dp)
    ) {
        // 选择画笔类型
        PenSelector(
            modifier = Modifier.padding(horizontal = 13.dp),
            selDoodlePen =selDoodlePen
        ){ pen ->
            brushSliderModel.saveLastDoodlePen(pen)
            onPanChange(pen)
        }
        LineIcon(
            height = selDoodlePen.progressToDp(sliderPosition),
            ratio = ratio)
        Spacer(Modifier.height(2.dp))
        Box(
            modifier = Modifier.fillMaxWidth()
                .clearAndSetSemantics {
                    contentDescription= String.format(context.getString(R.string.brush_slider_status),"${(ratio*100).toInt()}%")
                }
                .height(20.dp),
            contentAlignment = Alignment.Center
        ){
            Slider(
                value = sliderPosition,
                onValueChange = { progress ->
                    brushSliderModel.updateSliderPosition(progress,selDoodlePen)
                    with(drawBoardViewModel) {
                        val thickness = with(density) {selDoodlePen.progressToDp (progress).toPx() }
                        val brushColor = selDoodlePen.color
                        val newStrokeStyle = strokeStyle.copy(
                            width = thickness,
                            drawMode = DrawMode.PEN,
                            color = brushColor,
                            doodlePen = selDoodlePen
                        )
                        brushSliderModel.sendDrawBoardIntent(drawBoardViewModel, newStrokeStyle)
                        menuBarViewModel.onSelectBrush(newStrokeStyle)
                    }
                },
                valueRange = sliderMin..sliderMax,
                modifier = Modifier.fillMaxWidth(),
                colors = SliderDefaults.colors(
                    thumbColor = colorResource(R.color.edit_slider_indicator_color),// 滑块手柄的颜色
                    activeTrackColor = colorResource(R.color.edit_slider_phone_track_active_color),// 激活状态下滑块轨道的颜色
                    inactiveTrackColor = colorResource(R.color.edit_slider_phone_track_inactive_color) // 未激活状态下滑块轨道的颜色
                )
            )
            //TclSlider()
        }



    }
}



/**
 *  画笔线条粗细进度条上面的线条图标（需跟随进度条手柄滑动而滑动）
 */
@Composable
private fun LineIcon(
    height: Dp,
    ratio:Float,
    modifier: Modifier = Modifier
) {
    val slidingWidth =217.5 //父组件宽270减去两边的间距18,再减去滑块宽度18
    Box(
        modifier = modifier
            .invisibleSemantics()
            .width(18.dp)
            .height(21.dp)
            .offset(x = (ratio * slidingWidth).dp)
    ) {
        Image(
            modifier = Modifier.fillMaxSize().invisibleSemantics(),
            painter = painterResource(com.tcl.ai.note.handwritingtext.R.drawable.ic_slider_indicator),
            contentDescription =""
        )
        Box(
            modifier = Modifier.size(18.dp),
            contentAlignment = Alignment.Center
        ){

            Box(
                modifier = Modifier
                    .width(10.dp)
                    .size(height)
                    .background(colorResource(R.color.white), shape = RoundedCornerShape(90))
            )
        }

    }
}

