package com.tcl.ai.note.handwritingtext.ui.richtext.component

import android.annotation.SuppressLint
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Slider
import androidx.compose.material.SliderDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.dp
import androidx.compose.ui.util.fastForEachIndexed
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.utils.borderCircle
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.handwritingtext.vm.BrushSliderModel
import com.tcl.ai.note.handwritingtext.vm.ColorSelectorViewModel
import com.tcl.ai.note.handwritingtext.vm.DrawBoardViewModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.widget.HoverProofIconButton

/**
 * 颜色选择器
 */
@Composable
internal fun TabletColorSelector(
    dbViewModel: DrawBoardViewModel,
    colorSelectorViewModel: ColorSelectorViewModel = viewModel(),
    brushSliderModel: BrushSliderModel = hiltViewModel(),
    onColorChange: (color: Color) -> Unit
) {
    val sliderPosition = with(colorSelectorViewModel) {
        when (currentSelectorIdx.intValue) {
            0 -> sliderPositionState
            1 -> sliderPositionState1
            else -> sliderPositionState2
        }.value
    }
    val dimens = getGlobalDimens()
    LaunchedEffect(Unit) {
        // 启动时重置笔刷，避免橡皮状态残留
        colorSelectorViewModel.initSliderPosition(brushSliderModel.lastDoodlePen.value)
        brushSliderModel.recoverStatus(dbViewModel, brushSliderModel.lastDoodlePen.value)
    }
    Column(
        modifier = Modifier
            .width(dimens.popupWidth)
            .background(color = TclTheme.colorScheme.reWriteExpandBg)
            .padding(horizontal = 18.dp, vertical = 12.dp)
    ) {
        // 列表调色板
        ColorPalette(colors = colorSelectorViewModel.getPaletteColorList()) { index, color ->
            colorSelectorViewModel.apply {
                updatePenColor(color)
                updateSliderPosition(getStops(index), dbViewModel.strokeStyle.doodlePen)
            }
            onColorChange(color)
            colorSelectorViewModel.sendDrawBoardIntent(dbViewModel)
        }
        Spacer(modifier = Modifier.height(17.dp))
        // 滑块调色板
        ColorSlider(sliderStops = sliderPosition) { color: Color, position: Float ->
            colorSelectorViewModel.apply {
                updatePenColor(color)
                updateSliderPosition(position, dbViewModel.strokeStyle.doodlePen)
            }
            onColorChange(color)
            colorSelectorViewModel.sendDrawBoardIntent(dbViewModel)
        }
    }
}

/**
 * 颜色滑块
 */
@SuppressLint("DesignSystem")
@Composable
private fun ColorSlider(
    colorSelectorViewModel: ColorSelectorViewModel = viewModel(),
    sliderStops: Float,
    onColorChange: (color: Color, position: Float) -> Unit
) {
    // 获取渐变颜色列表
    val gradientColors = colorSelectorViewModel.getPaletteColorList()
    var sliderPosition by remember(sliderStops) { mutableFloatStateOf(sliderStops) }
    val  dimens = getGlobalDimens()
    val context = LocalContext.current
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(dimens.sliderHeight),
        contentAlignment = Alignment.Center
    ) {

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
        ) {
            Canvas(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(4.dp)
                    .padding(horizontal = 4.dp)
            ) {
                val brush = Brush.linearGradient(gradientColors)
                drawRoundRect(
                    brush = brush,
                    topLeft = Offset.Zero,
                    size = size.copy(width = sliderPosition * size.width),
                    cornerRadius = CornerRadius(10f, 10f)
                )
            }

            Canvas(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(4.dp)
                    .padding(horizontal = 4.dp)
            ) {
                drawRoundRect(
                    brush = Brush.linearGradient(gradientColors),
                    topLeft = Offset.Zero,
                    size = size,
                    cornerRadius = CornerRadius(10f, 10f)
                )
            }
        }
        Slider(
            value = sliderPosition,
            onValueChange = { value ->
                sliderPosition = value
                val color = colorSelectorViewModel.lerpColor(gradientColors, value)
                onColorChange(color, sliderPosition)
            },
            colors = SliderDefaults.colors(
                thumbColor = colorSelectorViewModel.lerpColor(gradientColors, sliderPosition),
                activeTrackColor = Color.Transparent,
                inactiveTrackColor = Color.Transparent
            ),
            modifier = Modifier
                .clearAndSetSemantics {
                    val desc =String.format(context.getString(R.string.color_slider_status),"${(sliderPosition*100).toInt()}%")
                    contentDescription= desc
                }
                .fillMaxSize()
                .pointerInput(Unit) {
                    detectTapGestures { offset ->
                        sliderPosition = offset.x / size.width
                        val color = colorSelectorViewModel.lerpColor(gradientColors, sliderPosition)
                        onColorChange(color, sliderPosition)
                    }
                }
        )
    }
}


/**
 * 调色板
 */
@Composable
private fun ColorPalette(colors: List<Color>, onColorSelected: (index: Int, color: Color) -> Unit) {
    val dimens = getGlobalDimens()
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.7.dp),
    ) {
        colors.fastForEachIndexed { index, color ->
            val contentDescription = when(index){
                0 ->R.string.brush_color_black.stringRes()
                1 ->R.string.brush_color_white.stringRes()
                2 ->R.string.brush_color_red.stringRes()
                3 ->R.string.brush_color_orange.stringRes()
                4 ->R.string.brush_color_yellow.stringRes()
                5 ->R.string.brush_color_green.stringRes()
                6 ->R.string.brush_color_blue.stringRes()
                else ->""
            }
            HoverProofIconButton (
                modifier = Modifier
                    .invisibleSemantics()
                    .size(22.dp),
                onClick = {
                    onColorSelected(index, color)
                }
            ) {
                //多套一层Box是为了无障碍框中
                Box(
                    modifier = Modifier.size(21.dp)
                        .semantics(mergeDescendants = true) {
                            this.contentDescription =contentDescription
                            this.role = Role.Button
                        },
                    contentAlignment = Alignment.Center
                ){
                    Box(
                        modifier = Modifier
                            .size(dimens.paletteCircleWidth)
                            .background(color, shape = CircleShape)
                            .then((color == colorResource(R.color.edit_palette_color_WHITE)).judge(Modifier.borderCircle(),Modifier))
                    )
                }

            }


        }
    }
}