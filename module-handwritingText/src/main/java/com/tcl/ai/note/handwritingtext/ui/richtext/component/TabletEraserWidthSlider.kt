package com.tcl.ai.note.handwritingtext.ui.richtext.component

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.Slider
import androidx.compose.material.SliderDefaults
import androidx.compose.material.TextButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.state.DrawBoardIntent
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.handwritingtext.vm.DrawBoardViewModel
import com.tcl.ai.note.handwritingtext.vm.EraserSliderModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.dp2px
import com.tcl.ai.note.utils.toPx


/***
 * 橡皮擦宽带滑块
 */
@Deprecated("")
@SuppressLint("DesignSystem")
@Composable
fun TabletEraserWidthSlider(
    drawBoardViewModel: DrawBoardViewModel = hiltViewModel(),
    eraserSliderModel: EraserSliderModel = hiltViewModel(),
    sliderMin:Float =0.26f,
    sliderMax:Float =1f,
    onWidthChanged:(width:Int) ->Unit
    ){

    val sliderPosition = eraserSliderModel.sliderPositionState.value
    val context = LocalContext.current
    val sliderWidthDp = 155

    LaunchedEffect(Unit) {
        //把drawMode调整为橡皮擦，且恢复上次宽
        eraserSliderModel.sendSliderProgressIntent(drawBoardViewModel,sliderPosition)
    }
    val dimens = getGlobalDimens()
    val horPadding = 18.dp

    Column {
        Box(
            modifier = Modifier.padding(top = 5.3.dp).height(54.dp)
                .onSizeChanged { size ->
                    onWidthChanged(size.width)
                }
        ) {
            Row(
                modifier = Modifier
                    .wrapContentWidth()
                    .align(Alignment.BottomEnd)
                    .height(42.dp)
                    .defShadow()
                    .background(color =TclTheme.colorScheme.reWriteExpandBg, shape = RoundedCornerShape(size = 6.dp)),
                verticalAlignment = Alignment.CenterVertically
            ){
                Box(
                    modifier = Modifier.height(dimensionResource(com.tcl.ai.note.handwritingtext.R.dimen.bottom_menu_brush_slider_height))
                        .padding(horizontal = horPadding)
                        .width(sliderWidthDp.dp)
                ){
                    Slider(
                        value = sliderPosition,
                        onValueChange = { progress ->
                            eraserSliderModel.updateSliderPosition(progress)
                            eraserSliderModel.sendSliderProgressIntent(drawBoardViewModel,progress)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .clearAndSetSemantics {
                                val desc =String.format(context.getString(R.string.eraser_slider_status),"${(sliderPosition*100).toInt()}%")
                                contentDescription= desc
                            }
                            .padding(horizontal = 0.dp),
                        valueRange = sliderMin..sliderMax,
                        colors = SliderDefaults.colors(
                            thumbColor = colorResource(R.color.edit_slider_indicator_color),// 滑块手柄的颜色
                            activeTrackColor = colorResource(R.color.edit_slider_phone_track_active_color),// 激活状态下滑块轨道的颜色
                            inactiveTrackColor = colorResource(R.color.edit_slider_phone_track_inactive_color) // 未激活状态下滑块轨道的颜色
                        )
                    )
                }
                Divider(
                    modifier = Modifier
                        .height(24.dp)
                        .width(0.75.dp),
                    color = colorResource(R.color.bg_outline_5)
                )
                Box(
                    modifier = Modifier.wrapContentWidth()
                        .padding(horizontal = 8.5.dp),
                    contentAlignment = Alignment.Center
                ){
                    TextButton(
                        contentPadding = PaddingValues(0.dp),
                        onClick = {
                            drawBoardViewModel.sendIntent(DrawBoardIntent.ClearAll())
                        },
                    ) {
                        Text(
                            text = stringResource(id = R.string.clear_all),
                            fontSize = 12.sp,
                            color = colorResource(R.color.btn_clear_all),
                            modifier = Modifier
                                .padding(start = 8.dp)
                        )
                    }
                }

            }
            Box(
                modifier = Modifier
                    .padding(horizontal = horPadding)
                    .width(sliderWidthDp.dp)
            ){
                IndicatorIcon(
                    progress = sliderPosition,
                    sliderMin =sliderMin,
                    sliderMax =sliderMax,
                    slidingWidthPx = sliderWidthDp.dp2px - horPadding.toPx,
                    Modifier.align(Alignment.TopStart)
                )
            }
        }
    }


}
/**
 *  橡皮擦线条粗细进度条上面的线条图标（需跟随进度条手柄滑动而滑动）
 */
@Composable
private fun IndicatorIcon(
    progress: Float,
    sliderMin:Float,
    sliderMax:Float,
    slidingWidthPx: Float,
    modifier: Modifier) {

    val height =10 * progress
    val ratio = (progress - sliderMin) / (sliderMax - sliderMin)
    val offsetX= (ratio * slidingWidthPx).toInt()
    Box(
        modifier = modifier
            .invisibleSemantics()
            .width(18.dp)
            .height(21.dp)
            .fillMaxHeight()
            .offset {
                IntOffset(offsetX, 0)
            }
    ) {
        Image(
            modifier = Modifier.fillMaxSize()
                .invisibleSemantics(),
            painter = painterResource(com.tcl.ai.note.handwritingtext.R.drawable.ic_slider_indicator),
            contentDescription =""
        )
        Box(
            modifier = Modifier.size(18.dp),
            contentAlignment = Alignment.Center
        ){
            Box(
                modifier = Modifier
                    .size(height.dp)
                    .background(colorResource(R.color.white), shape = CircleShape)
            )
        }

    }

}
