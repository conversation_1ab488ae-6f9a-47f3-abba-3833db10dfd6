package com.tcl.ai.note.handwritingtext.ui.richtext.component

import android.util.Log
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.handwritingtext.R
import com.tcl.ai.note.handwritingtext.database.entity.BgMode
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.database.entity.contentDescription
import com.tcl.ai.note.handwritingtext.database.entity.resId
import com.tcl.ai.note.handwritingtext.vm.SkinViewModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.judge


/**
 * 皮肤选择面板
 */
@Composable
internal fun TabletSkinControl(
    skinViewModel: SkinViewModel,
    onChangeSkin:(skinModel: Skin) -> Unit
) {
    val selBgMode = skinViewModel.bgModeState.value
    val selBgColor = skinViewModel.bgColorState.value


    Column(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .background(
                color = TclTheme.colorScheme.reWriteExpandBg,
                shape = RoundedCornerShape(8.dp),
                )
            .padding(dimensionResource(com.tcl.ai.note.handwritingtext.R.dimen.bottom_menu_skin_paddinng))
    ) {
        Row(
            modifier = Modifier.fillMaxWidth()
        ) {
            BgMode.entries.forEach { bgMode ->
                TabletBgStyle(bgMode,bgMode == selBgMode) {
                    skinViewModel.updateBgMode(bgMode)
                    onChangeSkin(skinViewModel.skinModel)
                }
            }
        }
        Spacer(modifier = Modifier.height(24.dp))
        TabletColorCardList(bgColors = skinViewModel.bgColors,selColor =selBgColor) { bgColor ->
            skinViewModel.updateBgColor(bgColor)
            onChangeSkin(skinViewModel.skinModel)
        }

    }
}


@Composable
internal fun TabletBgStyle(
    bgMode: BgMode,
    isCheck: Boolean,
    darkTheme: Boolean = isSystemInDarkTheme(),
    onCheck:(bgMode:BgMode) -> Unit) {
    val context = LocalContext.current
    Box(
        modifier = Modifier
            .semantics {
                this.contentDescription = context.getString(bgMode.contentDescription())
            }
            .padding(end = 15.5.dp)
            .size(
                dimensionResource(R.dimen.bottom_menu_skin_style_width),
                dimensionResource(R.dimen.bottom_menu_skin_style_height))
            .border(
                width = 2.dp,
                color = if (isCheck) colorResource(R.color.skin_style_check) else Color.Transparent
            )
            .background(
                color = darkTheme.judge(R.color.skin_card_bg.colorRes(),Color.Transparent)
            )
            .clickable {
                onCheck(bgMode)
            }
    ) {
        Image(
            painter = painterResource(bgMode.resId()),
            contentDescription = stringResource(com.tcl.ai.note.base.R.string.background_mode),
            modifier = Modifier
                .fillMaxSize()
        )

    }
}
@Composable
internal fun TabletColorCardList(
    darkTheme: Boolean = isSystemInDarkTheme(),
    bgColors:List<Color>,
    selColor: Color,
    onCheck:(color:Color) -> Unit){
    Row(
        modifier = Modifier.fillMaxWidth().wrapContentHeight(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        bgColors.forEachIndexed { index, color ->
            val contentDescription = when(index){
                0 -> stringResource(com.tcl.ai.note.base.R.string.edit_bottom_menu_skin_white)
                1 -> stringResource(com.tcl.ai.note.base.R.string.edit_bottom_menu_skin_grey)
                2 -> stringResource(com.tcl.ai.note.base.R.string.edit_bottom_menu_skin_green)
                3 -> stringResource(com.tcl.ai.note.base.R.string.edit_bottom_menu_skin_yellow)
                4 -> stringResource(com.tcl.ai.note.base.R.string.edit_bottom_menu_skin_pink)
                5 -> stringResource(com.tcl.ai.note.base.R.string.edit_bottom_menu_skin_purple)
                else ->""
            }
            TabletColorCardItem(
                darkTheme = (darkTheme && index ==0),
                color = color,
                isCheck = selColor == color,
                contentDescription = contentDescription) { _color ->
                onCheck(_color)
            }
        }
    }
}
@Composable
internal  fun TabletColorCardItem(darkTheme: Boolean ,color:Color,isCheck: Boolean,contentDescription:String,onCheck:(color:Color) -> Unit){
    Box(
        modifier = Modifier.size(40.dp)
            .semantics {
                this.contentDescription = contentDescription
            }
            .border(
                width = (isCheck).judge(2,1).dp,
                color =  (isCheck).judge(
                    R.color.skin_style_check,
                    darkTheme.judge(com.tcl.ai.note.base.R.color.transparent,R.color.skin_style_check_nor))
                    .colorRes(),
                shape = RoundedCornerShape(6.dp)
            )
            .background(color = darkTheme.judge(R.color.skin_card_bg.colorRes(),color),  shape = RoundedCornerShape(6.dp) )
            .clickable {
                onCheck(color)
            }
    )
}