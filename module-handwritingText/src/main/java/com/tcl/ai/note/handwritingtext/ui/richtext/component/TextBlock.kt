package com.tcl.ai.note.handwritingtext.ui.richtext.component

import android.widget.Toast
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.isSpecified
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.input.key.type
import androidx.compose.ui.layout.LayoutCoordinates
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.intent.RichTextIntent
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.DeleteDataDialog
import com.tcl.ai.note.handwritingtext.vm.DrawBoardViewModel
import com.tcl.ai.note.handwritingtext.vm.RichTextViewModel
import com.tcl.ai.note.theme.CursorColor
import com.tcl.ai.note.theme.editorRichTextStyle
import com.tcl.ai.note.utils.LocalScaffoldPadding
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.utils.tryToRequestFocus
import com.tcl.ai.note.voicetotext.view.widget.DeleteAudioDialog

/**
 * 可编辑文本块
 */
@Composable
fun TextBlock(
    viewModel: RichTextViewModel = hiltViewModel(),
    itemSize: Int,
    block: EditorContent.TextBlock,
    focusRequester: FocusRequester,
    shouldFocus: Boolean,
    index: Int,
    darkTheme: Boolean,
    lineHeight: TextUnit = Skin.lineHeight().sp,
    onAddContent: (EditorContent, Int?) -> Unit,
    onUpdateContent: (Int, EditorContent.TextBlock) -> Unit,
    onRemoveContent: (Int) -> Unit,
    onCursorChange: (Int,Boolean) -> Unit,
    currentTextStyle: TextStyle, // 接收当前样式
    drawBoardViewModel: DrawBoardViewModel = hiltViewModel(),
) {
    // 监听当前选中状态
    var text by remember(block) {
        mutableStateOf(
            processInitialTextStyle(block.text)
        )
    }
    LaunchedEffect(block) {
        text = processInitialTextStyle(block.text)
        viewModel.updateSelection(block.text.selection)
    }

    val state by viewModel.state.collectAsState()
    val coroutineScope = rememberCoroutineScope() // 获取当前可组合作用域的协程上下文
    // val bringIntoViewRequester = remember { BringIntoViewRequester() } // 创建 BringIntoViewRequester
    var layoutCoordinates: LayoutCoordinates? = remember { null }
    var isDeleteDialogVisible by remember { mutableStateOf(false) } // 控制删除对话框显示状态

    val context = LocalContext.current
    val view = LocalView.current
    val innerPadding = LocalScaffoldPadding.current
    val density = LocalDensity.current
    val imeHeight = WindowInsets.ime.getBottom(density)

    // 自动聚焦逻辑
    LaunchedEffect(shouldFocus, state.cursorPosition, state.editMode) {
        if (shouldFocus && state.editMode) {
            try {
                // 使用 try-catch 包裹焦点请求，避免崩溃
                focusRequester.tryToRequestFocus()
                // 更新光标位置
                if (state.cursorPosition >= 0 && text.selection.start != state.cursorPosition) {
                    text = text.copy(selection = TextRange(state.cursorPosition))
                }
                // 确保光标可见
                // coroutineScope.launch {
                //     try {
                //         bringIntoViewRequester.bringIntoView()
                //     } catch (e: Exception) {
                //         Logger.e("TextBlock", "Failed to bring into view: ${e.message}")
                //     }
                // }
            } catch (e: Exception) {
                // 如果请求焦点失败，记录日志但不崩溃
                Logger.e("TextBlock", "Failed to request focus: ${e.message}")
            }
        }
    }

    // 解构时，保持键盘不收起来
    DisposableEffect(shouldFocus) {
        onDispose {
            Logger.d("TodoBlock", "DisposableEffect: editMode=${state.editMode}, index=$index")
            if (state.editMode && state.focusedIndex == index){
                focusRequester.tryToRequestFocus()
            }
        }
    }

    Box(
        modifier = Modifier
            .padding(start = 24.dp, end = 24.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            // 段落符号
            when (block.paragraphStyle) {
                ParagraphStyle.NUMBERED -> {
                    // 动态计算序号（示例简化，实际需根据上下文计算）
                    val itemNumber = viewModel.getOrderNumberForBlock(index)
                    Text(
                        text = "$itemNumber. ",
                        modifier = Modifier.padding( end = 4.dp),
                        style =  editorRichTextStyle.copy(
                            color = darkTheme.judge(
                                R.color.white.colorRes(),
                                R.color.text_edit_color.colorRes()
                            ),
                            lineHeightStyle = LineHeightStyle(
                                alignment = LineHeightStyle.Alignment.Center,
                                trim = LineHeightStyle.Trim.None
                            ),
                        )
                    )
                }

                ParagraphStyle.BULLETED -> {
                    Text(
                        text = "• ",
                        modifier = Modifier.padding(end = 9.dp),
                        style =  editorRichTextStyle.copy(
                            color = darkTheme.judge(
                                R.color.white.colorRes(),
                                R.color.text_edit_color.colorRes()
                            ),
                            lineHeightStyle = LineHeightStyle(
                                alignment = LineHeightStyle.Alignment.Center,
                                trim = LineHeightStyle.Trim.None
                            ),
                        )
                    )
                }

                else -> {}
            }

            // 在组件内部维护当前块的选区状态
            var textLayoutResult by remember { mutableStateOf<TextLayoutResult?>(null) }
            // 添加防抖机制
            var lastUpdate by remember { mutableLongStateOf(0L) }
            val tip = stringResource(R.string.max_content_length)

            val cursorChange = change@{  cursorIdx: Int,isChange:Boolean ->
                onCursorChange(cursorIdx,isChange)
                val cursorVerticalPos = try {
                    textLayoutResult?.let { layoutResult ->
                        layoutCoordinates?.let { coordinates ->
                            val cursorOffset = try {
                                layoutResult.getCursorRect(cursorIdx).topLeft
                            } catch (ex: IllegalArgumentException) {
                                Offset(
                                    x = coordinates.size.width.toFloat(),
                                    y = coordinates.size.height.toFloat()
                                )
                            }
                            // 获取光标位置时，窗口可能已经重组，导致异常
                            // LayoutCoordinate operations are only valid when isAttached is true
                            val windowOffset = coordinates.localToWindow(cursorOffset)
                            windowOffset.y - innerPadding.calculateTopPadding().toPx
                        }
                    } ?: 0f
                } catch (ex: Exception) {
                    Logger.e(TAG, "try to move to cursor error: ${ex.message}")
                    0f
                }

                val maxCursorHeight = if (isTablet) {
                    (view.height
                            - innerPadding.calculateBottomPadding().toPx
                            - innerPadding.calculateTopPadding().toPx
                            - imeHeight
                            - 100)
                } else {
                    (view.height
                            - innerPadding.calculateBottomPadding().toPx
                            - innerPadding.calculateTopPadding().toPx
                            - 150)
                }
                val minCursorHeight = 50
                if (cursorVerticalPos < minCursorHeight) {
                    drawBoardViewModel.updateTransByDelta(y = minCursorHeight - cursorVerticalPos)
                } else if (cursorVerticalPos > maxCursorHeight) {
                    drawBoardViewModel.updateTransByDelta(y = maxCursorHeight - cursorVerticalPos)
                }
            }

            // 监听ViewModel的拆分忙状态（需和ViewModel里isSplittingBlock保持流同步）
            val isSplittingBlock by viewModel.isSplittingBlock.collectAsState()

            BasicTextField(
                value = text,
                onValueChange = { newValue ->
                    val oldText = text.text
                    val oldLineCount = oldText.count { it == '\n' }
                    val newText = newValue.text
                    val newLineCount = newText.count { it == '\n' }
                    val isListItem = block.paragraphStyle == ParagraphStyle.BULLETED || block.paragraphStyle == ParagraphStyle.NUMBERED
                    val selection = newValue.selection
                    val original = text.annotatedString
                    // 检测是否为删除操作
                    val isDeletion = newText.length < oldText.length

                    // 对于删除操作，不应用防抖限制；对于输入操作，保留防抖机制
                    if (!isDeletion && System.currentTimeMillis() - lastUpdate < 50) return@BasicTextField
                    lastUpdate = System.currentTimeMillis()

                    // 先对当前内容输入后的长度校验，如果超出限制则不能添加进来
                    val canAdd = viewModel.validateContentCanAdd(newValue.text)
                    if (!canAdd) {
                        Toast.makeText(context, tip, Toast.LENGTH_SHORT).show()
                        return@BasicTextField
                    }

                    // 只有当前不是在拆分处理，并且确实增加了换行，才允许提交拆分请求
                    if (isListItem && newLineCount > oldLineCount && !isSplittingBlock) {
                        val newAnnotatedString = transferStylesToNewText(text.annotatedString,text.text, newValue.text)
                        val patchedValue = newValue.copy(annotatedString = newAnnotatedString)
                        // 用原有handleEnterKey所需参数，交由ViewModel队列处理
                        viewModel.enqueueBlockSplit(
                            index = index,
                            block = block,
                            oldText = text,
                            newValue = patchedValue,
                            style = block.paragraphStyle
                        )
                    }else{
                        val builder = AnnotatedString.Builder(newText)
                        val delta = newText.length - oldText.length
                        val isInsertion = delta > 0

                        // 处理样式继承和调整
                        text.annotatedString.spanStyles.forEach { span ->
                            val styleWithoutColor = span.item.copy(color = Color.Unspecified)
                            var newStart = span.start.coerceAtLeast(0)
                            var newEnd = span.end

                            if (delta != 0) {
                                if (isInsertion) {
                                    // 插入处理
                                    val insertPos = selection.start - delta
                                    if (newStart >= insertPos) newStart += delta
                                    if (newEnd >= insertPos) newEnd += delta
                                } else {
                                    // 删除处理：计算删除范围
                                    val (deleteStart, deleteEnd) = findDeletionRange(
                                        oldText,
                                        newText
                                    ) ?: (0 to 0)
                                    val deleteLength = deleteEnd - deleteStart

                                    when {
                                        newStart >= deleteEnd -> newStart -= deleteLength
                                        newStart > deleteStart -> newStart = deleteStart
                                    }
                                    when {
                                        newEnd >= deleteEnd -> newEnd -= deleteLength
                                        newEnd > deleteStart -> newEnd = deleteStart
                                    }
                                }
                            }

                            // 确保范围有效性
                            newStart = newStart.coerceIn(0, newText.length)
                            newEnd = newEnd.coerceIn(newStart, newText.length)

                            if (newStart < newEnd) {
                                builder.addStyle(styleWithoutColor, newStart, newEnd)
                            }
                        }

                        // 处理插入字符样式
                        if (isInsertion) {
                            val insertPos = selection.start - delta
                            val end = insertPos + delta
                            builder.addStyle(currentTextStyle.toSpanStyle(), insertPos, end)
                        }

                        text = newValue.copy(annotatedString = builder.toAnnotatedString())

                        onUpdateContent(index, block.copy(text = text))
                        if(oldText==newText){
                            cursorChange(selection.start,true)
//                            viewModel.updateSelection(newValue.selection)
                        }else{
                            cursorChange(selection.start,false)
                        }
                    }
                },
                keyboardOptions = KeyboardOptions(
                    imeAction = ImeAction.Default // 允许换行
                ),
                cursorBrush = SolidColor(CursorColor),
                onTextLayout = { textLayoutResult = it },
                modifier = Modifier
                    .fillMaxSize()
                    .focusRequester(focusRequester) // 正确绑定
                    // .bringIntoViewRequester(bringIntoViewRequester) // 绑定 BringIntoViewRequester
                    .onFocusChanged { focusState ->
                        if (focusState.isFocused) {
                            val cursorPosition = text.selection.start
                            cursorChange(cursorPosition,false) // 光标位置
                        }
                    }
                    .onGloballyPositioned {
                        layoutCoordinates = it
                    }
                    .onPreviewKeyEvent { keyEvent -> // 使用 onPreviewKeyEvent 捕获事件
                        if (keyEvent.type == KeyEventType.KeyDown) {
                            when (keyEvent.key) {
                                Key.Backspace -> {
                                    val currentText = text.text
                                    val cursorPos = text.selection.start

                                    // 光标在块开头且不是第一个块
                                    if (cursorPos == 0 && index > 0) {
                                        val prevIndex = index - 1
                                        val prevBlock = viewModel.state.value.contents[prevIndex]

                                        when (prevBlock) {
                                            is EditorContent.TextBlock -> {
                                                // 将光标后的内容合并到前面的文本块中
                                                mergeTextBlocks(viewModel,prevBlock,block, index, text.annotatedString)
                                            }
                                            is EditorContent.TodoBlock -> {
                                                // 将光标后的内容合并到前面的待办事项中
                                                mergeTextWithTodoBlock(viewModel,prevBlock,block, index, text.annotatedString)
                                            }

                                            is EditorContent.AudioBlock -> {
                                                /*if (text.text.isEmpty() && block.paragraphStyle != ParagraphStyle.NONE){
                                                    onRemoveContent(index)
                                                    onAddContent(EditorContent.TextBlock(text = TextFieldValue(""), ParagraphStyle.NONE), index)
                                                } else {
                                                    isDeleteDialogVisible = true // 显示对话框
                                                }
                                                return@onPreviewKeyEvent true*/
                                            }
                                            is EditorContent.ImageBlock -> {
                                                if (text.text.isEmpty() && block.paragraphStyle != ParagraphStyle.NONE){
                                                    onRemoveContent(index)
                                                    onAddContent(EditorContent.TextBlock(text = TextFieldValue(""), ParagraphStyle.NONE), index)
                                                } else {
                                                    isDeleteDialogVisible = true // 显示对话框
                                                }
                                                return@onPreviewKeyEvent true
                                            }

                                            is EditorContent.RichTextV2 -> {}
                                        }

                                        return@onPreviewKeyEvent true
                                    }else if (currentText.isEmpty()) {
                                        onRemoveContent(index)
                                        viewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(0))
                                        viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(0,true))
                                        return@onPreviewKeyEvent true
                                    }
                                    // 首块保护
                                    return@onPreviewKeyEvent false
                                }

                                Key.Delete -> {
                                    // 全局禁用后向删除
                                    return@onPreviewKeyEvent true
                                }

                                Key.Enter -> {
                                    // 不return true,让其自然流转
                                    return@onPreviewKeyEvent false
                                }

                                else -> return@onPreviewKeyEvent false
                            }
                        }
                        false
                    },
                textStyle = editorRichTextStyle.copy(
                    color = darkTheme.judge(
                        R.color.white.colorRes(),
                        R.color.text_edit_color.colorRes()
                    ),
                    lineHeightStyle = LineHeightStyle(
                        alignment = LineHeightStyle.Alignment.Center,
                        trim = LineHeightStyle.Trim.None
                    ),
                    platformStyle = PlatformTextStyle(
                        includeFontPadding = false
                    ),


                    ),
                decorationBox = { innerTextField ->
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.CenterStart
                    ) {
                        if (itemSize == 1 && text.text.isEmpty() && block.paragraphStyle == ParagraphStyle.NONE) {
                            BasicTextField(
                                value = isTablet.judge(
                                    "",
                                    stringResource(R.string.enter_your_text)
                                ),
                                onValueChange = {},
                                textStyle =  editorRichTextStyle.copy(
                                    color = darkTheme.judge(
                                        R.color.text_input_hint_dark,
                                        R.color.text_input_hint
                                    ).colorRes()
                                ),
                                enabled = false,
                                modifier = Modifier
                                    .align(Alignment.CenterStart)
                                    .clearAndSetSemantics { }
                            )
                        }
                        innerTextField()
                    }
                }
            )
        }

    }

    // 对话框显示确认提示
    if (isDeleteDialogVisible) {
        val prevBlock = state.contents[index - 1]
        if(prevBlock is EditorContent.AudioBlock){
            DeleteAudioDialog(
                onDismiss = { isDeleteDialogVisible = false },
                onDelete = {
                    viewModel.handleIntent(RichTextIntent.RemoveContent(index - 1))
                    isDeleteDialogVisible = false
                }
            )
        }else if(prevBlock is EditorContent.ImageBlock){
            DeleteDataDialog(
                text = stringResource(R.string.dialog_title_delete_photo),
                onDelete = {
                    viewModel.handleIntent(RichTextIntent.RemoveContent(index - 1))
                    isDeleteDialogVisible = false // 隐藏对话框
                },
                onDismiss = {
                    isDeleteDialogVisible = false // 隐藏对话框
                }
            )
        }

    }
}

/**
 * 将原文本中的样式转换到新的文本中
 */
private fun transferStylesToNewText(
    original: AnnotatedString,
    oldText: String,
    newText: String
): AnnotatedString {
    // 建立 oldPos->newPos 映射
    val oldLen = oldText.length
    val newLen = newText.length
    val mapping = IntArray(oldLen + 1) { -1 }
    var oi = 0
    var ni = 0
    while (oi < oldLen && ni < newLen) {
        if (oldText[oi] == newText[ni]) {
            mapping[oi] = ni
            oi++
            ni++
        } else if (newText[ni] == '\n' && (oi == 0 || oldText[oi-1] == newText[ni-1])) {
            ni++   // 跳过新插入换行
        } else {
            mapping[oi] = ni // 容错
            oi++
            ni++
        }
    }
    mapping[oi] = ni

    // 保证每一span只迁移到有效覆盖区间
    val usedSpans = mutableSetOf<Pair<Int, Int>>() // 去重防抖

    return buildAnnotatedString {
        append(newText)
        original.spanStyles.forEach { span ->
            val mappedStart = mapping.getOrNull(span.start)?.takeIf { it != -1 } ?: 0
            val mappedEnd = mapping.getOrNull(span.end)?.takeIf { it != -1 } ?: newLen
            if (mappedStart < mappedEnd && mappedStart in 0 until newLen && mappedEnd in 1..newLen) {
                // "去重"；避免冗余span
                val key = Pair(mappedStart, mappedEnd)
                if (usedSpans.add(key)) {
                    addStyle(span.item.copy(color = span.item.color.takeIf { it.isSpecified } ?: Color.Unspecified), mappedStart, mappedEnd)
                }
            }
        }
    }
}

/**
 * 将光标后的内容合并到前一个文本块中
 */
private fun mergeTextBlocks(
    viewModel: RichTextViewModel,
    prevBlock: EditorContent.TextBlock,
    block: EditorContent.TextBlock,
    index: Int,
    currentAnnotatedString: AnnotatedString
) {
    val prevIndex = index - 1
    val prevAnnotatedString = prevBlock.text.annotatedString

    // 合并 AnnotatedString，保留样式
    val mergedAnnotatedString = buildAnnotatedString {
        append(prevAnnotatedString)
        append(currentAnnotatedString)
    }

    val prevBlockOriginal = prevBlock.copy(
        // 深拷贝完整的 text 和 paragraphStyle
        text = prevBlock.text.copy(),
        paragraphStyle = prevBlock.paragraphStyle
    )
    val curBlockOriginal = EditorContent.TextBlock(
        text = TextFieldValue(annotatedString = currentAnnotatedString),
        paragraphStyle = block.paragraphStyle // 用合并前（被删掉的块的）paragraphStyle
    )

    val newMerged = prevBlock.copy(
        text = prevBlock.text.copy(
            annotatedString = mergedAnnotatedString
        ),
        // ！！！合并结果的paragraphStyle继承prevBlock
        paragraphStyle = prevBlock.paragraphStyle
    )

    // 调用合并操作方法
    viewModel.handleMergeBlocks(
        prevIndex = prevIndex,
        prevBlockOriginal = prevBlockOriginal,
        curIndex = index,
        curBlockOriginal = curBlockOriginal,
        mergedBlock = newMerged
    )
}

/**
 * 将光标后的内容合并到前一个待办事项中
 */
private fun mergeTextWithTodoBlock(
    viewModel: RichTextViewModel,
    prevBlock: EditorContent.TodoBlock,
    block: EditorContent.TextBlock,
    index: Int,
    currentAnnotatedString: AnnotatedString
) {
    val prevIndex = index - 1
    val prevAnnotatedString = prevBlock.text.annotatedString

    // 合并 AnnotatedString，保留样式
    val mergedAnnotatedString = buildAnnotatedString {
        append(prevAnnotatedString)
        append(currentAnnotatedString)
    }

    val prevBlockOriginal = prevBlock.copy(
        // 深拷贝完整的 text
        text = prevBlock.text.copy()
    )
    val curBlockOriginal = EditorContent.TextBlock(
        text = TextFieldValue(annotatedString = currentAnnotatedString),
        paragraphStyle = block.paragraphStyle // 用合并前（被删掉的块的）paragraphStyle
    )

    val newMerged = prevBlock.copy(
        text = prevBlock.text.copy(
            annotatedString = mergedAnnotatedString
        )
    )

    // 调用合并操作方法
    viewModel.handleMergeBlocks(
        prevIndex = prevIndex,
        prevBlockOriginal = prevBlockOriginal,
        curIndex = index,
        curBlockOriginal = curBlockOriginal,
        mergedBlock = newMerged
    )
}

// 删除范围计算
private fun findDeletionRange(old: String, new: String): Pair<Int, Int>? {
    if (old.length <= new.length) return null
    var start = 0
    while (start < new.length && old[start] == new[start]) start++
    var end = old.lastIndex
    var newEnd = new.lastIndex
    while (end >= 0 && newEnd >= 0 && old[end] == new[newEnd]) {
        end--
        newEnd--
    }
    return start to (end + 1)
}

// 文本初始化处理函数
private fun processInitialTextStyle(textValue: TextFieldValue): TextFieldValue {
    val builder = AnnotatedString.Builder(textValue.text)
    textValue.annotatedString.spanStyles.forEach { span ->
        // 过滤颜色属性
        val filteredStyle = span.item.copy(color = Color.Unspecified)
        builder.addStyle(filteredStyle, span.start, span.end)
    }
    // 保留原始选择状态
    return textValue.copy(annotatedString = builder.toAnnotatedString())
}

private const val TAG = "TextBlock"