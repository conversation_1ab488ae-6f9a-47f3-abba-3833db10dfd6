package com.tcl.ai.note.handwritingtext.ui.richtext.component
import android.widget.Toast
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.relocation.BringIntoViewRequester
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.input.key.type
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.intent.RichTextIntent
import com.tcl.ai.note.handwritingtext.state.RichTextState
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.DeleteDataDialog
import com.tcl.ai.note.handwritingtext.vm.RichTextViewModel
import com.tcl.ai.note.theme.CursorColor
import com.tcl.ai.note.theme.LINE_HEIGHT
import com.tcl.ai.note.theme.editorRichTextStyle
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.tryToRequestFocus
import com.tcl.ai.note.voicetotext.view.widget.DeleteAudioDialog
import kotlinx.coroutines.launch

/**
 * 待办事项块
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun TodoBlock(
    viewModel: RichTextViewModel = hiltViewModel(),
    lineHeight: TextUnit = Skin.lineHeight().sp,
    block: EditorContent.TodoBlock,
    focusRequester: FocusRequester,
    shouldFocus: Boolean,
    index: Int,
    darkTheme: Boolean,
    onAddContent: (EditorContent, Int?) -> Unit,
    onUpdate: (EditorContent.TodoBlock) -> Unit,
    onCursorChange: (Int,Boolean) -> Unit,
    onEnterKeyPressed: () -> Unit, // 监听换行事件的回调
    onDeleteBlock: (Int) -> Unit, // 删除当前待办项的回调
    currentTextStyle: TextStyle // 接收当前样式
) {
    // 仅保存字符串，而非整个 TextFieldValue 对象
    var text by remember { mutableStateOf(block.text) }
    // 当block更新时同步text状态
    LaunchedEffect(block) {
        text = block.text
        viewModel.updateSelection(block.text.selection)
    }

    val state by viewModel.state.collectAsState()
    val coroutineScope = rememberCoroutineScope() // 获取当前可组合作用域的协程上下文
    val bringIntoViewRequester = remember { BringIntoViewRequester() } // 创建 BringIntoViewRequester
    var isDeleteDialogVisible by remember { mutableStateOf(false) } // 控制删除对话框显示状态

    val context = LocalContext.current

    LaunchedEffect(shouldFocus, state.cursorPosition, state.editMode) {
        if (shouldFocus && state.editMode) {
            try {
                // 使用 try-catch 包裹焦点请求，避免崩溃
                focusRequester.tryToRequestFocus()

                // 更新光标位置
                if (state.cursorPosition >= 0 && text.selection.start != state.cursorPosition) {
                    text = text.copy(selection = TextRange(state.cursorPosition))
                }
            } catch (e: Exception) {
                // 如果请求焦点失败，记录日志但不崩溃
                Logger.e("TodoBlock", "Failed to request focus: ${e.message}")
            }
        }
    }

    // 解构时，保持键盘不收起来
    DisposableEffect(shouldFocus) {
        onDispose {
            Logger.d("TodoBlock", "DisposableEffect: editMode=${state.editMode}, index=$index")
            if (state.editMode && state.focusedIndex == index){
                focusRequester.tryToRequestFocus()
            }
        }
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 20.dp, end = 20.dp),
    ) {
        if (block.isDone) {
            Box(
                Modifier
                    .padding(top = ((LINE_HEIGHT -20)/2).dp, end = 8.dp)
                    .size(20.dp)
                    .clickable {
                        onUpdate(block.copy(text = text, isDone = !block.isDone))
                    }, Alignment.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_todo_checked),
                    contentDescription = null
                )
            }
        } else {
            Box(
                Modifier
                    .padding(top = ((LINE_HEIGHT -20)/2).dp,end = 8.dp)
                    .size(20.dp)
                    .clickable {
                        onUpdate(block.copy(text = text, isDone = !block.isDone))
                    }, Alignment.Center
            ) {
                Image(
                    painter = painterResource(id = darkTheme.judge(R.drawable.ic_todo_unchecked_2, R.drawable.ic_edit_todo_unchecked)),
                    contentDescription = null
                )
            }
        }

        val doneColor = colorResource(darkTheme.judge(R.color.todo_block_color_sel,R.color.todo_block_color_nor))
        val normalColor = colorResource(darkTheme.judge(R.color.white, R.color.text_edit_color))
        var styleVersion by remember { mutableIntStateOf(0) }
        // 响应isDone变化的副作用
        LaunchedEffect(block.isDone,darkTheme,state.isBoldActive,state.isItalicActive,state.isUnderlineActive) {
            val processedText = processTextStyles(
                original = text.annotatedString,
                isDone = block.isDone,
                doneColor = doneColor,
                normalColor = normalColor,
                currentTextStyle = currentTextStyle
            )
            text = text.copy(annotatedString = processedText)
            styleVersion++  // 强制刷新文本样式
        }
        // 在组件内部维护当前块的选区状态
        var textLayoutResult by remember { mutableStateOf<TextLayoutResult?>(null) }
        val tip = stringResource(R.string.max_content_length)
        BasicTextField(
            value = text,
            onValueChange = { newValue ->
                // 先对当前内容输入后的长度校验，如果超出限制则不能添加进来
                val canAdd = viewModel.validateContentCanAdd(newValue.text)
                if(!canAdd){
                    Toast.makeText(context, tip, Toast.LENGTH_SHORT).show()
                    return@BasicTextField
                }

                // 检测是否新增了换行符
                val oldText = text.text
                val original = text.annotatedString
                val newText = newValue.text
                val selection = newValue.selection

                // 获取新增的换行符数量
                val newLineCount = newText.count { it == '\n' }
                val oldLineCount = oldText.count { it == '\n' }
                if (newLineCount > oldLineCount) {
                    // 将原文本中的样式转换到新的文本中
//                    val newAnnotated = transferStylesToNewText(original, oldText, newText)
//                    val processedValue = newValue.copy(annotatedString = newAnnotated)

                    // 创建去掉最后一个换行符的副本
                    val processedValue = if (newValue.text.endsWith("\n")) {
                        val newAnnotated = processAnnotatedAfterTruncation(newValue.annotatedString)
                        newValue.copy( // 仅设置 annotatedString
                            annotatedString = newAnnotated,
                            selection = TextRange(newAnnotated.text.length)
                        )
                    } else {
                        newValue
                    }
                    handleTodoEnterKey(viewModel, state, index, text, processedValue, block, onAddContent, onUpdate)
                }else{
                    val builder = AnnotatedString.Builder(newText)
                    val delta = newText.length - oldText.length
                    val isInsertion = delta > 0

                    // 处理样式继承和调整
                    text.annotatedString.spanStyles.forEach { span ->
                        val styleWithoutColor = span.item.copy(color = Color.Unspecified)
                        var newStart = span.start.coerceAtLeast(0)
                        var newEnd = span.end

                        if (delta != 0) {
                            if (isInsertion) {
                                // 插入处理
                                val insertPos = selection.start - delta
                                if (newStart >= insertPos) newStart += delta
                                if (newEnd >= insertPos) newEnd += delta
                            } else {
                                // 删除处理：计算删除范围
                                val (deleteStart, deleteEnd) = findDeletionRange(
                                    oldText,
                                    newText
                                ) ?: (0 to 0)
                                val deleteLength = deleteEnd - deleteStart

                                when {
                                    newStart >= deleteEnd -> newStart -= deleteLength
                                    newStart > deleteStart -> newStart = deleteStart
                                }
                                when {
                                    newEnd >= deleteEnd -> newEnd -= deleteLength
                                    newEnd > deleteStart -> newEnd = deleteStart
                                }
                            }
                        }

                        // 确保范围有效性
                        newStart = newStart.coerceIn(0, newText.length)
                        newEnd = newEnd.coerceIn(newStart, newText.length)

                        if (newStart < newEnd) {
                            builder.addStyle(styleWithoutColor, newStart, newEnd)
                        }
                    }

                    // 如果是插入操作，处理插入字符样式
                    if (isInsertion) {
                        val insertPos = selection.start - delta
                        val end = insertPos + delta
                        val baseStyle = currentTextStyle.toSpanStyle()
                        val finalStyle = if (block.isDone) {
                            baseStyle.copy(
                                color = doneColor,
                                textDecoration = baseStyle.textDecoration.addDecoration(TextDecoration.LineThrough)
                            )
                        } else {
                            baseStyle
                        }
                        builder.addStyle(finalStyle, insertPos, end)
                    }


                    text = newValue.copy(annotatedString = builder.toAnnotatedString())
                    onUpdate(block.copy(text = text))
                    if(oldText==newText){
                        onCursorChange(selection.start,true)
//                        viewModel.updateSelection(newValue.selection)
                    }else{
                        onCursorChange(selection.start,false)
                    }

                }
            },
            cursorBrush = SolidColor(CursorColor),
            onTextLayout = { textLayoutResult = it },
            modifier = Modifier
                .weight(1f)
                .onFocusChanged { focusState ->
                    if (focusState.isFocused) {
                        val cursorPosition = text.selection.start
                        onCursorChange(cursorPosition,false) // 光标位置
                        // 确保光标可见
                        coroutineScope.launch {
                            bringIntoViewRequester.bringIntoView()
                        }
                    }
                }
                .focusRequester(focusRequester) // 使用 block 自己的焦点控制器
                // .bringIntoViewRequester(bringIntoViewRequester) // 绑定 BringIntoViewRequester
                .onPreviewKeyEvent { keyEvent -> // 使用 onPreviewKeyEvent 捕获事件
                    if (keyEvent.type == KeyEventType.KeyDown) {
                        when (keyEvent.key) {
                            Key.Backspace -> {
                                val currentText = text.text
                                val cursorPos = text.selection.start
                                // 光标在块开头且不是第一个块
                                if (cursorPos == 0 && index > 0) {
                                    val prevIndex = index - 1
                                    val prevBlock = viewModel.state.value.contents[prevIndex]

                                    when (prevBlock) {
                                        is EditorContent.TextBlock -> {
                                            // 将光标后的内容合并到前面的文本块中
                                            todoMergeTextBlocks(viewModel,prevBlock, index, text.annotatedString,onDeleteBlock)
                                        }
                                        is EditorContent.TodoBlock -> {
                                            // 将光标后的内容合并到前面的待办事项中
                                            todoMergeTextWithTodoBlock(viewModel,prevBlock, index, text.annotatedString,onDeleteBlock)
                                        }

                                        is EditorContent.AudioBlock -> {
                                            /*if (text.text.isEmpty()){
                                                onDeleteBlock(index)
                                                onAddContent(EditorContent.TextBlock(text = TextFieldValue(""), ParagraphStyle.NONE), index)
                                            } else {
                                                isDeleteDialogVisible = true // 显示对话框
                                            }
                                            return@onPreviewKeyEvent true*/
                                        }
                                        is EditorContent.ImageBlock -> {
                                            if (text.text.isEmpty()){
                                                onDeleteBlock(index)
                                                onAddContent(EditorContent.TextBlock(text = TextFieldValue(""), ParagraphStyle.NONE), index)
                                            } else {
                                                isDeleteDialogVisible = true // 显示对话框
                                            }
                                            return@onPreviewKeyEvent true
                                        }

                                        is EditorContent.RichTextV2 ->{}
                                    }

                                    return@onPreviewKeyEvent true
                                }else if (currentText.isEmpty()) {
                                    onDeleteBlock(index) // 删除当前待办项
                                    viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(0,true))
                                    true // 拦截事件，停止冒泡
                                } else {
                                    false // 不删除，交给默认行为
                                }
                            }

                            Key.Enter -> {
                                handleTodoEnterKey(viewModel,state,index,oldText = TextFieldValue(""),text,block,onAddContent, onUpdate)
                                true
//                                // 不return true,让其自然流转
//                                return@onPreviewKeyEvent false
                            }

                            else -> {
                                false
                            }
                        }
                    } else {
                        false
                    }
                },
            textStyle =editorRichTextStyle.copy(
                color = if (block.isDone) doneColor else colorResource(darkTheme.judge(R.color.white, R.color.text_edit_color)),
                textDecoration = if (block.isDone) TextDecoration.LineThrough else TextDecoration.None,
                lineHeightStyle = LineHeightStyle(
                    alignment = LineHeightStyle.Alignment.Center,
                    trim = LineHeightStyle.Trim.None
                ),
                platformStyle = PlatformTextStyle(
                    includeFontPadding = false
                ),
            )
        )
    }

    // 对话框显示确认提示
    if (isDeleteDialogVisible) {
        val prevBlock = state.contents[index - 1]
        if(prevBlock is EditorContent.AudioBlock){
            DeleteAudioDialog(
                onDismiss = { isDeleteDialogVisible = false },
                onDelete = {
                    viewModel.handleIntent(RichTextIntent.RemoveContent(index - 1))
                    isDeleteDialogVisible = false
                }
            )
        }else if(prevBlock is EditorContent.ImageBlock){
            DeleteDataDialog(
                text = stringResource(R.string.dialog_title_delete_photo),
                onDelete = {
                    viewModel.handleIntent(RichTextIntent.RemoveContent(index - 1))
                    isDeleteDialogVisible = false // 隐藏对话框
                },
                onDismiss = {
                    isDeleteDialogVisible = false // 隐藏对话框
                }
            )
        }

    }
}

/**
 * 将原文本中的样式转换到新的文本中
 */
private fun transferStylesToNewText(
    original: AnnotatedString,
    oldText: String,
    newText: String
): AnnotatedString {
    // old -> new index映射
    val oldLen = oldText.length
    val newLen = newText.length
    val mapping = IntArray(oldLen + 1) { -1 }
    var oi = 0; var ni = 0
    while (oi < oldLen && ni < newLen) {
        if (oldText[oi] == newText[ni]) {
            mapping[oi] = ni
            oi++; ni++
        } else if (newText[ni] == '\n' && (oi == 0 || oldText.getOrNull(oi-1) == newText.getOrNull(ni-1))) {
            ni++
        } else {
            mapping[oi] = ni
            oi++; ni++
        }
    }
    mapping[oi] = ni

    // 遍历span, 只迁移 newText 上有实际内容的区间
    val usedSpans = mutableSetOf<Pair<Int,Int>>()
    return buildAnnotatedString {
        append(newText)
        original.spanStyles.forEach { span ->
            val start = mapping.getOrNull(span.start)?.takeIf { it != -1 } ?: 0
            val end = mapping.getOrNull(span.end)?.takeIf { it != -1 } ?: newLen
            // 防止冗余：只有首次区间才迁移
            if (start < end && usedSpans.add(start to end)) {
                addStyle(span.item, start, end)
            }
        }
    }
}

/**
 * 将光标后的内容合并到前一个文本块中
 */
private fun todoMergeTextBlocks(
    viewModel: RichTextViewModel,
    prevBlock: EditorContent.TextBlock,
    index: Int,
    currentAnnotatedString: AnnotatedString,
    onDeleteBlock: (Int) -> Unit
) {
    val prevAnnotatedString = prevBlock.text.annotatedString

    // 合并 AnnotatedString，保留样式，同时去除待办事项的删除线样式
    val mergedAnnotatedString = buildAnnotatedString {
        append(prevAnnotatedString)
        currentAnnotatedString.spanStyles.forEach { span ->
            val spanStyle = if (span.item.textDecoration?.contains(TextDecoration.LineThrough) == true) {
                // 如果存在删除线样式，移除它
                span.item.copy(
                    textDecoration = span.item.textDecoration?.removeDecoration(TextDecoration.LineThrough)
                )
            } else {
                span.item
            }
            addStyle(spanStyle, span.start + prevAnnotatedString.length, span.end + prevAnnotatedString.length)
        }
        append(currentAnnotatedString.text)
    }

    val newText = prevBlock.text.copy(annotatedString = mergedAnnotatedString)

    // 更新合并后的文本块
    viewModel.handleIntent(
        RichTextIntent.UpdateContent(
            index - 1,
            prevBlock.copy(text = newText)
        )
    )

    // 删除当前空的块
    onDeleteBlock(index)

    // 更新焦点和光标位置
    viewModel.handleIntent(
        RichTextIntent.UpdateFocusedIndex(index - 1)
    )
    viewModel.handleIntent(
        RichTextIntent.UpdateCursorPosition(prevBlock.text.text.length,true)
    )
}

/**
 * 将光标后的内容合并到前一个待办事项中
 */
private fun todoMergeTextWithTodoBlock(
    viewModel: RichTextViewModel,
    prevBlock: EditorContent.TodoBlock,
    index: Int,
    currentAnnotatedString: AnnotatedString,
    onDeleteBlock: (Int) -> Unit
) {
    val prevAnnotatedString = prevBlock.text.annotatedString

    // 合并 AnnotatedString，保留样式
    val mergedAnnotatedString = buildAnnotatedString {
        append(prevAnnotatedString)
        append(currentAnnotatedString)
    }

    val newText = prevBlock.text.copy(annotatedString = mergedAnnotatedString)

    // 更新待办事项
    viewModel.handleIntent(
        RichTextIntent.UpdateContent(
            index - 1,
            prevBlock.copy(text = newText)
        )
    )

    // 删除当前空的块
    onDeleteBlock(index)

    // 更新焦点和光标位置
    viewModel.handleIntent(
        RichTextIntent.UpdateFocusedIndex(index - 1)
    )
    viewModel.handleIntent(
        RichTextIntent.UpdateCursorPosition(prevBlock.text.text.length,true)
    )
}

// 删除范围计算
private fun findDeletionRange(old: String, new: String): Pair<Int, Int>? {
    if (old.length <= new.length) return null
    var start = 0
    while (start < new.length && old[start] == new[start]) start++
    var end = old.lastIndex
    var newEnd = new.lastIndex
    while (end >= 0 && newEnd >= 0 && old[end] == new[newEnd]) {
        end--
        newEnd--
    }
    return start to (end + 1)
}

private fun processAnnotatedAfterTruncation(source: AnnotatedString): AnnotatedString {
    val newLength = source.text.length - 1 // 截断后的新长度
    return buildAnnotatedString {
        append(source.text.take(newLength)) // 截断最后一个字符
        source.spanStyles.forEach { span ->
            // 将样式范围限制在新长度内
            val safeStart = minOf(span.start, newLength)
            val safeEnd = minOf(span.end, newLength)
            if (safeStart < safeEnd) {
                addStyle(span.item, safeStart, safeEnd)
            }
        }
    }
}

/**
 * 处理回车换行
 */
private fun handleTodoEnterKey(
    viewModel: RichTextViewModel,
    state: RichTextState,
    index: Int,
    oldText: TextFieldValue,
    newText: TextFieldValue,
    block: EditorContent.TodoBlock,
    onAddContent: (EditorContent, Int?) -> Unit,
    onUpdate: (EditorContent.TodoBlock) -> Unit
){
    val currentText = newText.text.trim('\n')
    val isEmptyBlock = currentText.isEmpty()
    // 处理换行
    val originalAnnotated = newText.annotatedString
    val selectionStart = newText.selection.start

    // 分割带样式的文本
    val (beforeAnnotated, afterAnnotated) = splitAnnotatedString(
        originalAnnotated,
        selectionStart
    )

    // 检查是否需要将待办事项转换成空文本块
    val shouldClearStyle = isEmptyBlock && oldText.text.isEmpty()
    if(shouldClearStyle){
        // 转换为普通文本块
        var text = TextFieldValue("")
        val tmpContent = state.contents[state.focusedIndex]
        if (tmpContent is EditorContent.TodoBlock) {
            text = tmpContent.text.copy(
                selection = TextRange(state.cursorPosition) // 恢复光标位置
            )
        }
        viewModel.handleIntent(
            RichTextIntent.UpdateContent(
                state.focusedIndex,
                EditorContent.TextBlock(text)
            )
        )
        viewModel.handleIntent(
            RichTextIntent.UpdateCursorPosition(0,true)
        )
        viewModel.handleIntent(RichTextIntent.UpdateMenuType(MenuBar.NONE))
    }else{
        // 更新当前块
        val updatedBlock = if (shouldClearStyle) {
            block.copy(text = TextFieldValue(""))
        } else {
            block.copy(
                text = TextFieldValue(
                    annotatedString = beforeAnnotated,
                    selection = TextRange(beforeAnnotated.length)
                )
            )
        }
        onUpdate(updatedBlock)
    }
//    if (originalAnnotated.text != beforeAnnotated.text || oldText.text.isEmpty()) {
//        // 更新当前块
//        val updatedBlock = if (shouldClearStyle) {
//            block.copy(
//                text = TextFieldValue(""),            )
//        } else {
//            block.copy(
//                text = TextFieldValue(
//                    annotatedString = beforeAnnotated,
//                    selection = TextRange(beforeAnnotated.length)
//                )
//            )
//        }
//        onUpdate(updatedBlock)
//    }

    // 创建新块时的处理
    if (!shouldClearStyle) {
        val newBlock = when {
            isEmptyBlock -> {
                EditorContent.TodoBlock(
                    text = TextFieldValue("").copy(
                        selection = TextRange(0) // 明确设置光标位置
                    ),
                    isDone = false
                )
            }
            else -> {
                EditorContent.TodoBlock(
                    text = TextFieldValue(
                        annotatedString = afterAnnotated,
                        selection = TextRange(0)
                    ),
                    isDone = false
                )
            }
        }
        onAddContent(newBlock, index + 1) // 添加新内容块
        viewModel.handleIntent(RichTextIntent.UpdateCursorPosition(0))
    }
}

// 添加分割带样式的文本的函数
private fun splitAnnotatedString(
    source: AnnotatedString,
    splitPosition: Int
): Pair<AnnotatedString, AnnotatedString> {
    // 调整分割点：如果分割点前是换行符，则往前移动一位
    val adjustedSplit = when {
        splitPosition > 0 && source.text[splitPosition - 1] == '\n' -> splitPosition - 1
        else -> splitPosition
    }

    val before = buildAnnotatedString {
        val charSeq = source.subSequence(0, adjustedSplit)
        val text = if (charSeq.isNotEmpty() && charSeq[charSeq.length - 1] == '\n') {
            charSeq.substring(0, charSeq.length - 1)
        } else {
            charSeq.toString()
        }
        append(AnnotatedString(text)) // 将 String 转换为 AnnotatedString
        source.spanStyles.forEach { span ->
            val start = span.start.coerceAtMost(adjustedSplit)
            val end = span.end.coerceAtMost(adjustedSplit)
            if (start < end) {
                addStyle(span.item, start, end)
            }
        }
    }

    // 计算 after 的起始位置（跳过可能存在的换行符）
    val afterStart = adjustedSplit + if (adjustedSplit < source.length && source[adjustedSplit] == '\n') 1 else 0

    val after = buildAnnotatedString {
        append(source.subSequence(afterStart, source.length))
        source.spanStyles.forEach { span ->
            val originalStart = span.start
            val originalEnd = span.end
            val safeStart = minOf(originalStart, originalEnd)
            val safeEnd = maxOf(originalStart, originalEnd)

            val start = (safeStart - afterStart).coerceAtLeast(0)
            val end = (safeEnd - afterStart).coerceAtLeast(0)
            val finalStart = minOf(start, end)
            val finalEnd = maxOf(start, end)
            if (finalStart < finalEnd) {
                addStyle(span.item, finalStart, finalEnd)
            }
        }
    }

    return Pair(before, after)
}


// 统一文本处理函数
private fun processTextStyles(
    original: AnnotatedString,
    isDone: Boolean,
    doneColor: Color,
    normalColor: Color,
    currentTextStyle: TextStyle
): AnnotatedString {
    val builder = AnnotatedString.Builder(original.text)

    // 处理现有Span样式（保留原有样式基础修改颜色和删除线）
    original.spanStyles.forEach { span ->
        val originalStyle = span.item
        val newColor = if (isDone) doneColor else normalColor
        val newDecoration = if (isDone) {
            originalStyle.textDecoration.addDecoration(TextDecoration.LineThrough)
        } else {
            originalStyle.textDecoration.removeDecoration(TextDecoration.LineThrough)
        }

        builder.addStyle(
            originalStyle.copy(
                color = newColor,
                textDecoration = newDecoration ?: TextDecoration.None
            ),
            span.start,
            span.end
        )
    }

    // 处理默认样式（仅影响没有自定义样式的文本）
    val baseSpanStyle = currentTextStyle.toSpanStyle().let {
        it.copy(
            color = if (isDone) doneColor else normalColor,
            textDecoration = if (isDone) {
                it.textDecoration.addDecoration(TextDecoration.LineThrough)
            } else {
                it.textDecoration.removeDecoration(TextDecoration.LineThrough)
            }
        )
    }

    // 仅在没有span覆盖的区域应用基础样式
    val coveredRanges = original.spanStyles.map { it.start..it.end }
    var lastPos = 0
    for (range in coveredRanges.sortedBy { it.start }) {
        if (lastPos < range.first) {
            builder.addStyle(baseSpanStyle, lastPos, range.start)
        }
        lastPos = maxOf(lastPos, range.last)
    }
    if (lastPos < original.text.length) {
        builder.addStyle(baseSpanStyle, lastPos, original.text.length)
    }

    return builder.toAnnotatedString()
}

// 扩展函数
private fun TextDecoration?.addDecoration(newDecoration: TextDecoration): TextDecoration {
    return when {
        this == null -> newDecoration
        this.contains(newDecoration) -> this
        else -> TextDecoration.combine(listOf(this, newDecoration))
    }
}

// 扩展函数，移除指定的文本装饰
fun TextDecoration?.removeDecoration(toRemove: TextDecoration): TextDecoration? {
    // 处理this为null的情况
    val currentDecorations = this?.decompose() ?: emptyList()
    val decorations = currentDecorations.filterNot { it == toRemove }
    return when {
        decorations.isEmpty() -> null
        decorations.size == 1 -> decorations[0]
        else -> TextDecoration.combine(decorations)
    }
}


private fun TextDecoration?.contains(other: TextDecoration): Boolean {
    return when (this) {
        null -> false
        other -> true
        else -> decompose().any { it == other }
    }
}

private fun TextDecoration.decompose(): List<TextDecoration> {
    return when (this) {
        TextDecoration.LineThrough -> listOf(TextDecoration.LineThrough)
        TextDecoration.Underline -> listOf(TextDecoration.Underline)
        else -> {
            // 处理组合装饰的分解
            if (this == TextDecoration.combine(listOf(TextDecoration.LineThrough, TextDecoration.Underline))) {
                listOf(TextDecoration.LineThrough, TextDecoration.Underline)
            } else {
                listOf(this)
            }
        }
    }
}