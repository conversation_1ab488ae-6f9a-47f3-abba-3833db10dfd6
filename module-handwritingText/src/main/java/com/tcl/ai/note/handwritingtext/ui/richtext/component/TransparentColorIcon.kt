package com.tcl.ai.note.handwritingtext.ui.richtext.component

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

/**
 * 透明色图标组件
 * 绘制一个棋盘格图案来表示透明
 *
 * @param modifier 修饰符
 * @param cellSize 棋盘格单元格大小
 * @param lightColor 浅色
 * @param darkColor 深色
 */
@Composable
fun TransparentColorIcon(
    modifier: Modifier = Modifier,
    cellSize: Float = 3f,
    lightColor: Color = Color.White,
    darkColor: Color = Color.LightGray.copy(alpha = 0.3f)
) {
    Canvas(modifier = modifier.size(18.dp)) {
        val canvasWidth = size.width
        val canvasHeight = size.height
        
        val cellSizePx = canvasWidth / cellSize
        
        var isLightCell = true
        
        for (y in 0 until cellSize.toInt()) {
            for (x in 0 until cellSize.toInt()) {
                val cellColor = if (isLightCell) lightColor else darkColor
                
                drawRect(
                    color = cellColor,
                    topLeft = Offset(x * cellSizePx, y * cellSizePx),
                    size = Size(cellSizePx, cellSizePx)
                )
                
                isLightCell = !isLightCell
            }
            
            // 确保新行的颜色交替模式正确
            if (cellSize.toInt() % 2 == 0) {
                isLightCell = !isLightCell
            }
        }
    }
} 