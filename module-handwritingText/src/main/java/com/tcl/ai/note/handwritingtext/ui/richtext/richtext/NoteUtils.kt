package com.tcl.ai.note.handwritingtext.ui.richtext.richtext

import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle

class NoteUtils {
    /**
     * Note数据是否只有音频
     */
    fun contentOnlyVoice(item: Note):<PERSON><PERSON><PERSON>{
        return item.firstPicture.isNullOrEmpty() && item.summary.isNullOrEmpty() && item.hasAudio == true
    }


    /**
     * 获取当前块在有序列表中的序号
     * @param index 内容块索引
     * @return 序号
     */
    fun getOrderNumberForBlock(index: Int,contents: List<EditorContent>): Int {
        var count = 1
        for (i in 0 until index) {
            val item = contents[i]
            if (item is EditorContent.TextBlock && item.paragraphStyle == ParagraphStyle.NUMBERED) {
                count++
            } else {
                // 遇到非有序列表项重置计数
                if (i < index) count = 1
            }
        }
        return count
    }
}