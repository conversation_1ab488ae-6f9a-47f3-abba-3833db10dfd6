package com.tcl.ai.note.handwritingtext.ui.richtext.richtext

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.semantics.invisibleToUser
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle
import com.tcl.ai.note.theme.editorRichTextStyle
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.px2dp

/**
 * 显示文本块
 */
@Composable
internal fun ShareTextBlock(item: EditorContent.TextBlock, index: Int, contents: List<EditorContent>){
    var text by remember(item) {
        mutableStateOf(
            processInitialTextStyle(item.text)
        )
    }
    Box(
        modifier = Modifier
            .width(GlobalContext.screenWidth.px2dp.dp)
            .padding(start = 24.dp, end = 24.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            // 段落符号
            when (item.paragraphStyle) {
                ParagraphStyle.NUMBERED -> {
                    // 动态计算序号（示例简化，实际需根据上下文计算）
                    val itemNumber = NoteUtils().getOrderNumberForBlock(index,contents)
                    Text(
                        text = "$itemNumber. ",
                        modifier = Modifier.padding(end = 4.dp),
                        style =  editorRichTextStyle.copy(
                            color = colorResource(R.color.text_title),
                            lineHeightStyle = LineHeightStyle(
                                alignment = LineHeightStyle.Alignment.Center,
                                trim = LineHeightStyle.Trim.None
                            )
                        )
                    )
                }

                ParagraphStyle.BULLETED -> {
                    Text(
                        text = "• ",
                        modifier = Modifier.padding( end = 4.dp),
                        style =  editorRichTextStyle.copy(
                            color = colorResource(R.color.text_title),
                            lineHeightStyle = LineHeightStyle(
                                alignment = LineHeightStyle.Alignment.Center,
                                trim = LineHeightStyle.Trim.None
                            ),
                        )
                    )
                }

                else -> {}
            }

            BasicTextField(
                value = text,
                onValueChange = {},
                readOnly = true,        // 禁止编辑
                enabled = false,        // 禁用交互
                modifier = Modifier
                    .background(Color.Transparent)
                    .semantics {
                        if (text.text.isEmpty()) {
                            invisibleToUser()
                        }
                    },
                textStyle = editorRichTextStyle.copy(
                    color = R.color.text_edit_color.colorRes(),
                    lineHeightStyle = LineHeightStyle(
                        alignment = LineHeightStyle.Alignment.Center,
                        trim = LineHeightStyle.Trim.None
                    ),
                    platformStyle = PlatformTextStyle(
                        includeFontPadding = false
                    ),
                )
            )
        }
    }
}


// 文本初始化处理函数
private fun processInitialTextStyle(textValue: TextFieldValue): TextFieldValue {
    val builder = AnnotatedString.Builder(textValue.text)
    textValue.annotatedString.spanStyles.forEach { span ->
        // 过滤颜色属性
        val filteredStyle = span.item.copy(color = Color.Unspecified)
        builder.addStyle(filteredStyle, span.start.coerceAtLeast(0), span.end)
    }
    // 保留原始选择状态
    return textValue.copy(annotatedString = builder.toAnnotatedString())
}