package com.tcl.ai.note.handwritingtext.ui.richtext.richtext

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.theme.LINE_HEIGHT
import com.tcl.ai.note.theme.editorRichTextStyle
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.px2dp

/**
 * 显示待办事项
 */
@SuppressLint("RememberReturnType")
@Composable
internal fun ShareTodoBlock(item: EditorContent.TodoBlock) {
    val textColor = if (item.isDone) {
        colorResource(R.color.btn_new_category_create)
    } else {
        colorResource(R.color.text_title)
    }

    val styledText = remember(item.text) {
        buildAnnotatedString {
            // 保留原有样式
            append(item.text.annotatedString)

            // 强制覆盖文本颜色
            addStyle(
                style = SpanStyle(color = textColor),
                start = 0,
                end = item.text.annotatedString.length
            )

            // 根据完成状态添加删除线
            if (item.isDone) {
                addStyle(
                    style = SpanStyle(textDecoration = TextDecoration.LineThrough),
                    start = 0,
                    end = item.text.annotatedString.length
                )
            }
        }
    }

    Box(
        modifier = Modifier
    ) {
        Row(
            modifier = Modifier
                .padding(start = 20.dp, end = 20.dp)
                .width(GlobalContext.screenWidth.px2dp.dp)
        ) {
            if (item.isDone) {
                Box(
                    Modifier
                        .padding(top = ((LINE_HEIGHT -20)/2).dp, end = 8.dp)
                        .size(20.dp)
                        .fillMaxHeight()
                    , Alignment.Center
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_todo_checked),
                        contentDescription = null
                    )
                }
            } else {
                Box(
                    Modifier
                        .padding(top = ((LINE_HEIGHT -20)/2).dp, end = 8.dp)
                        .size(20.dp)
                        .fillMaxHeight()
                    , Alignment.Center
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_todo_unchecked),
                        contentDescription = null
                    )
                }
            }

            Text(
                text = styledText,
                style = editorRichTextStyle.copy(
                    lineHeightStyle = LineHeightStyle(
                        alignment = LineHeightStyle.Alignment.Center,
                        trim = LineHeightStyle.Trim.None
                    ),
                    platformStyle = PlatformTextStyle(includeFontPadding = false)
                ),
                modifier = Modifier.weight(1f)
            )
        }
    }
}