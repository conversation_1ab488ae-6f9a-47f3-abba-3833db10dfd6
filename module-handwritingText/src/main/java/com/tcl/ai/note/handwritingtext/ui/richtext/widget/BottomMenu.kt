package com.tcl.ai.note.handwritingtext.ui.richtext.widget

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.AppBarDefaults
import androidx.compose.material.BottomAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.controller.BottomRoute
import com.tcl.ai.note.handwritingtext.bean.BrushMenu
import com.tcl.ai.note.handwritingtext.bean.DoodlePen
import com.tcl.ai.note.handwritingtext.bean.DrawMode
import com.tcl.ai.note.handwritingtext.bean.PictureSource
import com.tcl.ai.note.handwritingtext.bean.resId
import com.tcl.ai.note.handwritingtext.bean.*
import com.tcl.ai.note.handwritingtext.ui.richtext.component.ColorSelector

import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphType
import com.tcl.ai.note.handwritingtext.intent.RichTextIntent
import com.tcl.ai.note.handwritingtext.state.DrawBoardIntent
import com.tcl.ai.note.handwritingtext.ui.richtext.component.BrushControl
import com.tcl.ai.note.handwritingtext.ui.richtext.component.EraserWidthSlider
import com.tcl.ai.note.handwritingtext.ui.richtext.component.SkinControl
import com.tcl.ai.note.handwritingtext.utils.borderCircle
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.handwritingtext.vm.*
import com.tcl.ai.note.handwritingtext.vm.BrushSliderModel
import com.tcl.ai.note.handwritingtext.vm.ColorSelectorViewModel
import com.tcl.ai.note.handwritingtext.vm.DrawBoardViewModel
import com.tcl.ai.note.handwritingtext.vm.EraserSliderModel
import com.tcl.ai.note.handwritingtext.vm.RichTextViewModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.startHandwritingToText
import com.tcl.ai.note.utils.toArgbLong
import com.tcl.ai.note.utils.toComposeColor
import com.tcl.ai.note.voicetotext.view.widget.AudioView
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.widget.HoverProofIconButton
import com.tcl.ai.note.widget.IconThemeSwitcher

/**
 * 底部功能菜单
 */
@Deprecated("仅一期使用")
@SuppressLint("UnrememberedMutableState")
@Composable
fun BottomMenu(
    viewModel: RichTextViewModel = hiltViewModel(),
    drawBoardViewModel: DrawBoardViewModel = hiltViewModel(),
    colorSelectorViewModel: ColorSelectorViewModel = hiltViewModel(),
    brushSliderModel: BrushSliderModel = hiltViewModel(),
    beautifyViewModel: DrawingBeautifyViewModel = hiltViewModel(),
    eraserSliderModel: EraserSliderModel = hiltViewModel(),
    onBottomClick: (BottomRoute) -> Unit,
    onTodo: (MenuBar) -> Unit,
    onPickImage: () -> Unit,
    onTakePhoto: () -> Unit,
    onAudioClick: (String, Boolean) -> Unit,
    menuBarViewModel: MenuBarViewModel = hiltViewModel(),
) {
    val scrollState = rememberScrollState()
    val state by viewModel.state.collectAsState()
    // 当前点击的菜单栏类型
    var brushMenuType = state.brushMenuType

    // 当前点击的BottomMenu类型
    var bottomMenuType = state.bottomMenuType
    val isShowBottomAIPop = viewModel.isShowBottomAIPop.value

    // 是否显示图片选择弹框
    var showImageAddDialog by remember { mutableStateOf(false) }

    val selectedColor by colorSelectorViewModel.selectedColor
    val selDoodlePen  by brushSliderModel.lastDoodlePen

    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    val dimens = getGlobalDimens()


    val isInitCache =colorSelectorViewModel.isInit && brushSliderModel.isInit && eraserSliderModel.isInitCache
    //获取完持久层数据后，每次重组，进行画笔状态恢复
    LaunchedEffect(selDoodlePen,brushMenuType,isInitCache) {
        if(isInitCache){
            colorSelectorViewModel.initSliderPosition(selDoodlePen)
            when(brushMenuType){
                BrushMenu.ERASER ->eraserSliderModel.recoverStatus(drawBoardViewModel)
                BrushMenu.PEN -> {
                    brushSliderModel.recoverStatus(drawBoardViewModel,selDoodlePen)
                }
                else ->{}
            }
        }
    }

    LaunchedEffect(isInitCache) {
        if(!isInitCache){
            colorSelectorViewModel.loadCache()
            brushSliderModel.loadCache()
            eraserSliderModel.loadCache()
            viewModel.lastCacheBrushMenuType()
        }
    }


    Column(modifier = Modifier.imePadding()) {
        if (bottomMenuType == MenuBar.BRUSH && menuBarViewModel.showExpandButton) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.End,
                modifier = Modifier
                    .height(dimens.menuBarHeight)
                    .fillMaxWidth()
                    // 对齐开关菜单时的按钮位置
                    .padding(horizontal = dimens.barHorizontalPadding - 5.dp),
            ) {
                EditToggle {
                    menuBarViewModel.showExpandButton = false
                }
            }
        }

        // 画笔功能菜单
        if (bottomMenuType == MenuBar.BRUSH && !menuBarViewModel.showExpandButton) {
            when (brushMenuType) {
                BrushMenu.PEN -> {
                    // 画笔线条粗细控制器
                    if(drawBoardViewModel.isShowPenDrawer){
                        BrushControl(selDoodlePen = selDoodlePen){ doodlePen ->
                            with(drawBoardViewModel){
                                sendIntent(
                                    DrawBoardIntent.ChangeStrokeStyle(
                                        strokeStyle.copy(
                                            width = brushSliderModel.getCurPenWidth(doodlePen),
                                            color = selDoodlePen.color,
                                            drawMode =  DrawMode.PEN,
                                            doodlePen = doodlePen)
                                    )

                                )
                            }
                        }
                    }

                }
                BrushMenu.ERASER ->{
                    if(drawBoardViewModel.isShowEraserDrawer){
                        EraserWidthSlider()
                    }
                }
                BrushMenu.HANDWRITING_BEAUTIFICATION -> {
                    drawBoardViewModel.sendIntent(
                        DrawBoardIntent.ChangeStrokeStyle(
                            StrokeStyle(
                                color = selDoodlePen.color,
                                drawMode = DrawMode.BEAUTIFICATION,
                                doodlePen = DoodlePen.Ballpen,
                            )
                        )
                    )
                }
                BrushMenu.COLOR_SELECTOR -> {
                    if(drawBoardViewModel.isShowColorDrawer){
                        ColorSelector(drawBoardViewModel) {
                            selDoodlePen.color =it.toArgbLong()

                        }
                    }else{
                        brushMenuType =  BrushMenu.PEN
                        viewModel.handleIntent(RichTextIntent.UpdateBrushMenuType(brushMenuType))
                    }

                }
            }


            // 画笔菜单栏
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(dimens.menuBarHeight)
                    .background(color = TclTheme.colorScheme.reWriteExpandBg)
                    .padding(horizontal = dimens.barHorizontalPadding),
                horizontalArrangement = Arrangement.SpaceBetween, // 设置间距相等
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 笔迹美化, 平板暂时不支持
                if (!isTablet) {
                    IconThemeSwitcher(
                        btnSize = dimens.btnSize,
                        iconSize = dimens.iconSize,
                        painter =painterResource((brushMenuType == BrushMenu.HANDWRITING_BEAUTIFICATION).judge(
                            R.drawable.ic_edit_fluorescence_pen_selected,
                            R.drawable.ic_edit_fluorescence_pen
                        )),
                        contentDescription = R.string.edit_bottom_tool_beautify.stringRes(),
                        onClick = {
                            brushMenuType= BrushMenu.HANDWRITING_BEAUTIFICATION
                            drawBoardViewModel.goneDrawer()
                            viewModel.handleIntent(RichTextIntent.UpdateBrushMenuType(brushMenuType))
                        }
                    )
                }
                // 钢笔
                IconThemeSwitcher(
                    btnSize = dimens.btnSize,
                    iconSize = dimens.iconSize,
                    painter = painterResource(id =selDoodlePen.resId(brushMenuType == BrushMenu.PEN)),
                    contentDescription = R.string.edit_bottom_tool_brush.stringRes(),
                    onClick = {
                        brushMenuType =  BrushMenu.PEN
                        drawBoardViewModel.transformPenDrawerVisible()
                        viewModel.handleIntent(RichTextIntent.UpdateBrushMenuType(brushMenuType))
                    }
                )

                // 颜色选择器
                HoverProofIconButton (
                    modifier = Modifier
                        .clearAndSetSemantics {
                            this.contentDescription =
                                context.getString(R.string.edit_bottom_menu_color_select)
                            this.role = Role.Button
                        }
                        .size(dimens.btnSize),
                    onClick = {
                        brushMenuType = BrushMenu.COLOR_SELECTOR
                        drawBoardViewModel.transformColorDrawerDrawerVisible()
                        viewModel.handleIntent(
                            RichTextIntent.UpdateBrushMenuType(
                                brushMenuType
                            )
                        )
                    }
                ){
                    Box(
                        modifier = Modifier
                            .size(20.dp)
                            .background(selectedColor, shape = CircleShape)
                            .then(
                                (selectedColor == R.color.edit_palette_color_WHITE.colorRes())
                                    .judge(Modifier.borderCircle(), Modifier)
                            )
                    )
                }

                // 橡皮擦
                IconThemeSwitcher(
                    btnSize = dimens.btnSize,
                    iconSize = dimens.iconSize,
                    painter = painterResource((brushMenuType == BrushMenu.ERASER).judge(
                        R.drawable.ic_edit_eraser_sel,
                        R.drawable.ic_edit_eraser
                    )),
                    contentDescription = R.string.edit_bottom_tool_eraser.stringRes(),
                    onClick = {
                        brushMenuType = BrushMenu.ERASER
                        drawBoardViewModel.transformEraserDrawerVisible()
                        viewModel.handleIntent(RichTextIntent.UpdateBrushMenuType(brushMenuType))
                    }
                )

                // 手写转文字
                IconThemeSwitcher(
                    modifier = Modifier
                        .alpha(if (drawBoardViewModel.isStrokesEmpty) 0.5f else 1f),
                    btnSize = dimens.btnSize,
                    iconSize = dimens.iconSize,
                    enabled = !drawBoardViewModel.isStrokesEmpty,
                    painter = painterResource(id = R.drawable.ic_edit_convert_text),
                    contentDescription = R.string.edit_bottom_tool_convert_text.stringRes(),
                    onClick = {
                        viewModel.startNavigation()
                        context.startHandwritingToText(drawBoardViewModel.noteId)
                    }
                )

                // 画笔工具
                IconThemeSwitcher(
                    btnSize = dimens.btnSize,
                    iconSize = dimens.iconSize,
                    painter = painterResource(id = R.drawable.ic_edit_fold),
                    contentDescription = menuBarViewModel.showExpandButton.judge(
                        R.string.edit_bottom_tool_expand,
                        R.string.edit_bottom_menu_fold
                    ).stringRes(),
                    onClick = {
                        menuBarViewModel.showExpandButton = true
                    }
                )
               // HorizontalLine()
            }
        }

        // 字体样式菜单栏
        if (bottomMenuType == MenuBar.KEYBOARD) {
            BottomAppBar(
                backgroundColor = colorResource(R.color.bg_dialog),
                contentColor = Color.Black,
                modifier = Modifier.fillMaxWidth()
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .horizontalScroll(scrollState)
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center

                ) {
                    Row(
                        modifier = Modifier
                            .width(113.dp)
                            .height(39.dp)
                            .border(
                                width = 1.dp, // 边框宽度
                                color = colorResource(R.color.edit_border_menu), // 边框颜色
                                shape = RoundedCornerShape(5.dp) // 圆角大小
                            )
                            ,
                        verticalAlignment = Alignment.CenterVertically
                    )
                    {
                        // 无序列表
                        HoverProofIconButton(modifier = Modifier
                            .width(56.dp)
                            .background(
                                if (state.currentParagraphType == ParagraphType.BULLETED_LIST) {
                                    colorResource(R.color.text_category_list_selected)
                                } else {
                                    colorResource(R.color.bg_dialog)
                                }, shape = RoundedCornerShape(topStart = 5.dp, bottomStart = 5.dp)
                            )
                            .fillMaxHeight(), onClick = {
                            val newStyle = when (state.currentParagraphStyle) {
                                ParagraphStyle.BULLETED ->
                                    ParagraphStyle.NONE

                                else ->
                                    ParagraphStyle.BULLETED
                            }
                            val newType = when(state.currentParagraphType){
                                ParagraphType.BULLETED_LIST ->
                                    ParagraphType.TEXT
                                else -> ParagraphType.BULLETED_LIST
                            }
                            viewModel.handleIntent(RichTextIntent.UpdateCurrentParagraphStyle(newStyle))
                            viewModel.handleIntent(RichTextIntent.UpdateCurrentParagraphType(newType))
                            state.focusedIndex.let { index ->
                                viewModel.handleIntent(
                                    RichTextIntent.ToggleParagraphStyle(index, ParagraphStyle.BULLETED)
                                )
                            }
                        }) {
                            Image(
                                painter = painterResource(
                                    if (state.currentParagraphType == ParagraphType.BULLETED_LIST)
                                        R.drawable.ic_edit_list_bulleted_selected
                                    else
                                        R.drawable.ic_edit_list_bulleted
                                ),
                                contentDescription = stringResource(R.string.bulleted_list)
                            )
                        }
                        // 有序列表
                        HoverProofIconButton(modifier = Modifier
                            .width(56.dp)
                            .background(
                                if (state.currentParagraphType == ParagraphType.NUMBERED_LIST) {
                                    colorResource(R.color.text_category_list_selected)
                                } else {
                                    colorResource(R.color.bg_dialog)
                                },
                                shape = RoundedCornerShape(topEnd = 5.dp, bottomEnd = 5.dp)
                            )
                            .fillMaxHeight(), onClick = {
                            val newStyle = when (state.currentParagraphStyle) {
                                ParagraphStyle.NUMBERED ->
                                    ParagraphStyle.NONE

                                else ->
                                    ParagraphStyle.NUMBERED
                            }
                            val newType = when(state.currentParagraphType){
                                ParagraphType.NUMBERED_LIST ->
                                    ParagraphType.TEXT
                                else -> ParagraphType.NUMBERED_LIST
                            }
                            viewModel.handleIntent(RichTextIntent.UpdateCurrentParagraphStyle(newStyle))
                            viewModel.handleIntent(RichTextIntent.UpdateCurrentParagraphType(newType))
                            state.focusedIndex.let { index ->
                                viewModel.handleIntent(RichTextIntent.ToggleParagraphStyle(index, ParagraphStyle.NUMBERED))
                            }

                        }) {
                            Image(
                                painter = painterResource(
                                    if (state.currentParagraphType == ParagraphType.NUMBERED_LIST)
                                        R.drawable.ic_edit_list_numbered_selected
                                    else
                                        R.drawable.ic_edit_list_numbered
                                ),
                                contentDescription = stringResource(R.string.numbered_list)
                            )
                        }
                    }
                    Spacer(modifier = Modifier.weight(1f))

                    Row {
                        Spacer(modifier = Modifier.width(3.dp))

                        FormatButton(
                            painter =  painterResource(id = if (state.isBoldActive) R.drawable.ic_edit_bold_selected else R.drawable.ic_edit_bold),
                            contentDescription = stringResource(R.string.font_bold)
                        ) {
                            viewModel.handleIntent(RichTextIntent.UpdateBoldActive(!state.isBoldActive))
                            viewModel.toggleBold()
                            viewModel.applyCurrentStyleToSelection()
                        }

                        Spacer(modifier = Modifier.width(3.dp))
                        // 下划线
                        FormatButton(
                            painter =  painterResource(id = if (state.isUnderlineActive) R.drawable.ic_edit_underline_selected else R.drawable.ic_edit_underline),
                            contentDescription = stringResource(R.string.font_underline)
                        ) {
                            viewModel.handleIntent(RichTextIntent.UpdateUnderlineActive(!state.isUnderlineActive))
                            viewModel.toggleUnderline()
                            viewModel.applyCurrentStyleToSelection()
                        }

                        Spacer(modifier = Modifier.width(3.dp))
                        // 斜体
                        FormatButton(
                            painter = painterResource(id = if (state.isItalicActive) R.drawable.ic_edit_italic_selected else R.drawable.ic_edit_italic),
                            contentDescription = stringResource(R.string.font_italic)
                        ){
                            viewModel.handleIntent(RichTextIntent.UpdateItalicActive(!state.isItalicActive))
                            viewModel.toggleItalic()
                            viewModel.applyCurrentStyleToSelection()
                        }
                    }
                }

            }
        }
/*        if(bottomMenuType == MenuBar.SKIN && drawBoardViewModel.isShowSkinDrawer){
            SkinControl(
                skinViewModel = hiltViewModel<SkinViewModel>().apply {
                    updateBgMode(bgMode = state.bgMode)
                    updateBgColor(bgColor = state.bgColor.toComposeColor())
                }
            ){ skinModel ->
                viewModel.handleIntent(RichTextIntent.UpdateSkinStyle(skinModel.bgMode,skinModel.color))
                viewModel.handleIntent(RichTextIntent.RequestSave)
            }
        }*/
        // 功能菜单栏
        BottomAppBar(
            backgroundColor = TclTheme.colorScheme.reWriteExpandBg,
            windowInsets = AppBarDefaults
                .bottomAppBarWindowInsets,
            modifier = Modifier
                .fillMaxWidth(),
        ){
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(dimens.richTextToolBarHeight)
                    .padding(horizontal = dimens.barHorizontalPadding)
            ){
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween,
                    modifier = Modifier
                        .fillMaxSize()

                ) {
                    // 待办事项
                    IconThemeSwitcher(
                        btnSize = dimens.btnSize,
                        iconSize = dimens.iconSize,
                        painter = painterResource((state.currentParagraphType== ParagraphType.TODO_ITEM).judge(
                            R.drawable.ic_edit_todo_selected,
                            R.drawable.ic_edit_todo
                        )),
                        onClick = {
                            val isTodoSelected = state.currentFocusedType is EditorContent.TodoBlock
                            val newType = MenuBar.NONE
                            //val newType = if (isTodoSelected) MenuBar.NONE else MenuBar.TODO
                            menuBarViewModel.showExpandButton = false
                            /*bottomMenuType = if (bottomMenuType == MenuBar.TODO || isTodoSelected) {
                                MenuBar.NONE
                            } else {
                                MenuBar.TODO
                            }*/
                            bottomMenuType = MenuBar.NONE
                            if(state.currentParagraphType != ParagraphType.TODO_ITEM){
                                viewModel.handleIntent(RichTextIntent.UpdateCurrentParagraphType(ParagraphType.TODO_ITEM))
                            }else{
                                viewModel.handleIntent(RichTextIntent.UpdateCurrentParagraphType(ParagraphType.TEXT))
                            }
                            viewModel.handleIntent(RichTextIntent.UpdateMenuType(bottomMenuType))
                            onTodo(newType)
                            drawBoardViewModel.goneDrawer()
                        },
                        contentDescription = stringResource(R.string.menu_add_to_do),
                    )
                    IconThemeSwitcher(
                        btnSize = dimens.btnSize,
                        iconSize = dimens.iconSize,
                        painter = painterResource((state.currentParagraphType==ParagraphType.NUMBERED_LIST
                                || state.currentParagraphType == ParagraphType.BULLETED_LIST
                                || state.isBoldActive
                                || state.isItalicActive
                                || state.isUnderlineActive
                                ).judge(
                                R.drawable.ic_edit_font_selected,
                                R.drawable.ic_edit_font
                            )),
                        contentDescription = stringResource(R.string.menu_set_format),
                        onClick = {
                            menuBarViewModel.showExpandButton = false
                            bottomMenuType = if (bottomMenuType == MenuBar.KEYBOARD) MenuBar.NONE else MenuBar.KEYBOARD
                            viewModel.handleIntent(RichTextIntent.UpdateMenuType(bottomMenuType))
                            drawBoardViewModel.goneDrawer()
                        }
                    )
                    // 画笔
                    IconThemeSwitcher(
                        btnSize = dimens.btnSize,
                        iconSize = dimens.iconSize,
                        painter = painterResource((bottomMenuType == MenuBar.BRUSH).judge(
                            R.drawable.ic_edit_paint,
                            R.drawable.ic_edit_painting_brush
                        )),
                        contentDescription = (bottomMenuType == MenuBar.BRUSH).judge(
                            R.string.edit_bottom_menu_keyboard,
                            R.string.edit_bottom_menu_pen
                        ).stringRes(),
                        onClick ={
                            menuBarViewModel.showExpandButton = false
                            bottomMenuType = if (bottomMenuType == MenuBar.BRUSH) MenuBar.NONE else MenuBar.BRUSH
                            viewModel.handleIntent(RichTextIntent.UpdateMenuType(bottomMenuType))
                            drawBoardViewModel.goneDrawer()
                            focusManager.clearFocus()
                        }
                    )
                    // 皮肤
                    IconThemeSwitcher(
                        btnSize = dimens.btnSize,
                        iconSize = dimens.iconSize,
                        /*painter = painterResource((bottomMenuType == MenuBar.SKIN && drawBoardViewModel.isShowSkinDrawer).judge(
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_edit_skin_sel,
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_edit_skin
                        )),*/
                        painter = painterResource((drawBoardViewModel.isShowSkinDrawer).judge(
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_edit_skin_sel,
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_edit_skin
                        )),
                        contentDescription = stringResource(R.string.menu_skin),
                        onClick = {
                            menuBarViewModel.showExpandButton = false
                            //bottomMenuType =  MenuBar.SKIN
                            drawBoardViewModel.transformSkinDrawerVisible()
                            viewModel.handleIntent(RichTextIntent.UpdateMenuType(bottomMenuType))
                            focusManager.clearFocus()
                        }
                    )
                    // 插入图片按钮
                    IconThemeSwitcher(
                        btnSize = dimens.btnSize,
                        iconSize = dimens.iconSize,
                        painter = painterResource(R.drawable.ic_edit_phone_picture),
                        contentDescription = stringResource(R.string.menu_image),
                        onClick = {
                            menuBarViewModel.showExpandButton = false
                            bottomMenuType = MenuBar.NONE
                            //bottomMenuType = if (bottomMenuType == MenuBar.PICTURE) MenuBar.NONE else MenuBar.PICTURE
                            drawBoardViewModel.goneDrawer()
                            viewModel.handleIntent(RichTextIntent.UpdateMenuType(bottomMenuType))
                        }
                    )

                    // 插入语音
                    AudioView(
                        noteId = state.note?.noteId ?: 0L,
                        btnSize = dimens.btnSize,
                        iconSize = dimens.iconSize,
                        painterNor = painterResource(R.drawable.ic_phone_menu_recording_start) ,
                        painterEnable = painterResource( R.drawable.ic_phone_menu_recording_stop),
                        onAudioClick = { audioPath, isAdd ->
                            onAudioClick(audioPath, isAdd)
                            drawBoardViewModel.goneDrawer()
                        }
                    )
                    HoverProofIconButton(
                        modifier = Modifier.size(dimens.btnSize),
                        onClick = {
                            menuBarViewModel.showExpandButton = false
                            bottomMenuType = if (bottomMenuType == MenuBar.AI) MenuBar.NONE else MenuBar.AI
                            viewModel.handleIntent(RichTextIntent.UpdateMenuType(bottomMenuType))
                            drawBoardViewModel.goneDrawer()
                        }
                    ) {
                        Image(
                            painter = painterResource(R.drawable.ic_ai_icon),
                            contentDescription = R.string.edit_bottom_menu_ai_assistant.stringRes(),
                            modifier = Modifier
                                .size(20.dp)
                                .align(Alignment.CenterVertically) // 垂直居中
                        )
                    }


                }
            }
            /*if(bottomMenuType == MenuBar.PICTURE){
                ImageAddDialog(
                    onSelected = {
                        if(it== PictureSource.PICK_IMAGE){
                            onPickImage()
                        }else{
                            onTakePhoto()
                        }
                        menuBarViewModel.showExpandButton = false
                        bottomMenuType = MenuBar.NONE
                        viewModel.handleIntent(
                            RichTextIntent.UpdateMenuType(
                                bottomMenuType
                            )
                        )
                    },
                    onCancel = {
                        menuBarViewModel.showExpandButton = false
                        bottomMenuType = MenuBar.NONE
                        viewModel.handleIntent(
                            RichTextIntent.UpdateMenuType(
                                bottomMenuType
                            )
                        )
                    }
                )
            }*/
            EditBottomAIMenuPop(
                isShowBottomAIPop,
                onDismissRequest = {
                    viewModel.closeBottomAIPop()
                }, onUpdateBottomMenuType = { bottomMenuType ->
                    viewModel.handleIntent(
                        RichTextIntent.UpdateMenuType(
                            bottomMenuType
                        )
                    )
                }, onBottomClick
            )
        }



    }
}



@Composable
 fun FormatButton(
    width: Dp = 63.dp,
    height: Dp =39.dp,
    modifier: Modifier =Modifier,
    painter: Painter,
    contentDescription: String,
    onClick: () -> Unit,
){

    Box(
        modifier = Modifier.invisibleSemantics()
    ) {
        Image(
            modifier = modifier
                .invisibleSemantics()
                .border(
                    width = 1.dp,
                    color = colorResource(R.color.edit_border_menu),
                    shape = RoundedCornerShape(5.dp)
                )
                .width(width)
                .height(height),
            painter = painter,
            contentDescription = contentDescription
        )
        HoverProofIconButton(
            modifier = Modifier
                .semantics {
                    this.contentDescription = contentDescription
                }
                .width(width)
                .height(height),
            onClick = onClick
        ) {

        }
    }

}
