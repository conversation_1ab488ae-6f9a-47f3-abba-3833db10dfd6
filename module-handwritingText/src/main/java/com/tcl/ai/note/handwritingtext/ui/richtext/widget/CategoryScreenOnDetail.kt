package com.tcl.ai.note.handwritingtext.ui.richtext.widget

import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.intent.RichTextIntent
import com.tcl.ai.note.handwritingtext.state.ListNoteCategoryState
import com.tcl.ai.note.handwritingtext.utils.isExistCategory
import com.tcl.ai.note.handwritingtext.vm.RichTextViewModel
import com.tcl.ai.note.utils.CategoryColors
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.findLeastUsedColor
import com.tcl.ai.note.widget.CategoryDialog


@Composable
fun CategoryScreen(
    viewModel: RichTextViewModel = hiltViewModel(),
    isPreviewMode: Boolean,
    isAddCategoryMode: Boolean,
    onDismissRequest: () -> Unit
) {
    // Get category states from ViewModel
    val listNoteCategoryState by viewModel.listNoteCategoryState.collectAsState()
    val currentCategoryId = viewModel.currentCategoryId.value
    val currentCategoryName = viewModel.currentCategoryName.value
    val currentColorIndex = viewModel.currentColorIndex.value
    //val currentCategoryIcon = viewModel.currentCategoryIcon.value

    // State for the dialog
    var items by remember { mutableStateOf(listOf<NoteCategory>()) }
    val tmpCategoryState = listNoteCategoryState
    items = if (tmpCategoryState is ListNoteCategoryState.Success) {
        tmpCategoryState.items
    } else {
        listOf()
    }

    // Hoisted state for the dialog
    var categoryName by remember(currentCategoryName) {
        mutableStateOf(
            if (!isAddCategoryMode) currentCategoryName else ""
        )
    }

    val tmpColorIdx = currentColorIndex.toIntOrNull()
    val initialColorIndex = if (!isAddCategoryMode && tmpColorIdx != null) {
        tmpColorIdx
    } else {
        findLeastUsedColor(items){it.colorIndex} ?: CategoryColors.YELLOW_COLOR
    }
    var selectedColorIndex by remember { mutableIntStateOf(initialColorIndex) }

    val originalColorIndex by remember(currentColorIndex) {
        mutableIntStateOf(
            currentColorIndex.toIntOrNull() ?: 1
        )
    }

    var isOnChanged by remember { mutableStateOf(false) }

    // Compute dialog states for validation
    val isNotEmpty = categoryName.trim().isNotEmpty()
    val isExistCategory =
        isExistCategory(items, categoryName, selectedColorIndex, isAddCategoryMode)
    val isRepeated = isExistCategory && isNotEmpty
    val isDiffColor = selectedColorIndex != originalColorIndex
    val isEnabled = if (isAddCategoryMode) {
        !isExistCategory && isNotEmpty
    } else {
        (isDiffColor || !isExistCategory) && isNotEmpty
    }

    val toastText = stringResource(R.string.category_add_success)
    CategoryDialog(
        isAddCategoryMode = isAddCategoryMode,
        categoryName = categoryName,
        onCategoryNameChange = { newName ->
            categoryName = newName
            isOnChanged = true
        },
        selectedColorIndex = selectedColorIndex,
        onColorIndexSelected = { colorIndex ->
            selectedColorIndex = colorIndex
            isOnChanged = true
        },
        isEnabled = isEnabled,
        isRepeated = isRepeated,
        isOnChanged = isOnChanged,
        onDismiss = onDismissRequest,
        onConfirm = {
            if (isEnabled) {
                onDismissRequest()
                if (isPreviewMode) {
                    // Preview mode
                    val category = NoteCategory(
                        name = categoryName,
                        colorIndex = if (selectedColorIndex > 0) selectedColorIndex else CategoryColors.YELLOW_COLOR,
                        createTime = System.currentTimeMillis(),
                        modifyTime = System.currentTimeMillis()
                    )
                    viewModel.handleIntent(RichTextIntent.AddCategory(category, true))
                } else {
                    if (isAddCategoryMode) {
                        // Add new category
                        val category = NoteCategory(
                            name = categoryName,
                            colorIndex = selectedColorIndex,
                            createTime = System.currentTimeMillis(),
                            modifyTime = System.currentTimeMillis()
                        )
                        viewModel.handleIntent(RichTextIntent.AddCategory(category, false))
                        ToastUtils.makeWithCancel(toastText, Toast.LENGTH_SHORT)
                    } else {
                        // Rename category
                        currentCategoryId.toLongOrNull()?.let { categoryId ->
                            val newCategory = NoteCategory(
                                categoryId = categoryId,
                                name = currentCategoryName
                            )
                            newCategory.modifyTime = System.currentTimeMillis()
                            newCategory.name = categoryName
                            newCategory.colorIndex = selectedColorIndex
                            newCategory.isRename = currentCategoryName != categoryName
                            viewModel.handleIntent(RichTextIntent.RenameCategory(newCategory))
                        }
                    }
                }
            }
        }
    )
}


