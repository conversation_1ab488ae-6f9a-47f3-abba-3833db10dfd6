package com.tcl.ai.note.handwritingtext.ui.richtext.widget

import android.annotation.SuppressLint
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewFontScale
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.max
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import com.tcl.ai.note.base.R
import com.tcl.ai.note.controller.BottomRoute
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.theme.CornerShapeAIBottomMenuUpPop
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.widget.MarqueeText

/**
 * 向上弹出的 Popup
 * material3
 */
@Composable
fun EditBottomAIMenuPop(
    expanded: Boolean,
    onDismissRequest: () -> Unit,
    onUpdateBottomMenuType: (MenuBar) -> Unit,
    onBottomClick: (BottomRoute) -> Unit
) {

    // 带动画的向上弹出 Popup
//    AnimatedVisibility(
//        modifier = modifier,
//        visible = isPopupVisible,
//        enter = slideInVertically(initialOffsetY = { it }), // 从下方滑入
//        exit = slideOutVertically(targetOffsetY = { it }) // 向下滑出
//    ) {
    val uiDesignYHeight = 8.dp
    //底部BottomAppBar 自带 AppBarHorizontalPadding=4.dp
    val uiDesignXWidth = 16.dp - 4.dp //标注总偏移量-菜单自带padding
    val bottomBarHeight = 56.dp //menubar的高度40+ padding 16
    // 底部总偏移高度=底部菜单栏高度+UI标注的偏移高度
    val bottomTotalOffset = uiDesignYHeight + bottomBarHeight
    if (expanded) {
        Popup(
            onDismissRequest = {
                onUpdateBottomMenuType(MenuBar.NONE)
                onDismissRequest()
            },
            properties = PopupProperties(focusable = true),
            alignment = Alignment.BottomEnd,
            offset = IntOffset(
                x = -uiDesignXWidth.toPx.toInt(),
                -bottomTotalOffset.toPx.toInt()
            ) // 向上偏移，贴近底部菜单栏
        ) {
            CardMenuContent(onUpdateBottomMenuType, onBottomClick)
        }
//        }
    }

}

@SuppressLint("DesignSystem")
@Composable
internal fun CardMenuContent(
    onUpdateBottomMenuType: (MenuBar) -> Unit,
    onBottomClick: (BottomRoute) -> Unit
) {
    Card(
        modifier = Modifier
            .width(196.dp),
//            .height(438.fdp),
//                    .padding(vertical = 24.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 5.dp),
        shape = CornerShapeAIBottomMenuUpPop,
        colors = CardDefaults.cardColors(containerColor = colorResource(R.color.bg_dialog))
    ) {
        Column(modifier = Modifier.padding(vertical = 12.dp)) {
            // AI智能概要
            MenuItemRow(onMenuClick = {
                onUpdateBottomMenuType(MenuBar.NONE)
                onBottomClick(BottomRoute.Summary)
            }, R.drawable.ic_ai_menu_summary, stringResource(R.string.ai_summary))

            // AI帮写
            MenuItemRow(onMenuClick = {
                onUpdateBottomMenuType(MenuBar.NONE)
                onBottomClick(BottomRoute.HelpWrite)
            }, R.drawable.ic_ai_menu_helpwrite, stringResource(R.string.ai_writing_assistant))

            // AI润色
            MenuItemRow(onMenuClick = {
                onUpdateBottomMenuType(MenuBar.NONE)
                onBottomClick(BottomRoute.Polish)
            }, R.drawable.ic_ai_menu_rewrite, stringResource(R.string.ai_refinement))
        }
    }
}

@Composable
internal fun MenuItemRow(onMenuClick: () -> Unit, @DrawableRes id: Int, text: String) {
    Row(
        modifier = Modifier
            .padding(horizontal = 12.dp, vertical = 4.dp)
            .clip(
                RoundedCornerShape(8.dp)
            )
            .heightIn(33.dp)
            .clickable {
                onMenuClick()
            }
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        TclTheme.colorScheme.writingGradientLeftColor,
                        TclTheme.colorScheme.writingGradientRightColor
                    )
                )
            )
            .padding(start = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = id),
            contentDescription = "",
            modifier = Modifier.size(17.dp)
        )
        MarqueeText(
            text = text,
            modifier = Modifier.padding(start = 8.dp) // 添加权重，让文本占据剩余空间
                .weight(1f),
            overflow = TextOverflow.Ellipsis,
            color = colorResource(R.color.text_menu_color),
            fontSize = 14.sp
        )
    }
}

@Preview(fontScale = 2f, showBackground = true, locale = "sv")
@Composable
private fun EditBottomAIMenuPopPreview() {
    CardMenuContent(
        onUpdateBottomMenuType = {},
        onBottomClick = {}
    )
}