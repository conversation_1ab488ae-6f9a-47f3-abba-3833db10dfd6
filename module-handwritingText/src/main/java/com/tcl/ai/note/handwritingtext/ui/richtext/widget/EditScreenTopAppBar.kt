package com.tcl.ai.note.handwritingtext.ui.richtext.widget

import android.annotation.SuppressLint
import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.voicetotext.vm.RecordingViewModel
import com.tcl.ai.note.widget.HoverProofIconButton


/**
 *
 *             canUndo = true,
 *             canRedo = true,
 *             onUndo = {
 *             },
 *             onRedo = {
 *             },
 *             canSave = true,
 *             onReturn = {
 *             },
 *             onSave = {
 *             }
 */





/**
 * 编辑页面页面头部功能区
 */
@Deprecated("二期使用 TableEditScreenTopBar")
@SuppressLint("DesignSystem")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditScreenTopAppBar(
    canUndo: Boolean,
    canRedo: Boolean,
    onUndo: () -> Unit,
    onRedo: () -> Unit,
    backgroundColor: Color = TclTheme.colorScheme.reWriteExpandBg,
    canSave: Boolean,
    onReturn: () -> Unit,
    onSave: () -> Unit,
    hasAudio: Boolean = false,
    showAudioPanel: Boolean = false,
    onAudioPanelVisibleClick: () -> Unit = {},
    onAddAudio: (String) -> Unit = {},
    recordingViewModel: RecordingViewModel = hiltViewModel(),
) {
    val dimens = getGlobalDimens()
    val recordingState by recordingViewModel.recordState.collectAsState()
    var audioPath by remember(recordingState) { mutableStateOf(
        if (recordingState.isRecording) {
            recordingState.audioPath
        } else {
            ""
        }) }
    var checkAudioPermission by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .background(color = backgroundColor)
            .statusBarsPadding()
    ) {
        Row(
            modifier = Modifier
                .height(dimens.navigationBarHeight)
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {

            HoverProofIconButton(
                modifier = Modifier.size(dimens.iconSize),
                onClick = onReturn,
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_note_back),
                    contentDescription = stringResource(R.string.edit_top_menu_back_icon),
                )
            }
            Spacer(modifier = Modifier
                .height(1.dp)
                .fillMaxWidth()
                .weight(1f))

            // 录音
            // if (isTablet) {
            //     RecordingBarCrossfadeAnim(
            //         audioPath = audioPath,
            //         hasAudio = hasAudio,
            //         showAudioPanel = showAudioPanel,
            //         onAudioPanelVisibleClick = onAudioPanelVisibleClick,
            //         onStopAudioClick = {
            //             audioPath = ""
            //         },
            //         onRecordingError = {
            //             audioPath = ""
            //         },
            //         onAddAudio = onAddAudio,
            //         recordingViewModel = recordingViewModel
            //     )
            //     HoverProofIconButton(
            //         modifier = Modifier.size(dimens.iconSize),
            //         onClick = {
            //             checkAudioPermission = true
            //         },
            //         enabled = true,
            //     ) {
            //         Image(
            //             painter = painterResource(id = R.drawable.ic_add),
            //             contentDescription = "Add"
            //         )
            //     }
            //     Spacer(modifier = Modifier.width(16.dp))
            // }
            // 撤消
           /* HoverProofIconButton(
                modifier = Modifier.size(dimens.iconSize),
                onClick = { if (canUndo) onUndo() },
                enabled = canUndo,
            ) {
                Image(
                    painter = if (canUndo) painterResource(id = R.drawable.ic_edit_undo_enable)
                    else painterResource(id = R.drawable.ic_edit_undo),
                    contentDescription = stringResource(R.string.edit_top_menu_undo)
                )
            }
            Spacer(modifier = Modifier.width(16.dp))
            // 重做
            HoverProofIconButton(
                modifier = Modifier.size(dimens.iconSize),
                onClick = onRedo,
                enabled = canRedo,
            ) {
                Icon(
                    painter = if (canRedo) painterResource(id = R.drawable.ic_edit_redo_enable)
                    else painterResource(id = R.drawable.ic_edit_redo),
                    contentDescription = stringResource(R.string.edit_top_menu_redo)
                )
            }
            Spacer(modifier = Modifier.width(16.dp))*/
            // 保存
            /* HoverProofIconButton(
                modifier = Modifier.size(dimens.iconSize),
                onClick = onSave,
                enabled = canSave,
            ) {

                Icon(
                    painter = painterResource(id = canSave.judge(R.drawable.ic_edit_save_enable,R.drawable.ic_edit_save)),
                    contentDescription = stringResource(R.string.edit_top_menu_save_note),
                )
            }*/
        }
        /*if (isTablet) {
            ImportAudioHelper(
                viewModel = recordingViewModel,
                checkAudioPermission = checkAudioPermission,
                onResetCheckPermission = { checkAudioPermission = false },
                onStartRecording = {
                    if (!recordingState.isRecording) {
                        audioPath = generateAudioPath()
                        recordingViewModel.recordingIntent(RecordIntent.StartRecord(audioPath!!))
                        onAddAudio(audioPath!!)
                    }
                }
            )
        }*/
    }

}


/**
 * 预览页面页面头部功能区
 */
@SuppressLint("DesignSystem")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PreviewTopAppBar(
    showReturn:Boolean,
    showShare: Boolean = true,
    noteId:Long=0,
    currentCategoryIcon:String,
    currentCategoryColorIndex:Int,
    showCategoryPopup:Boolean,
    isDeleteDialogShow:Boolean,
    backgroundColor: Color = TclTheme.colorScheme.reWriteExpandBg,
    onReturn: () -> Unit,
    onChangeCategory: () -> Unit,
    onShare: (Context,String) -> Unit,
    onDelete: () -> Unit,
    isEditMode: Boolean = false,
    categoryPopup: @Composable () -> Unit = {},
) {
    var isShareDropdownMenuExpanded by remember { mutableStateOf(false) }
    val dimens = getGlobalDimens()
/*    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp // 屏幕宽度
//    val xOffset =(screenWidth*0.67).dp-8.dp
    val xOffset = 100.dp*/
    Column(
        modifier = Modifier
            .background(color = backgroundColor)
            .statusBarsPadding()
    ) {
        Row(
            modifier = Modifier
                .height(dimens.menuBarHeight)
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (isEditMode) return@Column
            if(showReturn){
                HoverProofIconButton(
                    modifier = Modifier.size(dimens.iconSize),
                    onClick = onReturn,
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_menu_back),
                        contentDescription = stringResource(R.string.edit_top_menu_back_icon),
                    )
                }
            }
            Spacer(modifier = Modifier
                .height(1.dp)
                .fillMaxWidth()
                .weight(1f))
            // 分类
            HoverProofIconButton(
                modifier = Modifier.size(dimens.iconSize),
                enabled = noteId>0,
                onClick = onChangeCategory,
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_category),
                    contentDescription = stringResource(R.string.edit_top_menu_category),
                    colorFilter = ColorFilter.tint(colorResource(if(showCategoryPopup) { R.color.text_category_list_selected} else { R.color.transparent_icon })),
                    alpha = if(showCategoryPopup) 1f else 0.85f
                )
                categoryPopup()
            }
            Spacer(modifier = Modifier.width(16.dp))
            // 分享
            val context = LocalContext.current
            HoverProofIconButton(
                modifier = Modifier.size(dimens.iconSize),
                enabled = showShare && noteId>0,
                onClick =
                {
                    isShareDropdownMenuExpanded = !isShareDropdownMenuExpanded
                }
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_menu_share),
                    contentDescription = stringResource(R.string.edit_top_menu_share),
                    colorFilter = ColorFilter.tint(colorResource(if (isShareDropdownMenuExpanded) { R.color.text_category_list_selected } else { R.color.transparent_icon })),
                    alpha = if(showCategoryPopup) 1f else 0.85f
                )
                DropdownMenu(
                    expanded = isShareDropdownMenuExpanded,
                    onDismissRequest = { isShareDropdownMenuExpanded = false },
                    offset = DpOffset(40.dp, 24.dp), // 偏移会带阴影，实际得比组件宽度要小一点
                    modifier = Modifier
                        .wrapContentWidth()
                        .clip(RoundedCornerShape(8.dp))
                        .background(
                            color = colorResource(R.color.bg_dialog),
                            shape = RoundedCornerShape(8.dp)
                        ),
                    shape = RoundedCornerShape(8.dp)
                ) {

                    ShareDropdownMenuItem(
                        text = stringResource(R.string.action_share_image_file)
                    ){
                        isShareDropdownMenuExpanded = false
                        onShare(context,"Image")  // 传递上下文和笔记对象
                    }
                    ShareDropdownMenuItem(
                        text = stringResource(R.string.action_share_text_file)
                    ){
                        isShareDropdownMenuExpanded = false
                        onShare(context,"Text")  // 传递上下文和笔记对象
                    }
                    ShareDropdownMenuItem(
                        text = stringResource(R.string.action_share_pdf_file)
                    ){
                        isShareDropdownMenuExpanded = false
                        onShare(context,"PDF")  // 传递上下文和笔记对象
                    }


                    /*DropdownMenuItem(
                        modifier = Modifier.background(Color.Red),
                        text = {
                            Text(stringResource(R.string.action_share_image_file))
                        },
                        onClick = {
                            isShareDropdownMenuExpanded = false
                            onShare(context,"Image")  // 传递上下文和笔记对象
                        },
                    )
                    DropdownMenuItem(
                        text = {
                            Text(stringResource(R.string.action_share_text_file))
                        },
                        onClick = {
                            isShareDropdownMenuExpanded = false
                            onShare(context,"Text")  // 传递上下文和笔记对象
                        },
                    )
                    DropdownMenuItem(
                        text = {
                            Text(stringResource(R.string.action_share_pdf_file))
                        },
                        onClick = {
                            isShareDropdownMenuExpanded = false
                            onShare(context,"PDF")  // 传递上下文和笔记对象
                        },
                    )*/
                }

            }
            Spacer(modifier = Modifier.width(16.dp))
            // 删除
            HoverProofIconButton(
                modifier = Modifier.size(dimens.iconSize),
                enabled = noteId>0,
                onClick = onDelete,
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_delete_selected),
                    contentDescription = stringResource(R.string.delete),
                    colorFilter = ColorFilter.tint(colorResource(if (isDeleteDialogShow) { R.color.text_category_list_selected } else { R.color.transparent_icon })),
                    alpha = if(showCategoryPopup) 1f else 0.85f
                )
            }
        }

    }

}

@Composable
private  fun ShareDropdownMenuItem(
    text:String,
    onClick: () -> Unit,
){
    Row (
        modifier = Modifier
            .wrapContentWidth()
            .height(48.dp)
            .clickable {
                onClick()
            }
            .padding(horizontal = 15.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Text(
            text = text,
            fontSize = 13.sp,
            color = R.color.black.colorRes()
        )
    }
}