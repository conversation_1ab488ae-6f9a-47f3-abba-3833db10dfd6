package com.tcl.ai.note.handwritingtext.ui.richtext.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.SemanticsProperties
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.widget.IconThemeSwitcher

@Composable
fun EditToggle(
    darkTheme: Boolean = isSystemInDarkTheme(),
    onClick: () ->Unit,
){
    val context = LocalContext.current

    Box(
        modifier = Modifier.size(40.dp).clearAndSetSemantics {
            contentDescription =context.getString(com.tcl.ai.note.base.R.string.edit_bottom_tool_expand)
            role  = Role.Button
        },
        contentAlignment = Alignment.Center
    ){
        Image(
            painter = painterResource(id = darkTheme.judge(
                com.tcl.ai.note.base.R.drawable.bg_toggle_dark,
                com.tcl.ai.note.base.R.drawable.bg_toggle
                )
            ),
            contentDescription = ""
        )
        IconThemeSwitcher(
            modifier = Modifier.align(alignment = Alignment.Center),
            painter =  painterResource(id = com.tcl.ai.note.handwritingtext.R.drawable.ic_edit_toggle_expansion),
            contentDescription =null,
            btnSize = 40.dp,
            onClick = onClick
        )
    }
}