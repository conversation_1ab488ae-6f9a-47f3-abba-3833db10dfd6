 package com.tcl.ai.note.handwritingtext.ui.richtext.widget

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.theme.TclTheme

 /**
 * 导入操作弹框
 * 尺寸：自适应内容宽度
 * 背景：TclTheme.colorScheme.tertiaryBackground，圆角20dp，阴影
 */
@SuppressLint("DesignSystem")
@Composable
internal fun ImportActionContent(
    onRecordAudio: () -> Unit,
    onChoosePhoto: () -> Unit,
    onTakePhoto: () -> Unit,
    isRecording: Boolean = false
) {
    val context = LocalContext.current

    Surface(
        modifier = Modifier
            .wrapContentWidth()
            .defShadow(20.dp),
        shape = RoundedCornerShape(20.dp),
        color = TclTheme.colorScheme.tertiaryBackground
    ) {
        Column(
            modifier = Modifier
                .width(IntrinsicSize.Max)
                .padding(4.dp)
        ) {
            // Record Audio
            ImportMenuItem(
                iconRes = com.tcl.ai.note.handwritingtext.R.drawable.ic_navigator_mic,
                text = stringResource(R.string.import_menu_record_audio),
                enabled = !isRecording,
                onClick = {
                    onRecordAudio()
                    //ComposableToast.show(context, "录音功能")
                }
            )
            
            // Choose Photo
            /*ImportMenuItem(
                iconRes = com.tcl.ai.note.handwritingtext.R.drawable.ic_navigator_image,
                text = stringResource(R.string.import_menu_choose_photo),
                onClick = {
                    onChoosePhoto()
                }
            )
            
            // Take Photo
            ImportMenuItem(
                iconRes = com.tcl.ai.note.handwritingtext.R.drawable.ic_navigator_camera,
                text = stringResource(R.string.import_menu_take_photo),
                onClick = {
                    onTakePhoto()
                }
            )*/
        }
    }
}

@Composable
private fun ImportMenuItem(
    iconRes: Int,
    text: String,
    onClick: () -> Unit,
    enabled: Boolean = true
) {
    val alpha = if (enabled) 1f else 0.4f
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(40.dp)
            .clip(RoundedCornerShape(16.dp))
            .clickable(enabled = enabled) {
                if (enabled) onClick()
            },
        contentAlignment = Alignment.Center
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = iconRes),
                contentDescription = text,
                modifier = Modifier.size(20.dp)
                    .alpha(alpha),
                tint = TclTheme.colorScheme.textDialogTitle.copy(alpha = alpha)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Text(
                text = text,
                fontSize = 14.sp,
                fontWeight = FontWeight.Normal,
                fontFamily = FontFamily.Default,
                color = TclTheme.colorScheme.textDialogTitle.copy(alpha = alpha),
                maxLines = 1
            )
        }
    }
} 