package com.tcl.ai.note.handwritingtext.ui.richtext.widget

import android.annotation.SuppressLint
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.PictureSource
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.utils.appendSemanticsButton
import com.tcl.ai.note.utils.globalDialogWidth
import com.tct.theme.core.designsystem.component.TclDialog
import com.tct.theme.core.designsystem.component.TclTextButton
import com.tct.theme.core.designsystem.component.TclWarningButton
import com.tct.theme.core.designsystem.theme.TclTheme

/**
 * 数据删除对话框组件
 * @param onDismiss 当对话框被关闭时执行的操作
 * @param onDelete 当用户选择删除时执行的操作
 */
@Composable
fun DeleteDataDialog (
    text: String,
    onDismiss: () -> Unit,
    onDelete: () -> Unit
) {
    val context = LocalContext.current
    val contentDescription = context.getString(
        R.string.dialog_semantics_description,
        context.getString(R.string.dialog_type_semantics),                         // 对话框类型
        text,                                                                      // 主要内容
        context.getString(R.string.btn_cancel).appendSemanticsButton(),            // 取消按钮
        context.getString(R.string.menu_delete_recording).appendSemanticsButton()  // 删除按钮
    )
    TclTheme(dynamicColor = false) {
        TclDialog(
            show = true,
            dialogMaxWidth = globalDialogWidth(),
            onDismissRequest = { onDismiss() },
            properties = DialogProperties(dismissOnClickOutside = true, dismissOnBackPress = true),
            content = {
                Text(
                    modifier = Modifier.semantics {
                        this.contentDescription = contentDescription
                    },
                    text = text
                )
            },
            actions = {
                TclTextButton(onClick = { onDismiss.invoke() }, contentColor = colorResource(id = R.color.home_title_color)) { Text( stringResource(id = com.tcl.ai.note.base.R.string.btn_cancel)) }
                TclWarningButton( onClick = { onDelete.invoke() }) { Text(stringResource(id = R.string.menu_delete_recording)) }
            },
        )
    }
}


/**
 * 存储权限对话框组件，提示用户需要权限并提供打开设置的选项。
 * @param onDismiss 当对话框被关闭时执行的操作
 * @param onGoToSettings 当用户选择打开设置时执行的操作
 */
@Composable
fun StoragePermissionDialog(onDismiss: () -> Unit, onGoToSettings: () -> Unit) {
    TclDialog(
        onDismissRequest = { onDismiss.invoke() },
        show = true,
        content = {
            Text(
                text = stringResource(R.string.dialog_grant_storage_permission)
            )
        },
        actions = {
            TclTextButton(onClick = { onDismiss.invoke() }) { Text(stringResource(id = com.tcl.ai.note.base.R.string.btn_cancel)) }
            TclTextButton(
                onClick = { onDismiss.invoke()
                    onGoToSettings.invoke() }) { Text(stringResource(id = com.tcl.ai.note.base.R.string.btn_go_settings)) }
        })
}


/**
 * 相机权限对话框组件，提示用户需要权限并提供打开设置的选项。
 * @param onDismiss 当对话框被关闭时执行的操作
 * @param onGoToSettings 当用户选择打开设置时执行的操作
 */
@Composable
fun CameraPermissionDialog(onDismiss: () -> Unit, onGoToSettings: () -> Unit) {
    TclDialog(
        onDismissRequest = { onDismiss.invoke() },
        show = true,
        content = {
            Text(
                text = stringResource(R.string.dialog_grant_camera_permission)
            )
        },
        actions = {
            TclTextButton(onClick = { onDismiss.invoke() }) { Text(stringResource(id = com.tcl.ai.note.base.R.string.btn_cancel)) }
            TclTextButton(
                onClick = { onDismiss.invoke()
                    onGoToSettings.invoke() }) { Text(stringResource(id = com.tcl.ai.note.base.R.string.btn_go_settings)) }
        })

}


/**
 * 图片添加对话框
 */
@SuppressLint("DesignSystem")
@Composable
internal fun ImageAddDialog(onSelected: (source:String) -> Unit, onCancel: () -> Unit) {
    val context = LocalContext.current
    val contentDescription = context.getString(
        R.string.dialog_semantics_description,
        context.getString(R.string.dialog_type_semantics),                         // 对话框类型
        "",                                                                      // 主要内容
        context.getString(R.string.select_photos).appendSemanticsButton(),            // 取消按钮
        context.getString(R.string.take_photos).appendSemanticsButton()  // 删除按钮
    )
    Dialog(
        onDismissRequest = onCancel,
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Box(
            modifier = Modifier
                .invisibleSemantics()
                .fillMaxSize()
                .padding(bottom = 80.dp, start = 16.dp, end = 16.dp)
                .clickable(onClick = onCancel),
            contentAlignment = Alignment.BottomCenter
        ) {
            Surface(
                color = colorResource(R.color.bg_dialog),
                shape = RoundedCornerShape(20.dp),
                modifier = Modifier
                    .wrapContentWidth()
                    .fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(24.dp)
                        .semantics {
                        this.contentDescription = contentDescription
                    },
                    horizontalAlignment = Alignment.Start,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = stringResource(R.string.select_photos),
                        fontSize = 16.sp,
                        maxLines = 1,
                        modifier = Modifier
                            .fillMaxWidth()
                            .semantics {
                                role = Role.Button
                            }
                            .clickable {
                                onSelected(PictureSource.PICK_IMAGE)
                            },
                        color = colorResource(R.color.text_title)
                    )
                    Spacer(modifier = Modifier.height(24.dp))
                    Text(
                        text = stringResource(R.string.take_photos),
                        fontSize = 16.sp,
                        maxLines = 1,
                        modifier = Modifier
                            .semantics {
                                role = Role.Button
                            }
                            .fillMaxWidth()
                            .clickable {
                                onSelected(PictureSource.TAKE_PHOTO)
                            },
                        color = colorResource(R.color.text_title)
                    )
                }
            }
        }
    }
}