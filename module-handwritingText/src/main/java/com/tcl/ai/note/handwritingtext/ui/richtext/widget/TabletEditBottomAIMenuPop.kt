package com.tcl.ai.note.handwritingtext.ui.richtext.widget

import android.annotation.SuppressLint
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import com.tcl.ai.note.base.R
import com.tcl.ai.note.controller.BottomRoute
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.theme.CornerShapeAIBottomMenuUpPop
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.toPx

/**
 * 向上弹出的 Popup
 * material3
 */
@Composable
fun TabletEditBottomAIMenuPop(
    xOffset:Int,
    yOffset:Int,
    onDismissRequest: () -> Unit,
    onUpdateBottomMenuType: (MenuBar) -> Unit,
    onBottomClick: (BottomRoute) -> Unit
) {

    // 带动画的向上弹出 Popup
//    AnimatedVisibility(
//        modifier = modifier,
//        visible = isPopupVisible,
//        enter = slideInVertically(initialOffsetY = { it }), // 从下方滑入
//        exit = slideOutVertically(targetOffsetY = { it }) // 向下滑出
//    ) {

    val uiDesignYHeight=8.dp
    //底部BottomAppBar 自带 AppBarHorizontalPadding=4.dp
    val uiDesignXWidth=16.dp-4.dp //标注总偏移量-菜单自带padding
    val bottomBarHeight=56.dp //menubar的高度40+ padding 16
    // 底部总偏移高度=底部菜单栏高度+UI标注的偏移高度
    val bottomTotalOffset=uiDesignYHeight+bottomBarHeight
    Popup(
        onDismissRequest = {
            onUpdateBottomMenuType(MenuBar.NONE)
            onDismissRequest()
        },
        properties = PopupProperties(focusable = true),
        alignment = Alignment.TopStart,
        offset = IntOffset(xOffset, yOffset) // 向上偏移，贴近底部菜单栏
    ) {
        CardMenuContent(onUpdateBottomMenuType, onBottomClick)
    }

}