package com.tcl.ai.note.handwritingtext.ui.richtext.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.Dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.utils.borderCircle
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.widget.HoverProofIconButton


@Composable
fun TabletMenuColorCircle(
    modifier: Modifier = Modifier,
    btnSize:Dp,
    selectedColor:Color,
    isSelected:Boolean,
    onClick: () ->Unit
){
    val context = LocalContext.current
    val colorSelectorSize =
        dimensionResource(com.tcl.ai.note.handwritingtext.R.dimen.menu_color_select_button_size)
    val colorSelectorBorder =
        dimensionResource(com.tcl.ai.note.handwritingtext.R.dimen.menu_color_select_button_size_border_width)
    HoverProofIconButton  (
        modifier = modifier.size(btnSize)
            .semantics {
                this.contentDescription =context.getString(R.string.edit_bottom_menu_color_select)
                this.role = Role.Button
            },
        onClick = {
            onClick()
        }
    ){
        Box(
            modifier = Modifier
                .size(colorSelectorSize)
                .background(selectedColor, shape = CircleShape)
                .then((selectedColor == colorResource(R.color.edit_palette_color_WHITE)).judge(Modifier.borderCircle(),Modifier))
                .drawBehind {
                    if (isSelected) {
                        drawCircle(
                            color = selectedColor,
                            alpha = 0.3f,
                            radius = (colorSelectorSize + colorSelectorBorder).toPx() / 2,
                            center = Offset(
                                size.width / 2,
                                size.height / 2,
                            ),
                            style = Stroke(width = colorSelectorBorder.toPx())
                        )

                    }
                }
        )
    }


}