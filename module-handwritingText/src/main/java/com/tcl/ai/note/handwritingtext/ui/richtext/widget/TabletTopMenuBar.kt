package com.tcl.ai.note.handwritingtext.ui.richtext.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.controller.BottomRoute
import com.tcl.ai.note.handwritingtext.bean.BrushMenu
import com.tcl.ai.note.handwritingtext.bean.DrawMode
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.bean.PictureSource
import com.tcl.ai.note.handwritingtext.bean.tabletResId
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphType
import com.tcl.ai.note.handwritingtext.intent.RichTextIntent
import com.tcl.ai.note.handwritingtext.state.DrawBoardIntent
import com.tcl.ai.note.handwritingtext.ui.richtext.component.TabletBrushControl
import com.tcl.ai.note.handwritingtext.ui.richtext.component.TabletColorSelector
import com.tcl.ai.note.handwritingtext.ui.richtext.component.TabletEraserWidthSlider
import com.tcl.ai.note.handwritingtext.ui.richtext.component.TabletSkinControl
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.handwritingtext.vm.BrushSliderModel
import com.tcl.ai.note.handwritingtext.vm.ColorSelectorViewModel
import com.tcl.ai.note.handwritingtext.vm.DrawBoardViewModel
import com.tcl.ai.note.handwritingtext.vm.EraserSliderModel
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.handwritingtext.vm.RichTextViewModel
import com.tcl.ai.note.handwritingtext.vm.SkinViewModel
import com.tcl.ai.note.theme.CornerShapeMenuUpPop
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.utils.toArgbLong
import com.tcl.ai.note.utils.toComposeColor
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.widget.DelayedBackgroundIconButton
import com.tcl.ai.note.widget.HoverProofIconButton
import com.tcl.ai.note.widget.VerticalLine

/**
 * 顶部功能菜单栏
 */
@Deprecated("二期使用 TopMenuBar")
@Composable
fun TabletTopMenuBar(
    richtextViewModel: RichTextViewModel = hiltViewModel(),
    drawBoardViewModel: DrawBoardViewModel = hiltViewModel(),
    colorSelectorViewModel: ColorSelectorViewModel = hiltViewModel(),
    brushSliderModel: BrushSliderModel = hiltViewModel(),
    eraserSliderModel: EraserSliderModel = hiltViewModel(),
    menuBarViewModel: MenuBarViewModel = hiltViewModel(),
    onBottomClick: (BottomRoute) -> Unit,
    onTodo: (String) -> Unit,
    onPickImage: () -> Unit,
    onTakePhoto: () -> Unit,
    onAudioClick: (String, Boolean) -> Unit,
    topAppBarHeight: Int,
) {
    val scrollState = rememberScrollState()
    val state by richtextViewModel.state.collectAsState()
    // 当前点击的菜单栏类型
    var brushMenuType = state.brushMenuType
    // 当前点击的BottomMenu类型
    var bottomMenuType = state.bottomMenuType
    val isShowBottomAIPop = richtextViewModel.isShowBottomAIPop.value

    var currentSelectedColorIdx by colorSelectorViewModel.currentSelectorIdx
    val selectedColor by colorSelectorViewModel.selectedColor
    val selectedColor1 by colorSelectorViewModel.selectedColor1
    val selectedColor2 by colorSelectorViewModel.selectedColor2
    val selDoodlePen by brushSliderModel.lastDoodlePen
    val isPenMode = brushMenuType == BrushMenu.PEN && bottomMenuType == MenuBar.BRUSH

    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    val density = LocalDensity.current

    // 钢笔图标位置
    var penIconPosition by remember { mutableStateOf(Offset.Zero) }
    // 颜色图标位置
    var colorIconPosition by remember { mutableStateOf(Offset.Zero) }
    // 橡皮擦图标位置
    var eraserIconPosition by remember { mutableStateOf(Offset.Zero) }
    // 套索图标位置
    var lassoIconPosition by remember { mutableStateOf(Offset.Zero) }
    // 直尺图标位置
    var rulerIconPosition by remember { mutableStateOf(Offset.Zero) }
    // 手写转文图标位置
    var hwttIconPosition by remember { mutableStateOf(Offset.Zero) }
    // 笔迹美化图标位置
    var beautifyIconPosition by remember { mutableStateOf(Offset.Zero) }
    // 皮肤图标位置
    var skinIconPosition by remember { mutableStateOf(Offset.Zero) }
    // 字体图标位置
    var fontIconPosition by remember { mutableStateOf(Offset.Zero) }
    // AI图标位置
    var aiIconPosition by remember { mutableStateOf(Offset.Zero) }

    val layoutDirection = LocalLayoutDirection.current

    val screenWidthDp = LocalConfiguration.current.screenWidthDp


    val dimens = getGlobalDimens()

    val isInitCache =
        colorSelectorViewModel.isInit && brushSliderModel.isInit && eraserSliderModel.isInitCache
    // 获取完持久层数据后，每次重组，进行画笔状态恢复
    LaunchedEffect(selDoodlePen, brushMenuType, isInitCache, currentSelectedColorIdx) {
        if (isInitCache) {
            colorSelectorViewModel.initSliderPosition(selDoodlePen)
            when (brushMenuType) {
                BrushMenu.ERASER -> eraserSliderModel.recoverStatus(drawBoardViewModel)
                BrushMenu.PEN -> {
                    brushSliderModel.recoverStatus(drawBoardViewModel, selDoodlePen)
                }

                else -> {}
            }
        }
    }

    LaunchedEffect(isInitCache) {
        if (!isInitCache) {
            colorSelectorViewModel.loadCache()
            brushSliderModel.loadCache()
            eraserSliderModel.loadCache()
            richtextViewModel.lastCacheBrushMenuType()
        }

    }


    val colorClick: (colorIdx: Int) -> Unit = { colorIdx ->
        brushMenuType = BrushMenu.COLOR_SELECTOR
        richtextViewModel.handleIntent(
            RichTextIntent.UpdateBrushMenuType(
                brushMenuType
            )
        )
        bottomMenuType = MenuBar.BRUSH
        richtextViewModel.handleIntent(
            RichTextIntent.UpdateMenuType(
                bottomMenuType
            )
        )
        if (currentSelectedColorIdx == colorIdx) {
            drawBoardViewModel.transformColorDrawerDrawerVisible()
        } else {
            currentSelectedColorIdx = colorIdx
            if (!drawBoardViewModel.isShowColorDrawer) {
                drawBoardViewModel.transformColorDrawerDrawerVisible()
            }
        }
        focusManager.clearFocus()
    }


    Column {
        // 功能菜单列表
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(44.dp)
                .background(TclTheme.colorScheme.reWriteExpandBg),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {

            // 键盘
            DelayedBackgroundIconButton(
                btnSize = dimens.btnSize,
                onClick = {
                    bottomMenuType =
                        if (bottomMenuType == MenuBar.BRUSH) MenuBar.NONE else MenuBar.BRUSH
                    richtextViewModel.handleIntent(
                        RichTextIntent.UpdateMenuType(
                            bottomMenuType
                        )
                    )
                    focusManager.clearFocus()
                    drawBoardViewModel.goneDrawer()
                    if (bottomMenuType != MenuBar.BRUSH) {

                        richtextViewModel.handleIntent(RichTextIntent.UpdateFocusedIndex(0))
                        richtextViewModel.handleIntent(RichTextIntent.UpdateCursorPosition(0))
                    }
                },
                painter = painterResource(com.tcl.ai.note.handwritingtext.R.drawable.ic_menu_keyboard_nor),
                isChecked = bottomMenuType != MenuBar.BRUSH,
                contentDescription = R.string.edit_bottom_menu_keyboard.stringRes(),
            )

            Spacer(modifier = Modifier.width(18.dp))
            // 中间的竖线
            VerticalLine(
                modifier = Modifier.padding(vertical = 9.dp)
            )
            Spacer(modifier = Modifier.width(18.dp))

            // 钢笔
            Box(
                modifier = Modifier
                    .wrapContentSize()
                    .onGloballyPositioned { layoutCoordinates ->
                        val position = layoutCoordinates.localToWindow(Offset.Zero)
                        penIconPosition = position
                    }
            ) {
                DelayedBackgroundIconButton(
                    btnSize = dimens.btnSize,
                    painter = painterResource(
                        id = selDoodlePen.tabletResId(
                            sel = brushMenuType == BrushMenu.PEN && bottomMenuType == MenuBar.BRUSH && drawBoardViewModel.isShowPenDrawer
                        )
                    ),
                    isChecked = brushMenuType == BrushMenu.PEN && bottomMenuType == MenuBar.BRUSH && !drawBoardViewModel.isShowPenDrawer,
                    contentDescription = R.string.edit_bottom_tool_brush.stringRes(),
                    onClick = {
                        brushMenuType = BrushMenu.PEN
                        drawBoardViewModel.transformPenDrawerVisible()
                        richtextViewModel.handleIntent(
                            RichTextIntent.UpdateBrushMenuType(
                                brushMenuType
                            )
                        )
                        bottomMenuType = MenuBar.BRUSH
                        richtextViewModel.handleIntent(
                            RichTextIntent.UpdateMenuType(
                                bottomMenuType
                            )
                        )
                        focusManager.clearFocus()
                    }
                )
            }
            Spacer(modifier = Modifier.width(12.dp))

            // 橡皮擦
            DelayedBackgroundIconButton(
                modifier = Modifier
                    .onGloballyPositioned {
                        val position = it.localToWindow(Offset.Zero)
                        eraserIconPosition = position
                    },
                painter = painterResource(
                    (brushMenuType == BrushMenu.ERASER && bottomMenuType == MenuBar.BRUSH && drawBoardViewModel.isShowEraserDrawer)
                        .judge(
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_eraser_weak,
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_eraser_nor
                        )
                ),
                isChecked = (brushMenuType == BrushMenu.ERASER && bottomMenuType == MenuBar.BRUSH) && !drawBoardViewModel.isShowEraserDrawer,
                btnSize = dimens.btnSize,
                contentDescription = R.string.edit_bottom_tool_eraser.stringRes(),
                onClick = {
                    bottomMenuType = MenuBar.BRUSH
                    richtextViewModel.handleIntent(
                        RichTextIntent.UpdateMenuType(
                            bottomMenuType
                        )
                    )
                    focusManager.clearFocus()

                    brushMenuType = BrushMenu.ERASER
                    drawBoardViewModel.transformEraserDrawerVisible()
                    richtextViewModel.handleIntent(
                        RichTextIntent.UpdateBrushMenuType(
                            brushMenuType
                        )
                    )
                }
            )

            Spacer(modifier = Modifier.width(12.dp))

            // 套索工具
            DelayedBackgroundIconButton(
                modifier = Modifier
                    .onGloballyPositioned {
                        val position = it.localToWindow(Offset.Zero)
                        lassoIconPosition = position
                    },
                painter = painterResource(
                    (bottomMenuType == MenuBar.LASSO && drawBoardViewModel.isLassoOn)
                        .judge(
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_lasso_weak,
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_lasso_nor
                        )
                ),
                isChecked =  (bottomMenuType == MenuBar.LASSO)&& !drawBoardViewModel.isLassoOn,
                btnSize = dimens.btnSize,
                contentDescription = R.string.edit_menu_lasso.stringRes(),
                onClick = {
                    bottomMenuType = if (bottomMenuType == MenuBar.LASSO) MenuBar.NONE else MenuBar.LASSO
                    richtextViewModel.handleIntent(
                        RichTextIntent.UpdateMenuType(
                            bottomMenuType
                        )
                    )
                    focusManager.clearFocus()
                }
            )

            Spacer(modifier = Modifier.width(12.dp))

            // 直尺工具
            DelayedBackgroundIconButton(
                modifier = Modifier
                    .onGloballyPositioned {
                        val position = it.localToWindow(Offset.Zero)
                        rulerIconPosition = position
                    },
                painter = painterResource(
                    (bottomMenuType == MenuBar.RULER && drawBoardViewModel.isRulerOn)
                        .judge(
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_ruler_weak,
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_ruler_nor
                        )
                ),
                isChecked =  (bottomMenuType == MenuBar.RULER)&& !drawBoardViewModel.isRulerOn,
                btnSize = dimens.btnSize,
                contentDescription = R.string.edit_menu_ruler.stringRes(),
                onClick = {
                    bottomMenuType = if (bottomMenuType == MenuBar.RULER) MenuBar.NONE else MenuBar.RULER
                    richtextViewModel.handleIntent(
                        RichTextIntent.UpdateMenuType(
                            bottomMenuType
                        )
                    )
                    focusManager.clearFocus()
                }
            )

            Spacer(modifier = Modifier.width(12.dp))

            // 手写转文
            DelayedBackgroundIconButton(
                modifier = Modifier
                    .onGloballyPositioned {
                        val position = it.localToWindow(Offset.Zero)
                        hwttIconPosition = position
                    },
                painter = painterResource(
                    (bottomMenuType == MenuBar.HANDWRITING_TO_TEXT && drawBoardViewModel.isHWTTOn)
                        .judge(
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_handwriting_to_text_weak,
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_handwriting_to_text_nor
                        )
                ),
                isChecked =  (bottomMenuType == MenuBar.HANDWRITING_TO_TEXT)&& !drawBoardViewModel.isHWTTOn,
                btnSize = dimens.btnSize,
                contentDescription = R.string.edit_menu_handwriting_to_text.stringRes(),
                onClick = {
                    bottomMenuType = if (bottomMenuType == MenuBar.HANDWRITING_TO_TEXT) MenuBar.NONE else MenuBar.HANDWRITING_TO_TEXT
                    richtextViewModel.handleIntent(
                        RichTextIntent.UpdateMenuType(
                            bottomMenuType
                        )
                    )
                    focusManager.clearFocus()
                }
            )

            Spacer(modifier = Modifier.width(18.dp))
            // 中间的竖线
            VerticalLine(
                modifier = Modifier.padding(vertical = 9.dp)
            )
            Spacer(modifier = Modifier.width(18.dp))

            // 颜色选择器0
            TabletMenuColorCircle(
                btnSize = dimens.btnSize,
                selectedColor = selectedColor,
                isSelected = isPenMode && currentSelectedColorIdx == 0
            ) {
                colorClick(0)
            }

            Spacer(modifier = Modifier.width(12.dp))

            // 颜色选择器1
            TabletMenuColorCircle(
                modifier = Modifier.onGloballyPositioned {
                    val position = it.localToWindow(Offset.Zero)
                    colorIconPosition = position
                },
                btnSize = dimens.btnSize,
                selectedColor = selectedColor1,
                isSelected = isPenMode && currentSelectedColorIdx == 1
            ) {
                colorClick(1)
            }

            Spacer(modifier = Modifier.width(12.dp))

            // 颜色选择器2
            TabletMenuColorCircle(
                btnSize = dimens.btnSize,
                selectedColor = selectedColor2,
                isSelected = isPenMode && currentSelectedColorIdx == 2
            ) {
                colorClick(2)
            }

            Spacer(modifier = Modifier.width(18.dp))
            // 中间的竖线
            VerticalLine(
                modifier = Modifier.padding(vertical = 9.dp)
            )
            Spacer(modifier = Modifier.width(18.dp))

            // 假设左侧有3个图标
            // 待办事项
/*            DelayedBackgroundIconButton(
                btnSize = dimens.btnSize,
                painter = painterResource(com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_todo_nor),
                isChecked = (state.currentParagraphType == ParagraphType.TODO_ITEM),
                contentDescription = stringResource(R.string.menu_add_to_do),
                onClick = {
                    val isTodoSelected =
                        state.currentFocusedType is EditorContent.TodoBlock
                    val newType = if (isTodoSelected) "" else MenuTypes.TODO
                    bottomMenuType =
                        if (bottomMenuType == MenuTypes.TODO || isTodoSelected) {
                            ""
                        } else {
                            MenuTypes.TODO
                        }
                    if (state.currentParagraphType != ParagraphType.TODO_ITEM) {
                        viewModel.handleIntent(
                            RichTextIntent.UpdateCurrentParagraphType(
                                ParagraphType.TODO_ITEM
                            )
                        )
                    } else {
                        viewModel.handleIntent(
                            RichTextIntent.UpdateCurrentParagraphType(
                                ParagraphType.TEXT
                            )
                        )
                    }
                    viewModel.handleIntent(
                        RichTextIntent.UpdateBottomMenuType(
                            bottomMenuType
                        )
                    )
                    onTodo(newType)
                    drawBoardViewModel.goneDrawer()
                }
            )


            Spacer(modifier = Modifier.width(12.dp))
            // 字体样式
            DelayedBackgroundIconButton(
                modifier = Modifier
                    .onGloballyPositioned { layoutCoordinates ->
                        val position = layoutCoordinates.localToWindow(Offset.Zero)
                        fontIconPosition = position
                    },
                btnSize = dimens.btnSize,
                painter = painterResource(
                    (bottomMenuType == MenuTypes.FONT && drawBoardViewModel.isShowFontDrawer).judge(
                        com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_font_weak,
                        com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_font_nor
                    )
                ),
                isChecked = (state.currentParagraphType == ParagraphType.NUMBERED_LIST
                        || state.currentParagraphType == ParagraphType.BULLETED_LIST
                        || state.isBoldActive
                        || state.isItalicActive
                        || state.isUnderlineActive
                        ) && !drawBoardViewModel.isShowFontDrawer,
                contentDescription = stringResource(R.string.menu_set_format),
                onClick = {
                    bottomMenuType = MenuTypes.FONT

                    viewModel.handleIntent(
                        RichTextIntent.UpdateBottomMenuType(
                            bottomMenuType
                        )
                    )
                    drawBoardViewModel.transformFontDrawerVisible()

                }
            )
            Spacer(modifier = Modifier.width(12.dp))
            // 皮肤
            IconThemeSwitcher(
                modifier = Modifier
                    .onGloballyPositioned { layoutCoordinates ->
                        val position = layoutCoordinates.localToWindow(Offset.Zero)
                        skinIconPosition = position

                    },
                btnSize = dimens.btnSize,
                painter = painterResource(
                    (bottomMenuType == MenuTypes.SKIN && drawBoardViewModel.isShowSkinDrawer)
                        .judge(
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_skin_weak,
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_skin_nor
                        )
                ),
                contentDescription = stringResource(R.string.menu_skin),
                onClick = {
                    bottomMenuType = MenuTypes.SKIN
                    drawBoardViewModel.transformSkinDrawerVisible()
                    viewModel.handleIntent(
                        RichTextIntent.UpdateBottomMenuType(
                            bottomMenuType
                        )
                    )
                    focusManager.clearFocus()
                }
            )
            Spacer(modifier = Modifier.width(12.dp))
            // 插入图片按钮
            IconThemeSwitcher(
                btnSize = dimens.btnSize,
                painter = painterResource(R.drawable.ic_edit_picture),
                contentDescription = stringResource(R.string.menu_image),
                onClick = {
                    bottomMenuType =
                        if (bottomMenuType == MenuTypes.PICTURE) "" else MenuTypes.PICTURE
                    viewModel.handleIntent(
                        RichTextIntent.UpdateBottomMenuType(
                            bottomMenuType
                        )
                    )
                    drawBoardViewModel.goneDrawer()
                }
            )

            Spacer(modifier = Modifier.width(12.dp))

            // 插入语音
            AudioView(
                btnSize = dimens.btnSize,
                painterNor = painterResource(com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_recording_start),
                painterEnable = painterResource(com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_recording_stop),
                onAudioClick = { audioPath, isAdd ->
                    onAudioClick(audioPath, isAdd)
                    drawBoardViewModel.goneDrawer()
                }
            )

            Spacer(modifier = Modifier.width(12.dp))*/
            HoverProofIconButton(
                modifier = Modifier.size(dimens.btnSize)
                    .onGloballyPositioned { layoutCoordinates ->
                    val position = layoutCoordinates.localToWindow(Offset.Zero)
                    aiIconPosition = position

                },
                onClick = {
                    bottomMenuType =
                        if (bottomMenuType == MenuBar.AI) MenuBar.NONE else MenuBar.AI
                    richtextViewModel.handleIntent(
                        RichTextIntent.UpdateMenuType(
                            bottomMenuType
                        )
                    )
                }
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_ai_icon),
                    contentDescription = R.string.edit_bottom_menu_ai_assistant.stringRes(),
                    modifier = Modifier
                        .size(20.dp)
                        .align(Alignment.CenterVertically) // 垂直居中
                )
            }

            Spacer(modifier = Modifier.width(12.dp))

            // 笔迹美化
            DelayedBackgroundIconButton(
                modifier = Modifier
                    .onGloballyPositioned {
                        val position = it.localToWindow(Offset.Zero)
                        beautifyIconPosition = position
                    },
                painter = painterResource(
                    (bottomMenuType == MenuBar.BEAUTIFY && drawBoardViewModel.isBeautifyOn)
                        .judge(
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_beautify_weak,
                            com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_beautify_nor
                        )
                ),
                isChecked =  (bottomMenuType == MenuBar.BEAUTIFY)&& !drawBoardViewModel.isBeautifyOn,
                btnSize = dimens.btnSize,
                contentDescription = R.string.edit_menu_beautify.stringRes(),
                onClick = {
                    bottomMenuType = if (bottomMenuType == MenuBar.BEAUTIFY) MenuBar.NONE else MenuBar.BEAUTIFY
                    richtextViewModel.handleIntent(
                        RichTextIntent.UpdateMenuType(
                            bottomMenuType
                        )
                    )
                    focusManager.clearFocus()
                }
            )

            Spacer(modifier = Modifier.width(12.dp))

            DelayedBackgroundIconButton(
                btnSize = dimens.btnSize,
                painter = painterResource(
                    com.tcl.ai.note.handwritingtext.R.drawable.ic_tablet_menu_pan_only_disable
                ),
                isChecked = drawBoardViewModel.panOnly,
                onClick = {
                    drawBoardViewModel.panOnly = !drawBoardViewModel.panOnly
                    drawBoardViewModel.goneDrawer()
                },
                contentDescription = drawBoardViewModel.panOnly.judge(
                    R.string.btn_open_hand_drawn_mode,
                    R.string.btn_close_hand_drawn_mode

                ).stringRes()
            )
        }
    }

    // 字体样式菜单栏
    if (bottomMenuType == MenuBar.KEYBOARD && drawBoardViewModel.isShowFontDrawer) {
        val iconSize = 20.dp
        val popupWidth = 270.dp

        val xOffset = with(density) {
            if (layoutDirection == LayoutDirection.Rtl) {
                (screenWidthDp.dp.toPx() - fontIconPosition.x - iconSize.toPx() / 2 - popupWidth.toPx() / 2).toInt()
            } else {
                (fontIconPosition.x + iconSize.toPx() / 2 - popupWidth.toPx() / 2).toInt()
            }

        }

        val yOffset = with(density) {
            (fontIconPosition.y + 48.dp.toPx()).toInt()
        }

        Popup(
            alignment = Alignment.TopStart,
            offset = IntOffset(xOffset, yOffset),

            ) {
            Box(
                modifier = Modifier
                    .width(popupWidth)
                    .defShadow()
            ) {
                Row(
                    modifier = Modifier
                        .width(270.dp)
                        .background(colorResource(R.color.bg_dialog))
                        .horizontalScroll(scrollState)
                        .padding(horizontal = 12.dp, vertical = 6.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        modifier = Modifier
                            .width(81.dp)
                            .height(30.dp)
                            .border(
                                width = 1.dp, // 边框宽度
                                color = colorResource(R.color.edit_border_menu), // 边框颜色
                                shape = RoundedCornerShape(5.dp) // 圆角大小
                            )
                            .background(
                                color = colorResource(R.color.bg_dialog),
                                shape = RoundedCornerShape(5.dp) // 圆角大小，与边框一致
                            ),
                        verticalAlignment = Alignment.CenterVertically
                    )
                    {
                        // 无序列表
                        HoverProofIconButton(
                            modifier = Modifier
                                .width(42.37.dp)
                                .background(
                                    if (state.currentParagraphType == ParagraphType.BULLETED_LIST) {
                                        colorResource(R.color.text_category_list_selected)
                                    } else {
                                        colorResource(R.color.bg_dialog)
                                    },
                                    shape = RoundedCornerShape(topStart = 5.dp, bottomStart = 5.dp)
                                )
                                .fillMaxHeight(), onClick = {
                                val newStyle = when (state.currentParagraphStyle) {
                                    ParagraphStyle.BULLETED ->
                                        ParagraphStyle.NONE

                                    else ->
                                        ParagraphStyle.BULLETED
                                }
                                val newType = when (state.currentParagraphType) {
                                    ParagraphType.BULLETED_LIST ->
                                        ParagraphType.TEXT

                                    else -> ParagraphType.BULLETED_LIST
                                }
                                richtextViewModel.handleIntent(
                                    RichTextIntent.UpdateCurrentParagraphStyle(
                                        newStyle
                                    )
                                )
                                richtextViewModel.handleIntent(
                                    RichTextIntent.UpdateCurrentParagraphType(
                                        newType
                                    )
                                )
                                state.focusedIndex.let { index ->
                                    richtextViewModel.handleIntent(
                                        RichTextIntent.ToggleParagraphStyle(
                                            index,
                                            ParagraphStyle.BULLETED
                                        )
                                    )
                                }
                            }) {
                            Image(
                                painter = painterResource(
                                    if (state.currentParagraphType == ParagraphType.BULLETED_LIST)
                                        R.drawable.ic_edit_list_bulleted_selected
                                    else
                                        R.drawable.ic_edit_list_bulleted
                                ),
                                contentDescription = stringResource(R.string.bulleted_list)
                            )
                        }
                        // 有序列表
                        HoverProofIconButton(
                            modifier = Modifier
                                .width(42.37.dp)
                                .background(
                                    if (state.currentParagraphType == ParagraphType.NUMBERED_LIST) {
                                        colorResource(R.color.text_category_list_selected)
                                    } else {
                                        colorResource(R.color.bg_dialog)
                                    },
                                    shape = RoundedCornerShape(topEnd = 5.dp, bottomEnd = 5.dp)
                                )
                                .fillMaxHeight(), onClick = {
                                val newStyle = when (state.currentParagraphStyle) {
                                    ParagraphStyle.NUMBERED ->
                                        ParagraphStyle.NONE

                                    else ->
                                        ParagraphStyle.NUMBERED
                                }
                                val newType = when (state.currentParagraphType) {
                                    ParagraphType.NUMBERED_LIST ->
                                        ParagraphType.TEXT

                                    else -> ParagraphType.NUMBERED_LIST
                                }
                                richtextViewModel.handleIntent(
                                    RichTextIntent.UpdateCurrentParagraphStyle(
                                        newStyle
                                    )
                                )
                                richtextViewModel.handleIntent(
                                    RichTextIntent.UpdateCurrentParagraphType(
                                        newType
                                    )
                                )
                                state.focusedIndex.let { index ->
                                    richtextViewModel.handleIntent(
                                        RichTextIntent.ToggleParagraphStyle(
                                            index,
                                            ParagraphStyle.NUMBERED
                                        )
                                    )
                                }

                            }) {
                            Image(
                                painter = painterResource(
                                    if (state.currentParagraphType == ParagraphType.NUMBERED_LIST)
                                        R.drawable.ic_edit_list_numbered_selected
                                    else
                                        R.drawable.ic_edit_list_numbered
                                ),
                                contentDescription = stringResource(R.string.numbered_list)
                            )
                        }
                    }
                    Spacer(modifier = Modifier.weight(1f))
                    Row {
                        // 粗体

                        TabletFormatButton(
                            painter = painterResource(id = if (state.isBoldActive) R.drawable.ic_edit_bold_selected else R.drawable.ic_edit_bold),
                            contentDescription = stringResource(R.string.font_bold)
                        ) {
                            richtextViewModel.handleIntent(RichTextIntent.UpdateBoldActive(!state.isBoldActive))
                            richtextViewModel.toggleBold()
                            richtextViewModel.applyCurrentStyleToSelection()
                        }

                        Spacer(modifier = Modifier.width(3.dp))
                        // 下划线
                        TabletFormatButton(
                            painter = painterResource(id = if (state.isUnderlineActive) R.drawable.ic_edit_underline_selected else R.drawable.ic_edit_underline),
                            contentDescription = stringResource(R.string.font_underline)
                        ) {
                            richtextViewModel.handleIntent(RichTextIntent.UpdateUnderlineActive(!state.isUnderlineActive))
                            richtextViewModel.toggleUnderline()
                            richtextViewModel.applyCurrentStyleToSelection()
                        }

                        Spacer(modifier = Modifier.width(3.dp))
                        // 斜体
                        TabletFormatButton(
                            painter = painterResource(id = if (state.isItalicActive) R.drawable.ic_edit_italic_selected else R.drawable.ic_edit_italic),
                            contentDescription = stringResource(R.string.font_italic)
                        ) {
                            richtextViewModel.handleIntent(RichTextIntent.UpdateItalicActive(!state.isItalicActive))
                            richtextViewModel.toggleItalic()
                            richtextViewModel.applyCurrentStyleToSelection()
                        }

                    }
                }
            }
        }
    }

    // 画笔功能菜单
    if (bottomMenuType == MenuBar.BRUSH) {
        when (brushMenuType) {
            BrushMenu.PEN -> {
                val popupWidth = 270.dp

                // 计算居中偏移
                val xOffset = with(density) {
                    if (layoutDirection == LayoutDirection.Rtl) {
                        (screenWidthDp.dp.toPx() - penIconPosition.x - dimens.btnSize.toPx / 2 - popupWidth.toPx / 2).toInt()
                    } else {
                        (penIconPosition.x + dimens.btnSize.toPx / 2 - popupWidth.toPx() / 2).toInt()
                    }

                }

                val yOffset = with(density) {

                    // 在钢笔图标底部下方14dp显示弹窗
                    (penIconPosition.y + (dimens.menuBarHeight + 8.dp).toPx()).toInt()
                }
                if (drawBoardViewModel.isShowPenDrawer) {
                    Popup(
                        alignment = Alignment.TopStart,
                        offset = IntOffset(xOffset, yOffset),
                    ) {
                        Box(
                            modifier = Modifier
                                .background(
                                    color = Color.Transparent,
                                    shape = RoundedCornerShape(8.dp)
                                )
                                .width(dimensionResource(com.tcl.ai.note.handwritingtext.R.dimen.table_popup_width))
                                .defShadow()
                        ) {
                            TabletBrushControl(
                                selDoodlePen = selDoodlePen,
                                onPanChange = { doodlePen ->
                                    with(drawBoardViewModel) {
                                        sendIntent(
                                            DrawBoardIntent.ChangeStrokeStyle(
                                                strokeStyle.copy(
                                                    width = brushSliderModel.getCurPenWidth(
                                                        doodlePen
                                                    ),
                                                    color = selDoodlePen.color,
                                                    drawMode = DrawMode.PEN,
                                                    doodlePen = doodlePen
                                                )
                                            )

                                        )
                                    }
                                }
                            )
                        }
                    }
                }


            }

            BrushMenu.ERASER -> {
                // 橡皮擦线条粗细控制器

                val offx =
                    (eraserIconPosition.x + dimens.btnSize.toPx / 2 - dimens.popupWidth.toPx / 2).toInt()
                var xOffset by remember { mutableStateOf(offx) }
                var eraserWidth by remember { mutableStateOf(0) }
                val yOffset = with(density) {
                    (eraserIconPosition.y + (dimens.menuBarHeight).toPx()).toInt()
                }

                xOffset = with(density) {
                    if (layoutDirection == LayoutDirection.Rtl) {
                        (screenWidthDp.dp.toPx() - eraserIconPosition.x - dimens.btnSize.toPx / 2 - eraserWidth / 2).toInt()
                    } else {
                        (eraserIconPosition.x + dimens.btnSize.toPx / 2 - eraserWidth / 2).toInt()
                    }
                }
                if (drawBoardViewModel.isShowEraserDrawer) {
                    Popup(
                        alignment = Alignment.TopStart,
                        offset = IntOffset(xOffset, yOffset),
                    ) {
                        TabletEraserWidthSlider{ width: Int ->
                            eraserWidth =width
                        }
                    }
                }

            }

            BrushMenu.COLOR_SELECTOR -> {
                // 颜色选择器
                val popupWidth = dimens.colorPaletteWidth

                val xOffset = with(density) {
                    if (layoutDirection == LayoutDirection.Rtl) {
                        (screenWidthDp.dp.toPx() - colorIconPosition.x - dimens.btnSize.toPx / 2 - popupWidth.toPx() / 2).toInt()
                    } else {
                        (colorIconPosition.x + dimens.btnSize.toPx / 2 - popupWidth.toPx() / 2).toInt()
                    }

                }

                val yOffset = with(density) {
                    (colorIconPosition.y + (dimens.menuBarHeight + 8.dp).toPx()).toInt()
                }
                if (drawBoardViewModel.isShowColorDrawer) {
                    Popup(
                        alignment = Alignment.TopStart,
                        onDismissRequest = {
                            drawBoardViewModel.goneDrawer()
                        },
                        offset = IntOffset(xOffset, yOffset)
                    ) {
                        Box(
                            modifier = Modifier
                                .background(
                                    color = Color.Transparent,
                                    shape = RoundedCornerShape(8.dp)
                                )
                                .width(popupWidth)
                                .defShadow()
                        ) {
                            TabletColorSelector(drawBoardViewModel) {
                                selDoodlePen.color = it.toArgbLong()
                            }

                        }
                    }
                } else {
                    brushMenuType = BrushMenu.PEN
                    richtextViewModel.handleIntent(RichTextIntent.UpdateBrushMenuType(brushMenuType))
                }
            }
        }
    }

    if (drawBoardViewModel.isShowSkinDrawer) {
    //if (bottomMenuType == MenuBar.SKIN && drawBoardViewModel.isShowSkinDrawer) {
        // 皮肤选择器
        val iconSize = 20.dp
        val popupWidth = 360.dp
        val topMargin = 8.dp
        val xOffset = with(density) {
            if (layoutDirection == LayoutDirection.Rtl) {
                (screenWidthDp.dp.toPx() - skinIconPosition.x - iconSize.toPx() / 2 - popupWidth.toPx() / 2).toInt()
            } else {
                (skinIconPosition.x + iconSize.toPx() / 2 - popupWidth.toPx() / 2).toInt()
            }

        }

        val yOffset = with(density) {
            (skinIconPosition.y + (dimens.menuBarHeight + topMargin).toPx()).toInt()
        }

        Popup(
            alignment = Alignment.TopStart,
            offset = IntOffset(xOffset, yOffset)
        ) {
            Box(
                modifier = Modifier
                    .width(popupWidth)
                    .background(
                        color = Color.Transparent,
                        shape = CornerShapeMenuUpPop
                    )
                    .defShadow()
            ) {
                TabletSkinControl(
                    skinViewModel = hiltViewModel<SkinViewModel>().apply {
                        updateBgMode(bgMode = state.bgMode)
                        updateBgColor(bgColor = state.bgColor.toComposeColor())
                    }
                ) { skinModel ->
                    richtextViewModel.handleIntent(
                        RichTextIntent.UpdateSkinStyle(
                            skinModel.bgMode,
                            skinModel.color
                        )
                    )
                    richtextViewModel.handleIntent(RichTextIntent.RequestSave)
                }
            }
        }

    }

    // AI菜单栏
    if (isShowBottomAIPop) {
        val iconSize = 20.dp
        val popupWidth = 196.dp

        val xOffset = with(density) {
            (aiIconPosition.x + iconSize.toPx() / 2 - popupWidth.toPx() / 2).toInt()
        }

        val yOffset = with(density) {
            (aiIconPosition.y + 48.dp.toPx()).toInt()
        }
        TabletEditBottomAIMenuPop(
            xOffset = xOffset,
            yOffset = yOffset,
            onDismissRequest = {
                richtextViewModel.closeBottomAIPop()
            }, onUpdateBottomMenuType = { bottomMenuType ->
                richtextViewModel.handleIntent(
                    RichTextIntent.UpdateMenuType(
                        bottomMenuType
                    )
                )
            }, onBottomClick
        )
    }
/*    if (bottomMenuType == MenuBar.PICTURE) {
        ImageAddDialog(
            onSelected = {
                if (it == PictureSource.PICK_IMAGE) {
                    onPickImage()
                } else {
                    onTakePhoto()
                }
                bottomMenuType = MenuBar.NONE
                richtextViewModel.handleIntent(
                    RichTextIntent.UpdateMenuType(
                        bottomMenuType
                    )
                )
            },
            onCancel = {
                bottomMenuType = MenuBar.NONE
                richtextViewModel.handleIntent(
                    RichTextIntent.UpdateMenuType(
                        bottomMenuType
                    )
                )
            }
        )
    }*/
}

@Composable
internal fun TabletFormatButton(
    width: Dp = 48.dp,
    height: Dp = 30.dp,
    modifier: Modifier = Modifier,
    painter: Painter,
    contentDescription: String,
    onClick: () -> Unit,
) {
    FormatButton(
        width = width,
        height = height,
        modifier = modifier,
        painter = painter,
        contentDescription = contentDescription,
        onClick = onClick
    )
}


private const val TABLET_COLOR_SELECTOR_COUNT = 3