package com.tcl.ai.note.handwritingtext.ui.richtext.widget

import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import com.tcl.ai.note.widget.HoverProofIconButton
import com.tcl.ai.note.base.R

@Composable
fun BackButton(
    onClick: () -> Unit,
    btnSize: Dp,
    iconSize: Dp,
    modifier: Modifier = Modifier
) {
    HoverProofIconButton(
        modifier = modifier.size(btnSize),
        rippleSize = btnSize,
        onClick = onClick,
    ) {
        Icon(
            modifier = modifier.size(iconSize),
            painter = painterResource(id = R.drawable.ic_menu_back),
            contentDescription = stringResource(R.string.edit_top_menu_back_icon)
        )
    }
}

@Composable
fun ImportButton(
    onClick: () -> Unit,
    btnSize: Dp,
    iconSize: Dp,
    modifier: Modifier = Modifier
) {
    HoverProofIconButton(
        modifier = modifier.size(btnSize),
        rippleSize = btnSize,
        onClick = onClick,
    ) {
        Icon(
            modifier = modifier.size(iconSize),
            painter = painterResource(id = R.drawable.ic_navigator_import),
            contentDescription = stringResource(R.string.navigation_import_button)
        )
    }
}

@Composable
fun MoreButton(
    onClick: () -> Unit,
    btnSize: Dp,
    iconSize: Dp,
    modifier: Modifier = Modifier
) {
    HoverProofIconButton(
        modifier = modifier.size(btnSize),
        rippleSize = btnSize,
        onClick = onClick,
    ) {
        Icon(
            modifier = modifier.size(iconSize),
            painter = painterResource(id = R.drawable.ic_navigator_more),
            contentDescription = stringResource(R.string.navigation_more_button)
        )
    }
} 