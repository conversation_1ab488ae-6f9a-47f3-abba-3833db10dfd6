package com.tcl.ai.note.handwritingtext.ui.swatches

import android.util.Log
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateIntOffsetAsState
import androidx.compose.animation.core.spring
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.handwritingtext.R
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.drawableRes
import com.tcl.ai.note.widget.LongPressIconButton
import kotlinx.coroutines.delay
import kotlin.math.ceil
import kotlin.math.min
import kotlin.math.roundToInt

@Composable
internal fun AlphaSliderWithControls(
    modifier: Modifier = Modifier,
    baseColor: Color,
    alphaValue: Int,
    onAlphaChanged: (Int) -> Unit,

) {
    val context = LocalContext.current
    var isLongPressingDecrease by remember { mutableStateOf(false) }
    var isLongPressingIncrease by remember { mutableStateOf(false) }


    val sliderHeight = 20.dp
    val handleSize = 17.dp

    var dragAlpha by remember { mutableIntStateOf(alphaValue) }
    val increaseAlpha:() -> Unit = {
        dragAlpha = (dragAlpha + 1).coerceAtMost(100)
        onAlphaChanged(dragAlpha)


    }
    val decreaseAlpha:() -> Unit = {
        dragAlpha = (dragAlpha - 1).coerceAtLeast(1)
        onAlphaChanged(dragAlpha)

    }


    LaunchedEffect(isLongPressingDecrease) {
        while (isLongPressingDecrease) {
            decreaseAlpha()
            delay(100)
        }
    }
    LaunchedEffect(isLongPressingIncrease) {
        while (isLongPressingIncrease) {
            increaseAlpha()
            delay(100)
        }
    }

    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ){
        LongPressIconButton(
            onLongPress = { isLongPressing ->
                isLongPressingDecrease = isLongPressing
            },
            onClick = {
                decreaseAlpha()
            },
            modifier = Modifier.size(32.dp)
            .clearAndSetSemantics {
                role = Role.Button
                contentDescription = context.getString(com.tcl.ai.note.base.R.string.color_alpha_size_less)
            },
        ) {
            Icon(
                painter = R.drawable.ic_value_decrease.drawableRes(),
                contentDescription = ""
            )
        }
        Box(
            modifier = Modifier
                .height(sliderHeight)
                .width(160.dp)
                .clearAndSetSemantics {
                    val desc =String.format(context.getString(com.tcl.ai.note.base.R.string.color_alpha_slider_status),"${dragAlpha}%")
                    contentDescription= desc
                }

        ){
            CheckerboardTexture(
                modifier = Modifier.fillMaxWidth()
                    .height(sliderHeight)
                    .align(Alignment.Center)
            )
            AlphaGradientCanvas(
                modifier = Modifier.fillMaxWidth()
                    .padding(0.5.dp)
                    .height(sliderHeight)
                    .align(Alignment.Center),
                color = baseColor
            )

            SliderHandle(
                modifier =  Modifier.matchParentSize(),
                handleSize =handleSize,
                sliderHeight = sliderHeight,
                color = baseColor,
                dragAlpha = dragAlpha
            ){ alpha ->
                dragAlpha = alpha
                onAlphaChanged(alpha)
            }

        }
        LongPressIconButton(
            onLongPress = { isLongPressing ->
                isLongPressingIncrease = isLongPressing
            },
            onClick = {
                increaseAlpha()
            },
            modifier = Modifier.size(32.dp)
                .clearAndSetSemantics {
                    role = Role.Button
                    contentDescription = context.getString(com.tcl.ai.note.base.R.string.color_alpha_size_add)
                }
        ) {
            Icon(
                painter = R.drawable.ic_value_increase.drawableRes(),
                contentDescription = ""
            )
        }
        AlphaIndicator(
            modifier = Modifier
                .width(36.dp)
                .height(24.dp),
            alpha = dragAlpha
        )

    }


}
@Composable
fun AlphaIndicator(
    modifier: Modifier,
    alpha: Int
){
    val density = LocalDensity.current
    Box(
        modifier = modifier
            .border(
                width = 1.dp,
                color = R.color.slider_indicator_border.colorRes(),
                shape = RoundedCornerShape(4.dp)
            ),
        contentAlignment = Alignment.Center
    ){
        Text(
            text = buildAnnotatedString {

                append("${alpha}")
                withStyle(style = SpanStyle(fontWeight = FontWeight.Medium, fontSize = 8.sp)) {
                    append("%")
                }
            },
            textAlign = TextAlign.Center,
            color = R.color.slider_indicator.colorRes(),
            fontSize = with(density) { 12.dp.toSp() },
            fontWeight = FontWeight.Medium,
            lineHeight = with(density) { 14.dp.toSp() },
        )
    }
}

@Composable
fun SliderHandle(
    modifier: Modifier,
    handleSize: Dp,
    sliderHeight: Dp,
    color: Color,
    dragAlpha:Int,
    onAlphaChanged: (Int) -> Unit,
){
    var sliderWidth by remember { mutableIntStateOf(0) }
    val density = LocalDensity.current
    val sliderWidthPx = with(density) {
        (sliderWidth - handleSize.toPx())
    }
    val handleOffsetY = with(density) { (sliderHeight - handleSize).toPx() }

    val handleOffsetX = (dragAlpha-1) / 99f * sliderWidthPx

    Box(
        modifier =modifier
            .padding(horizontal = 1.dp)
            .onSizeChanged { size ->
                sliderWidth =size.width
            }
            .pointerInput(Unit) {
                awaitEachGesture {
                    while (true) {
                        val event = awaitPointerEvent()
                        val finger = event.changes.firstOrNull() ?: break
                        if (!finger.pressed) break

                        val pos = finger.position
                        val x = pos.x.coerceIn(0f, sliderWidthPx)
                        val ratio = (x / sliderWidthPx).coerceIn(0.01f, 1f)
                        onAlphaChanged( (ratio * 100).roundToInt())

                    }
                }
            }
    ){
        if(handleOffsetX>=0){
            val animatedOffset by animateIntOffsetAsState(
                targetValue = IntOffset(
                    x = handleOffsetX.roundToInt(),
                    y = handleOffsetY.div(2).roundToInt()
                ),
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioLowBouncy,
                    stiffness = Spring.StiffnessMedium
                ),
                label = "handle_offset_animation"
            )

            Box(
                Modifier
                    .offset {
                        animatedOffset
                    }
                    .size(handleSize)
                    .clip(CircleShape)
                    .border(
                        width = 1.5.dp,
                        color = R.color.color_check_box.colorRes().copy(alpha = dragAlpha/100f),
                        shape = CircleShape
                    )
                    .background(color = R.color.slider_handle_bg.colorRes())

            ) {
                Box(
                    modifier = Modifier
                        .size(14.dp)
                        .align(Alignment.Center)
                        .background(color = color.copy(alpha = dragAlpha/100f), shape = CircleShape)
                )
            }
        }

    }


}

@Composable
internal fun AlphaGradientCanvas(
    modifier: Modifier,
    color: Color
){
    Canvas(modifier = modifier
        .height(20.dp)
        .clip(RoundedCornerShape(10.dp))) {
        drawRect(
            brush = Brush.horizontalGradient(
                listOf(
                    color.copy(alpha = 0f),
                    color.copy(alpha = 1f)
                ),
                startX = 0f,
                endX = size.width
            ),
            size = size
        )
    }
}

@Composable
internal fun CheckerboardTexture(
    modifier: Modifier,
){
    val color1 = R.color.checkerboard_cell1.colorRes()
    val color2 =R.color.checkerboard_cell2.colorRes()
    val borderLineColor = R.color.slider_border_line.colorRes()
    Canvas(modifier = modifier
        .border(
            width = 0.5.dp,
            color = borderLineColor,
            shape = RoundedCornerShape(10.dp)
        )
        .clip(RoundedCornerShape(10.dp))){
        val squareSize = 6.5.dp.toPx()
        val rows = ceil(size.height / squareSize).toInt()
        val cols = ceil(size.width / squareSize).toInt()


        for (row in 0 until rows) {
            for (col in 0 until cols) {
                val isLight = (row + col) % 2 == 0
                drawRect(
                    color = if (isLight) color1 else color2,
                    topLeft = Offset(col * squareSize, row * squareSize),
                    size = Size(
                        width = min(squareSize, size.width - col * squareSize),
                        height = min(squareSize, size.height - row * squareSize)
                    )
                )
            }
        }
    }
}