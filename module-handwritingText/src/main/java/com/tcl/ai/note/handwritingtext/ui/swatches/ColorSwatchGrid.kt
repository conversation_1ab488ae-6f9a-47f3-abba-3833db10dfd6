package com.tcl.ai.note.handwritingtext.ui.swatches


import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateIntOffsetAsState
import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.dp2px
import com.tcl.ai.note.utils.judge
import kotlin.math.abs


private const val MAX_COLUMNS = 13
private const val MAX_ROWS = 11


/**
 *色谱渐变排列网格列表
 * HSL插值
 */
@Composable
fun ColorSwatchGrid(
    modifier: Modifier = Modifier,
    selColor: Color,
    onClick: (color: Color) -> Unit
){
    val selectBoxSize = 24.dp
    var intOffset by remember {
        mutableStateOf<IntOffset?>(null)
    }
    var selctedColor by remember(selColor) {
        mutableStateOf(selColor)
    }
    val padddingXdp =1
    val paddingYdp = 1
    val animatedOffset by animateIntOffsetAsState(
        targetValue = intOffset?: IntOffset.Zero,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioLowBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "offset_animation"
    )

    Box(
        modifier = modifier.fillMaxWidth().wrapContentHeight().padding(horizontal = 20.dp),
    ){
        MColorSwatchGrid(
            modifier = Modifier.padding(horizontal = padddingXdp.dp,vertical = paddingYdp.dp).width(286.dp).height(242.dp),
            selColor = selColor,
            onIdCenter = { center ->
                center?.let {
                    intOffset = IntOffset(
                        center.first + (padddingXdp - selectBoxSize.value / 2).dp2px,
                        center.second+(padddingXdp - selectBoxSize.value / 2).dp2px
                    )
                }?:let {
                    intOffset = null
                }

            },
            onClick = { color ->
                onClick(color)
                selctedColor = color
            }
        )
        if(intOffset != null){
            Box(
                modifier = Modifier.size(selectBoxSize)
                    .offset {
                        animatedOffset
                    }
                    .shadow(
                        elevation = 4.dp,
                        shape = RoundedCornerShape(3.dp),
                        clip = false
                    )
                    .border(
                        width = 2.dp,
                        color = com.tcl.ai.note.handwritingtext.R.color.color_check_box.colorRes(),
                        shape = RoundedCornerShape(3.dp)
                    )
                    .background(
                        color = selctedColor,
                        shape = RoundedCornerShape(3.dp)
                    )


            )
        }

    }
}


@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
internal fun MColorSwatchGrid(
    modifier: Modifier = Modifier,
    cornerRadius: Dp = 12.dp,
    cellSizeDp:Int = 22,
    columns: Int = MAX_COLUMNS,
    saturationBoost: Float = 1f,
    selColor: Color,
    onIdCenter:(center:Pair<Int, Int>?) -> Unit,
    onClick: (color: Color) -> Unit
) {
    val colors = remember(saturationBoost) { createLerpColor(saturationBoost) }
    val rows = colors.chunked(columns)
    val cellPx = with(LocalDensity.current) { cellSizeDp.dp.roundToPx().toFloat() }
    val context = LocalContext.current
    val currentOnClick by rememberUpdatedState(onClick)


    val cellSizes = remember { mutableStateMapOf<Int, IntSize>() }
    BoxWithConstraints(modifier = modifier
        .pointerInput(colors, selColor) {
            awaitEachGesture {
                val widthPx = size.width.toFloat()
                val heightPx = size.height.toFloat()
                while (true) {
                    val event = awaitPointerEvent()
                    val finger = event.changes.firstOrNull() ?: break

                    if (!finger.pressed) break

                    val pos = finger.position
                    if (pos.isInBox(widthPx, heightPx)) {
                        val idx = pointToColorIdx(
                            pos.x.coerceIn(0f, widthPx),
                            pos.y.coerceIn(0f, heightPx),
                            cellPx,
                            columns,
                            colors.size
                        )
                        if (idx != null && colors[idx] != selColor) {
                            currentOnClick(colors[idx])
                        }
                    }

                }
            }
        }
        .wrapContentSize()
    ) {

        Column(
            modifier = Modifier.fillMaxSize()
        ){
            var isHasColor =false
            rows.forEachIndexed { rowIndex, rowColors ->
                Row(
                    modifier = Modifier.fillMaxWidth()
                        .weight(1f)
                ) {
                    rowColors.forEachIndexed { cellIndex, color ->
                        val hsl = color.toHslArray()
                        val desc = getColorDescription(context,hsl[0], hsl[1], hsl[2])
                        val idx = rowIndex * MAX_COLUMNS + cellIndex
                        val shape: Shape = when {
                            rowIndex == 0 && cellIndex == 0 -> RoundedCornerShape(topStart = cornerRadius)
                            rowIndex == 0 && cellIndex == columns - 1 -> RoundedCornerShape(topEnd = cornerRadius)
                            rowIndex == rows.lastIndex && cellIndex == 0 -> RoundedCornerShape(
                                bottomStart = cornerRadius
                            )

                            rowIndex == rows.lastIndex && cellIndex == columns - 1 -> RoundedCornerShape(
                                bottomEnd = cornerRadius
                            )

                            else -> RectangleShape
                        }
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxHeight()
                                .background(color = color, shape = shape)
                                .onSizeChanged { size ->
                                    cellSizes[idx] = size
                                }
                                .then(
                                    (rowIndex == 0 && cellIndex == 0).judge(
                                        Modifier.border(
                                            width = 1.dp,
                                            color =Color(0xffEBEBEB),
                                            shape = shape
                                        ),
                                        Modifier
                                    )
                                )
                                .semantics {
                                    contentDescription = desc
                                }

                        )
                        if(selColor == color){
                            if(cellSizes.containsKey(idx)){
                                onIdCenter(calculateCellCenter( idx,MAX_COLUMNS,cellSizes))
                                isHasColor=true
                            }

                        }
                    }
                }
            }
            if(!isHasColor){
                onIdCenter(null)
            }
        }
    }
}


fun calculateCellCenter(
    idx: Int,
    columns: Int,
    cellSizes: Map<Int, IntSize>
): Pair<Int, Int>? {
    val row = idx / columns
    val col = idx % columns

    //计算当前cell左侧所有cell宽度总和
    var x = 0
    for (c in 0 until col) {
        val leftIdx = row * columns + c
        x += cellSizes[leftIdx]?.width ?: return null
    }

    //cell上方所有cell高度总和
    var y = 0
    for (r in 0 until row) {
        val aboveIdx = r * columns + col
        y += cellSizes[aboveIdx]?.height ?: return null
    }

    val curSize = cellSizes[idx] ?: return null

    val centerX = x + curSize.width / 2
    val centerY = y + curSize.height / 2

    return Pair(centerX, centerY)
}


private fun Offset.isInBox(width: Float, height: Float): Boolean {
    return (x in 0f..width) || (y in 0f..height)
}



//坐标转换Id
private fun pointToColorIdx(
    x: Float,
    y: Float,
    cellPx: Float,
    columns: Int,
    total: Int
): Int? {
    val col = (x / cellPx).toInt().coerceIn(0, columns - 1)
    val row = (y / cellPx).toInt().coerceIn(0, MAX_ROWS)
    if (col < 0 || row < 0) return null
    val idx = row * columns + col
    return idx.takeIf { it in 0 until total }
}


fun createLerpColor(saturationBoost: Float = 1f): List<Color> {
    val colors = arrayListOf<Color>()
    val rows = MAX_ROWS
    val lerpsColor = listOf(
        Color(0xffFFFFFF) to Color(0xff000000),
        Color(0xffFFDBD8) to Color(0xff5C0701),
        Color(0xffFFE2D6) to Color(0xff5A1C00),
        Color(0xffFFECD4) to Color(0xff583300),
        Color(0xffFFF2D5) to Color(0xff563D00),
        Color(0xffFEFCDD) to Color(0xff666100),
        Color(0xffF7FADB) to Color(0xff4F5504),
        Color(0xffDFEED4) to Color(0xff263E0F),
        Color(0xffCBF0FF) to Color(0xff00374A),
        Color(0xffD3E2FF) to Color(0xff011D57),
        Color(0xffD9C9FE) to Color(0xff11053B),
        Color(0xffEFCAFF) to Color(0xff2E063D),
        Color(0xffF9D3E0) to Color(0xff3C071B)

    )


    repeat(rows) { i ->
        repeat(MAX_COLUMNS) { j ->
            val fraction = i / (rows - 1f)
            val color = lerpHslEnhance(
                lerpsColor[j].first,
                lerpsColor[j].second,
                fraction,
                saturationBoost
            )
            colors.add(color)
        }
    }
    return colors
}


// 将Color转为HSL
fun Color.toHslArray(): FloatArray {
    val r = red
    val g = green
    val b = blue

    val max = maxOf(r, g, b)
    val min = minOf(r, g, b)
    val l = (max + min) / 2f

    var h = 0f
    var s = 0f

    if (max == min) {
        h = 0f
        s = 0f
    } else {
        val d = max - min
        s = if (l > 0.5f) d / (2f - max - min) else d / (max + min)

        h = when (max) {
            r -> (g - b) / d + (if (g < b) 6 else 0)
            g -> (b - r) / d + 2
            else -> (r - g) / d + 4
        }
        h /= 6f
    }
    return floatArrayOf(h * 360f, s, l)
}


private fun lerpHslEnhance(
    start: Color,
    stop: Color,
    @androidx.annotation.FloatRange(from = 0.0, to = 1.0) fraction: Float,
    saturationBoost: Float = 1f
): Color {
    val hslStart = start.toHslArray()
    val hslEnd = stop.toHslArray()


    // 360°环形插值
    var deltaH = hslEnd[0] - hslStart[0]
    if (abs(deltaH) > 180) {
        if (deltaH > 0) {
            hslStart[0] = hslStart[0] + 360
        } else {
            hslEnd[0] = hslEnd[0] + 360

        }
    }
    val h = (hslStart[0] + (hslEnd[0] - hslStart[0]) * fraction) % 360
    val s = (hslStart[1] + (hslEnd[1] - hslStart[1]) * fraction) * saturationBoost
    val l = hslStart[2] + (hslEnd[2] - hslStart[2]) * fraction
    val a = start.alpha + (stop.alpha - start.alpha) * fraction

    val finalS = s.coerceIn(0f, 1f)
    val finalL = l.coerceIn(0f, 1f)

    return Color.hsl(
        hue = h,
        saturation = finalS,
        lightness = finalL,
        alpha = a
    )
}



//无障碍颜色播报
fun getColorDescription(context: Context, h: Float, s: Float, l: Float): String {
    val colorName = when {
        h == 0.0f -> {
            when (l) {
                1.0f -> context.getString(R.string.color_swatch_white)
                in 0.45f..0.9099f -> context.getString(R.string.color_swatch_light_gray)
                in 0.18f..0.3648f -> context.getString(R.string.color_swatch_dark_gray)
                else -> context.getString(R.string.color_swatch_black)
            }
        }

        h in 18f..18.8765f -> {
            when {
                l <= 0.25f -> context.getString(R.string.color_swatch_dark_orange_red)
                l < 0.70f -> context.getString(R.string.color_swatch_orange_red)
                else -> context.getString(R.string.color_swatch_light_orange_red)
            }
        }

        h in 254.44f..258.889f -> {
            when {
                l <= 0.25f -> context.getString(R.string.color_swatch_dark_blue_violet)
                l < 0.70f -> context.getString(R.string.color_swatch_blue_violet)
                else -> context.getString(R.string.color_swatch_light_blue_violet)
            }
        }

        h >= 345f || h < 15f -> {
            when {
                l <= 0.25f -> context.getString(R.string.color_swatch_dark_red)
                l < 0.70f -> context.getString(R.string.color_swatch_red)
                else -> context.getString(R.string.color_swatch_light_red)
            }
        }

        h in 41f..43.2559f || h < 45f -> {
            when {
                l <= 0.25f -> context.getString(R.string.color_swatch_dark_orange)
                l < 0.70f -> context.getString(R.string.color_swatch_orange)
                else -> context.getString(R.string.color_swatch_light_orange)
            }
        }

        h in 63f..66.207f -> {
            when {
                l <= 0.25f -> context.getString(R.string.color_swatch_dark_yellow_green)
                l < 0.70f -> context.getString(R.string.color_swatch_yellow_green)
                else -> context.getString(R.string.color_swatch_light_yellow_green)
            }
        }

        h < 70f -> {
            when {
                l <= 0.25f -> context.getString(R.string.color_swatch_dark_yellow)
                l < 0.70f -> context.getString(R.string.color_swatch_yellow)
                else -> context.getString(R.string.color_swatch_light_yellow)
            }
        }

        h < 170f -> {
            when {
                l <= 0.25f -> context.getString(R.string.color_swatch_dark_green)
                l < 0.70f -> context.getString(R.string.color_swatch_green)
                else -> context.getString(R.string.color_swatch_light_green)
            }
        }

        h < 200f -> {
            when {
                l <= 0.25f -> context.getString(R.string.color_swatch_dark_cyan)
                l < 0.70f -> context.getString(R.string.color_swatch_cyan)
                else -> context.getString(R.string.color_swatch_light_cyan)
            }
        }

        h < 260f -> {
            when {
                l <= 0.25f -> context.getString(R.string.color_swatch_dark_blue)
                l < 0.70f -> context.getString(R.string.color_swatch_blue)
                else -> context.getString(R.string.color_swatch_light_blue)
            }
        }

        h < 320f -> {
            when {
                l <= 0.25f -> context.getString(R.string.color_swatch_dark_purple)
                l < 0.70f -> context.getString(R.string.color_swatch_purple)
                else -> context.getString(R.string.color_swatch_light_purple)
            }
        }

        h in 337f..339.474f -> {
            when {
                l <= 0.25f -> context.getString(R.string.color_swatch_dark_magenta)
                l < 0.70f -> context.getString(R.string.color_swatch_magenta)
                else -> context.getString(R.string.color_swatch_light_magenta)
            }
        }

        else -> context.getString(R.string.color_swatch_red)
    }
    return colorName
}