package com.tcl.ai.note.handwritingtext.ui.utils

import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp

/**
 * 键盘和导航栏相关的工具类
 * 提供键盘状态检测和偏移计算功能
 */
object KeyboardUtils {
    
    /**
     * 键盘状态数据类
     */
    data class KeyboardState(
        val isVisible: Boolean,
        val height: Dp,
        val navigationBarHeight: Dp,
        val bottomOffset: Dp
    )
    
    /**
     * 获取键盘状态的Composable函数
     * 
     * @return 键盘状态，包含可见性、高度和计算好的底部偏移量
     */
    @Composable
    fun rememberKeyboardState(): State<KeyboardState> {
        val density = LocalDensity.current
        val imeInsets = WindowInsets.ime
        val navigationInsets = WindowInsets.navigationBars
        
        return remember {
            derivedStateOf {
                val imeHeight = imeInsets.getBottom(density)
                val navigationHeight = navigationInsets.getBottom(density)
                val isKeyboardVisible = imeHeight > 0
                
                val imeHeightDp = with(density) { imeHeight.toDp() }
                val navigationHeightDp = with(density) { navigationHeight.toDp() }
                
                // 计算底部偏移量：键盘弹出时跟随键盘，否则在导航栏上方
                val bottomOffset = if (isKeyboardVisible) {
                    -imeHeightDp
                } else {
                    -navigationHeightDp
                }
                
                KeyboardState(
                    isVisible = isKeyboardVisible,
                    height = imeHeightDp,
                    navigationBarHeight = navigationHeightDp,
                    bottomOffset = bottomOffset
                )
            }
        }
    }
}

/**
 * 扩展函数：获取键盘状态
 * 提供更简洁的API
 */
@Composable
fun rememberKeyboardState(): State<KeyboardUtils.KeyboardState> {
    return KeyboardUtils.rememberKeyboardState()
} 