package com.tcl.ai.note.handwritingtext.ui.utils.scale;

/**
 * desc: Canvas mode
 *
 * <AUTHOR>
 */
public enum CanvasMode {
    /**
     * Default mode - Unlimited canvas
     */
    DEFAULT(0),

    /**
     * Horizontal mode - Horizontal slide
     */
    HORIZONTAL(1),

    /**
     * Vertical mode - Slide vertically
     */
    VERTICAL(2),

    /**
     * Fixed mode - Fixed size(visible size)
     */
    FIXED(3),

    /**
     * View port mode - View port
     */
    VIEW_PORT(4),

    /**
     * Fixed and view port mode - Fixed size and view port
     */
    FIXED_AND_VIEW_PORT(5);

    public int value;

    CanvasMode(int value) {
        this.value = value;
    }
}
