package com.tcl.ai.note.handwritingtext.ui.utils.scale;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.graphics.PointF;
import android.graphics.RectF;
import android.os.Handler;
import android.os.Looper;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.animation.DecelerateInterpolator;

import androidx.annotation.NonNull;

import androidx.annotation.Nullable;
import com.sunia.penengine.sdk.operate.canvas.ScaleInfo;
import com.sunia.singlepage.sdk.tools.EngineConfigs;
import com.sunia.singlepage.sdk.listener.ICanvasStateListener;
import com.sunia.singlepage.impl.utils.CommonUtils;
import com.sunia.singlepage.impl.utils.VLogUtil;
import com.tcl.ai.note.GlobalContext;
import com.tcl.ai.note.utils.Logger;
import kotlin.Deprecated;

/**
 * on 2023/09/26.
 * desc: scroll and scale handler
 *
 * <AUTHOR>
 */
@Deprecated(message = "废弃，由OffsetAndScaleHandler替换")
public class ScrollAndScaleHandler {
    private static final String TAG = "ScrollAndScaleHandler";
    /**
     * enable fling
     */
    private final boolean enableFling = true;

    /**
     * canvas mode
     */
    private int canvasMode = CanvasMode.DEFAULT.value;

    /**
     * canvas ratio
     */
    private float canvasWidth;

    /**
     * canvas height
     */
    private float canvasHeight;

    /**
     * max scale
     */
    private static float SCALE_MAX = 6f;

    /**
     * min scale
     */
    private static float SCALE_MIN = 0.6f;

    /**
     * visible width、height
     */
    private float visibleWidth, visibleHeight;

    /**
     * current scale
     */
    private float scale = 1f;

    /**
     * last scale
     */
    private float lastScale = 1f;

    /**
     * current offset x
     */
    private float currentOffsetX;

    /**
     * current offset y
     */
    private float currentOffsetY;

    /**
     * current scale info
     */
    private final ScaleInfo scaleInfo = new ScaleInfo();

    /**
     * last scale info
     */
    private final ScaleInfo lastScaleInfo = new ScaleInfo();

    /**
     * canvas scale listener
     */
    private ICanvasStateListener canvasScaleListener;

    /**
     * 滚动中
     */
    private boolean isScrolling;

    // 引擎配置
    private final EngineConfigs engineConfigs;

    private MainHandler handler;
    private final Object logTag;

    // 无限垂直画布，添加滚动限制
    private int verticalScrollLimit;

    public void setVerticalScrollLimit(int limit){
        verticalScrollLimit = limit;
    }

    private ScaleGestureDetector.OnScaleGestureListener scaleGestureListener = new ScaleGestureDetector.OnScaleGestureListener() {
        @Override
        public boolean onScale(@NonNull ScaleGestureDetector detector) {
            doScale(detector);
            return true;
        }

        @Override
        public boolean onScaleBegin(@NonNull ScaleGestureDetector detector) {
            if(scrollAndScaleCallBack != null) {
                scrollAndScaleCallBack.onStart();
            }
            doScale(detector);
            return true;
        }

        @Override
        public void onScaleEnd(@NonNull ScaleGestureDetector detector) {
            if(scrollAndScaleCallBack != null) {
                scrollAndScaleCallBack.onEnd();
            }
        }
    };
    public ScaleGestureDetector scaleGestureDetector = new ScaleGestureDetector(GlobalContext.getAppContext(), scaleGestureListener, new Handler(Looper.getMainLooper()));

    private GestureDetector.OnGestureListener gestureListener = new GestureDetector.OnGestureListener() {
        @Override
        public boolean onDown(@NonNull MotionEvent e) {
            return true;
        }

        @Override
        public void onShowPress(@NonNull MotionEvent e) {

        }

        @Override
        public boolean onSingleTapUp(@NonNull MotionEvent e) {
            return false;
        }

        @Override
        public boolean onScroll(@Nullable MotionEvent e1, @NonNull MotionEvent e2, float distanceX, float distanceY) {
            doScroll(distanceX, distanceY);
            return false;
        }

        @Override
        public void onLongPress(@NonNull MotionEvent e) {

        }

        @Override
        public boolean onFling(@Nullable MotionEvent e1, @NonNull MotionEvent e2, float velocityX, float velocityY) {
            int action = e2.getActionMasked();
            if (action == MotionEvent.ACTION_CANCEL) {
                return false;
            }
            if (action == MotionEvent.ACTION_UP && (e2.getFlags() & MotionEvent.FLAG_CANCELED) == MotionEvent.FLAG_CANCELED) {
                return false;
            }
            if (isFling()) {

                doFling(velocityX, velocityY);
                return true;
            }
            return false;
        }
    };
    public GestureDetector gestureDetector = new GestureDetector(GlobalContext.getAppContext(), gestureListener, new Handler(Looper.getMainLooper()));

    public ScrollAndScaleHandler(EngineConfigs engineConfigs, Object logTag) {
        this.logTag = logTag;
        this.engineConfigs = engineConfigs;
        handler = new MainHandler(Looper.getMainLooper());
    }

    private Runnable scrollRunnable = new Runnable() {
        @Override
        public void run() {
            doScrollReal(untreatedDx, untreatedDy);
        }
    };

    public void setCanvasMode(CanvasMode canvasMode) {
        this.canvasMode = canvasMode.value;
    }

    public void setVisibleSize(float width, float height) {
        if (width == 0 || height == 0) {
            return;
        }
        this.visibleWidth = width;
        this.visibleHeight = height;
    }

    public boolean isScrolling() {
        return isScrolling;
    }

    /**
     * 获取缩放值
     *
     * @return 缩放值
     */
    public float getScale() {
        return scale;
    }

    /**
     * 获取最新的缩放信息
     *
     * @return 缩放信息
     */
    public ScaleInfo getScaleInfo() {
        return scaleInfo;
    }

    /**
     * 获取上次的缩放信息
     *
     * @return 缩放信息
     */
    public ScaleInfo getLastScaleInfo() {
        return lastScaleInfo;
    }

    // 吸附缩放值
    private float adsorptionScale = 1.0f;

    private long scaleTime = 0;
    /**
     * 缩放
     *
     * @param detector 缩放数据
     */
    private void doScale(@NonNull ScaleGestureDetector detector) {
//        VLogUtil.d(TAG, logTag,  "doScale");
        scaleTime = System.currentTimeMillis();
        handler.removeCallbacks(scrollRunnable);

        adsorptionScale *= detector.getScaleFactor();
        scale = lastScale * detector.getScaleFactor();
        // 移除缩放吸附
        // if (Math.abs(adsorptionScale - 1.0f) < 0.05f){
        //     scale = 1.0f;
        // }else {
        //     scale = adsorptionScale;
        // }
        // 缩放值修正
        if (scale > SCALE_MAX) {
            scale = SCALE_MAX;
            adsorptionScale = SCALE_MAX;
        }
        if (scale < SCALE_MIN) {
            scale = SCALE_MIN;
            adsorptionScale = SCALE_MIN;
        }


        boolean dragging = false;
        VLogUtil.d(TAG, logTag,  "doScale: " + currentOffsetY + ", " + dragging);
        // 偏移值计算
        currentOffsetX = detector.getFocusX() - (detector.getFocusX() - currentOffsetX) * scale / lastScale - untreatedDx;
        if (dragging) {
            currentOffsetY = 0;
        } else {
            currentOffsetY = detector.getFocusY() - (detector.getFocusY() - currentOffsetY) * scale / lastScale - untreatedDy;
        }

        untreatedDx = 0;
        untreatedDy = 0;
        // 修正偏移量
        reviseOffset();
        // 缩放设置
        scaleInfo.scale = scale / lastScaleInfo.scale;
//        scaleInfo.offsetX = currentOffsetX / scaleInfo.scale - lastScaleInfo.offsetX;
//        scaleInfo.offsetY = currentOffsetY / scaleInfo.scale - lastScaleInfo.offsetY;
        scaleInfo.offsetX = currentOffsetX - lastScaleInfo.offsetX;
        scaleInfo.offsetY = currentOffsetY - lastScaleInfo.offsetY;
        scaleInfo.scaleCenterX = lastScaleInfo.offsetX;
        scaleInfo.scaleCenterY = lastScaleInfo.offsetY;
        if (tempLastScaleInfo.scale == scaleInfo.scale
                && tempLastScaleInfo.offsetX == scaleInfo.offsetX
                && tempLastScaleInfo.offsetY == scaleInfo.offsetY
                && tempLastScaleInfo.scaleCenterX == scaleInfo.scaleCenterX
                && tempLastScaleInfo.scaleCenterY == scaleInfo.scaleCenterY) {
            VLogUtil.d(TAG, logTag,  "doScrollReal: scale and offset is same");
            return;
        }
        isScrolling = true;
        tempLastScaleInfo.setScale(scaleInfo.scale);
        tempLastScaleInfo.setOffsetX(scaleInfo.offsetX);
        tempLastScaleInfo.setOffsetY(scaleInfo.offsetY);
        tempLastScaleInfo.setScaleCenterX(scaleInfo.scaleCenterX);
        tempLastScaleInfo.setScaleCenterY(scaleInfo.scaleCenterY);
        // 引擎缩放
        if (scrollAndScaleCallBack != null) {
            scrollAndScaleCallBack.onScrollAndScale(scaleInfo);
        }
        // 记录上一次缩放值
        lastScale = scale;
        // 回调缩放状态
        if (canvasScaleListener != null) {
            outScaleInfo.scale = scale;
            outScaleInfo.offsetX = currentOffsetX;
            outScaleInfo.offsetY = currentOffsetY;
            canvasScaleListener.onScale(outScaleInfo);
        }
    }
    private final ScaleInfo outScaleInfo = new ScaleInfo();

    private float lastDx, lastDy;
    private float untreatedDx, untreatedDy;
    /**
     * 滚动
     *
     * @param dx 偏移量x
     * @param dy 偏移量y
     */
    private void doScroll(float dx, float dy) {
//        VLogUtil.d(TAG, logTag,  "doScroll: " + dx + ", " + dy);
        if (scrollAndScaleCallBack == null) {
            return;
        }
        if (!engineConfigs.enableScaleMode) {
            doScrollReal(dx, dy);
        } else {
            if(handler.hasCallbacks(scrollRunnable)){
                handler.removeCallbacks(scrollRunnable);
                doScrollReal(untreatedDx, untreatedDy);
            }
            untreatedDx = dx;
            untreatedDy = dy;
            handler.postDelayed(scrollRunnable, 5);
        }
    }

    /**
     * 真实滚动
     *
     * @param dx 偏移量x
     * @param dy 偏移量y
     */
    private void doScrollReal(float dx, float dy) {
//        VLogUtil.d(TAG, logTag,  "doScroll");
//        isScrolling = true;
        boolean dragging = false;
        VLogUtil.d(TAG, logTag,  "doScrollReal: "+ dy + ", " + currentOffsetY);
        // 偏移值计算
        currentOffsetX -= dx;
        if (dragging) {
            currentOffsetY = 0;
        } else {
            currentOffsetY -= dy;
        }
        untreatedDx = 0;
        untreatedDy = 0;
        // 修正偏移量
        reviseOffset();
        // 偏移增量计算
//        scaleInfo.offsetX = currentOffsetX / scaleInfo.scale - lastScaleInfo.offsetX;
//        scaleInfo.offsetY = currentOffsetY / scaleInfo.scale - lastScaleInfo.offsetY;
        scaleInfo.offsetX = currentOffsetX - lastScaleInfo.offsetX;
        scaleInfo.offsetY = currentOffsetY - lastScaleInfo.offsetY;
        scaleInfo.scaleCenterX = lastScaleInfo.offsetX;
        scaleInfo.scaleCenterY = lastScaleInfo.offsetY;

        if (engineConfigs.enableScaleMode) {
            if (tempLastScaleInfo.scale == scaleInfo.scale
                    && tempLastScaleInfo.offsetX == scaleInfo.offsetX
                    && tempLastScaleInfo.offsetY == scaleInfo.offsetY
                    && tempLastScaleInfo.scaleCenterX == scaleInfo.scaleCenterX
                    && tempLastScaleInfo.scaleCenterY == scaleInfo.scaleCenterY) {
                VLogUtil.d(TAG, logTag,  "doScrollReal: scale and offset is same");
                return;
            }
            isScrolling = true;
            tempLastScaleInfo.setScale(scaleInfo.scale);
            tempLastScaleInfo.setOffsetX(scaleInfo.offsetX);
            tempLastScaleInfo.setOffsetY(scaleInfo.offsetY);
            tempLastScaleInfo.setScaleCenterX(scaleInfo.scaleCenterX);
            tempLastScaleInfo.setScaleCenterY(scaleInfo.scaleCenterY);
            // 引擎缩放
            if (scrollAndScaleCallBack != null) {
                scrollAndScaleCallBack.onScrollAndScale(scaleInfo);
            }
        } else {
            isScrolling = true;
            // 引擎平移
            if (scrollAndScaleCallBack != null) {
                scrollAndScaleCallBack.onScrollAndScale(scaleInfo);
            }
            lastDx = currentOffsetX - lastScaleInfo.offsetX;
            lastDy = currentOffsetY - lastScaleInfo.offsetY;
        }

        // 回调缩放状态
        if (canvasScaleListener != null) {
            outScaleInfo.scale = scale;
            outScaleInfo.offsetX = currentOffsetX;
            outScaleInfo.offsetY = currentOffsetY;
            canvasScaleListener.onScale(outScaleInfo);
        }
    }

    /**
     * 滚动停止
     */
    private boolean onScrollEnd() {
        if (!isScrolling) {
            return false;
        }
        if(handler.hasCallbacks(scrollRunnable)){
            handler.removeCallbacks(scrollRunnable);
            doScrollReal(untreatedDx, untreatedDy);
        }
        isScrolling = false;
        VLogUtil.d(TAG, logTag,  "onScrollEnd: " + scale + ", " + currentOffsetX + ", " + currentOffsetY);
        scaleInfo.scale = 1f;
        scaleInfo.offsetX = 0;
        scaleInfo.offsetY = 0;
        scaleInfo.scaleCenterX = 0;
        scaleInfo.scaleCenterY = 0;
        lastScaleInfo.scale = scale;
        lastScaleInfo.offsetX = currentOffsetX;
        lastScaleInfo.offsetY = currentOffsetY;
        lastDx = 0;
        lastDy = 0;
        if (scrollAndScaleCallBack == null) {
            return false;
        }
        scrollAndScaleCallBack.onEnd();
        return true;
    }

    private ScaleInfo tempLastScaleInfo = new ScaleInfo();
    /**
     * 重置缩放数据
     */
    public void reset() {
        VLogUtil.d(TAG, logTag,  "reset");
        scaleInfo.scale = 1f;
        scaleInfo.offsetX = 0;
        scaleInfo.offsetY = 0;
        scaleInfo.scaleCenterX = 0;
        scaleInfo.scaleCenterY = 0;

        tempLastScaleInfo = new ScaleInfo();

        lastScaleInfo.scale = 1f;
        lastScaleInfo.offsetX = 0;
        lastScaleInfo.offsetY = 0;

        scale = 1f;
        lastScale = 1f;
        currentOffsetX = 0;
        currentOffsetY = 0;
        adsorptionScale = 1f;

        lastDx = 0;
        lastDy = 0;
        // 回调缩放状态
        if (canvasScaleListener != null) {
            outScaleInfo.scale = scale;
            outScaleInfo.offsetX = currentOffsetX;
            outScaleInfo.offsetY = currentOffsetY;
            canvasScaleListener.onScale(outScaleInfo);
        }
    }

    // 修正偏移量
    private void reviseOffset() {
        if (canvasMode == CanvasMode.DEFAULT.value) {
            // 无限画布不限制
            return;
        }
        if (canvasMode == CanvasMode.VIEW_PORT.value || canvasMode == CanvasMode.FIXED_AND_VIEW_PORT.value) {
            resetViewPort();
            if (viewPortRectF.isEmpty()) {
                return;
            }
            float minOffsetX = getMinOffsetX(viewPortRectF);
            // 修正右边界
            if (currentOffsetX < minOffsetX) {
                currentOffsetX = minOffsetX;
            }
            float maxOffsetX = getMaxOffsetX(viewPortRectF);
            // 修正左边界
            if (currentOffsetX > maxOffsetX) {
                currentOffsetX = maxOffsetX;
            }
            float minOffsetY = getMinOffsetY(viewPortRectF);
            // 修正下边界
            if (currentOffsetY < minOffsetY) {
                currentOffsetY = minOffsetY;
            }
            float maxOffsetY = getMaxOffsetY(viewPortRectF);
            // 修正上边界
            if (currentOffsetY > maxOffsetY) {
                currentOffsetY = maxOffsetY;
            }
        } else {
            if (isFixedWidth()) {
                float minOffsetX = getMinOffsetX();
                // 修正右边界
                if (currentOffsetX < minOffsetX) {
                    currentOffsetX = minOffsetX;
                }
                float maxOffsetX = getMaxOffsetX();
                // 修正左边界
                if (currentOffsetX > maxOffsetX) {
                    currentOffsetX = maxOffsetX;
                }
            }
            if (engineConfigs.verticalScrollMaxHeight > 0) {
                float minOffsetY = getMinOffsetY(engineConfigs.verticalScrollMaxHeight);
                // 修正下边界
                if (currentOffsetY < minOffsetY) {
                    currentOffsetY = minOffsetY;
                }
                float maxOffsetY = getMaxOffsetY(engineConfigs.verticalScrollMaxHeight);
                // 修正上边界
                if (currentOffsetY > maxOffsetY) {
                    currentOffsetY = maxOffsetY;
                }
            } else {
                if (canvasMode == CanvasMode.VERTICAL.value) {
                    float minOffsetY = visibleHeight-(verticalScrollLimit * scale);
                    // 修正下边界
                    if (currentOffsetY < minOffsetY) {
                        currentOffsetY = minOffsetY;
                    }
                    Logger.d(TAG, "currentOffsetY: " + currentOffsetY);

                    // 垂直画布不能超过0的位置， 最大值由上面的verticalScrollMaxHeight控制
                    if (currentOffsetY > 0) {
                        currentOffsetY = 0;
                    }
                } else {
                    if (isFixedHeight()) {
                        float minOffsetY = getMinOffsetY();
                        // 修正下边界
                        if (currentOffsetY < minOffsetY) {
                            currentOffsetY = minOffsetY;
                        }
                        float maxOffsetY = getMaxOffsetY();
                        // 修正上边界
                        if (currentOffsetY > maxOffsetY) {
                            currentOffsetY = maxOffsetY;
                        }
                    }
                }
            }
        }
    }

    private RectF viewPortRectF = new RectF();
    private void resetViewPort() {
        if (canvasMode == CanvasMode.FIXED.value) {
            viewPortRectF.set(0, 0, visibleWidth, visibleHeight);
            viewPortRectF.left *= scale;
            viewPortRectF.top *= scale;
            viewPortRectF.right *= scale;
            viewPortRectF.bottom *= scale;
            return;
        }
        if (engineConfigs.viewPortRectF == null) {
            viewPortRectF.setEmpty();
            return;
        }
        if (engineConfigs.viewPortRectF.isEmpty()) {
            viewPortRectF.setEmpty();
            return;
        }
        if (visibleWidth == 0) {
            viewPortRectF.setEmpty();
            return;
        }
        float ratio = visibleWidth / 1000f;
        viewPortRectF.set(engineConfigs.viewPortRectF);
        viewPortRectF.left *= ratio;
        viewPortRectF.top *= ratio;
        viewPortRectF.right *= ratio;
        viewPortRectF.bottom *= ratio;
        viewPortRectF.left *= scale;
        viewPortRectF.top *= scale;
        viewPortRectF.right *= scale;
        viewPortRectF.bottom *= scale;
    }

    private float getMinOffsetX() {
        if (scale > 1f) {
            return visibleWidth * (1 - scale);
        } else {
            return visibleWidth * (1 - scale) / 2;
        }
    }
    private float getDrawRatio(){
        return visibleWidth / 1000f;
    }

    private float getMaxOffsetX() {
        if (scale > 1f) {
            return 0;
        } else {
            return visibleWidth * (1 - scale) / 2;
        }
    }

    private float getMinOffsetY() {
        if (scale > 1f) {
            return visibleHeight * (1 - scale);
        } else {
            return visibleHeight * (1 - scale) / 2;
        }
    }

    private float getMaxOffsetY() {
        if (scale > 1f) {
            return 0;
        } else {
            return visibleHeight * (1 - scale) / 2;
        }
    }

    private float getMinOffsetX(@NonNull RectF rectF) {
        if (rectF.width() > visibleWidth) {
            return visibleWidth - rectF.right;
        }
        return visibleWidth / 2 - viewPortRectF.centerX();
    }

    private float getMaxOffsetX(@NonNull RectF rectF) {
        if (rectF.width() > visibleWidth) {
            return -rectF.left;
        }
        return visibleWidth / 2 - viewPortRectF.centerX();
    }

    private float getMinOffsetY(@NonNull RectF rectF) {
        if (rectF.height() > visibleHeight) {
            return visibleHeight - rectF.bottom;
        }
        return visibleHeight / 2 - viewPortRectF.centerY();
    }

    private float getMaxOffsetY(@NonNull RectF rectF) {
        if (rectF.height() > visibleHeight) {
            return -rectF.top;
        }
        return visibleHeight / 2 - viewPortRectF.centerY();
    }

    private float getMinOffsetY(int maxY) {
        float max = maxY * scale* getDrawRatio();
        if (max > visibleHeight) {
            return visibleHeight - max;
        }
        return 0;
    }

    private float getMaxOffsetY(int maxY) {
        float max = maxY * scale* getDrawRatio();
        if (max > visibleHeight) {
            return 0;
        }
        return 0;
    }

    private boolean isFixedWidth() {
        if (canvasMode == CanvasMode.VERTICAL.value || canvasMode == CanvasMode.FIXED.value) {
            return true;
        }
        return false;
    }

    private boolean isFixedHeight() {
        if (canvasMode == CanvasMode.HORIZONTAL.value || canvasMode == CanvasMode.FIXED.value) {
            return true;
        }
        return false;
    }

    private final Animator.AnimatorListener animatorListener = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(@NonNull Animator animation) {}
        @Override
        public void onAnimationEnd(@NonNull Animator animation) {
            VLogUtil.d(TAG, logTag,  "onAnimationEnd: " + animator.isRunning());
            onScrollEnd();
            if (animEndListener != null) {
                animEndListener.onAnimEnd();
                animEndListener = null;
            }
        }
        @Override
        public void onAnimationCancel(@NonNull Animator animation) {}
        @Override
        public void onAnimationRepeat(@NonNull Animator animation) {}
    };

    private ValueAnimator animator;
    private float lastAnimatorValue;
    /**
     * 执行惯性滑动
     *
     * @param velX 位移x
     * @param velY 位移y
     */
    private void doFling(float velX, float velY) {
        long time = System.currentTimeMillis();
        if(time - scaleTime < 100){
            onScrollEnd();
            return;
        }
        if (!enableFling) {
            onScrollEnd();
            return;
        }
        if (Math.abs(velX) < 1000 && Math.abs(velY) < 1000) {
            onScrollEnd();
            return;
        }
        final float velocityX = (float) (Math.pow(Math.abs(velX), 0.7) * Math.signum(velX));
        final float velocityY = (float) (Math.pow(Math.abs(velY), 0.7) * Math.signum(velY));
        doAnimator(velocityX, velocityY);
    }

    private void doAnimator(float velocityX, float velocityY) {
        if (animator != null && animator.isRunning()) {
            animator.cancel();
        }
        lastAnimatorValue = 100f;
        animator = ValueAnimator.ofFloat(lastAnimatorValue, 0);
        animator.setInterpolator(new DecelerateInterpolator(1.5f));
        int duration = CommonUtils.calculateTimeForDeceleration((int) Math.max(Math.abs(velocityX), Math.abs(velocityY)));
        duration = Math.max(duration, 300);
        VLogUtil.d(TAG, logTag,  "doFling: " + velocityX + ", " + velocityY + ", " + duration);
        animator.setDuration(duration);
        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(@NonNull ValueAnimator animation) {
                doAnimationUpdate((float) animation.getAnimatedValue(), velocityX, velocityY);
            }
        });
        animator.addListener(animatorListener);
        animator.start();
    }

    private void doAnimationUpdate(float value, float velocityX, float velocityY) {
        float dx = 0;
        float dy = 0;
        if (Math.abs(velocityY) >= Math.abs(velocityX)) {
            dy = (value - lastAnimatorValue) * velocityY / 100;
        } else {
            dx = (value - lastAnimatorValue) * velocityX / 100;
        }
        doScrollReal(dx, dy);
        lastAnimatorValue = value;
    }

    /**
     * 绝对缩放，设置多少就是多少
     * @param newScaleInfo 目标缩放值
     */
    public void updateAbsScaleInfo(ScaleInfo newScaleInfo) {
        scale = newScaleInfo.scale;
        // 缩放值修正
        if (scale > SCALE_MAX) {
            scale = SCALE_MAX;
        }
        if (scale < SCALE_MIN) {
            scale = SCALE_MIN;
        }
        // 偏移值计算
        currentOffsetX = newScaleInfo.offsetX;
        currentOffsetY = newScaleInfo.offsetY;
        // 修正偏移量
        reviseOffset();

        if (engineConfigs.enableScaleMode) {
            scaleInfo.scale = scale;
            scaleInfo.scaleCenterX = newScaleInfo.scaleCenterX;
            scaleInfo.scaleCenterY = newScaleInfo.scaleCenterY;
        }
        scaleInfo.offsetX = currentOffsetX;
        scaleInfo.offsetY = currentOffsetY;
        // 引擎缩放
        if (scrollAndScaleCallBack != null) {
            scrollAndScaleCallBack.onScrollAndScale(scaleInfo);
        }
        // 补充上次缩放值，避免强制设置后，下次缩放值出现跳变
        lastScale = scale;
    }

    /**
     * 相对目前缩放
     * @param updateScaleInfo 相对缩放值
     */
    public void updateScaleInfo(ScaleInfo updateScaleInfo) {
        VLogUtil.d(TAG, logTag,  "updateScaleInfo2");
        isScrolling = true;
        float scaleFactor = updateScaleInfo.scale / lastScaleInfo.scale;
        adsorptionScale *= scaleFactor;
        scale = lastScale * scaleFactor;
        // 缩放值修正
        if (scale > SCALE_MAX) {
            scale = SCALE_MAX;
            adsorptionScale = SCALE_MAX;
        }
        if (scale < SCALE_MIN) {
            scale = SCALE_MIN;
            adsorptionScale = SCALE_MIN;
        }
        float lastOffsetX = currentOffsetX;
        float lastOffsetY = currentOffsetY;
        // 偏移值计算
        currentOffsetX = updateScaleInfo.offsetX;
        currentOffsetY = updateScaleInfo.offsetY;
        // 修正偏移量
        reviseOffset();
        float offsetX = currentOffsetX - lastOffsetX;
        float offsetY = currentOffsetY - lastOffsetY;
        if (engineConfigs.enableScaleMode) {
            // 缩放设置
            scaleInfo.scale = scale / lastScaleInfo.scale;
            scaleInfo.offsetX = currentOffsetX - lastScaleInfo.offsetX;
            scaleInfo.offsetY = currentOffsetY - lastScaleInfo.offsetY;
            scaleInfo.scaleCenterX = lastScaleInfo.offsetX;
            scaleInfo.scaleCenterY = lastScaleInfo.offsetY;
            // 引擎缩放
            if (scrollAndScaleCallBack != null) {
                scrollAndScaleCallBack.onScrollAndScale(scaleInfo);
            }
        } else {
            // 引擎画布平移
            if (scrollAndScaleCallBack != null) {
                scaleInfo.offsetX = offsetX;
                scaleInfo.offsetY = offsetY;
                scaleInfo.scaleCenterX = lastScaleInfo.offsetX;
                scaleInfo.scaleCenterY = lastScaleInfo.offsetY;
                scrollAndScaleCallBack.onScrollAndScale(scaleInfo);
            }
        }

        // 记录上一次缩放值
        lastScale = scale;
        // 回调缩放状态
        if (canvasScaleListener != null) {
            outScaleInfo.scale = scale;
            outScaleInfo.offsetX = currentOffsetX;
            outScaleInfo.offsetY = currentOffsetY;
            canvasScaleListener.onScale(outScaleInfo);
        }
//        onScrollEnd();
    }

//    public void scrollBy(float dx, float dy, boolean anim) {
//        stopFling();
//        if (anim) {
//            scrollByAnim(dx, dy);
//        } else {
//            doScrollReal(dx, dy);
//            onScrollEnd();
//        }
//    }

    private void scrollByAnim(float velocityX, float velocityY) {
        lastAnimatorValue = 100f;
        animator = ValueAnimator.ofFloat(lastAnimatorValue, 0);
        animator.setInterpolator(new DecelerateInterpolator(1.5f));
        int duration = CommonUtils.calculateTimeForDeceleration((int) Math.max(Math.abs(velocityX), Math.abs(velocityY)));
        duration = Math.max(duration, 300);
        VLogUtil.d(TAG, logTag,  "doFling: " + velocityX + ", " + velocityY + ", " + duration);
        animator.setDuration(duration);
        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(@NonNull ValueAnimator animation) {
                doAnimationUpdate((float) animation.getAnimatedValue(), velocityX, velocityY);
            }
        });
        animator.addListener(animatorListener);
        animator.start();
    }

    public void changeScaleLimit(float min, float max) {
        SCALE_MIN = min;
        SCALE_MAX = max;
    }

    /**
     * 是否惯性动画中
     *
     * @return 是否
     */
    public boolean isFling() {
        return animator != null && animator.isRunning();
    }

    /**
     * 停止惯性滚动
     */
    public void stopFling() {
        if (engineConfigs.closeScrollAndScaleHandle) {
            return;
        }
        if (animator != null && animator.isRunning()) {
            animator.cancel();
        }
        onScrollEnd();
    }

    public void setScaleLimit(float minScale, float maxScale) {
        if (minScale < 0.35f) {
            SCALE_MIN = 0.35f;
        } else if (minScale > 1f) {
            SCALE_MIN = 1f;
        } else {
            SCALE_MIN = minScale;
        }
        if (maxScale > 6f) {
            SCALE_MAX = 6f;
        } else if (maxScale < 1f) {
            SCALE_MAX = 1f;
        } else {
            SCALE_MAX = maxScale;
        }
    }

    private static final int MSG_DO_SCROLL = 1;
    private static class MainHandler extends Handler {
        public MainHandler(Looper mainLooper) {
            super(mainLooper);
        }
    }

    private AnimEndListener animEndListener;
    public void setAnimEndListener(AnimEndListener animEndListener) {
        this.animEndListener = animEndListener;
    }
    public interface AnimEndListener {
        void onAnimEnd();
    }

    private ScrollAndScaleCallBack scrollAndScaleCallBack;
    public void setScrollAndScaleCallBack(ScrollAndScaleCallBack callBack) {
        this.scrollAndScaleCallBack = callBack;
    }
    public interface ScrollAndScaleCallBack {
        void onStart();
        void onScrollAndScale(@NonNull ScaleInfo scaleInfo);
        void onEnd();
    }
}
