package com.tcl.ai.note.handwritingtext.ui.widget

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.theme.TclTheme
import kotlinx.coroutines.delay

@Composable
fun ScaleIndicator(
    scaleState: Float,
    modifier: Modifier = Modifier,
) {
    var visible by remember { mutableStateOf(false) }

    LaunchedEffect(scaleState) {
        visible = true
        delay(SLOW_OUT_DELAY)
        visible = false
    }

    AnimatedVisibility(
        visible = visible,
        enter = fadeIn(animationSpec = inSpec),
        exit = fadeOut(animationSpec = outSpec),
        modifier = modifier
            .padding(top = 12.dp, end = 11.dp),
    ) {
        Box(
            modifier = Modifier
                .shadow(
                    elevation = 50.dp,
                    shape = RoundedCornerShape(33.dp),
                    spotColor = Color(0x26000000),
                )
                .background(TclTheme.colorScheme.primaryBackground)
        ) {
            Text(
                text = "%.0f%%".format(scaleState * 100),
                textAlign = TextAlign.Center,
                color = TclTheme.colorScheme.tctStanderTextThird,
                fontSize = 14.sp,
                modifier = Modifier
                    .widthIn(
                        min = with(LocalDensity.current) {
                            56.sp.toDp()
                        }
                    )
                    .padding(vertical = 2.dp, horizontal = 8.dp),
            )
        }
    }
}

private const val SLOW_OUT_DELAY = 2000L
private val inSpec = tween<Float>(
    durationMillis = 300,
    easing = LinearOutSlowInEasing
)
private val outSpec = tween<Float>(
    durationMillis = 200,
    easing = LinearOutSlowInEasing
)