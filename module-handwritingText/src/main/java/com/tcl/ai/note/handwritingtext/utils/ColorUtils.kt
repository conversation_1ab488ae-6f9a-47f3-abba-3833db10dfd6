package com.tcl.ai.note.handwritingtext.utils

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import com.tcl.ai.note.handwritingtext.ui.swatches.toHslArray
import java.util.Locale
import kotlin.math.abs

/**
 * 颜色相关的工具类
 */
object ColorUtils {
    
    /**
     * 预定义的标准颜色集合
     */
    val TEXT_COLORS = listOf(
        Color.Black,                  // #000000
        Color(0xFFFF4B6B),            // #FF4B6B
        Color(0xFFAD77FF),            // #AD77FF
        Color(0xFF398FFF),            // #398FFF
        Color(0xFF1FBEA2),            // #1FBEA2
        Color(0xFFFFC72D),            // #FFC72D
        Color(0xFFFA9F5E)             // #FA9F5E
    )
    
    /**
     * 将Color转换为十六进制字符串（如 #FF0000）
     */
    fun colorToHexString(color: Color): String {
        val alpha = (color.alpha * 255).toInt()
        val red = (color.red * 255).toInt()
        val green = (color.green * 255).toInt()
        val blue = (color.blue * 255).toInt()
        
        return if (alpha < 255) {
            String.format(Locale.getDefault(), "#%02X%02X%02X%02X", alpha, red, green, blue)
        } else {
            String.format(Locale.getDefault(), "#%02X%02X%02X", red, green, blue)
        }
    }
    
    /**
     * 将十六进制字符串转换为Color对象
     */
    fun hexStringToColor(hexColor: String): Color {
        val colorString = hexColor.replace("#", "")
        
        return when (colorString.length) {
            6 -> {
                Color(
                    red = Integer.valueOf(colorString.substring(0, 2), 16) / 255f,
                    green = Integer.valueOf(colorString.substring(2, 4), 16) / 255f,
                    blue = Integer.valueOf(colorString.substring(4, 6), 16) / 255f
                )
            }
            8 -> {
                Color(
                    alpha = Integer.valueOf(colorString.substring(0, 2), 16) / 255f,
                    red = Integer.valueOf(colorString.substring(2, 4), 16) / 255f,
                    green = Integer.valueOf(colorString.substring(4, 6), 16) / 255f,
                    blue = Integer.valueOf(colorString.substring(6, 8), 16) / 255f
                )
            }
            else -> {
                Color.Black
            }
        }
    }
    
    /**
     * 判断颜色是否为暗色
     */
    fun isDarkColor(color: Color): Boolean {
        val darkness = 1 - (0.299 * color.red + 0.587 * color.green + 0.114 * color.blue)
        return darkness >= 0.5
    }
    
    /**
     * 获取颜色的对比色
     */
    fun getContrastColor(color: Color): Color {
        return if (isDarkColor(color)) Color.White else Color.Black
    }
    
    /**
     * 获取颜色的透明版本
     */
    fun getTransparentColor(color: Color, alpha: Float): Color {
        return color.copy(alpha = alpha)
    }

    /**
     * 反色 L = 1 - L
     */
    fun Color.inverseColor(): Color {
        val hsl = this.toHslArray()
        val newLightness = 1f - hsl[2]
        return Color.hsl(hue = hsl[0], saturation = hsl[1], lightness = newLightness, alpha = this.alpha)

    }

} 