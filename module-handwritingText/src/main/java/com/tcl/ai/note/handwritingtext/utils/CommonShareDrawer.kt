package com.tcl.ai.note.handwritingtext.utils

import android.content.Context
import android.content.Intent
import android.graphics.*
import android.net.Uri
import android.text.*
import android.text.style.MetricAffectingSpan
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.DrawStroke
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle
import com.tcl.ai.note.handwritingtext.repo.DrawBoardRepository
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * 功能特性：
 * 1. 动态计算内容高度
 * 2. 支持文本、待办事项、图片混合内容
 * 3. 自动处理图片缩放和内存优化
 * 4. 使用Android FileProvider安全分享文件
 */
class CommonShareDrawer {

    /** 默认屏幕宽度 */
    private val maxWidth = GlobalContext.instance.resources.displayMetrics.widthPixels

    /** 左右边距 */
    private val margin = 36f

    /** 行间距系数（1.2倍字体高度） */
    private var lineSpacing = 1.2f

    // endregion

    /**
     * 创建PDF文件（包含两阶段处理）
     * 第一阶段：计算内容总高度
     * 第二阶段：创建动态高度页面并绘制内容
     */
    suspend fun drawContent(
        context: Context,
        canvas: Canvas,
        note: Note,
        contentList: List<EditorContent>,
    ) = withContext(Dispatchers.IO) {
        // 初始化，获取所有数据
        // 获取手绘笔画
        val drawStrokeList = DrawBoardRepository.getDrawByNoteIdBlock(note.noteId)?.strokes ?: listOf()

        // 重置编号计数器
        var localNumberedCounter = 1

        // 第一阶段：计算内容总高度
        val contentHeight = calculateContentHeight(context, note, contentList)
        // 计算手绘高度
        val handwritingHeight = calculateHandWritingHeight(drawStrokeList)
        // 比较手绘和常规内容的高度更高
        val totalHeight = if (handwritingHeight > contentHeight) handwritingHeight else contentHeight
        Logger.d("PdfShareUtils", "contentHeight: $contentHeight, handwritingHeight: $handwritingHeight ,totalHeight: $totalHeight")

        // 计算页面高度
        val pageHeight = (totalHeight + margin * 2).toInt()

        // 第二阶段：绘制内容
        var currentY = margin
        currentY = drawTitle(canvas, note.title, currentY)

        contentList.forEach { content ->
            currentY = when (content) {
                is EditorContent.TextBlock -> {
                    val isCurrentNumbered = content.paragraphStyle == ParagraphStyle.NUMBERED

                    // 确定是否切换和编号递增
                    val currentNumber = if (isCurrentNumbered) {
                        localNumberedCounter++
                    } else {
                        localNumberedCounter = 1 // 编号重置
                        0 // 非编号段落不显示编号
                    }

                    drawTextBlock(
                        canvas = canvas,
                        text = content.text.text,
                        paragraphStyle = content.paragraphStyle,
                        spanStyles = content.text.annotatedString.spanStyles,
                        startY = currentY,
                        localNumber = currentNumber // 传递当前编号状态
                    )
                }
                is EditorContent.TodoBlock -> drawTodoBlock(
                    canvas = canvas,
                    todo = content,
                    text = content.text.annotatedString.text,
                    spanStyles = content.text.annotatedString.spanStyles,
                    startY = currentY,
                    pageWidth = maxWidth,
                    context = context
                )
                is EditorContent.ImageBlock -> drawImageBlock(
                    context = context,
                    canvas = canvas,
                    uri = content.uri,
                    startY = currentY
                )
                is EditorContent.AudioBlock -> {
                    currentY += 10f // 临时实现，音频占位高度
                    currentY
                }

                is EditorContent.RichTextV2 -> { 1.0f }
            }
        }

        // 绘制手写笔画
        val handwritingBitmap = Bitmap.createBitmap(maxWidth, pageHeight, Bitmap.Config.ARGB_8888)
        val handwritingCanvas = Canvas(handwritingBitmap)
        drawStrokeList.forEach { it.toDrawPathDisplay().draw(handwritingCanvas) }
        canvas.drawBitmap(handwritingBitmap, 0f, 0f, null)
    }

    /**
     * 计算内容总高度（包含所有元素）
     * @return 总高度（单位：点）
     */
    internal suspend fun calculateContentHeight(
        context: Context,
        note: Note,
        contentList: List<EditorContent>
    ): Float = withContext(Dispatchers.IO) {
        var height = 0f

        // 标题高度（包含下方间距）
        height += calculateTitleHeight(note.title)

        // 各内容块高度累加
        contentList.forEach { content ->
            height += when (content) {
                is EditorContent.TextBlock -> calculateTextHeight(content.text.text)
                is EditorContent.TodoBlock -> calculateTodoHeight(content.text.text, maxWidth)
                is EditorContent.ImageBlock -> calculateImageHeight(context, content.uri)
                is EditorContent.AudioBlock -> 30f // 临时实现，音频占位高度
                is EditorContent.RichTextV2 -> {30f}
            }
        }

        return@withContext height
    }


    // region 高度计算辅助方法
    /**
     * 计算标题高度（包含20点下边距）
     */
    private fun calculateTitleHeight(title: String): Float {
        val paint = TextPaint().apply {
            textSize = 18f
            typeface = Typeface.DEFAULT_BOLD
        }
        val maxWidth = maxWidth - margin * 2
        val layout = StaticLayout.Builder.obtain(
            title, 0, title.length, paint, maxWidth.toInt()
        ).setLineSpacing(0f, lineSpacing).build()
        return layout.height + 20f // 标题高度+下边距
    }

    /**
     * 计算普通文本高度（包含10点下边距）
     */
    private fun calculateTextHeight(text: String): Float {
        val paint = TextPaint().apply { textSize = 12f }
        val maxWidth = maxWidth - margin * 2
        val layout = StaticLayout.Builder.obtain(
            text, 0, text.length, paint, maxWidth.toInt()
        ).setLineSpacing(0f, lineSpacing).build()
        return layout.height + 10f // 文本高度+间距
    }

    /**
     * 计算手绘高度
     */
    internal fun calculateHandWritingHeight(drawStrokes: List<DrawStroke>): Float {
        var height = 0F
        drawStrokes.forEach {
            it.points.forEach {
                height = Math.max(it.y, height)
            }
        }
        return height
    }

    /**
     * 计算待办事项高度（固定行高 + 间距）
     */
    private fun calculateTodoHeight(todoText: String, pageWidth: Int): Float {
        val textPaint = TextPaint().apply {
            // 保持与绘制参数一致
            textSize = 24f
            typeface = Typeface.SANS_SERIF
        }

        // 计算可用文本宽度（屏幕宽度 - 边距*2 - 复选框 - 间距）
        val checkboxSize = 14f
        val horizontalSpacing = 12f + 4f
        val maxTextWidth = pageWidth - margin.toInt() * 2 - checkboxSize.toInt() - horizontalSpacing.toInt()

        val layout = StaticLayout.Builder.obtain(
            todoText, 0, todoText.length, textPaint, maxTextWidth.toInt()
        ).setLineSpacing(0f, lineSpacing)
            .build()

        return layout.height + 16f // 增加底部间距
    }

    /**
     * 计算图片高度（包含10点下边距）
     */
    private fun calculateImageHeight(context: Context, uri: Uri): Float {
        return try {
            context.contentResolver.openFileDescriptor(uri, "r")?.use { pfd ->
                val options = BitmapFactory.Options().apply { inJustDecodeBounds = true }
                BitmapFactory.decodeFileDescriptor(pfd.fileDescriptor, null, options)
                val maxWidth = maxWidth - margin * 2
                val scale = maxWidth / options.outWidth
                (options.outHeight * scale) + 10f // 图片高度+下边距
            } ?: 30f // 加载失败占位高度
        } catch (e: Exception) {
            30f
        }
    }
    // endregion

    // region 内容绘制方法
    /**
     * 绘制标题（居中显示）
     * @param startY 起始Y坐标
     * @return 新的Y坐标
     */
    private fun drawTitle(
        canvas: Canvas,
        title: String,
        startY: Float
    ): Float {
        val paint = TextPaint().apply {
            color = 0xFF333333.toInt()
            textSize = 18f
            typeface = Typeface.DEFAULT_BOLD
        }

        val maxWidth = maxWidth - margin * 2

        val layout = StaticLayout.Builder.obtain(
            title, 0, title.length, paint, maxWidth.toInt()
        ).setAlignment(Layout.Alignment.ALIGN_NORMAL)
            .setLineSpacing(0f, lineSpacing)
            .build()

        canvas.save()
        canvas.translate(margin, startY)
        layout.draw(canvas)
        canvas.restore()

        return startY + layout.height + 20f
    }

    /**
     * 绘制普通文本块
     * @param startY 起始Y坐标
     * @return 新的Y坐标
     */
    private fun drawTextBlock(
        canvas: Canvas,
        text: String,
        paragraphStyle: ParagraphStyle,
        spanStyles: List<AnnotatedString.Range<SpanStyle>>,
        startY: Float,
        localNumber: Int // 增加参数，用于局部编号控制
    ): Float {
        var currentY = startY
        val textPaint = TextPaint().apply {
            color = 0xFF333333.toInt()
            textSize = 12f
        }

        text.split("\n").forEach { paragraph ->
            // 处理段落符号
            val (symbol, indent) = when (paragraphStyle) {
                ParagraphStyle.BULLETED -> Pair("•  ", 24f)
                ParagraphStyle.NUMBERED -> Pair("$localNumber.  ", 36f) // 使用局部编号
                else -> Pair("", 0f)
            }

            // 绘制符号
            if (symbol.isNotEmpty()) {
                canvas.drawText(symbol, margin, currentY + textPaint.textSize, textPaint)
            }

            // 创建带样式的文本布局
            val layout = createStyledLayout(
                text = buildAnnotatedString {
                    append(paragraph)
                    spanStyles.forEach {
                        // 过滤颜色属性
                        val filteredStyle = it.item.copy(color = androidx.compose.ui.graphics.Color(textPaint.color))
                        addStyle(filteredStyle, it.start.coerceAtLeast(0), it.end)
                    }
                },
                indent = indent,
                paint = textPaint,
                maxWidth = maxWidth - margin * 2 - indent
            )

            // 绘制文本
            canvas.save()
            canvas.translate(margin + indent, currentY)
            layout.draw(canvas)

            // 绘制自定义下划线
            spanStyles.filter { it.item.textDecoration?.contains(TextDecoration.Underline) == true }
                .forEach { range ->
                    val startLine = layout.getLineForOffset(range.start)
                    val endLine = layout.getLineForOffset(range.end)

                    for (line in startLine..endLine) {
                        val lineStart = layout.getLineStart(line).coerceAtLeast(range.start)
                        val lineEnd = layout.getLineEnd(line).coerceAtMost(range.end)

                        if (lineStart < lineEnd) {
                            val baseline = layout.getLineBaseline(line)
                            canvas.drawLine(
                                layout.getPrimaryHorizontal(lineStart),
                                baseline + 3f, // 调整下划线位置
                                layout.getLineWidth(line).takeIf { it > 0 } ?: layout.getPrimaryHorizontal(lineEnd),
                                baseline + 3f,
                                Paint().apply {
                                    color = textPaint.color
                                    strokeWidth = 1f // 调整线宽
                                }
                            )
                        }
                    }
                }

            canvas.restore()
            currentY += layout.height + 6f
        }

        return currentY + 10f
    }

    private fun createStyledLayout(
        text: AnnotatedString,
        indent: Float,
        paint: TextPaint,
        maxWidth: Float
    ): StaticLayout {
        val spannable = SpannableString(text)

        text.spanStyles.forEach { range ->
            val style = range.item
            spannable.setSpan(
                object : MetricAffectingSpan() {
                    override fun updateDrawState(ds: TextPaint) {
                        applyStyle(ds, style)
                    }
                    override fun updateMeasureState(ds: TextPaint) {
                        applyStyle(ds, style)
                    }
                },
                range.start.coerceAtLeast(0),
                range.end,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        return StaticLayout.Builder.obtain(
            spannable, 0, spannable.length,
            TextPaint(paint), maxWidth.toInt()
        )
            .setLineSpacing(0f, lineSpacing)
            .build()
    }

    private fun applyStyle(paint: TextPaint, style: SpanStyle) {
        // 保留原有删除线状态
        val originalFlags = paint.flags

        // 字体样式处理
        val typeface = when {
            style.fontWeight == FontWeight.Bold && style.fontStyle == FontStyle.Italic ->
                Typeface.create(Typeface.DEFAULT, Typeface.BOLD_ITALIC)
            style.fontWeight == FontWeight.Bold ->
                Typeface.DEFAULT_BOLD
            style.fontStyle == FontStyle.Italic ->
                Typeface.create(Typeface.DEFAULT, Typeface.ITALIC)
            else -> Typeface.DEFAULT
        }
        paint.typeface = typeface ?: Typeface.DEFAULT

        // 处理半粗体
        if (style.fontWeight?.weight?.compareTo(FontWeight.Normal.weight) == 1) {
            paint.isFakeBoldText = true
        }

        // 颜色处理
        style.color.let { paint.color = it.toArgb() }

        // 恢复删除线状态并处理下划线
        paint.flags = originalFlags.let { flags ->
            var newFlags = flags
            if (style.textDecoration?.contains(TextDecoration.Underline) == true) {
                newFlags = newFlags and Paint.UNDERLINE_TEXT_FLAG.inv()
            }
            newFlags
        }
    }

    /**
     * 绘制待办事项
     * @param startY 起始Y坐标
     * @return 新的Y坐标
     */
    private fun drawTodoBlock(
        canvas: Canvas,
        todo: EditorContent.TodoBlock,
        text: String,
        spanStyles: List<AnnotatedString.Range<SpanStyle>>,
        startY: Float,
        pageWidth: Int,
        context: Context
    ): Float {
        val checkboxSize = 24f // 匹配实际图标尺寸
        val horizontalSpacing = 12f // 增加基础间距


        // 1. 加载矢量图标资源
        val (iconRes, textColor) = if (todo.isDone) {
            Pair(R.drawable.ic_todo_checked, R.color.btn_new_category_create)
        } else {
            Pair(R.drawable.ic_todo_unchecked, R.color.text_title)
        }

        // 2. 安全加载图标
        val checkIcon = loadVectorBitmap(
            context = context,
            resId = if (todo.isDone) R.drawable.ic_todo_checked else R.drawable.ic_todo_unchecked,
            size = 24 // 必须与checkboxSize一致
        ) ?: createFallbackCheckbox()

        // 3. 文本样式配置
        val textPaint = TextPaint().apply {
            color = textColor
            textSize = 14f
            typeface = Typeface.SANS_SERIF
            if (todo.isDone) flags = flags or Paint.STRIKE_THRU_TEXT_FLAG
        }

        // 4. 计算文本布局
        val maxTextWidth = pageWidth - margin.toInt() * 2 - checkboxSize.toInt() - horizontalSpacing.toInt()

        // 创建带完整样式的布局
        val textLayout = createStyledLayout(
            text = buildAnnotatedString {
                append(text)
                // 修复spanStyles引用问题
                todo.text.annotatedString.spanStyles.forEach { range ->
                    // 过滤颜色属性
                    val filteredStyle = range.item.copy(color = androidx.compose.ui.graphics.Color(textPaint.color))
                    addStyle(filteredStyle, range.start, range.end)
                }
            },
            indent = 0f,
            paint = textPaint,
            maxWidth = maxTextWidth.toFloat()
        )

        val iconTop = if (textLayout.lineCount > 0) {
            // 基于首行实际高度计算
            val lineHeight = textLayout.getLineBottom(0) - textLayout.getLineTop(0)
            startY + (lineHeight - checkIcon.height) / 2
        } else {
            startY // 异常情况处理
        }

        // 3. 先绘制图标
        canvas.drawBitmap(
            checkIcon,
            margin,
            iconTop,
            Paint().apply { isAntiAlias = true }
        )

        // 增加下划线绘制逻辑
        canvas.save()
        canvas.translate(
            margin + checkboxSize + horizontalSpacing,
            startY
        )
        textLayout.draw(canvas)

        // 绘制自定义下划线
        spanStyles.filter { it.item.textDecoration?.contains(TextDecoration.Underline) == true }
            .forEach { range ->
                val start = range.start.coerceIn(0, textLayout.text.length)
                val end = range.end.coerceIn(0, textLayout.text.length)

                for (line in textLayout.getLineForOffset(start)..textLayout.getLineForOffset(end)) {
                    val lineStart = textLayout.getLineStart(line).coerceAtLeast(start)
                    val lineEnd = textLayout.getLineEnd(line).coerceAtMost(end)

                    // 获取行实际绘制宽度
                    val lineRight = textLayout.getLineRight(line)

                    // 处理跨行情况
                    val adjustedEndX = when {
                        line == textLayout.lineCount - 1 -> {
                            // 最后一行使用文本实际宽度
                            textLayout.getPrimaryHorizontal(lineEnd).coerceAtMost(lineRight)
                        }
                        lineEnd < textLayout.text.length -> {
                            // 中间行使用行完整宽度
                            lineRight
                        }
                        else -> lineRight
                    }

                    if (lineStart < lineEnd) {
                        val baseline = textLayout.getLineBaseline(line)
                        val startX = textLayout.getPrimaryHorizontal(lineStart)

                        // 绘制扩展到行尾
                        canvas.drawLine(
                            startX,
                            baseline + 3f,
                            adjustedEndX,  // 使用调整后的结束点
                            baseline + 3f,
                            Paint().apply {
                                color = textPaint.color
                                strokeWidth = 1.5f
                                isAntiAlias = true
                                strokeCap = Paint.Cap.ROUND
                            }
                        )
                    }
                }
            }
        canvas.restore()

        return startY + textLayout.height + 6f
    }

    /**
     * 安全加载矢量图资源
     */
    private fun loadVectorBitmap(context: Context, resId: Int, size: Int): Bitmap? {
        return try {
            ContextCompat.getDrawable(context, resId)?.let { drawable ->
                Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888).apply {
                    Canvas(this).apply {
                        drawable.setBounds(0, 0, size, size)
                        drawable.draw(this)
                    }
                }
            }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 创建图标加载失败时的复选框
     */
    private fun createFallbackCheckbox(): Bitmap {
        val size = 24
        return Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888).apply {
            Canvas(this).apply {
                drawRect(0f, 0f, size.toFloat(), size.toFloat(), Paint().apply {
                    style = Paint.Style.STROKE
                    color = Color.GRAY
                    strokeWidth = 2f
                })
            }
        }
    }

    /**
     * 绘制图片块（支持自动缩放和错误处理）
     * @param startY 起始Y坐标
     * @return 新的Y坐标
     */
    private fun drawImageBlock(
        context: Context,
        canvas: Canvas,
        uri: Uri,
        startY: Float
    ): Float {
        try {
            context.contentResolver.openFileDescriptor(uri, "r")?.use { pfd ->
                // 第一阶段：获取图片尺寸
                val options = BitmapFactory.Options().apply { inJustDecodeBounds = true }
                BitmapFactory.decodeFileDescriptor(pfd.fileDescriptor, null, options)

                // 计算缩放比例
                val maxWidth = maxWidth - margin * 2
                val scale = maxWidth / options.outWidth
                val scaledHeight = options.outHeight * scale

                // 第二阶段：加载缩放后的图片
                options.inJustDecodeBounds = false
                options.inSampleSize = calculateInSampleSize(options, maxWidth)
                val bitmap = BitmapFactory.decodeFileDescriptor(pfd.fileDescriptor, null, options)

                bitmap?.let {
                    canvas.drawBitmap(
                        it,
                        Rect(0, 0, it.width, it.height),
                        Rect(
                            margin.toInt(),
                            startY.toInt(),
                            (margin + maxWidth).toInt(),
                            (startY + scaledHeight).toInt()
                        ),
                        null
                    )
                    return startY + scaledHeight + 10f
                }
            }
        } catch (e: Exception) {
            val errorPaint = TextPaint().apply {
                color = 0xFFCC0000.toInt()
                textSize = 10f
            }
            // 图片加载失败处理
            canvas.drawText(
                "["+context.getString(R.string.image_loading_failed)+"]",
                margin,
                startY + errorPaint.textSize,
                errorPaint
            )
            return startY + errorPaint.textSize + 10f
        }
        return startY
    }
    // endregion

    /**
     * 计算位图采样率（内存优化）
     */
    private fun calculateInSampleSize(options: BitmapFactory.Options, reqWidth: Float): Int {
        return (options.outWidth / reqWidth).toInt().coerceAtLeast(1)
    }

    /**
     * 分享PDF文件
     */
    private fun sharePdfFile(context: Context, file: File) {
        val contentUri = FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            file
        )

        Intent(Intent.ACTION_SEND).apply {
            type = "application/pdf"
            putExtra(Intent.EXTRA_STREAM, contentUri)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }.also { intent ->
            context.startActivity(Intent.createChooser(intent, context.getString(R.string.show_content_share)))
        }
    }
}