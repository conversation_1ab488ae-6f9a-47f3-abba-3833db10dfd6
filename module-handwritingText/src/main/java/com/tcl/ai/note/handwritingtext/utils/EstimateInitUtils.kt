package com.tcl.ai.note.handwritingtext.utils

import android.content.Context
import android.hardware.display.DisplayManager
import android.util.Log
import android.view.Display
import android.view.MotionEvent
import com.jideos.predengine.JidePredManager
import com.jideos.predengine.PointBean
import com.tcl.ai.note.utils.Logger

object EstimateInitUtils {

    private var jidePredManager: JidePredManager? = null

    fun init(context: Context) {
        jidePredManager = JidePredManager(context).apply {
            val xdpi = context.resources.displayMetrics.xdpi
            val ydpi = context.resources.displayMetrics.ydpi
            val screenX = context.resources.displayMetrics.widthPixels
            val screenY = context.resources.displayMetrics.heightPixels
            val displayManager: DisplayManager? =
                context.getSystemService(DisplayManager::class.java)
            val defaultDisplay = displayManager?.getDisplay(Display.DEFAULT_DISPLAY)
            setRefreshRate(defaultDisplay?.refreshRate ?: 60f)
            setXYDpi(xdpi, ydpi)
            setScreenPx(screenX.toFloat(), screenY.toFloat())

            setPenReportRate(240f)
            setScreenReportRate(240f)
            setPressureLevel(1)

            // 设置预测类型（手指还是手写笔）
            setPredType(3)
            // 设置预测等级
            setPredLevel(2)
            // 设置log级别
            setLogLevel(Log.DEBUG)
        }
    }

    fun release() {
        jidePredManager?.release()
        jidePredManager = null
    }

    fun doPredict(e: MotionEvent): PointBean? {
        val r = jidePredManager?.doPredict(e) ?: return null
        return if (r.isNotEmpty()) r.last() else null
    }

    fun setMaxSpeed(maxSpeed: Int?) {
        jidePredManager?.apply {
            if (maxSpeed != null) {
                isOpenFastPred(false)
                setMaxSpeed(maxSpeed)
            } else {
                isOpenFastPred(true)
            }
        }
    }
}