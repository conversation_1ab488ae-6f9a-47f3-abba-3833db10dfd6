package com.tcl.ai.note.handwritingtext.utils

import android.content.Context
import com.tcl.ai.note.utils.Logger

/**
 *  author : junze.liu
 *  date : 2025-07-16 21:08
 *  description : 通过feature属性，有配置的话，则为禁用防误触功能（目前Sunrise NA OM不支持防误触，其他机型暂未配置该属性）
 */

object FeatureUtils {

    const val key = "vendor.software.note.disable.hand.drawn.mode"

    fun hasSystemFeature(context: Context, key:String):<PERSON><PERSON><PERSON> {
        val result = context.packageManager.hasSystemFeature(key)
        return result
    }

    fun isDisableHandDrawnMode(context: Context):Boolean{
        //是否有配置feature属性，有配置的话，则为禁用防误触功能
        val disable = context.packageManager.hasSystemFeature(key)
        Logger.i("FeatureUtils","isDisableHandDrawnMod, disable:$disable")
        return disable
    }


}