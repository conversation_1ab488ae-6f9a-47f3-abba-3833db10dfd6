package com.tcl.ai.note.handwritingtext.utils

import android.content.ContentResolver
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.media.ExifInterface
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import com.tcl.ai.note.GlobalContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

object FileUtils {

    /**
     * 将系统相册URI复制到应用私有目录
     * @return 新文件的Uri，或null表示失败
     */
    fun copyUriToInternalStorage(context: Context, uri: Uri): Uri? {
        val contentResolver: ContentResolver = context.contentResolver
        var inputStream: InputStream? = null
        var outputStream: FileOutputStream? = null

        try {
            // 创建目标目录（如果不存在）
            val storageDir = File(context.filesDir, "images")
            if (!storageDir.exists()) {
                storageDir.mkdirs()
            }

            // 生成唯一文件名
            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            var fileName = "IMG_${timeStamp}.${getFileExtension(context, uri)}"
            val mimeType = context.contentResolver.getType(uri)
            if (mimeType == "image/gif") {
                fileName = "GIF_${timeStamp}.${getFileExtension(context, uri)}"
            }

            // 创建目标文件
            val destFile = File(storageDir, fileName)

            inputStream = contentResolver.openInputStream(uri)
            outputStream = FileOutputStream(destFile)
            inputStream?.use { input ->
                outputStream?.use { output ->
                    input.copyTo(output, bufferSize = 1024)
                }
            }

            // 压缩处理（仅处理JPEG/JPG且大于1MB）
            if (destFile.length() > 1 * 1024 * 1024 && mimeType?.startsWith("image/jpeg") == true) {
                compressJpegImage(destFile)
            }

            // 返回新文件的Uri
            return Uri.fromFile(destFile)
        } catch (e: Exception) {
            e.printStackTrace()
            // 删除可能存在的部分写入文件
            outputStream?.channel?.truncate(0)
            return null
        } finally {
            inputStream?.close()
            outputStream?.close()
        }
    }

    /**
     * JPEG图片压缩处理（尺寸缩放+质量压缩）
     */
    private fun compressJpegImage(imageFile: File) {
        var resizedBitmap: Bitmap? = null
        var rotatedBitmap: Bitmap? = null
        var outputStream: FileOutputStream? = null
        try {
            // 获取图片旋转角度
            val rotation = getExifRotation(imageFile.absolutePath)

            // 第一步：尺寸缩放
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(imageFile.absolutePath, options)

            val targetSize = 1024 // 目标最大边长
            val scale = calculateInSampleSize(options, targetSize)

            // 第二次解码时应用缩放
            resizedBitmap = BitmapFactory.Options().run {
                inSampleSize = scale
                inJustDecodeBounds = false
                BitmapFactory.decodeFile(imageFile.absolutePath, this)
            } ?: return

            // 第二步：旋转校正
            if (rotation != 0) {
                rotatedBitmap = rotateBitmap(resizedBitmap!!, rotation)
                if (rotatedBitmap != resizedBitmap) {
                    resizedBitmap.recycle()
                }
            } else {
                rotatedBitmap = resizedBitmap
            }

            // 第三步：质量压缩
            outputStream = FileOutputStream(imageFile)
            rotatedBitmap?.compress(Bitmap.CompressFormat.JPEG, 80, outputStream)
            outputStream.flush()
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            resizedBitmap?.recycle()
            rotatedBitmap?.recycle()
            outputStream?.close()
        }
    }

    /**
     * 旋转Bitmap
     */
    private fun rotateBitmap(source: Bitmap, degrees: Int): Bitmap {
        if (degrees == 0) return source
        var rotatedBitmap: Bitmap? = null
        try {
            val matrix = Matrix().apply {
                postRotate(degrees.toFloat())
            }
            rotatedBitmap = Bitmap.createBitmap(source, 0, 0, source.width, source.height, matrix, true)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return rotatedBitmap ?: source
    }

    /**
     * 计算合适的缩放比例
     */
    private fun calculateInSampleSize(options: BitmapFactory.Options, reqSize: Int): Int {
        val (height: Int, width: Int) = options.run { outHeight to outWidth }
        var inSampleSize = 1

        if (height > reqSize || width > reqSize) {
            val halfHeight: Int = height / 2
            val halfWidth: Int = width / 2
            while (halfHeight / inSampleSize >= reqSize && halfWidth / inSampleSize >= reqSize) {
                inSampleSize *= 2
            }
        }
        return inSampleSize
    }

    /**
     * 获取图片Exif旋转信息
     */
    private fun getExifRotation(imagePath: String): Int {
        return try {
            val exif = ExifInterface(imagePath)
            when (exif.getAttributeInt(
                ExifInterface.TAG_ORIENTATION,
                ExifInterface.ORIENTATION_UNDEFINED
            )) {
                ExifInterface.ORIENTATION_ROTATE_90 -> 90
                ExifInterface.ORIENTATION_ROTATE_180 -> 180
                ExifInterface.ORIENTATION_ROTATE_270 -> 270
                else -> 0
            }
        } catch (e: Exception) {
            0
        }
    }

    /**
     * 检查URI对应的文件是否存在且可访问
     */
    fun checkUriExists(context: Context, uri: Uri): Boolean {
        return when (uri.scheme) {
            ContentResolver.SCHEME_CONTENT -> checkContentUriExists(context, uri)
            ContentResolver.SCHEME_FILE -> checkFileUriExists(uri)
            else -> false
        }
    }

    // 辅助方法：获取文件扩展名
    private fun getFileExtension(context: Context, uri: Uri): String? {
        return context.contentResolver.getType(uri)?.substringAfterLast("/")
    }

    // 检查Content Uri是否存在
    private fun checkContentUriExists(context: Context, uri: Uri): Boolean {
        return try {
            context.contentResolver.openInputStream(uri)?.use {
                // 如果能成功打开流则认为存在
                true
            } ?: false
        } catch (e: SecurityException) {
            // 处理权限问题
            false
        } catch (e: IOException) {
            false
        }
    }

    // 检查File Uri是否存在
    private fun checkFileUriExists(uri: Uri): Boolean {
        return try {
            val path = uri.path ?: return false
            File(path).exists()
        } catch (e: SecurityException) {
            false
        }
    }

    /**
     * 兼容Android Q及以上的文件路径获取
     */
    fun getRealPathFromUri(context: Context, uri: Uri): String? {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> {
                // 使用直接文件描述符方式
                copyUriToInternalStorage(context, uri)?.path
            }
            else -> {
                // 传统方式查询媒体库
                val projection = arrayOf(MediaStore.Images.Media.DATA)
                context.contentResolver.query(uri, projection, null, null, null)?.use {
                    if (it.moveToFirst()) {
                        val columnIndex = it.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
                        it.getString(columnIndex)
                    } else {
                        null
                    }
                }
            }
        }
    }

    /**
     * 获取日记内容缩略图路径
     */
    fun getJournalContentThumbnailPath(
        journalId: Long,
        pageIndex: Int,
        context: Context = GlobalContext.instance,
    ): String {
        val saveDir = File(context.filesDir, "journalContentThumbnails/$journalId")
        if (!saveDir.exists()) {
            saveDir.mkdirs()
        }
        return saveDir.absolutePath + "/thumbnail_${pageIndex}.png"
    }

    /**
     * 删除回忆集内容
     */
    fun deleteJournalContent(journalId: Long, context: Context = GlobalContext.instance) {
        File(context.filesDir, "journalContentThumbnails/$journalId").delete()
        File(context.filesDir, "journalContentEnts/$journalId").delete()
    }

    /**
     * 回忆集内容 Bitmap 写入 filesDir 目录
     */
    fun saveJournalContentBitmapToFilesDir(
        bitmap: Bitmap,
        journalId: Long,
        pageIndex: Int,
        format: Bitmap.CompressFormat = Bitmap.CompressFormat.WEBP_LOSSY,
        quality: Int = 80   // PNG无损，JPG建议80~100
    ): Boolean {
        return try {
            val path = File(getJournalContentThumbnailPath(journalId = journalId, pageIndex = pageIndex))
            if (!path.exists()) {
                path.createNewFile()  // 只创建空文件
            }
            FileOutputStream(path).use { fos ->
                bitmap.compress(format, quality, fos)
            }
            true   // 保存成功
        } catch (e: Exception) {
            e.printStackTrace()
            false  // 保存失败
        }
    }

    /**
     * 获取回忆集内容ent路径
     */
    fun getJournalContentEntPath(
        journalId: Long,
        pageIndex: Int,
        context: Context = GlobalContext.instance,
    ): String {
        //调用保存接口
        val saveDir = File(context.filesDir, "journalContentEnts/$journalId")
        if (!saveDir.exists()) {
            saveDir.mkdirs()
        }
        return saveDir.absolutePath + "/ent_${pageIndex}.ent"
    }
}