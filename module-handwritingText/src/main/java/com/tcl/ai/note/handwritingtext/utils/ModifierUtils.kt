package com.tcl.ai.note.handwritingtext.utils

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.foundation.border
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.invisibleToUser
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.handwritingtext.database.entity.BgMode
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.utils.dp2px
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.screenSizeMin
import com.tcl.ai.note.utils.toPx

@SuppressLint("SuspiciousModifierThen")
fun Modifier.infiniteMeshBackground(
    bgMode: BgMode = BgMode.none,
    paddingTop:Dp = 17.dp,
    lineColor: Color = Color.LightGray.copy(alpha = 0.5f),
    screenWidthDp: Int = 0,
    width:Dp ,
    strokeWidth: Dp = 1.dp,
    lineSpace: Dp = Skin.lineHeight().dp,
    height: Float,
    isNeedBorder: Boolean = true
) = this.then(

    if (bgMode == BgMode.none) {
        this
    } else {

        drawBehind {
            val strokeWidthPx = strokeWidth.toPx()
            val lineSpacePx = lineSpace.toPx()

            val startY = (bgMode == BgMode.grid).judge(isTablet.judge(
                paddingTop.toPx(),
                lineSpacePx+paddingTop.toPx()),
                lineSpacePx+paddingTop.toPx())
            val widthPx = width.toPx
            var x = 0f
            if (bgMode == BgMode.grid) {
                while (x <= widthPx) {
                    /*if (isNeedBorder || (x >0f && x <= (width-lineSpacePx))) {
                        drawLine(
                            color = lineColor,
                            start = Offset(x, startY),
                            end = Offset(x, height),
                            strokeWidth = strokeWidthPx
                        )
                    }*/
                    drawLine(
                        color = lineColor,
                        start = Offset(x, startY),
                        end = Offset(x, height),
                        strokeWidth = strokeWidthPx
                    )
                    x += lineSpacePx
                }


            }
            var y = startY

            //横线
            while (y < height) {
                if (isNeedBorder ||  y != 0f) {
                    drawLine(
                        color = lineColor,
                        start = Offset(0f, y),
                        end = Offset(widthPx, y),
                        strokeWidth = strokeWidthPx
                    )
                }
                y += lineSpacePx
            }
        }
    }
)
fun  Modifier.borderCircle(
    width: Dp =1.dp,
    color: Color = Color.Gray,
) =this.border(
    width =width,
    color = color,
    shape = CircleShape
)


fun Modifier.invisibleSemantics() = this.semantics {
    invisibleToUser()
}

fun Modifier.defShadow(radius: Dp = 8.dp) = this.shadow(
    elevation = 6.dp, // 模糊半径近似值
    shape = RoundedCornerShape(radius), // 圆角与组件保持一致
    ambientColor = Color.Black.copy(alpha = 0.37f), // 阴影色与透明度
    spotColor = Color.Black.copy(alpha = 0.37f) // 阴影的"聚光"色，一般和 ambientColor 保持一致。
)
