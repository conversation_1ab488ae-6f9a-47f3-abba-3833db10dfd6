package com.tcl.ai.note.handwritingtext.utils

import android.graphics.Path
import android.graphics.Paint as AndroidPaint
import android.graphics.Path as AndroidPath
import androidx.compose.ui.graphics.Path as ComposePath
import android.graphics.PathMeasure
import android.graphics.RectF
import android.graphics.Region
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.asAndroidPath
import androidx.compose.ui.graphics.toAndroidRect
import androidx.compose.ui.util.fastForEach
import androidx.compose.ui.util.fastForEachIndexed
import com.tcl.ai.note.handwritingtext.bean.DrawPoint
import com.tcl.ai.note.handwritingtext.vo.DrawPathDisplay
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.windowedPairIndex
import kotlin.math.absoluteValue
import kotlin.math.hypot
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt
import kotlin.math.sqrt

fun List<DrawPoint>.toPath(offset: Offset = Offset(0f, 0f)) = AndroidPath().apply {
    <EMAIL> { i, drawPoint ->
        if (i == 0) {
            moveTo(drawPoint.x - offset.x, drawPoint.y - offset.y)
        } else {
            lineTo(drawPoint.x - offset.x, drawPoint.y - offset.y)
        }
    }
}

fun List<DrawPoint>.toPathBezier(offset: Offset = Offset(0f, 0f)) =
    AndroidPath().apply {
        when {
            <EMAIL>() -> {}
            <EMAIL> == 1 -> {
                val x = this@toPathBezier[0].x - offset.x
                val y = this@toPathBezier[0].y - offset.y
                moveTo(x, y)
                lineTo(x, y)
            }

            <EMAIL> == 2 -> {
                <EMAIL> { i, drawPoint ->
                    val x = drawPoint.x - offset.x
                    val y = drawPoint.y - offset.y
                    if (i == 0) {
                        moveTo(x, y)
                    } else {
                        lineTo(x, y)
                    }
                }
            }

            else -> {
                val x0 = this@toPathBezier[0].x - offset.x
                val y0 = this@toPathBezier[0].y - offset.y
                moveTo(x0, y0)
                for (i in 0 until <EMAIL> - 1) {
                    val p0x = this@toPathBezier[i].x - offset.x
                    val p0y = this@toPathBezier[i].y - offset.y
                    val p1x = this@toPathBezier[i + 1].x - offset.x
                    val p1y = this@toPathBezier[i + 1].y - offset.y
                    val mx = (p0x + p1x) / 2
                    val my = (p0y + p1y) / 2
                    if (i == 0) {
                        lineTo(mx, my)
                    } else {
                        quadTo(p0x, p0y, mx, my)
                    }
                }
                val xl = this@toPathBezier[size - 1].x - offset.x
                val yl = this@toPathBezier[size - 1].y - offset.y
                lineTo(xl, yl)
            }
        }
    }

fun List<DrawPoint>.forEachBezierPathSegmentWithWidth(
    path: AndroidPath,
    baseWidth: Float,
    offset: Offset = Offset(0f, 0f),
    widthStepLength: Float = WIDTH_STEP_LENGTH,
    action: (path: AndroidPath, width: Float) -> Unit,
) {
    when {
        isEmpty() -> {}
        size == 1 -> {
            action(path, baseWidth * this[0].pressureScale)
        }

        size == 2 -> {
            val start = this[0] - offset
            val end = this[1] - offset
            val (widthSteps, step) = (baseWidth * start.pressureScale).splitWithStepLength(
                baseWidth * end.pressureScale,
                widthStepLength
            )
            val actionPath = AndroidPath()
            val measure = PathMeasure()
            measure.setPath(path, false)
            measure.length.splitWithStep(step).windowedPairIndex { a, b, i ->
                actionPath.rewind()
                measure.getSegment(a, b, actionPath, true)
                action(actionPath, widthSteps[i])
            }
        }

        else -> {
            val subPath = AndroidPath()
            val actionPath = AndroidPath()
            val distanceSums = FloatArray(size - 1)
            var euLength = 0f
            for (i in 0 until size - 1) {
                val p1 = this[i] - offset
                val p2 = this[i + 1] - offset
                val euDistance = euDistance(p1.x, p1.y, p2.x, p2.y)
                euLength += euDistance
                distanceSums[i] = euLength
            }
            val measure = PathMeasure()
            val subMeasure = PathMeasure()
            measure.setPath(path, false)
            val pathLength = measure.length
            FloatArray(size) { i ->
                when (i) {
                    0 -> 0f
                    size - 1 -> 1f
                    else -> {
                        distanceSums[i - 1] / euLength
                    }
                }
            }.windowedPairIndex { a, b, i ->
                subPath.rewind()
                val startLength = a * pathLength
                val endLength = b * pathLength
                measure.getSegment(startLength, endLength, subPath, true)
                val startPressure = this[i].pressureScale
                val endPressure = this[i + 1].pressureScale
                val (widthSteps, step) = (baseWidth * startPressure).splitWithStepLength(
                    baseWidth * endPressure,
                    widthStepLength,
                )
                subMeasure.setPath(subPath, false)
                subMeasure.length.splitWithStep(step).windowedPairIndex { c, d, j ->
                    actionPath.rewind()
                    subMeasure.getSegment(c, d, actionPath, true)
                    action(actionPath, widthSteps[j])
                }
            }
        }
    }
}

fun List<DrawPoint>.toPathCompose(offset: Offset = Offset(0f, 0f)) =
    androidx.compose.ui.graphics.Path().apply {
        <EMAIL> { i, drawPoint ->
            if (i == 0) {
                moveTo(drawPoint.x - offset.x, drawPoint.y - offset.y)
            } else {
                lineTo(drawPoint.x - offset.x, drawPoint.y - offset.y)
            }
        }
    }

fun List<DrawPoint>.toPathBezierCompose(offset: Offset = Offset(0f, 0f)) =
    androidx.compose.ui.graphics.Path().apply {
        when {
            <EMAIL>() -> {}
            <EMAIL> == 1 -> {
                val x = this@toPathBezierCompose[0].x - offset.x
                val y = this@toPathBezierCompose[0].y - offset.y
                moveTo(x, y)
                lineTo(x, y)
            }

            <EMAIL> == 2 -> {
                <EMAIL> { i, drawPoint ->
                    val x = drawPoint.x - offset.x
                    val y = drawPoint.y - offset.y
                    if (i == 0) {
                        moveTo(x, y)
                    } else {
                        lineTo(x, y)
                    }
                }
            }

            else -> {
                val x0 = this@toPathBezierCompose[0].x - offset.x
                val y0 = this@toPathBezierCompose[0].y - offset.y
                moveTo(x0, y0)
                for (i in 0 until <EMAIL> - 1) {
                    val p0x = get(i).x - offset.x
                    val p0y = get(i).y - offset.y
                    val p1x = get(i + 1).x - offset.x
                    val p1y = get(i + 1).y - offset.y
                    val mx = (p0x + p1x) / 2
                    val my = (p0y + p1y) / 2
                    if (i == 0) {
                        lineTo(mx, my)
                    } else {
                        quadraticTo(p0x, p0y, mx, my)
                    }
                }
                val xl = get(size - 1).x - offset.x
                val yl = get(size - 1).y - offset.y
                lineTo(xl, yl)
            }
        }
    }

fun List<DrawPoint>.forEachBezierPathSegmentWithWidthCompose(
    path: ComposePath,
    baseWidth: Float,
    offset: Offset = Offset(0f, 0f),
    widthStepLength: Float = WIDTH_STEP_LENGTH,
    action: (path: androidx.compose.ui.graphics.Path, width: Float) -> Unit,
) {
    when {
        isEmpty() -> {}
        size == 1 -> {
            action(path, baseWidth * this[0].pressureScale)
        }

        size == 2 -> {
            val start = this[0] - offset
            val end = this[1] - offset
            val (widthSteps, step) = (baseWidth * start.pressureScale).splitWithStepLength(
                baseWidth * end.pressureScale,
                widthStepLength
            )
            val actionPath = ComposePath()
            val measure = androidx.compose.ui.graphics.PathMeasure()
            measure.setPath(path, false)
            measure.length.splitWithStep(step).windowedPairIndex { a, b, i ->
                actionPath.rewind()
                measure.getSegment(a, b, actionPath, true)
                action(actionPath, widthSteps[i])
            }
        }

        else -> {
            val subPath = androidx.compose.ui.graphics.Path()
            val actionPath = androidx.compose.ui.graphics.Path()
            val distanceSums = FloatArray(size - 1)
            var euLength = 0f
            for (i in 0 until size - 1) {
                val p1 = this[i] - offset
                val p2 = this[i + 1] - offset
                val euDistance = euDistance(p1.x, p1.y, p2.x, p2.y)
                euLength += euDistance
                distanceSums[i] = euLength
            }
            val measure = androidx.compose.ui.graphics.PathMeasure()
            val subMeasure = androidx.compose.ui.graphics.PathMeasure()
            measure.setPath(path, false)
            val pathLength = measure.length
            FloatArray(size) { i ->
                when (i) {
                    0 -> 0f
                    size - 1 -> 1f
                    else -> {
                        distanceSums[i - 1] / euLength
                    }
                }
            }.windowedPairIndex { a, b, i ->
                subPath.rewind()
                val startLength = a * pathLength
                val endLength = b * pathLength
                measure.getSegment(startLength, endLength, subPath, true)
                val startPressure = this[i].pressureScale
                val endPressure = this[i + 1].pressureScale
                val (widthSteps, step) = (baseWidth * startPressure).splitWithStepLength(
                    baseWidth * endPressure,
                    widthStepLength,
                )
                subMeasure.setPath(subPath, false)
                subMeasure.length.splitWithStep(step).windowedPairIndex { c, d, j ->
                    actionPath.rewind()
                    subMeasure.getSegment(c, d, actionPath, true)
                    action(actionPath, widthSteps[j])
                }
            }
        }
    }
}

fun euDistance(ax: Float, ay: Float, bx: Float, by: Float): Float {
    val dx = bx - ax
    val dy = by - ay
    return sqrt(dx * dx + dy * dy)
}

fun Float.splitWithStep(end: Float, step: Int): FloatArray {
    val delta = end - this
    val stepLength = delta / step
    return FloatArray(step + 1) { i ->
        if (i < step) {
            i * stepLength
        } else {
            this
        }
    }
}

fun Float.splitWithStep(step: Int): FloatArray {
    val stepLength = this / step
    return FloatArray(step + 1) { i ->
        if (i < step) {
            i * stepLength
        } else {
            this
        }
    }
}

fun Float.splitWithStepLength(end: Float, stepLength: Float): Pair<FloatArray, Int> {
    val delta = end - this
    val stepCount = (delta.absoluteValue / stepLength).roundToInt().coerceAtLeast(1)
    val stepLengthRound = delta / (stepCount - 1)
    return FloatArray(stepCount) { i ->
        if (i < stepCount - 1) {
            this + i * stepLengthRound
        } else {
            end
        }
    } to stepCount
}

fun DrawPathDisplay.isIntersectedWith(other: ComposePath, width: Float): Boolean {
    val regionA = this.path.toRegion(style.width)
    val regionB = other.toRegion(width)
    return regionA.op(regionB, Region.Op.INTERSECT)
}

fun DrawPathDisplay.isIntersectedWith(other: DrawPathDisplay): Boolean {
    val fillPath1 = this.path.asAndroidPath().toStrokedPath(this.style.width)
    val fillPath2 = if (other.radius <= POINT_SIZE) {
        AndroidPath().apply {
            addRect(
                RectF(
                    other.bounds.left - POINT_TOLERANCE_SIZE,
                    other.bounds.top - POINT_TOLERANCE_SIZE,
                    other.bounds.right + POINT_TOLERANCE_SIZE,
                    other.bounds.bottom + POINT_TOLERANCE_SIZE
                ),
                Path.Direction.CW
            )
        }
    } else {
        other.path.asAndroidPath().toStrokedPath(other.style.width)
    }

    val result = AndroidPath()
    val success = result.op(fillPath1, fillPath2, AndroidPath.Op.INTERSECT)
    return success && !result.isEmpty
}

fun AndroidPath.isIntersectedWith(other: AndroidPath): Boolean {
    val rect1 = RectF()
    this.computeBounds(rect1, true)
    val region1 = Region()
    region1.setPath(
        this, Region(
            rect1.left.roundToInt(),
            rect1.top.roundToInt(),
            rect1.right.roundToInt(),
            rect1.bottom.roundToInt(),
        )
    )

    val rect2 = RectF()
    other.computeBounds(rect2, true)
    val region2 = Region()
    region2.setPath(
        other, Region(
            rect2.left.roundToInt(),
            rect2.top.roundToInt(),
            rect2.right.roundToInt(),
            rect2.bottom.roundToInt(),
        )
    )
    return region1.op(region2, Region.Op.INTERSECT)
}

fun Pair<Offset, Offset>.isIntersectedWith(other: DrawPathDisplay, width: Float): Boolean {
    val start = this.first
    val end = this.second
    val path = ComposePath()
    path.moveTo(start.x, start.y)
    path.lineTo(end.x, end.y)
    return other.isIntersectedWith(path, width)
}

fun ComposePath.toRegion(width: Float) = Region().apply {
    setPath(
        <EMAIL>().toStrokedPath(width),
        Region(<EMAIL>().toAndroidRect())
    )
}

fun AndroidPath.toStrokedPath(strokeWidth: Float) =
    AndroidPath().also {
        AndroidPaint().apply {
            style = AndroidPaint.Style.STROKE
            this.strokeWidth = strokeWidth + ERASER_TOLERANCE_PX
        }.getFillPath(this@toStrokedPath, it)
    }

fun List<DrawPoint>.bounds(): RectF {
    if (isEmpty()) return RectF()
    var minX = first().x
    var maxX = first().x
    var minY = first().y
    var maxY = first().y
    fastForEach {
        minX = min(minX, it.x)
        maxX = max(maxX, it.x)
        minY = min(minY, it.y)
        maxY = max(maxY, it.y)
    }
    return RectF(minX, minY, maxX, maxY)
}

fun RectF.radius() = hypot(width(), height())

private const val WIDTH_STEP_LENGTH = 0.8f
private const val ERASER_TOLERANCE_PX = 2
private const val POINT_SIZE = 5
private const val POINT_TOLERANCE_SIZE = 10