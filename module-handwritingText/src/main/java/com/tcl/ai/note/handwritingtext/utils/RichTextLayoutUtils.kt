package com.tcl.ai.note.handwritingtext.utils

import android.graphics.BitmapFactory
import android.util.TypedValue
import android.view.View.MeasureSpec
import androidx.annotation.MainThread
import androidx.compose.ui.unit.IntSize
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.ImageBlockScaleMode
import com.tcl.ai.note.handwritingtext.richtext.utils.DisplayUtils
import com.tcl.ai.note.handwritingtext.richtext.viewholder.RichTextViewHolder.Companion.LINE_HEIGHT_SP
import com.tcl.ai.note.handwritingtext.richtext.viewholder.RichTextViewHolder.Companion.PADDING_HORIZONTAL_PHONE_DP
import com.tcl.ai.note.handwritingtext.richtext.viewholder.RichTextViewHolder.Companion.PADDING_HORIZONTAL_TABLET_DP
import com.tcl.ai.note.handwritingtext.richtext.viewholder.RichTextViewHolder.Companion.PADDING_VERTICAL_PHONE_DP
import com.tcl.ai.note.handwritingtext.richtext.viewholder.RichTextViewHolder.Companion.PADDING_VERTICAL_TABLET_DP
import com.tcl.ai.note.handwritingtext.richtext.viewholder.RichTextViewHolder.Companion.TEXT_SIZE_SP
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText
import com.tcl.ai.note.utils.dp2px
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.screenSizeMin
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt

object RichTextLayoutUtils {
    val horizontalPaddingPx = DisplayUtils.dp2px(
        isTablet.judge(
            PADDING_HORIZONTAL_TABLET_DP,
            PADDING_HORIZONTAL_PHONE_DP
        )
    )
    val verticalPaddingPx = DisplayUtils.dp2px(
        isTablet.judge(
            PADDING_VERTICAL_TABLET_DP,
            PADDING_VERTICAL_PHONE_DP
        )
    )
    private val availableTextWidth = screenSizeMin - horizontalPaddingPx * 2

    // 必须主线程使用，涉及到view测量
    @MainThread
    suspend fun getTextHeightPx(
        text: String,
        withPrefix: Boolean = false,
    ) = withContext(Dispatchers.Main) {
        val view = buildMeasuredTextLayout(text, withPrefix)
        RichTextMetricPx.measuredLineHeight * view.lineCount
    }

    fun getImageSizePx(imageContent: EditorContent.ImageBlock): IntSize {
        val path = imageContent.uri.path
        val options = BitmapFactory.Options().apply {
            inJustDecodeBounds = true
        }
        BitmapFactory.decodeFile(path, options)
        val measuredWidth = options.outWidth
        val measuredHeight = options.outHeight
        val textWidth = availableTextWidth.toFloat()
        val targetWidth = when (imageContent.scaleMode) {
            ImageBlockScaleMode.Large -> textWidth
            ImageBlockScaleMode.Origin -> measuredWidth.toFloat()
            ImageBlockScaleMode.Small -> when {
                measuredWidth > textWidth -> textWidth * 0.4f
                measuredWidth > textWidth / 2 -> measuredWidth * 0.4f
                else -> measuredWidth * 0.6f
            }
        }
        val realWidth = min(targetWidth, textWidth)
        val scale = realWidth / measuredWidth
        val realHeight = measuredHeight * scale
        return IntSize(realWidth.roundToInt(), realHeight.roundToInt())
    }

    @MainThread
    fun buildMeasuredTextLayout(text: String, withPrefix: Boolean = false) =
        AREditText(GlobalContext.instance).apply {
            setText(text)
            textSize = TEXT_SIZE_SP.toFloat()
            setLineHeight(TypedValue.COMPLEX_UNIT_SP, LINE_HEIGHT_SP.toFloat())
            // 需对齐富文本列表等项目宽度
            val prefixWidth = if (withPrefix) {
                28.dp2px
            } else {
                0
            }
            val availableWidth = max(availableTextWidth - prefixWidth, 0)
            // 模拟父容器约束：设置宽度为精确值，高度无限制
            val widthMeasureSpec = MeasureSpec.makeMeasureSpec(availableWidth, MeasureSpec.EXACTLY)
            val heightMeasureSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
            // 强制触发测量
            measure(widthMeasureSpec, heightMeasureSpec)
        }
}

// 必须主线程使用，涉及到view测量
data object RichTextMetricPx {
    val textSize: Float
    val measuredLineHeight: Float

    init {
        val view1 = RichTextLayoutUtils.buildMeasuredTextLayout("1")
        val view2 = RichTextLayoutUtils.buildMeasuredTextLayout("2\n")
        textSize = view1.textSize
        measuredLineHeight = view2.measuredHeight.toFloat() - view1.measuredHeight.toFloat()
    }
}

