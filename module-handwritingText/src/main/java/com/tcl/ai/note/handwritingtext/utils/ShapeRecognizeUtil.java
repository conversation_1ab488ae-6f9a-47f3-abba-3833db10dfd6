package com.tcl.ai.note.handwritingtext.utils;

import android.content.Context;
import android.text.TextUtils;
import com.google.gson.Gson;
import com.sunia.HTREngines.shape.sdk.Engine;
import com.sunia.HTREngines.shape.sdk.EngineAuthenticateCallback;
import com.sunia.HTREngines.shape.sdk.Params;
import com.sunia.HTREngines.shape.sdk.RecognitionMode;
import com.sunia.HTREngines.shape.sdk.RecognizeListener;
import com.sunia.HTREngines.shape.sdk.RecognizePoint;
import com.sunia.HTREngines.shape.sdk.editor.Editor;
import com.sunia.penengine.sdk.engine.VerifyInfo;
import com.sunia.penengine.sdk.operate.touch.TouchPoint;
import com.tcl.ai.note.utils.Logger;

import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class ShapeRecognizeUtil {
    private static final String TAG = "ShapeRecognize";
    private static ShapeRecognizeUtil shapeRecognizeUtil;
    Context context;
    private Engine recognizeEngine;
    private Editor editor;
    private VerifyInfo verifyInfo;
    private int level;
    private String fileDir;
    private String shapeLog;


    public static ShapeRecognizeUtil getInstance(Context context, VerifyInfo verifyInfo) {
        if (shapeRecognizeUtil == null) {
            shapeRecognizeUtil = new ShapeRecognizeUtil(context);
            shapeRecognizeUtil.init();
        }
        shapeRecognizeUtil.setVerifyInfo(verifyInfo);

        return shapeRecognizeUtil;
    }

    private void init() {
        if (shapeRecognizeUtil != null) {
            File file = new File(context.getExternalCacheDir(), "hwr_shape");
            if (!file.exists()) file.mkdirs();
            File logFile = new File(file, "shapelog");
            if (!logFile.exists()) logFile.mkdirs();
            shapeLog = logFile.getAbsolutePath();
            shapeRecognizeUtil.setLog(3, file.getAbsolutePath());
        }
    }

    private ShapeRecognizeUtil(Context context) {
        this.context = context;
    }

    public void setVerifyInfo(VerifyInfo verifyInfo) {
        this.verifyInfo = verifyInfo;
    }

    public void setShapeRecognizeParam(boolean enable) {
        if (enable) {
            createRecognizeEngine(verifyInfo);
            createEditor();
        }
    }

    public void createRecognizeEngine(VerifyInfo verifyInfo) {
        if (verifyInfo == null) {
            return;
        }
        long start =System.currentTimeMillis();
        Logger.d(TAG, " createRecognizeEngine start");
        if (recognizeEngine == null) {
            recognizeEngine = Engine.createInstance(context, verifyInfo.authEncryptionPath);
            recognizeEngine.setLogDir(this.fileDir);
            recognizeEngine.setDebugLevel(this.level);
            recognizeEngine.setInitializeCallback(new EngineAuthenticateCallback() {
                @Override
                public void onSuccess(Object o, String s, String s1) {
                    Logger.d(TAG, "auth success");
                }

                @Override
                public void onError(Exception e, String s) {
                    Logger.d(TAG, "auth error " + e.getMessage() + "  " + s);
                }

            });
            recognizeEngine.start();

        }
        Logger.d(TAG, " createRecognizeEngine end. cost time:"+(System.currentTimeMillis() - start));

    }

    public void setLog(int level, String filePath) {
        this.level = level;
        this.fileDir = filePath;
        if (recognizeEngine != null) {
            recognizeEngine.setLogDir(filePath);
            recognizeEngine.setDebugLevel(level);
        }
    }

    public void createEditor() {
        if (recognizeEngine == null || !recognizeEngine.isValidate()) {
            return;
        }
        long start =System.currentTimeMillis();
        Logger.d(TAG, " createEditor start");

        if (editor == null) {
            Params params = Params.createInstance();
            editor = recognizeEngine.createEditor(params);
            if (editor == null) return;
            params.setMode(RecognitionMode.MODE_ECR);
            params.setDataDir("configs");
            params.setConfigName("shape.conf");
            params.setResultCalculate(false);
            params.setResultCoordinate(true);
            params.setWordSplitTimeLot(500);
            params.setResultAssociational(true);
            params.setResultCandidate(true);
            params.setResultPartitionCoordinate(true);
            params.setResultSpanProcessed(true);
            editor.setRecognizeEngineListener(new RecognizeListener() {
                @Override
                public void onLoaded(Editor editor) {
                    Logger.d(TAG, "syncRecognize editor onLoaded . cost time:"+(System.currentTimeMillis() - start));
                }

                @Override
                public void onError(Editor editor, int i, Exception e) {
                    Logger.e(TAG, "syncRecognize editor onError " + i);
                }

                @Override
                public void onContentChanged(Editor editor, String s) {
                    Logger.d(TAG, "syncRecognize editor onContentChanged " + s);
                }

                @Override
                public void onAssociationalChanged(Editor editor, String s) {

                }

                @Override
                public void onCandidateChanged(Editor editor, String s) {

                }
            });
            editor.open(params);
        }
        Logger.d(TAG, "init editor " + editor);
    }

    public String recognize(double[] x, double[] y, double[] time) {
        if (x == null || y == null || time == null) {
            return null;
        }
        if (editor == null) {
            return null;
        }
        List<RecognizePoint> recognizePoints = new ArrayList<>();
        for (int i = 0; i < x.length; i++) {
            RecognizePoint point = new RecognizePoint();
            if (i == 0) {
                point.down((float) x[i], (float) y[i], (long) time[i]);
            } else if (i == x.length - 1) {
                point.up((float) x[i], (float) y[i], (long) time[i]);
            } else {
                point.move((float) x[i], (float) y[i], (long) time[i]);
            }
            recognizePoints.add(point);
        }

        String result = null;
        if (editor != null) {
            result = editor.syncRecognize(recognizePoints);
            Logger.d(TAG, "syncRecognize shape s: " + result);
        }
        if(level > 0 && isShape(result)){
            String name = "shape" + System.currentTimeMillis() + ".txt";
            File f =  new File(shapeLog, name);
            String json = new Gson().toJson(recognizePoints);
            // 方便调试结果保存到本地
//            FileUtils.stringSaveToFile(json,f.getAbsolutePath());
            Logger.d(TAG, "log " + f.getAbsolutePath());
        }
        return result;
    }

    public boolean isShape(String result) {
        if (TextUtils.isEmpty(result)) {
            return false;
        }
        try {
            JSONObject jsonObject = new JSONObject(result);
            String label = jsonObject.optString("label");
            if (TextUtils.isEmpty(label)) {
                return false;
            }
            String[] args = label.split(",");
            if (args.length == 0) {
                return false;
            }
            int type = Integer.parseInt(args[0]);
            if (type > 0) {
                return true;
            }
        } catch (Exception e) {

        }
        return false;
    }

    private String recognize(TouchPoint[] points){
        if (editor != null) {
            if (points == null || points.length==0) {
                return null;
            }
            List<RecognizePoint> recognizePoints = new ArrayList<>();
            int length = points.length;
            for (int i = 0; i < length; i++) {
                RecognizePoint point = new RecognizePoint();
                if (i == 0) {
                    point.down((float) points[i].x, (float) points[i].y, (long) points[i].time);
                } else if (i == length - 1) {
                    point.up((float) points[i].x, (float) points[i].y, (long) points[i].time);
                } else {
                    point.move((float) points[i].x, (float) points[i].y, (long) points[i].time);
                }
                recognizePoints.add(point);
            }
            String s = editor.syncRecognize(recognizePoints);
            return s;
        }
        return null;
    }

    public void close() {
        if (editor != null) {
            editor.close();
        }
        if (recognizeEngine != null) {
            recognizeEngine.release();
        }
    }
}
