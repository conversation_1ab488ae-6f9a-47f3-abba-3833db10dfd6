package com.tcl.ai.note.handwritingtext.utils

import com.sunia.penengine.sdk.operate.canvas.ScaleInfo
import com.tcl.ai.note.handwritingtext.ui.utils.scale.MatrixInfo

fun ScaleInfo.copy(
    scale: Float? = null,
    offsetX: Float? = null,
    offsetY: Float? = null,
    scaleCenterX: Float? = null,
    scaleCenterY: Float? = null,
): ScaleInfo {
    val ret = ScaleInfo()
    ret.scale = scale ?: this.scale
    ret.offsetX = offsetX ?: this.offsetX
    ret.offsetY = offsetY ?: this.offsetY
    ret.scaleCenterX = scaleCenterX ?: this.scaleCenterX
    ret.scaleCenterY = scaleCenterY ?: this.scaleCenterY
    return ret
}

fun ScaleInfo.reset(
    scale: Float,
    offsetX: Float,
    offsetY: Float,
    scaleCenterX: Float,
    scaleCenterY: Float,
): ScaleInfo {
    this.scale = scale
    this.offsetX = offsetX
    this.offsetY = offsetY
    this.scaleCenterX = scaleCenterX
    this.scaleCenterY = scaleCenterY
    return this
}

fun ScaleInfo.reset(
    scaleInfo: ScaleInfo
): ScaleInfo {
    this.scale = scaleInfo.scale
    this.offsetX = scaleInfo.offsetX
    this.offsetY = scaleInfo.offsetY
    this.scaleCenterX = scaleInfo.scaleCenterX
    this.scaleCenterY = scaleInfo.scaleCenterY
    return this
}

fun ScaleInfo.reset(
    matrixInfo: MatrixInfo
): ScaleInfo {
    this.scale = matrixInfo.scale
    this.offsetX = matrixInfo.offsetX
    this.offsetY = matrixInfo.offsetY
    this.scaleCenterX = matrixInfo.scaleCenterX
    this.scaleCenterY = matrixInfo.scaleCenterY
    return this
}

fun ScaleInfo.minus(
    matrixInfo: MatrixInfo
): ScaleInfo {
    this.scale /= matrixInfo.scale
    this.offsetX -= matrixInfo.offsetX
    this.offsetY -= matrixInfo.offsetY
    return this
}

fun ScaleInfo.minusAndSet(
    a: ScaleInfo,
    b: MatrixInfo
): ScaleInfo {
    this.scale = b.scale / a.scale
    this.offsetX = b.offsetX - a.offsetX
    this.offsetY = b.offsetY - a.offsetY
    return this
}