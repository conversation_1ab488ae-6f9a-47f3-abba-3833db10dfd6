package com.tcl.ai.note.handwritingtext.vm


import android.util.Log
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.handwritingtext.bean.DoodlePen
import com.tcl.ai.note.handwritingtext.bean.DrawMode
import com.tcl.ai.note.handwritingtext.bean.StrokeStyle
import com.tcl.ai.note.handwritingtext.bean.progressToDp
import com.tcl.ai.note.handwritingtext.bean.toStrName
import com.tcl.ai.note.handwritingtext.state.DrawBoardIntent
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.toArgbLong
import com.tcl.ai.note.utils.toComposeColor
import com.tcl.ai.note.utils.toPx
import kotlinx.coroutines.launch

/**
 * 笔刷大小状态管理ViewModel
 */
class BrushSliderModel: ViewModel() {
    var isInit by mutableStateOf(false)
        private  set
    private val _sliderPosition = mutableFloatStateOf(0f)
    val sliderPositionState: State<Float> =_sliderPosition


    private var fountainPenSliderPosition =0.575f //进度条的一半
    private var markpenPenSliderPosition = 0.575f
    private var ballpenSliderPosition = 0.575f

    private val _lastDoodlePen = mutableStateOf<DoodlePen>(DoodlePen.FountainPen)
    val lastDoodlePen: State<DoodlePen> = _lastDoodlePen

    fun getSliderPositionState(doodlePen: DoodlePen) = when (doodlePen) {
        DoodlePen.FountainPen -> fountainPenSliderPosition
        DoodlePen.Ballpen -> ballpenSliderPosition
        DoodlePen.Markpen -> markpenPenSliderPosition
        else -> sliderPositionState.value
    }


    /**
     * 恢复当前选择笔刷的上一次状态
     */
    fun initSliderPosition(doodlePen: DoodlePen){
        Logger.d(TAG,"initSliderPosition")
        _sliderPosition.floatValue = when(doodlePen){
            DoodlePen.FountainPen ->fountainPenSliderPosition
            DoodlePen.Ballpen ->ballpenSliderPosition
            DoodlePen.Markpen ->markpenPenSliderPosition
        }
    }
    fun loadCache(){
        Logger.d(TAG,"brush,loadCache")
        viewModelScope.launch {
            fountainPenSliderPosition = AppDataStore.getData(BrushSliderModel::fountainPenSliderPosition.name,fountainPenSliderPosition)
            markpenPenSliderPosition = AppDataStore.getData(BrushSliderModel::markpenPenSliderPosition.name,markpenPenSliderPosition)
            ballpenSliderPosition = AppDataStore.getData(BrushSliderModel::ballpenSliderPosition.name,ballpenSliderPosition)
            _lastDoodlePen.value = DoodlePen.toDoodPen(AppDataStore.getStringData(BrushSliderModel::lastDoodlePen.name,""))
            isInit =true
        }
    }

    fun updateSliderPosition(position:Float,doodlePen: DoodlePen){
        Logger.d(TAG,"brush,updateSliderPosition")
        viewModelScope.launch {
            _sliderPosition.floatValue =position
            when(doodlePen){
                DoodlePen.FountainPen ->{
                    fountainPenSliderPosition = position
                    AppDataStore.putData(BrushSliderModel::fountainPenSliderPosition.name,position)
                }
                DoodlePen.Ballpen ->{
                    ballpenSliderPosition = position
                    AppDataStore.putData(BrushSliderModel::ballpenSliderPosition.name,position)
                }
                DoodlePen.Markpen ->{
                    markpenPenSliderPosition = position
                    AppDataStore.putData(BrushSliderModel::markpenPenSliderPosition.name,position)
                }
            }
        }
    }

    fun saveLastDoodlePen(doodlePen: DoodlePen){
        _lastDoodlePen.value = doodlePen
        viewModelScope.launch {
            AppDataStore.putStringData(BrushSliderModel::lastDoodlePen.name,doodlePen.toStrName())
        }
    }
    fun getCurPenWidth(doodlePen: DoodlePen):Float{
        val progress= getSliderPositionState(doodlePen)
        return doodlePen.progressToDp(progress).toPx
    }


    fun recoverStatus(
        drawBoardViewModel: DrawBoardViewModel,
        selDoodlePen: DoodlePen,
    ) {
        Logger.d(TAG,"brush,recoverStatus")
        initSliderPosition(selDoodlePen)
        //把drawMode调整为笔刷，且恢复上次宽度
        sendDrawBoardIntent(
            drawBoardViewModel =drawBoardViewModel,
            pxValue = (selDoodlePen.progressToDp(sliderPositionState.value)).toPx,
            color =  selDoodlePen.color.toComposeColor(),
            selDoodlePen = selDoodlePen
        )

    }

    fun sendDrawBoardIntent(drawBoardViewModel: DrawBoardViewModel, strokeStyle: StrokeStyle) {
        drawBoardViewModel.sendIntent(DrawBoardIntent.ChangeStrokeStyle(strokeStyle))
    }

    fun sendDrawBoardIntent(
        drawBoardViewModel: DrawBoardViewModel,
        pxValue: Float,
        color: Color,
        selDoodlePen: DoodlePen) {
        Logger.d(TAG,"sendDrawBoardIntent,pxValue =${pxValue},drawMode = PEN ,doodlePen =${selDoodlePen}")
        with(drawBoardViewModel) {
            sendIntent(
                DrawBoardIntent.ChangeStrokeStyle(
                    strokeStyle.copy(
                        width = pxValue,
                        drawMode = DrawMode.PEN,
                        color = color.toArgbLong(),
                        doodlePen = selDoodlePen
                    )
                )
            )
        }
    }

    companion object {
        private const val TAG = "BrushSliderModel"
    }
}