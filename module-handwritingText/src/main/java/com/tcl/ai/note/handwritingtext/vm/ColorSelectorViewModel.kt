package com.tcl.ai.note.handwritingtext.vm

import android.util.Log
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.handwritingtext.bean.DoodlePen
import com.tcl.ai.note.handwritingtext.bean.DrawMode
import com.tcl.ai.note.handwritingtext.state.DrawBoardIntent
import com.tcl.ai.note.theme.red
import com.tcl.ai.note.theme.yellow
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.toArgbLong
import kotlinx.coroutines.launch

class ColorSelectorViewModel : ViewModel() {
    private val _selectedColor = mutableStateOf(Color.Black)
    val selectedColor: State<Color> = _selectedColor
    private val _selectedColor1 = mutableStateOf(yellow)
    val selectedColor1: State<Color> = _selectedColor1
    private val _selectedColor2 = mutableStateOf(red)
    val selectedColor2: State<Color> = _selectedColor2

    private val _sliderPosition = mutableFloatStateOf(0f)
    val sliderPositionState: State<Float> = _sliderPosition
    private val _sliderPosition1 = mutableFloatStateOf(0f)
    val sliderPositionState1: State<Float> = _sliderPosition1
    private val _sliderPosition2 = mutableFloatStateOf(0f)
    val sliderPositionState2: State<Float> = _sliderPosition2

    var currentSelectorIdx = mutableIntStateOf(0)

    var isInit by mutableStateOf(false)
        private set

    // 默认情况下，7种颜色均匀分布位置
    val stopsColor = listOf(0.0f, 0.16f, 0.33f, 0.5f, 0.66f, 0.83f, 1.0f)

    /**
     * 恢复当前选择笔刷的上一次状态
     */
    fun initSliderPosition(doodlePen: DoodlePen) {
        Logger.d(TAG,"color,initSliderPosition")

        _selectedColor.value = lerpColor(fraction = _sliderPosition.floatValue)
        _selectedColor1.value = lerpColor(fraction = _sliderPosition1.floatValue)
        _selectedColor2.value = lerpColor(fraction = _sliderPosition2.floatValue)
        doodlePen.color = when (currentSelectorIdx.intValue) {
            0 -> _selectedColor.value
            1 -> _selectedColor1.value
            else -> _selectedColor2.value
        }.toArgbLong()
    }

    fun loadCache() {
        Logger.d(TAG,"color,loadCache")
        viewModelScope.launch {
            _sliderPosition.floatValue =
                AppDataStore.getData(::_sliderPosition.name, 0f)
            _sliderPosition1.floatValue =
                AppDataStore.getData(::_sliderPosition1.name, 0.66f)
            _sliderPosition2.floatValue =
                AppDataStore.getData(::_sliderPosition2.name, 0.33f)
            isInit = true
        }
    }

    fun updatePenColor(color: Color) {
        Logger.d(TAG,"color,updatePenColor")

        when (currentSelectorIdx.intValue) {
            0 -> _selectedColor.value = color
            1 -> _selectedColor1.value = color
            else -> _selectedColor2.value = color
        }
    }

    /**
     * 计算滑块位置的颜色
     */
    fun lerpColor(colors: List<Color> = getPaletteColorList(), fraction: Float): Color {
        val position = (colors.size - 1) * fraction
        val i = position.toInt()
        val colorFraction = position - i

        val (startColor, endColor) = colors[i] to colors[(i + 1).coerceAtMost(colors.size - 1)]

        return startColor.copy(
            red = startColor.red + (endColor.red - startColor.red) * colorFraction,
            green = startColor.green + (endColor.green - startColor.green) * colorFraction,
            blue = startColor.blue + (endColor.blue - startColor.blue) * colorFraction
        )
    }

    /**
     * 获取编辑页面调色板颜色列表
     */

    fun getPaletteColorList(): List<Color> {
        return listOf(
            Color(0xff000000),
            Color(0xffFFFFFF),
            Color(0xffFF0601),
            Color(0xffFF8001),
            Color(0xffFDFF01),
            Color(0xff01FD20),
            Color(0xff110FFF)
        )
    }

    fun updateSliderPosition(position: Float, doodlePen: DoodlePen) {
        Logger.d(TAG,"color,updateSliderPosition")
        viewModelScope.launch {
            when (currentSelectorIdx.intValue) {
                0 -> {
                    _sliderPosition.floatValue = position
                    AppDataStore.putData(
                        ::_sliderPosition.name,
                        _sliderPosition.floatValue
                    )
                }

                1 -> {
                    _sliderPosition1.floatValue = position
                    AppDataStore.putData(
                        ::_sliderPosition1.name,
                        _sliderPosition1.floatValue
                    )
                }

                else -> {
                    _sliderPosition2.floatValue = position
                    AppDataStore.putData(
                        ::_sliderPosition2.name,
                        _sliderPosition2.floatValue
                    )
                }
            }
        }
    }

    fun sendDrawBoardIntent(drawBoardViewModel: DrawBoardViewModel) {
        Logger.d(TAG,"color,sendDrawBoardIntent")
        with(drawBoardViewModel) {
            sendIntent(
                DrawBoardIntent.ChangeStrokeStyle(
                    strokeStyle.copy(
                        color = when (currentSelectorIdx.intValue) {
                            0 -> selectedColor.value
                            1 -> selectedColor1.value
                            else -> selectedColor2.value
                        }.toArgbLong(),
                        // 重置橡皮状态，避免切换至颜色调节时无法绘制
                        // TODO: 需要恢复上次选择的笔刷
                        drawMode =
                        if (strokeStyle.drawMode == DrawMode.ERASER) {
                            DrawMode.PEN
                        } else {
                            strokeStyle.drawMode
                        },
                    )
                )
            )
            Logger.d(TAG,"change board color strokeStyle =${strokeStyle}")
        }
    }

    fun getStops(index: Int): Float {
        return stopsColor.getOrNull(index) ?: let { 0.0f }
    }
    companion object {
        private const val TAG = "ColorSelectorViewModel"
    }
}

