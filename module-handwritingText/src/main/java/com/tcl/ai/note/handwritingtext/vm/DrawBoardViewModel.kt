package com.tcl.ai.note.handwritingtext.vm

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.util.fastForEach
import androidx.compose.ui.util.fastMapTo
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.event.AIEventType
import com.tcl.ai.note.event.AIReplaceEvent
import com.tcl.ai.note.handwritingtext.bean.*
import com.tcl.ai.note.handwritingtext.vo.DrawPathDisplay
import com.tcl.ai.note.handwritingtext.database.entity.Draw
import com.tcl.ai.note.handwritingtext.intent.CanvasDrawEvent
import com.tcl.ai.note.handwritingtext.lowlatency.gl.FastRender
import com.tcl.ai.note.handwritingtext.repo.DrawBoardRepository
import com.tcl.ai.note.handwritingtext.repo.EntRepository
import com.tcl.ai.note.handwritingtext.state.DrawBoardIntent
import com.tcl.ai.note.handwritingtext.track.AnalyticsHandWritingTextModel
import com.tcl.ai.note.handwritingtext.utils.isIntersectedWith
import com.tcl.ai.note.handwritingtext.utils.toPathBezierCompose
import com.tcl.ai.note.handwritingtext.utils.toPathCompose
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.coerceAtMost
import com.tcl.ai.note.utils.extractAndRemove
import com.tcl.ai.note.utils.launchIO
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import kotlin.math.max

@Deprecated("仅使用于一期")
class DrawBoardViewModel : ViewModel() {
    var noteId = 0L
        private set

    var scale by mutableFloatStateOf(1f)
    var translation by mutableStateOf(Offset(0f, 0f))
        private set
    var panOnly by mutableStateOf(false)

    // 富文本布局大小
    var richTextSize by mutableStateOf(IntSize(0, 0))
        private set

    var translationLimit by mutableStateOf(Offset(0f, 0f))
    private val transFlow = MutableSharedFlow<Offset>()
    private val transDeltaFlow = MutableSharedFlow<Offset>()
    private var lastTextTrans = Offset(0f, 0f)

    private var lastEraserUpdateTime = System.currentTimeMillis()
    private val eraserPath = Path()
    private var eraserLastPoint = Offset.Unspecified
    private val eraserFLow = MutableSharedFlow<CanvasDrawEvent>()
    private val eraserRemovePaths = mutableListOf<DrawPathDisplay>()
    internal var eraserCurrentPoint by mutableStateOf(Offset.Unspecified)
        private set

    private val _canvasDrawEventFlow = MutableSharedFlow<CanvasDrawEvent>()
    internal val canvasDrawEventFlow = _canvasDrawEventFlow.asSharedFlow()
    internal val currentCanvasDrawPoints = mutableStateListOf<DrawPoint>()
    internal var currentCanvasDrawPath by mutableStateOf(Path())

    internal var currentMotionEvent by mutableStateOf(DrawMotionEvent.IDLE)
    internal var lastPosition = Offset.Unspecified
    internal var currentPosition by mutableStateOf(Offset.Unspecified)

    var displayPaths by mutableStateOf(emptyList<DrawPathDisplay>())

    private val operationStack = ArrayDeque<Operation>()
    private val redoStack = ArrayDeque<Operation>()
    var canUndo by mutableStateOf(false)
        private set
    var canRedo by mutableStateOf(false)
        private set
    var isStrokesEmpty by mutableStateOf(true)
        private set

    private val intentFlow = MutableSharedFlow<DrawBoardIntent>()
    internal val fastRenderIntentFlow = MutableSharedFlow<FastRender.Intent>(
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    internal var strokeStyle = StrokeStyle()
        private set
    private val updateTaskFlow = MutableSharedFlow<Draw>()
    private val insertStrokeFlow = MutableSharedFlow<DrawStroke>()

    private var drawEntity = Draw()

    var isShowPenDrawer by mutableStateOf(false)
        private set
    var isShowColorDrawer by mutableStateOf(false)
        private set
    var isShowEraserDrawer by mutableStateOf(false)
        private set
    var isShowSkinDrawer by mutableStateOf(false)
        private set
    var isShowFontDrawer by mutableStateOf(false)
        private set

    // TODO 此状态待处理
    var isLassoOn by mutableStateOf(false)
//        private set

    // TODO 此状态待处理
    var isRulerOn by mutableStateOf(false)
//        private set

    // TODO 此状态待处理
    var isHWTTOn by mutableStateOf(false)
//        private set

    // TODO 此状态待处理
    var isBeautifyOn by mutableStateOf(false)
//        private set


    init {
        // 把当前viewmodel一些数据，加入分析
        AnalyticsHandWritingTextModel.loadDrawBoardViewModel(this)
        viewModelScope.launch {
            // 替换事件，要清空画笔
            AIReplaceEvent.getAIReplaceEvent().collect { replace ->
                if (replace is AIEventType.HandwritingToTextReplace) {
                    sendIntent(DrawBoardIntent.ClearAll(false))
                }
            }
        }
    }

    fun goneDrawer(excludeIndex: Int? = null) {
        val drawers = listOf(
            ::isShowPenDrawer,
            ::isShowSkinDrawer,
            ::isShowColorDrawer,
            ::isShowEraserDrawer,
            ::isShowFontDrawer
        )
        drawers.forEachIndexed { index, kMutableProperty0 ->
            if (index != excludeIndex) {
                kMutableProperty0.set(false)
            }

        }
    }

    fun transformPenDrawerVisible() {
        isShowPenDrawer = !isShowPenDrawer
        goneDrawer(0)
    }

    fun transformSkinDrawerVisible() {
        isShowSkinDrawer = !isShowSkinDrawer
        goneDrawer(1)
    }

    fun transformColorDrawerDrawerVisible() {
        isShowColorDrawer = !isShowColorDrawer
        goneDrawer(2)
    }

    fun transformEraserDrawerVisible() {
        isShowEraserDrawer = !isShowEraserDrawer
        goneDrawer(3)
    }

    fun transformFontDrawerVisible() {
        isShowFontDrawer = !isShowFontDrawer
        goneDrawer(4)
    }


    internal fun addPaths(
        points: List<DrawPoint>,
        style: StrokeStyle,
        needAddTrans: Boolean = true
    ) {
        Logger.d(
            TAG,
            "add path, style: $style, strokeStyle: $strokeStyle points size: ${points.size}, first: ${
                points.getOrNull(0)
            }"
        )
        if (style.drawMode == DrawMode.ERASER
            && drawEntity.strokes.isEmpty()
            && displayPaths.isEmpty()
        ) {
            Logger.v(TAG, "draw mode is eraser and path is empty, skip add path")
            return
        }
        val path = when {
            style.drawMode == DrawMode.ERASER ->
                points.toPathCompose(
                    if (needAddTrans) translation
                    else Offset(0f, 0f)
                )

            else ->
                points.toPathBezierCompose(
                    if (needAddTrans) translation
                    else Offset(0f, 0f)
                )
        }
        val transPoints = mutableListOf<DrawPoint>()
        points.fastForEach { drawPoint ->
            val x = if (needAddTrans) {
                (drawPoint.x - translation.x) / scale
            } else {
                drawPoint.x
            }
            val y = if (needAddTrans) {
                (drawPoint.y - translation.y) / scale
            } else {
                drawPoint.y
            }
            transPoints.add(drawPoint.copy(x = x, y = y))
        }

        val drawPathDisplay =
            DrawPathDisplay(path = path, points = transPoints, style = style)
        val drawPaths = listOf(drawPathDisplay)
        val tmpPaths = displayPaths.toMutableList()
        tmpPaths.add(drawPathDisplay)
        displayPaths = tmpPaths
        operationStack.addLast(Operation.Add(drawPaths))
        redoStack.clear()
        updateCanUndoRedo()
        insertStrokeAsync(drawPathDisplay.toDrawStroke())
    }

    private fun insertStrokeAsync(stroke: DrawStroke) {
        viewModelScope.launchIO {
            insertStrokeFlow.emit(stroke)
        }
    }

    private var observeNoteChangedByDatabaseJob: Job? = null
    private fun observeNoteChangedByDatabase(noteId: Long) {
        observeNoteChangedByDatabaseJob?.cancel()
        // 监听数据库实体类变化
        observeNoteChangedByDatabaseJob = viewModelScope.launchIO {
            DrawBoardRepository.getFlowByNoteId(noteId).filterNotNull().collect {
                drawEntity = it
            }
        }
    }

    fun updateNoteIdAndLoadStrokes(noteId: Long) {
        this.noteId = noteId
        if (noteId == -1L)
            return
        viewModelScope.launchIO {
            val tmpPersisPaths = mutableListOf<DrawPathDisplay>()
            val draw = DrawBoardRepository.getDrawByNoteIdBlock(noteId)
            observeNoteChangedByDatabase(noteId)
            if (draw == null) {
                drawEntity = drawEntity.copy(noteId = noteId)
                val id = DrawBoardRepository.insertOrReplaceDrawBlock(drawEntity)
                drawEntity = drawEntity.copy(id = id)
            } else {
                drawEntity = draw
            }
            isStrokesEmpty = drawEntity.strokes.isEmpty()
            Logger.v(TAG, "updateNoteIdAndLoadStrokes, noteId: $noteId, draw: $drawEntity")
            // 如果显示的path为空，说明第一次进入并读取，那路径加入持久层
            // 如果不为空，说明进入时是空画板，绘制一次后刷新创建的note，不需要加入持久层
            if (displayPaths.isEmpty()) {
                tmpPersisPaths.addAll(drawEntity.strokes.map { it.toDrawPathDisplay() })
                displayPaths = tmpPersisPaths
            }
        }
    }

    fun emitCanvasDrawEvent(motion: DrawMotionEvent, point: DrawPoint) {
        viewModelScope.launchIO {
            val transPoint = point - translation
            if (ENABLE_LINE_ERASER && strokeStyle.drawMode == DrawMode.ERASER) {
                eraserFLow.emit(CanvasDrawEvent(motion, transPoint))
            } else {
                _canvasDrawEventFlow.emit(CanvasDrawEvent(motion, transPoint))
            }
        }
    }

    // 同步更新橡皮圆圈位置，避免异步导致延迟
    fun updateEraserCirclePoint(point: DrawPoint? = null) {
        if (point == null || point.toOffset() == Offset.Unspecified) {
            eraserCurrentPoint = Offset.Unspecified
            return
        }
        val transPoint = point - translation
        eraserCurrentPoint = transPoint.toOffset()
    }

    fun updateRichTextSize(intSize: IntSize) {
        val width = max(intSize.width, GlobalContext.screenWidth)
        val height = max(intSize.height, GlobalContext.screenHeight)
        richTextSize = IntSize(width, height)
    }

    fun updateTrans(x: Float = 0f, y: Float = 0f) {
        viewModelScope.launchIO {
            transFlow.emit(Offset(x.coerceAtMost(0f), y.coerceAtMost(0f)))
        }
    }

    fun updateTransByDelta(x: Float = 0f, y: Float = 0f) {
        if (x == 0f && y == 0f) return
        viewModelScope.launchIO {
            transDeltaFlow.emit(Offset(x, y))
        }
    }

    fun saveLastTextTrans() {
        lastTextTrans = translation.copy()
    }

    fun resumeLastTextTrans() {
        updateTrans(lastTextTrans.x, lastTextTrans.y)
    }

    private fun handleFlow() {
        viewModelScope.launchIO {
            updateTaskFlow.collect { entity ->
                if (noteId != -1L) {
                    entity.copy(modifyTime = System.currentTimeMillis()).let {
                        DrawBoardRepository.updateStrokes(it)
                    }
                }
            }
        }
        viewModelScope.launchIO {
            transFlow.collect {
                translation = it
            }
        }
        viewModelScope.launchIO {
            transDeltaFlow.collect {
                translation = (translation + it).coerceAtMost(0f)
            }
        }
        viewModelScope.launchIO {
            insertStrokeFlow.collect { stroke ->
                val tmpList = drawEntity.strokes.toMutableList()
                tmpList.add(stroke)
                drawEntity = drawEntity.copy(
                    strokes = tmpList,
                    modifyTime = System.currentTimeMillis(),
                )
                isStrokesEmpty = drawEntity.strokes.isEmpty()
                Logger.v(TAG, "draw: $drawEntity")
                updateTaskFlow.emit(drawEntity)
            }
        }
        viewModelScope.launchIO {
            eraserFLow.collect { event ->
                val motionEvent = event.motionEvent
                val point = event.drawPoint
                when (motionEvent) {
                    DrawMotionEvent.IDLE -> {
                        eraserPath.rewind()
                        eraserLastPoint = Offset.Unspecified
                        eraserRemovePaths.clear()
                    }

                    DrawMotionEvent.DOWN -> {
                        eraserPath.rewind()
                        eraserLastPoint = point.toOffset()
                        eraserRemovePaths.clear()
                    }

                    DrawMotionEvent.MOVE -> {
                        eraserPath.rewind()
                        if (eraserLastPoint == Offset.Unspecified)
                            eraserLastPoint = point.toOffset()
                        val path = Path()
                        path.moveTo(eraserLastPoint.x, eraserLastPoint.y)
                        path.lineTo(point.x, point.y)
                        val eraserPath = DrawPathDisplay(
                            path = path,
                            points = listOf(eraserLastPoint.toDrawPoint(), point),
                            style = strokeStyle.copy()
                        )
                        val tmpDisplayPaths = displayPaths.toMutableList()
                        val remove = tmpDisplayPaths.extractAndRemove {
                            eraserPath.isIntersectedWith(it)
                        }
                        eraserRemovePaths.addAll(remove)
                        displayPaths = tmpDisplayPaths
                        eraserLastPoint = point.toOffset()
                    }

                    DrawMotionEvent.UP -> {
                        eraserPath.rewind()
                        eraserLastPoint = Offset.Unspecified
                        if (eraserRemovePaths.isEmpty()) return@collect
                        operationStack.addLast(Operation.Remove(eraserRemovePaths.toList()))
                        redoStack.clear()
                        updateCanUndoRedo()
                        val tmpList = drawEntity.strokes.toMutableList()
                        val removeIds = eraserRemovePaths.mapTo(HashSet()) { it.id }
                        tmpList.removeIf { it.id in removeIds }
                        drawEntity = drawEntity.copy(
                            strokes = tmpList
                        )
                        isStrokesEmpty = drawEntity.strokes.isEmpty()
                        updateTaskFlow.emit(drawEntity)
                    }
                }
            }
        }
    }

    private fun handleIntent() {
        viewModelScope.launchIO {
            intentFlow.collect { intent ->
                when (intent) {
                    is DrawBoardIntent.ChangeStrokeStyle -> {
                        strokeStyle = with(intent.strokeStyle) {
                            copy(
                                color = when {
                                    drawMode == DrawMode.ERASER -> 0x0000000
                                    doodlePen == DoodlePen.Markpen -> (0x80000000 or (color and 0x00FFFFFF))
                                    else -> color
                                }
                            )
                        }
                        Logger.d(TAG, "change stroke style: $strokeStyle")
                        fastRenderIntentFlow.emit(FastRender.Intent.ChangeStrokeStyle(strokeStyle))
                    }

                    is DrawBoardIntent.ClearAll -> clearAllPaths(intent.canRedo)
                    DrawBoardIntent.UndoLast -> undoLast()
                    DrawBoardIntent.RedoLast -> redoLast()
                }
            }
        }
    }

    fun sendIntent(drawBoardIntent: DrawBoardIntent) {
        viewModelScope.launchIO {
            intentFlow.emit(drawBoardIntent)
        }
    }

    private fun clearAllPaths(canRedo: Boolean = true) {
        clearFastRender()
        if (displayPaths.isEmpty())
            return
        if (canRedo) {
            operationStack.addLast(Operation.Remove(displayPaths))
        }
        displayPaths = emptyList()
        drawEntity = drawEntity.copy(
            strokes = emptyList()
        )
        redoStack.clear()
        updateCanUndoRedo()
        isStrokesEmpty = drawEntity.strokes.isEmpty()
        viewModelScope.launchIO {
            updateTaskFlow.emit(drawEntity)
        }
    }

    // 撤销
    private fun undoLast() {
        clearFastRender()
        if (operationStack.isEmpty())
            return
        val tmpDisplayPaths = displayPaths.toMutableList()
        when (val lastOperation = operationStack.removeLast()) {
            is Operation.Add -> {
                // 上一次操作是加的，那撤销就是删除
                val removed = tmpDisplayPaths.extractAndRemove { path ->
                    path.id in lastOperation.ids
                }
                redoStack.addLast(Operation.Remove(removed))
            }

            is Operation.Remove -> {
                // 上一次是删除的，那撤销就是加
                tmpDisplayPaths.addAll(lastOperation.paths)
                redoStack.addLast(Operation.Add(lastOperation.paths))
            }
        }
        displayPaths = tmpDisplayPaths
        isStrokesEmpty = displayPaths.isEmpty()
        updateCanUndoRedo()
        viewModelScope.launchIO {
            drawEntity = drawEntity.copy(
                strokes = mutableListOf<DrawStroke>().apply {
                    displayPaths.fastMapTo(this) { it.toDrawStroke() }
                }
            )
            updateTaskFlow.emit(drawEntity)
        }
    }

    // 重做
    private fun redoLast() {
        clearFastRender()
        if (redoStack.isEmpty())
            return
        Logger.v(TAG, "redo: ${redoStack.last()}")
        val tmpDisplayPaths = displayPaths.toMutableList()
        when (val lastRedo = redoStack.removeLast()) {
            is Operation.Add -> {
                // 上一次撤销是加的，那重做就是删除
                val removed = tmpDisplayPaths.extractAndRemove {
                    it.id in lastRedo.ids
                }
                operationStack.addLast(Operation.Remove(removed))
            }

            is Operation.Remove -> {
                // 上一次撤销是删除，那重做就是加
                tmpDisplayPaths.addAll(lastRedo.paths)
                operationStack.addLast(Operation.Add(lastRedo.paths))
            }
        }
        displayPaths = tmpDisplayPaths
        isStrokesEmpty = displayPaths.isEmpty()
        updateCanUndoRedo()
        viewModelScope.launchIO {
            drawEntity = drawEntity.copy(
                strokes = mutableListOf<DrawStroke>().apply {
                    displayPaths.fastMapTo(this) { it.toDrawStroke() }
                }
            )
            updateTaskFlow.emit(drawEntity)
        }
    }

    private fun updateCanUndoRedo() {
        canUndo = !operationStack.isEmpty() && redoStack.size < 50
        canRedo = !redoStack.isEmpty()
    }

    internal fun clearFastRender() {
        viewModelScope.launchIO {
            fastRenderIntentFlow.emit(FastRender.Intent.Clear)
        }
    }

    internal fun cancelFastRender() {
        viewModelScope.launchIO {
            // 清空Canvas绘制
            currentCanvasDrawPoints.clear()
            currentCanvasDrawPath.rewind()
            currentPosition = Offset.Unspecified
            // 清空前端画板
            fastRenderIntentFlow.emit(FastRender.Intent.Cancel)
        }
    }


    /**
     * 清除所有绘图内容的公共方法
     */
    fun clearAll() {
        sendIntent(DrawBoardIntent.ClearAll())
    }


    fun saveEnt() {
        viewModelScope.launchIO {
            val time = System.currentTimeMillis()
            val tmpDrawEntity = drawEntity.copy(modifyTime = time)
            val scale = 2200 / 1000
            val toEntStrokes = tmpDrawEntity.strokes.map { stroke ->
                val scaleStroke = stroke.points.map {
                    it.copy(x = it.x / scale, y = it.y / scale)
                }
                val scaleWidthStyle = stroke.style.copy(width = stroke.style.width / scale)
                stroke.copy(
                    points = scaleStroke,
                    style = scaleWidthStyle
                )
            }

            try {
                // EntRepository.writeJsonData(tmpDrawEntity, "json_$time")
                EntRepository.writeEntDataToExt(
                    tmpDrawEntity.copy(
                        strokes = toEntStrokes
                    ), "ent_$time"
                )
            } catch (ex: Exception) {
                Logger.e(TAG, "write ent data error: ${ex.message}")
            }
        }
    }

    init {
        handleIntent()
        handleFlow()
    }

    companion object {
        private const val TAG = "DrawBoardViewModel"
        private const val ERASER_LAG = 1000
        private const val ENABLE_LINE_ERASER = true
    }
}

private sealed class Operation(
    val paths: List<DrawPathDisplay>,
) {
    val ids: Set<Long> = paths.mapTo(HashSet()) { it.id }

    class Add(paths: List<DrawPathDisplay>) : Operation(paths = paths)
    class Remove(paths: List<DrawPathDisplay>) : Operation(paths = paths)
}