package com.tcl.ai.note.handwritingtext.vm

import android.util.Log
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.unit.dp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.handwritingtext.bean.DrawMode
import com.tcl.ai.note.handwritingtext.state.DrawBoardIntent
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.toPx
import kotlinx.coroutines.launch

class EraserSliderModel: ViewModel() {
    var isInitCache by mutableStateOf(false)
        private  set
    private var lastEraserSliderPosition = 0.63f //进度条的一半
    private val _sliderPosition = mutableFloatStateOf(lastEraserSliderPosition)
    val sliderPositionState: State<Float> =_sliderPosition
    //橡皮擦变大系数
    val rise = 30



    fun updateSliderPosition(position:Float){
        Logger.d(TAG,"eraser,updateSliderPosition")
        viewModelScope.launch {
            _sliderPosition.floatValue =position
            lastEraserSliderPosition = position
            AppDataStore.putData(EraserSliderModel::lastEraserSliderPosition.name,lastEraserSliderPosition)
        }
    }
    fun loadCache(){
        Logger.d(TAG,"eraser,loadCache")
        viewModelScope.launch {
            lastEraserSliderPosition = AppDataStore.getData(EraserSliderModel::lastEraserSliderPosition.name,lastEraserSliderPosition)
            _sliderPosition.floatValue = lastEraserSliderPosition
            isInitCache =true
        }
    }
    fun recoverStatus(drawBoardViewModel: DrawBoardViewModel){
        Logger.d(TAG,"recoverStatus")
        sendSliderProgressIntent(drawBoardViewModel, sliderPositionState.value)
    }

    fun sendSliderProgressIntent(drawBoardViewModel: DrawBoardViewModel,progress: Float){
        Logger.d(TAG,"sendSliderProgressIntent")
        with(drawBoardViewModel){
            sendIntent(
                DrawBoardIntent.ChangeStrokeStyle(
                    strokeStyle.copy(width = (progress*rise).dp.toPx,drawMode = DrawMode.ERASER)
                )

            )
        }
    }

    companion object {
        private const val TAG = "EraserSliderModel"
    }
}