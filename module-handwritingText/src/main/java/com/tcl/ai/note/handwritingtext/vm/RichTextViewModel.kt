package com.tcl.ai.note.handwritingtext.vm

import android.net.Uri
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextDecoration
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.base.R
import com.tcl.ai.note.event.AIEventType
import com.tcl.ai.note.event.AIReplaceEvent
import com.tcl.ai.note.handwritingtext.bean.BrushMenu
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.database.CategoryRepository
import com.tcl.ai.note.handwritingtext.database.NoteRepository
import com.tcl.ai.note.handwritingtext.database.entity.BgMode
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphType
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.intent.RichTextIntent
import com.tcl.ai.note.handwritingtext.repo.HandWritingThumbnailRepo
import com.tcl.ai.note.handwritingtext.state.ListNoteCategoryState
import com.tcl.ai.note.handwritingtext.state.RichTextState
import com.tcl.ai.note.handwritingtext.track.AnalyticsHandWritingTextModel
import com.tcl.ai.note.handwritingtext.ui.richtext.RichTextController
import com.tcl.ai.note.net.NetworkUseCase
import com.tcl.ai.note.theme.editorRichTextStyle
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.CategoryColors
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject


@Deprecated("仅一期使用")
@HiltViewModel
class RichTextViewModel @Inject constructor(
    private val networkUseCase: NetworkUseCase,
) : ViewModel() {
    val APP_DATA_KEY_LAST_BRUSH_MENU_TYPE ="APP_DATA_LAST_BRUSH_MENU_TYPE_KEY"
    private val _state = MutableStateFlow(RichTextState())
    val state: StateFlow<RichTextState> = _state.asStateFlow()

    // 分类列表数据状态的流
    private val _listNoteCategoryState =
        MutableStateFlow<ListNoteCategoryState>(ListNoteCategoryState.Loading)
    val listNoteCategoryState: StateFlow<ListNoteCategoryState> =
        _listNoteCategoryState.asStateFlow()

    //当前选择的分类
    var currentCategoryId = mutableStateOf<String>("")
        private set
    var currentCategoryName = mutableStateOf<String>("")
        private set
    var currentColorIndex = mutableStateOf<String>("")
        private set
    var currentCategoryIcon = mutableStateOf<String>("")
        private set

    var lastBrushMenuType  by mutableStateOf("")
        private set

    // 标题焦点状态追踪
    private val _hasTitleFocus = mutableStateOf(false)
    private val hasTitleFocus: State<Boolean> = _hasTitleFocus
    //控制底部AI弹窗的state
    private val _isShowBottomAIPop = mutableStateOf(false)
    val isShowBottomAIPop: State<Boolean> = _isShowBottomAIPop
    val isOffline: StateFlow<Boolean> = networkUseCase.isOffline(viewModelScope)

    var isChangeSkin by mutableStateOf(false)
        private set

    // 用于标识平板横屏在编辑页点保存再按返回时，保存操作的协程还没开始而依赖保存标识的返回事件已经执行完了
    var isSaveStart by mutableStateOf(false)

    // 设置导航标识，用于区分按Home键退到后台的情况
    fun startNavigation() {
        _state.update {
            it.copy(isNavigatingWithinApp = true)
        }
    }

    // 导航结束（手动重置）
    fun resetNavigation() {
        _state.update {
            it.copy(isNavigatingWithinApp = false)
        }
    }

    /**
     * 更新标题焦点状态
     * @param hasFocus 是否获得焦点
     */
    private fun updateTitleFocus(hasFocus: Boolean) {
        _hasTitleFocus.value = hasFocus
        if(hasFocus){
            _state.update {
                it.copy(editMode = true)
            }
        }
    }

    /**
     * 更新编辑模式状态
     * @param enabled 是否处于编辑模式
     */
    private fun updateEditMode(enabled: Boolean) {
        if (!enabled) {
            updateBottomMenuType(MenuBar.NONE)
        }
        _state.update {
            it.copy(editMode = enabled)
        }
    }

    /**
     * 初始化编辑模式
     * @param isLandscape 是否横屏（横屏初始化默认为编辑状态）
     */
    private fun initEditMode(noteId: Long?,isLandscape:Boolean) {
        if(isLandscape){
            _state.update {
                it.copy(editMode = true)
            }
        }else{
            _state.update {
                it.copy(editMode = when {
                    noteId == null -> true // 新建笔记直接进入编辑模式
                    else -> _state.value.cursorPosition>0 || hasTitleFocus.value // 已有笔记根据焦点状态决定
                })
            }
        }
    }

    // 更新焦点索引
    private fun updateFocusedIndex(index: Int) {
        if(_state.value.focusedIndex != index){
            _state.update {
                it.copy(focusedIndex = index)
            }
        }

        // 根据新的索引解析当前内容块样式
//        parseCurrentStyles(index)
    }

    // 更新光标位置
    private fun updateCursorPosition(position: Int,isChange: Boolean) {
        updateEditMode(true)
        if(_state.value.cursorPosition != position){
            _state.update {
                it.copy(cursorPosition = position, titleCursorPosition = -1)
            }
        }
        if(isChange){
            parseCurrentStyles(_state.value.focusedIndex)
        }
    }

    // 更新标题光标位置
    private fun updateTitleCursorPosition(position: Int) {
        if(_state.value.titleCursorPosition != position){
            _state.update {
                it.copy(titleCursorPosition = position, cursorPosition = -1, focusedIndex = -1)
            }
        }
    }


    /**
     * 更新当前焦点数据类型
     */
    private fun updateCurrentFocusedType(editorContent: EditorContent?) {
        _state.update {
            it.copy(currentFocusedType = editorContent)
        }
    }

    /**
     * 更新编辑标题
     * @param title 新的标题内容
     */
    private fun updateTitle(title: String) {
        val curState = _state.value
        if (curState.title != title) {
            Logger.d(TAG, "record title, old:${curState.title}, new:$title")
            recordOperation(Operation.UpdateTitle(oldTitle = curState.title, newTitle = title))
        }
        _state.update { old ->
            old.copy(title=title)
        }
    }
    /**
     * 更新保存状态
     * @param canSave 是否可以保存
     */
    private fun updateSaveState(canSave: Boolean) {
        _state.update {
            it.copy(canSave = canSave)
        }
    }

    /**
     * 更新Skin状态
     */
    private fun updateSkinStyle(bgMode: BgMode,bgColor: Long){
        isChangeSkin =true
        _state.update {
            it.copy(bgMode = bgMode, bgColor = bgColor)
        }
    }

    // 当前设置字体样式
    private val _currentTextStyle = mutableStateOf(
        editorRichTextStyle
    )

    val currentTextStyle: State<TextStyle> = _currentTextStyle

    private val _selection = MutableStateFlow<TextRange?>(null)
    val selection: StateFlow<TextRange?> = _selection.asStateFlow()

    /**
     * 更新选区范围
     */
    fun updateSelection(range: TextRange?) {
        _selection.value = range
    }

    /**
     * 将当前文本样式应用到当前选区
     * 仅当用户主动点击样式按钮时触发调用
     */
    fun applyCurrentStyleToSelection() {
        val range = _selection.value ?: return
        if (range.collapsed) return

        val index = state.value.focusedIndex
        if (index !in state.value.contents.indices) return

        when (val content = state.value.contents[index]) {
            is EditorContent.TextBlock -> {
                // 普通文本块，直接在选区叠加当前样式
                val currentText = content.text.annotatedString.text
                val max = currentText.length
                var safeStart = range.start.coerceIn(0, max)
                var safeEnd = range.end.coerceIn(0, max)

                // 确保 safeStart <= safeEnd
                if (safeStart > safeEnd) {
                    val temp = safeStart
                    safeStart = safeEnd
                    safeEnd = temp
                }

                val builder = AnnotatedString.Builder(content.text.annotatedString)
                builder.addStyle(
                    _currentTextStyle.value.toSpanStyle(),
                    safeStart,
                    safeEnd
                )
                val newText = content.text.copy(
                    annotatedString = builder.toAnnotatedString(),
                    selection = safeTextRange(safeStart, safeEnd)
                )
                updateContent(index, content.copy(text = newText))
            }
            is EditorContent.TodoBlock -> {
                val currentText = content.text.annotatedString.text
                val max = currentText.length
                var safeStart = range.start.coerceIn(0, max)
                var safeEnd = range.end.coerceIn(0, max)

                // 确保 safeStart <= safeEnd
                if (safeStart > safeEnd) {
                    val temp = safeStart
                    safeStart = safeEnd
                    safeEnd = temp
                }

                val builder = AnnotatedString.Builder(content.text.annotatedString)
                // 待办事项已完成，确保删除线必然存在
                if (content.isDone) {
                    // 获取当前样式
                    val baseSpan = _currentTextStyle.value.toSpanStyle()
                    // 合并删除线
                    val lineThroughSpan = baseSpan.copy(
                        textDecoration = baseSpan.textDecoration?.let {
                            // 已有删除线/其他装饰时加上删除线
                            if (it.contains(TextDecoration.LineThrough)) it
                            else TextDecoration.combine(listOf(it, TextDecoration.LineThrough))
                        } ?: TextDecoration.LineThrough // 没有装饰时直接删除线
                    )
                    builder.addStyle(
                        lineThroughSpan,
                        safeStart,
                        safeEnd
                    )
                } else {
                    // 未完成时，直接应用当前样式
                    builder.addStyle(
                        _currentTextStyle.value.toSpanStyle(),
                        safeStart,
                        safeEnd
                    )
                }
                val newText = content.text.copy(
                    annotatedString = builder.toAnnotatedString(),
                    selection = safeTextRange(safeStart, safeEnd)
                )
                updateContent(index, content.copy(text = newText))
            }
            is EditorContent.AudioBlock -> {}
            is EditorContent.ImageBlock -> {}
            is EditorContent.RichTextV2 -> {}
        }
    }

    /**
     * 切换粗体
     */
    fun toggleBold() {
        val newWeight = if (_currentTextStyle.value.fontWeight == FontWeight.Bold) {
            FontWeight.Normal
        } else {
            FontWeight.Bold
        }
        updateTextStyle(_currentTextStyle.value.copy(fontWeight = newWeight))
    }

    /**
     * 切换斜体
     */
    fun toggleItalic() {
        val newStyle = if (_currentTextStyle.value.fontStyle == FontStyle.Italic) {
            FontStyle.Normal  // 使用Compose的FontStyle枚举
        } else {
            FontStyle.Italic
        }
        updateTextStyle(_currentTextStyle.value.copy(fontStyle = newStyle))
    }

    /**
     * 切换下划线
     */
    fun toggleUnderline() {
        val newDecoration = if (_currentTextStyle.value.textDecoration == TextDecoration.Underline) {
            TextDecoration.None
        } else {
            TextDecoration.Underline
        }
        updateTextStyle(_currentTextStyle.value.copy(textDecoration = newDecoration))
    }

    /**
     * 更新字体样式
     * @param newTextStyle 新的字体样式
     */
    private fun updateTextStyle(newTextStyle: TextStyle) {
        // 合并样式而不是替换
        val merged = _currentTextStyle.value.merge(newTextStyle)
        _currentTextStyle.value = merged
    }

    /**
     * 更新当前段落样式
     */
    private fun updateCurrentParagraphStyle(currentParagraphStyle: ParagraphStyle){
        _state.update {
            it.copy(currentParagraphStyle = currentParagraphStyle)
        }
    }

    /**
     * 更新当前焦点数据类型
     */
    private fun updateCurrentParagraphType(currentParagraphType: ParagraphType){
        _state.update {
            it.copy(currentParagraphType = currentParagraphType)
        }
    }

    /**
     * 更新粗体高亮状态
     */
    private fun updateBoldActive(isBoldActive:Boolean){
        _state.update {
            it.copy(isBoldActive = isBoldActive)
        }
    }

    /**
     * 更新斜体高亮状态
     */
    private fun updateItalicActive(isItalicActive:Boolean){
        _state.update {
            it.copy(isItalicActive = isItalicActive)
        }
    }

    /**
     * 更新下划线高亮状态
     */
    private fun updateUnderlineActive(isUnderlineActive:Boolean){
        _state.update {
            it.copy(isUnderlineActive = isUnderlineActive)
        }
    }

    /**
     * 解析当前内容块样式，根据焦点所在块和光标/选区，更新富文本状态中的段落样式和字体样式。
     * 支持如下逻辑：
     * - 无选区时（range == null 或 collapsed）：
     *   - 光标为0且内容长度为0时，重置样式；
     *   - 光标为0且有内容时，应用第一个字符样式；
     *   - 其它情况（光标 > 0），应用光标左侧字符样式。
     * - 有选区时：取选区 [start, end) 内所有字符样式的交集，有且仅有全部包含才点亮对应按钮，否则重置样式。
     */
    private fun parseCurrentStyles(index: Int) {
        val content = _state.value.contents.getOrNull(index)
        if (content == null) {
            resetStyles()
            return
        }

        // 目前selection是compose层和viewmodel同步的
        val selection: TextRange? = _selection.value

        when (content) {
            is EditorContent.TextBlock, is EditorContent.TodoBlock -> {
                val annotated = when (content) {
                    is EditorContent.TextBlock -> content.text.annotatedString
                    is EditorContent.TodoBlock -> content.text.annotatedString
                    else -> AnnotatedString("")
                }
                val length = annotated.length

                // 选区为空/单点
                if (selection == null || selection.collapsed) {
                    val cursor = _state.value.cursorPosition.coerceAtLeast(0)
                    when {
                        cursor == 0 && length == 0 -> {
                            // 光标在最前且块为空 重置
                            resetStyles()
                        }
                        cursor == 0 && length > 0 -> {
                            // 光标在最前且有内容时，取第一个字符样式
                            val firstSpan = annotated.spanStyles.lastOrNull { 0 in it.start until it.end }
                            if (firstSpan != null) {
                                setStylesFromSpans(listOf(firstSpan))
                            } else {
                                resetStyles()
                            }
                        }
                        cursor > 0 && cursor <= length -> {
                            // 光标后，取左侧字符样式
                            val idx = cursor - 1
                            val leftSpan = annotated.spanStyles.lastOrNull { idx in it.start until it.end }
                            if (leftSpan != null) {
                                setStylesFromSpans(listOf(leftSpan))
                            } else {
                                resetStyles()
                            }
                        }
                        else -> {
                            // 超出范围
                            resetStyles()
                        }
                    }

                    // 段落/类型切换
                    when (content) {
                        is EditorContent.TextBlock -> {
                            updateParagraphStyle(content.paragraphStyle)
                            when (content.paragraphStyle) {
                                ParagraphStyle.NUMBERED -> updateCurrentParagraphType(ParagraphType.NUMBERED_LIST)
                                ParagraphStyle.BULLETED -> updateCurrentParagraphType(ParagraphType.BULLETED_LIST)
                                else -> updateCurrentParagraphType(ParagraphType.TEXT)
                            }
                        }
                        is EditorContent.TodoBlock -> {
                            updateParagraphStyle(ParagraphStyle.NONE)
                            updateCurrentParagraphType(ParagraphType.TODO_ITEM)
                        }
                        else -> {}
                    }
                } else {
                    // selection非collapsed，有选区。以选区交集决定按钮
                    val safeStart = selection.min.coerceAtLeast(0)
                    val safeEnd = selection.max.coerceAtMost(length)
                    if (safeStart >= safeEnd) {
                        resetStyles()
                        return
                    }

                    // 选区内每个字符的span
                    var allBold = true
                    var allItalic = true
                    var allUnderline = true

                    for (i in safeStart until safeEnd) {
                        val s = annotated.spanStyles.lastOrNull { i in it.start until it.end }?.item
                        if (s?.fontWeight != FontWeight.Bold) allBold = false
                        if (s?.fontStyle != FontStyle.Italic) allItalic = false
                        if (s?.textDecoration?.contains(TextDecoration.Underline) != true) allUnderline = false
                    }
                    // 全部统一才激活，否则重置
                    if (allBold || allItalic || allUnderline) {
                        _state.update {
                            it.copy(
                                isBoldActive = allBold,
                                isItalicActive = allItalic,
                                isUnderlineActive = allUnderline,
                            )
                        }
                        // 也更新currentTextStyle
                        updateTextStyle(_currentTextStyle.value.copy(
                            fontWeight = if (allBold) FontWeight.Bold else FontWeight.Normal,
                            fontStyle = if (allItalic) FontStyle.Italic else FontStyle.Normal,
                            textDecoration = if (allUnderline) TextDecoration.Underline else TextDecoration.None,
                        ))
                    } else {
                        resetStyles()
                    }

                    // 段落/类型切换
                    when (content) {
                        is EditorContent.TextBlock -> {
                            updateParagraphStyle(content.paragraphStyle)
                            when (content.paragraphStyle) {
                                ParagraphStyle.NUMBERED -> updateCurrentParagraphType(ParagraphType.NUMBERED_LIST)
                                ParagraphStyle.BULLETED -> updateCurrentParagraphType(ParagraphType.BULLETED_LIST)
                                else -> updateCurrentParagraphType(ParagraphType.TEXT)
                            }
                        }
                        is EditorContent.TodoBlock -> {
                            updateParagraphStyle(ParagraphStyle.NONE)
                            updateCurrentParagraphType(ParagraphType.TODO_ITEM)
                        }
                        else -> {}
                    }
                }
            }
            else -> resetStyles()
        }
    }

    /**
     * 根据获取的光标前一个字符的样式，更新富文本状态的字体样式。
     * @param spans 光标前一个字符的样式集合
     */
    private fun setStylesFromSpans(spans: List<AnnotatedString.Range<SpanStyle>>) {
        // 根据样式集合更新富文本状态中的样式（如粗体、斜体、下划线）
        _state.update {
            it.copy(
                isBoldActive = spans.any { it.item.fontWeight == FontWeight.Bold },  // 检查是否包含粗体样式
                isItalicActive = spans.any { it.item.fontStyle == FontStyle.Italic }, // 检查是否包含斜体样式
                isUnderlineActive = spans.any { it.item.textDecoration?.contains(TextDecoration.Underline) == true } // 检查是否包含下划线样式
            )
        }

        // 将字体样式同步更新到当前样式状态
        val newWeight = if (_state.value.isBoldActive) FontWeight.Bold else FontWeight.Normal
        updateTextStyle(_currentTextStyle.value.copy(fontWeight = newWeight))

        val newStyle = if (_state.value.isItalicActive) FontStyle.Italic else FontStyle.Normal
        updateTextStyle(_currentTextStyle.value.copy(fontStyle = newStyle))

        val newDecoration = if (_state.value.isUnderlineActive) TextDecoration.Underline else TextDecoration.None
        updateTextStyle(_currentTextStyle.value.copy(textDecoration = newDecoration))
    }

    /**
     * 重置富文本状态中的字体样式。
     * 当光标前一个字符没有样式信息或焦点从富文本块切换到其他类型块时调用，例如图片或音频块。
     */
    private fun resetStyles() {
        _state.update {
            it.copy(
                isBoldActive = false, // 关闭粗体样式
                isItalicActive = false, // 关闭斜体样式
                isUnderlineActive = false // 关闭下划线样式
            )
        }

        // 重置当前文本样式对象
        updateTextStyle(
            /*TextStyle(
                fontSize = 16.sp,
                lineHeight = Skin.lineHeight().sp,
                fontWeight = FontWeight.Normal,
                fontStyle = FontStyle.Normal,
                textDecoration = TextDecoration.None
            )*/
            editorRichTextStyle.copy(
                fontWeight = FontWeight.Normal,
                fontStyle = FontStyle.Normal,
                textDecoration = TextDecoration.None
            )
        )
    }

    /**
     * 更新富文本状态中的段落样式。
     * @param style 要设置的段落样式（如有序列表、无序列表等）
     */
    private fun updateParagraphStyle(style: ParagraphStyle) {
        _state.update {
            // 将段落样式更新到富文本状态中
            it.copy(currentParagraphStyle = style)
        }
    }

    /**
     * 切换段落样式（有序/无序列表）
     * @param index 内容块索引
     * @param targetStyle 目标段落样式
     */
    private fun toggleParagraphStyle(index: Int, targetStyle: ParagraphStyle) {
        val content = _state.value.contents.getOrNull(index)
        if (content is EditorContent.TextBlock) {
            // 检查是否要切换回普通样式
            val newStyle = if (content.paragraphStyle == targetStyle) {
                ParagraphStyle.NONE
            } else {
                targetStyle
            }

            if(newStyle == targetStyle){
                // 将纯文本切换到有序/无序列表

                // 获取当前文本内容并确保光标位置有效
                val textValue = content.text
                val fullText = textValue.text
                val cursorPos = _state.value.cursorPosition.coerceIn(0, fullText.length)

                // 将文本拆分为多行并确定光标所在行
                val lines = fullText.split('\n')
                var charCount = 0
                var targetLineIndex = -1

                // 处理单行情况
                if (lines.size == 1) {
                    val updatedBlock = content.copy(paragraphStyle = newStyle)
                    updateContent(index, updatedBlock)
                } else {
                    // 多行纯文本转有序/无序列表
                    // 计算每行的偏移量以识别目标行
                    val lineOffsets = lines.map { line ->
                        val start = charCount
                        charCount += line.length + 1 // 包含换行符的长度计算
                        start
                    }

                    for (i in lines.indices) {
                        // 包括光标位于换行符的情况
                        if (cursorPos in lineOffsets[i]..(lineOffsets[i] + lines[i].length)) {
                            targetLineIndex = i
                            break
                        }
                    }
                    if (targetLineIndex != -1) {
                        // 计算当前行的真实范围（包含换行符）
                        val lineStart = lineOffsets[targetLineIndex]
                        val lineEnd = if (targetLineIndex < lines.lastIndex) {
                            lineStart + lines[targetLineIndex].length + 1 // 包含换行符
                        } else {
                            lineStart + lines[targetLineIndex].length     // 最后一行
                        }

                        // 分割三部分（保留原有换行结构）
                        val beforeText = fullText.substring(0, lineStart).let {
                            // 移除前一个换行符（如果当前行不是第一行）
                            if (targetLineIndex > 0) it.dropLast(1) else it
                        }
                        val afterText = fullText.substring(lineEnd)

                        val sanitizedBlocks = mutableListOf<EditorContent>()

                        // 前部
                        if (beforeText.isNotEmpty()) {
                            sanitizedBlocks.add(
                                EditorContent.TextBlock(
                                    text = TextFieldValue(
                                        annotatedString = extractAnnotatedRange(
                                            content.text.annotatedString, beforeText.indices
                                        ),
                                        selection = TextRange(beforeText.length)
                                    ),
                                    paragraphStyle = ParagraphStyle.NONE
                                )
                            )
                        }
                        // 当前行，转有序/无序列表
                        val lineTextLength = lines[targetLineIndex].length
                        val blockRange = lineStart until (lineStart + lineTextLength)
                        sanitizedBlocks.add(
                            EditorContent.TextBlock(
                                text = TextFieldValue(
                                    annotatedString = extractAnnotatedRange(
                                        content.text.annotatedString, blockRange
                                    ),
                                    selection = TextRange((cursorPos - lineStart).coerceIn(0, lineTextLength))
                                ),
                                paragraphStyle = targetStyle
                            )
                        )
                        // 后部
                        if (afterText.isNotEmpty() && afterText.any { it != '\n' }) {
                            sanitizedBlocks.add(
                                EditorContent.TextBlock(
                                    text = TextFieldValue(
                                        annotatedString = extractAnnotatedRange(
                                            content.text.annotatedString,
                                            lineEnd until lineEnd + afterText.length
                                        ),
                                        selection = TextRange(0)
                                    ),
                                    paragraphStyle = ParagraphStyle.NONE
                                )
                            )
                        }

                        // 替换原有块
                        val newContents = _state.value.contents.toMutableList().apply {
                            removeAt(_state.value.focusedIndex)
                            addAll(_state.value.focusedIndex, sanitizedBlocks)
                        }

                        replaceAllContents(newContents)

                        // 更新焦点到新创建的有序/无序列表块
                        val newFocusIndex = _state.value.focusedIndex + sanitizedBlocks.indexOfFirst { it is EditorContent.TextBlock && it.paragraphStyle == targetStyle }
                        updateFocusedIndex(newFocusIndex)
                        updateCursorPosition(lineOffsets[targetLineIndex],false)
                    }
                }
            }else{
                // 将有序/无序列表切换到普通出文本
                val updatedBlock = content.copy(paragraphStyle = newStyle)
                updateContent(index, updatedBlock)
            }

        }else if(content is EditorContent.TodoBlock){
            // 将待办事项转换为文本块
            (_state.value.contents[_state.value.focusedIndex] as? EditorContent.TodoBlock)?.let { item->
                val text = item.text.copy(
                    selection = TextRange(_state.value.cursorPosition) // 恢复光标位置
                )
                updateContent(
                    _state.value.focusedIndex,
                    EditorContent.TextBlock(text = text, paragraphStyle = targetStyle)
                )
            }
        }
    }

    /**
     * 从 source 中提取 [range] 区间对应的文本和样式，生成新的 AnnotatedString
     * 新的 span start/end 需相对新字符串做偏移
     */
    private fun extractAnnotatedRange(source: AnnotatedString, range: IntRange): AnnotatedString {
        val newStr = source.text.substring(range)
        return buildAnnotatedString {
            append(newStr)
            val absStart = range.first
            val absEnd = range.last + 1 // IntRange 是闭区间，substring 是半开区，+1
            source.spanStyles.forEach { span ->
                // 有交集才复制，为准确起见用 max/min 裁剪
                val overlapStart = maxOf(span.start, absStart)
                val overlapEnd = minOf(span.end, absEnd)
                if (overlapStart < overlapEnd) {
                    // 变换到新字符串的区间
                    val relStart = overlapStart - absStart
                    val relEnd = overlapEnd - absStart
                    addStyle(span.item, relStart, relEnd)
                }
            }
        }
    }

    /**
     * 获取当前块在有序列表中的序号
     * @param index 内容块索引
     * @return 序号
     */
    fun getOrderNumberForBlock(index: Int): Int {
        var count = 1
        for (i in 0 until index) {
            val item = _state.value.contents[i]
            if (item is EditorContent.TextBlock && item.paragraphStyle == ParagraphStyle.NUMBERED) {
                count++
            } else {
                // 遇到非有序列表项重置计数
                if (i < index) count = 1
            }
        }
        return count
    }

    /**
     * 添加新的内容块
     * @param index 插入位置索引（可选，默认为末尾）
     * @param content 要添加的内容块
     */
    private fun addContent(index: Int? = null, content: EditorContent) {
        val insertIndex = index ?: state.value.contents.size
        _state.update {
            it.copy(contents = _state.value.contents.toMutableList().apply {
                if (index != null && index in this.indices) {
                    add(index, content) // 插入到指定位置
                } else {
                    add(content) // 添加到末尾
                }
            })
        }
        if (index != null) {
            _state.update {
                it.copy(focusedIndex = index)
            }
            if(index>1){
                // 当前为空白行且前一个内容为图片或录音（图片或录音添加操作）
                val preBlock = _state.value.contents[index-1]
                if((preBlock is EditorContent.ImageBlock
                            || preBlock is EditorContent.AudioBlock)
                    && content is EditorContent.TextBlock
                    && content.text.text.isEmpty()
                ){
                    updateCursorPosition(0,true)
                }
            }

        }
        // 第一次默认加了一个空内容TextBlock,所有无需添加撤销重做操作记录
        if(_state.value.contents.size>1){
            if (!isTablet || content !is EditorContent.AudioBlock) {
                // 仅在非平板或非文本块时记录操作
                recordOperation(Operation.Add(insertIndex, content))
            }
            // 保存当前数据
//            requestSave()
        }
        requestSave()
    }

    /**
     * 更新内容块
     * @param index 要更新的内容块索引
     * @param updatedContent 更新后的内容块
     */
    private fun updateContent(index: Int, updatedContent: EditorContent) {
        // 添加索引检查
        val tmpContents = _state.value.contents
        if (index !in tmpContents.indices) {
            Logger.e(TAG, "Invalid index $index during update")
            return
        }
        val old = tmpContents[index]

        // 只检测两种类型
        val isOnlySelectionChanged = when {
            // 普通文本块
            old is EditorContent.TextBlock && updatedContent is EditorContent.TextBlock -> {
                old.text.annotatedString == updatedContent.text.annotatedString
                        && old.text.text == updatedContent.text.text
                        && old.paragraphStyle == updatedContent.paragraphStyle
                        && old !== updatedContent // 防止引用完全同一个
                        && old.text.selection != updatedContent.text.selection // selection发生变化
            }
            // 待办事项
            old is EditorContent.TodoBlock && updatedContent is EditorContent.TodoBlock -> {
                old.text.annotatedString == updatedContent.text.annotatedString
                        && old.text.text == updatedContent.text.text
                        && old.isDone == updatedContent.isDone
                        && old !== updatedContent
                        && old.text.selection != updatedContent.text.selection
            }
            else -> false
        }

        if (isOnlySelectionChanged) {
            // 只是光标移动
            // 只需要更新 selection 的 StateFlow
            // 比如
            updateSelection(
                when (updatedContent) {
                    is EditorContent.TextBlock -> updatedContent.text.selection
                    is EditorContent.TodoBlock -> updatedContent.text.selection
                    else -> null
                }
            )
            // 不做内容相关更新和操作记录
            return
        }

        if (index in tmpContents.indices) {
            _state.update {
                it.copy(contents = _state.value.contents.toMutableList().apply {
                    this[index] = updatedContent
                })
            }
        }
        _state.update {
            it.copy(currentFocusedType = updatedContent)
        }
        recordOperation(Operation.Update(index, old, updatedContent))
        // 保存当前数据
        requestSave()
    }

    /**
     * 删除内容块
     * @param index 要删除的内容块索引
     */
    private fun removeContent(index: Int) {
        val removed = state.value.contents.getOrNull(index) ?: return
        val isImageBlock = removed is EditorContent.ImageBlock

        // 先执行文件删除（如果是图片块）
        if (removed is EditorContent.ImageBlock) {
            deleteImageFile(removed.uri.toString())
            // 同步清理操作记录
            cleanImageOperations(removed.uri.toString())
        }

        // 更新状态
        _state.update {
            it.copy(contents = it.contents.toMutableList().apply { removeAt(index) })
        }

        // 调整焦点索引
        val newIndex = (index - 1).coerceAtLeast(0)
        _state.update { it.copy(focusedIndex = newIndex) }

        // 非图片块才记录撤销操作
        if (!isImageBlock) {
            recordOperation(Operation.Remove(index, removed))
        }

        // 立即保存
        requestSave()
    }

    /**
     * 删除图片文件
     */
    private fun deleteImageFile(uriString: String) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val uri = Uri.parse(uriString)
                uri.path?.let { path ->
                    File(path).takeIf { it.exists() }?.delete()
                    // 可选：同时删除缩略图等关联文件
                }
            } catch (e: Exception) {
                Logger.e("ImageDelete", "Failed to delete image: $uriString")
            }
        }
    }


    /**
     * 清理操作记录
     * @param uri 要清理的图片URI
     */
    private fun cleanImageOperations(uri: String) {
        // 清理撤销栈
        undoStack.removeAll { operation ->
            when (operation) {
                is Operation.Add -> operation.content.let {
                    it is EditorContent.ImageBlock && it.uri.toString() == uri
                }
                is Operation.Update -> {
                    val oldIsTarget = operation.old.let {
                        it is EditorContent.ImageBlock && it.uri.toString() == uri
                    }
                    val newIsTarget = operation.new.let {
                        it is EditorContent.ImageBlock && it.uri.toString() == uri
                    }
                    oldIsTarget || newIsTarget
                }
                else -> false
            }
        }

        // 清理重做栈
        redoStack.removeAll { operation ->
            when (operation) {
                is Operation.Add -> operation.content.let {
                    it is EditorContent.ImageBlock && it.uri.toString() == uri
                }
                is Operation.Update -> {
                    val oldIsTarget = operation.old.let {
                        it is EditorContent.ImageBlock && it.uri.toString() == uri
                    }
                    val newIsTarget = operation.new.let {
                        it is EditorContent.ImageBlock && it.uri.toString() == uri
                    }
                    oldIsTarget || newIsTarget
                }
                else -> false
            }
        }

        // 更新按钮状态
        updateButtonStates()
    }

    /**
     * 更新画笔类型
     * @param type 新的画笔类型
     */
    private fun updateBrushMenuType(type: String) {
        _state.update {
            it.copy(brushMenuType = type)
        }
    }

    /**
     * 更新 BottomMenu 类型
     * @param type 新的 BottomMenu 类型
     */
    private fun updateBottomMenuType(type: MenuBar) {
        _state.update {
            it.copy(bottomMenuType = type)
        }
        /*if(type == MenuBar.TODO){
            updateCurrentParagraphStyle(ParagraphStyle.NONE)
        }*/
        showBottomAIPop(type)
        // 保存当前数据
//        requestSave()
    }

    private fun showBottomAIPop(type: MenuBar) {
        if (type == MenuBar.AI) {
            _isShowBottomAIPop.value = true
        }
    }
    /**
     * 关闭底部AI弹窗,为了实现没有网络时，不关闭弹窗功能
     */
    fun closeBottomAIPop() {
        _isShowBottomAIPop.value = false
    }

    // 顺序换行分块
    private val splitQueue = Channel<SplitRequest>(capacity = Channel.UNLIMITED)
    private val _isSplittingBlock = MutableStateFlow(false)
    val isSplittingBlock = _isSplittingBlock.asStateFlow()

    // 换行分块的请求字段
    data class SplitRequest(
        val index: Int,
        val block: EditorContent.TextBlock,
        val oldText: TextFieldValue,
        val newValue: TextFieldValue,
        val blockParagraphStyle: ParagraphStyle
    )

    /**
     * 向队列中提交分割请求（仅当上一次已处理完毕）
     */
    fun enqueueBlockSplit(
        index: Int,
        block: EditorContent.TextBlock,
        oldText: TextFieldValue,
        newValue: TextFieldValue,
        style: ParagraphStyle
    ) {
        if (!_isSplittingBlock.value) {
            splitQueue.trySend(SplitRequest(index, block, oldText,newValue, style))
        }
    }

    /**
     * 真正执行分块及块插入，按 SplitRequest 的内容
     * 参考你原 handleEnterKey 的逻辑，拷贝进来即可
     */
    private suspend fun actualHandleEnterKey(req: SplitRequest) {
        val index = req.index
        val block = req.block
        val oldText = req.oldText
        val newText = req.newValue
        val style = req.blockParagraphStyle

        // 处理文本及样式分割逻辑
        val currentText = newText.text.trim('\n')
        val isEmptyBlock = currentText.isEmpty()
        val originalAnnotated = newText.annotatedString
        val selectionStart = newText.selection.start

        // 分割带样式的文本, 你原有的 splitAnnotatedString
        val (beforeAnnotated, afterAnnotated) = splitAnnotatedString(
            originalAnnotated,
            selectionStart
        )

        // 检查是否需要清除段落样式
        val shouldClearStyle = isEmptyBlock && block.paragraphStyle != ParagraphStyle.NONE

        if (originalAnnotated.text != beforeAnnotated.text || oldText.text.isEmpty()) {
            // 更新当前块
            val updatedBlock = if (shouldClearStyle) {
                block.copy(
                    text = TextFieldValue(""),
                    paragraphStyle = ParagraphStyle.NONE
                )
            } else {
                block.copy(
                    text = TextFieldValue(
                        annotatedString = beforeAnnotated,
                        selection = TextRange(beforeAnnotated.length)
                    )
                )
            }

            // UI更新必须按顺序依赖
            updateContent(index, updatedBlock)
            if (updatedBlock.paragraphStyle == ParagraphStyle.NONE) {
                handleIntent(RichTextIntent.UpdateCurrentParagraphStyle(ParagraphStyle.NONE))
            }
            if(shouldClearStyle){
                handleIntent(
                    RichTextIntent.UpdateCursorPosition(0,true)
                )
            }else{
                handleIntent(
                    RichTextIntent.UpdateCursorPosition(0)
                )
            }
        }

        // 创建新块
        if (!shouldClearStyle) {
            val newBlock = when {
                isEmptyBlock && block.paragraphStyle != ParagraphStyle.NONE -> {
                    EditorContent.TextBlock(
                        text = TextFieldValue(""),
                        paragraphStyle = ParagraphStyle.NONE,
                    )
                }
                else -> {
                    EditorContent.TextBlock(
                        text = TextFieldValue(
                            annotatedString = afterAnnotated,
                            selection = TextRange(0)
                        ),
                        paragraphStyle = block.paragraphStyle,
                    )
                }
            }
            addContent(index + 1, newBlock)
            // 注意：仅在下一个分块前，Compose才会处理聚焦/光标
            updateFocusedIndex(index + 1)
            updateCursorPosition(0,false)
        } else {
            updateCursorPosition(0,false)
        }
    }

    /**
     * 文本分割
     */
    private fun splitAnnotatedString(
        source: AnnotatedString,
        splitPosition: Int
    ): Pair<AnnotatedString, AnnotatedString> {
        // -- 1. 预处理分割点和跳过换行符
        val adjustedSplit = if (splitPosition > 0 && source.text.getOrNull(splitPosition - 1) == '\n') {
            splitPosition - 1
        } else {
            splitPosition
        }
        val afterStart = if (adjustedSplit < source.length && source[adjustedSplit] == '\n') {
            adjustedSplit + 1
        } else {
            adjustedSplit
        }

        // -- 2. before部分
        val before = buildAnnotatedString {
            append(source.subSequence(0, adjustedSplit))
            source.spanStyles.forEach { span ->
                val start = span.start.coerceAtMost(adjustedSplit)
                val end = span.end.coerceAtMost(adjustedSplit)
                if (start < end) {
                    addStyle(span.item, start, end)
                }
            }
        }

        // -- 3. after部分，关键在于span截止不要越界，且跨段样式start为0
        val afterRaw = source.subSequence(afterStart, source.length)
        val after = buildAnnotatedString {
            append(afterRaw)
            val afterLen = afterRaw.length
            source.spanStyles.forEach { span ->
                val spanStartInAfter = (span.start - afterStart).coerceAtLeast(0)
                val spanEndInAfter = (span.end - afterStart).coerceAtLeast(0)
                // 限制在after字符串合法区间
                val start = spanStartInAfter.coerceIn(0, afterLen)
                val end = spanEndInAfter.coerceIn(0, afterLen)
                if (start < end) {
                    addStyle(span.item, start, end)
                }
            }
        }

        return Pair(before, after)
    }

    init {
        // 把viewmodel模块的数据加入分析模块
        AnalyticsHandWritingTextModel.loadRichTextViewModel(this)

        handleIntent(RichTextIntent.GetCategories) // 处理获取分类的意图
        if (currentCategoryName.value.isEmpty()) {
            viewModelScope.launch {
                currentCategoryId.value = AppDataStore.getStringData("currentCategoryId", "")
                val categoryId = currentCategoryId.value.toLongOrNull()
                if (categoryId != null && categoryId > 0) {
                    val category = CategoryRepository.getCategory(categoryId)
                    category?.let {
                        currentCategoryName.value = category.name
                        currentColorIndex.value = category.colorIndex.toString()
                        currentCategoryIcon.value = category.icon.toString()
                    }?:run {
                        resetToDefaultCategory()
                    }
                }else{
                    resetToDefaultCategory()
                }
            }
        }

        viewModelScope.launch {
            AIReplaceEvent.getAIReplaceEvent().collect{replace->
                if (replace is AIEventType.AIPolishReplace) {
                    _state.update {
                        it.copy(aiReplaceText = replace.text)
                    }
                }else {
                    _state.update {
                        it.copy(aiInsertText = replace.text)
                    }
                }
            }
        }

        viewModelScope.launch {
            for (req in splitQueue) {
                _isSplittingBlock.value = true
                actualHandleEnterKey(req)
                // 等UI同步（保证本次分割完成&聚焦到位）
                delay(100)         // 视UI流畅度调节
                _isSplittingBlock.value = false
            }
        }
    }

    /**
     * 重置当前选择的分类
     */
    private fun resetToDefaultCategory(){
        currentCategoryName.value = ""
        currentColorIndex.value = ""
        currentCategoryIcon.value = ""
        currentCategoryIcon.value = ""
    }

    /**
     * 获取初始化的分类数据
     */
    private fun getCategories() {
        viewModelScope.launch {
            _listNoteCategoryState.value = ListNoteCategoryState.Loading
            try {
                val categories = CategoryRepository.getAllCategories()
                if (categories.isNotEmpty()) {
                    val allCategories = NoteCategory(categoryId = -1,
                        name = "",
                        colorIndex = 0
                    )
                    allCategories.noteCounts = NoteRepository.getNoteCount()
                    allCategories.icon = R.drawable.ic_all_notes
                    val listAllCategories = listOf(allCategories)+categories
                    _listNoteCategoryState.value = ListNoteCategoryState.Success(listAllCategories)
                    if (currentCategoryName.value.isEmpty()) {
                        currentCategoryId.value = AppDataStore.getStringData("currentCategoryId", "")
                        val categoryId = currentCategoryId.value.toLongOrNull()
                        if (categoryId != null && categoryId > 0) {
                            val category = CategoryRepository.getCategory(categoryId)
                            category?.let {
                                currentCategoryName.value = category.name
                                currentColorIndex.value = category.colorIndex.toString()
                                currentCategoryIcon.value = category.icon.toString()
                            }?:run {
                                resetToDefaultCategory()
                            }
                        }else{
                            resetToDefaultCategory()
                        }
                    }
                }else{
                    _listNoteCategoryState.value = ListNoteCategoryState.Error("无分类数据")
                }
            }catch (e: Exception) {
                _listNoteCategoryState.value = ListNoteCategoryState.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 刷新分类列表
     */
    private fun refreshCategories(categoryId: Long = -1) {
        viewModelScope.launch {
            _listNoteCategoryState.value = ListNoteCategoryState.Loading
            try {
                val categories = CategoryRepository.getAllCategories()
                if (categories.isNotEmpty()) {
                    val allCategories = NoteCategory(categoryId = -1,
                        name = "",
                        colorIndex = 0
                    )
                    allCategories.noteCounts = NoteRepository.getNoteCount()
                    allCategories.icon = R.drawable.ic_all_notes
                    val listAllCategories = listOf(allCategories)+categories
                    _listNoteCategoryState.value = ListNoteCategoryState.Success(listAllCategories)
                }else{
                    _listNoteCategoryState.value = ListNoteCategoryState.Error("无分类数据")
                }
            } catch (e: Exception) {
                _listNoteCategoryState.value = ListNoteCategoryState.Error(e.message ?: "未知错误")
            } finally {
                _state.update { it.copy(newCategory = CategoryRepository.getCategory(categoryId)) }
            }
        }
    }

    /**
     * 新增category
     */
    private fun addCategory(noteCategory: NoteCategory,isPreviewMode:Boolean) = viewModelScope.launch {
        val categoryId = CategoryRepository.addCategory(noteCategory)
        if (categoryId > 0) {
            if(isPreviewMode){
                // 将当前Note移之新增分类下
                _state.value.note?.let { updateNoteCategoryId(it.noteId, categoryId) }
                _state.update { it.copy(newCategory = CategoryRepository.getCategory(categoryId)) }
                // 刷新分类列表
                refreshCategories(categoryId)
            }else{
                // 刷新分类列表及当前选中分类信息
                getCategories()
                // 更新当前选中的分类
                AppDataStore.putStringData("currentCategoryId", categoryId.toString())
                AppDataStore.putStringData("currentCategoryName", noteCategory.name)
                AppDataStore.putStringData("currentColorIndex", noteCategory.colorIndex.toString())
                AppDataStore.putStringData("currentCategoryIcon", determineIcon(noteCategory).toString())
                currentCategoryId.value = AppDataStore.getStringData("currentCategoryId", "")
                currentCategoryName.value = AppDataStore.getStringData("currentCategoryName", "")
                currentColorIndex.value = AppDataStore.getStringData("currentColorIndex", "")
                currentCategoryIcon.value = AppDataStore.getStringData("currentCategoryIcon", "")
            }
        }

    }

    /**
     * 确定分类icon
     */
    private fun determineIcon(category: NoteCategory): Int {
        // 这里是确定图标的逻辑，可以根据分类的某些属性或预置逻辑设置图标。
        return when (category.colorIndex) {
            CategoryColors.NONE_COLOR -> R.drawable.ic_category_uncategorised // 未分类
            CategoryColors.YELLOW_COLOR -> R.drawable.ic_category_yellow
            CategoryColors.ORANGE_COLOR -> R.drawable.ic_category_orange
            CategoryColors.PINK_COLOR -> R.drawable.ic_category_pink
            CategoryColors.PURPLE_COLOR -> R.drawable.ic_category_purple
            CategoryColors.BLUE_COLOR -> R.drawable.ic_category_blue
            CategoryColors.GREEN_COLOR -> R.drawable.ic_category_green
            else -> R.drawable.ic_all_notes
        }
    }

    /**
     * 重命名一个Category
     */
    private fun renameCategory(noteCategory: NoteCategory) = viewModelScope.launch {
        CategoryRepository.updateCategory(noteCategory)
        val newCategory = CategoryRepository.getCategory(noteCategory.categoryId)
        newCategory?.let {
            AppDataStore.putStringData("currentCategoryName", noteCategory.name)
            AppDataStore.putStringData("currentColorIndex",noteCategory.colorIndex.toString())
            AppDataStore.putStringData("currentCategoryIcon", newCategory.icon.toString())
            currentCategoryId.value = AppDataStore.getStringData("currentCategoryId", "")
            currentCategoryName.value = AppDataStore.getStringData("currentCategoryName", "")
            currentColorIndex.value = AppDataStore.getStringData("currentColorIndex", "")
            currentCategoryIcon.value = AppDataStore.getStringData("currentCategoryIcon", "")
        }?:run {
            resetToDefaultCategory()
        }

        // 刷新分类列表
        getCategories()
    }

    /**
     * 删除一条Note
     */
    private fun deleteOneNote(noteId:Long) = viewModelScope.launch {
        NoteRepository.deleteNote(noteId)
    }

    /**
     * 更新单条指定 Note 的 categoryId
     */
    private fun updateNoteCategoryId(noteId: Long, categoryId: Long) = viewModelScope.launch{
        NoteRepository.updateNoteCategoryId(noteId,categoryId)

        currentCategoryId.value = categoryId.toString()
        _state.update { it.copy(note = it.note?.copy(categoryId = categoryId)) }

        // 刷新分类列表
        refreshCategories(categoryId)
    }


    private var saveJob: Job? = null
    private val saveDebounceTime = 500L // 500毫秒防抖

    private val contentCache = mutableMapOf<Long, String>() // 基于noteId的缓存

    private fun styleHash(annotatedString: AnnotatedString): Int {
        return annotatedString.spanStyles.fold(1) { acc, span ->
            31 * acc + span.item.let { style ->
                listOf(
                    style.color.hashCode(),
                    style.fontSize.hashCode(),
                    style.fontWeight?.hashCode() ?: 0,
                    style.fontStyle?.hashCode() ?: 0,
                    style.textDecoration?.hashCode() ?: 0,
                    style.background.hashCode()
                ).fold(1) { styleAcc, value ->
                    31 * styleAcc + value
                } + span.start + span.end
            }
        }
    }

    private fun logSavePerformance(startTime: Long) {
        val duration = System.currentTimeMillis() - startTime
        Logger.v(TAG, "Save takes ${duration}ms")
    }

    private fun requestSave() {
        saveJob?.cancel()
        saveJob = viewModelScope.launch {
            delay(saveDebounceTime)
            performSave()
        }
    }

    private suspend fun performSave() {
        withContext(Dispatchers.IO) {
            _state.update { it.copy(isSaving = true) } // 开始保存
            try {
                val startTime = System.currentTimeMillis()
                val contents = _state.value.contents
                val title = _state.value.title
                val bgMode = _state.value.bgMode
                val bgColor = _state.value.bgColor
                val categoryId = _state.value.note?.categoryId
                val finalCategoryID = categoryId?:(currentCategoryId.value.toLongOrNull()?:1L)

                // 1. 处理Note对象
                val existingNote = _state.value.note ?: run {
                    // 创建新Note
                    val newNote = createNewNote(title, contents, bgMode, bgColor, finalCategoryID)
                    val newId = NoteRepository.insertNote(newNote)
                    newNote.copy(noteId = newId)
                }
                // 保存最新手绘缩略图
                HandWritingThumbnailRepo.saveRealTimeHandwritingThumbnail(existingNote.noteId)

                // 更新现有Note
                val updatedNote = updateNoteData(existingNote, title, contents, bgMode, bgColor, finalCategoryID)
                NoteRepository.updateNote(updatedNote)
                NoteRepository.getNote(updatedNote.noteId)?.let { newNote ->
                    _state.update { it.copy(note = newNote,) }
                }
                logSavePerformance(startTime)
                _state.update { it.copy(isSaving = false) } // 保存结束
            } catch (e: Exception) {
                Logger.e(TAG, "Save failed: ${e.message}\n${e.stackTraceToString()}")
                if (e is CancellationException) {
                    // 恢复保存状态允许重试
                    _state.update { it.copy(canSave = true) }
                } else {
                    // 出现异常，强制结束
                    _state.update { it.copy(isSaving = false) }
                }
            }
        }
    }

    private fun createNewNote(title: String, contents: List<EditorContent>,bgMode: BgMode,bgColor: Long, categoryId: Long): Note {
        var firstImageUri: String? = null
        var hasAudio = false

        // 获取第一个文本块或待办事项的内容
        val summaryContent = contents.firstOrNull { content ->
            content is EditorContent.TextBlock || content is EditorContent.TodoBlock
        }?.let { content ->
            when (content) {
                is EditorContent.TextBlock -> content.text.text
                is EditorContent.TodoBlock -> content.text.text
                else -> "" // 理论上不会走到这个分支
            }
        }?.take(300) ?: "" // 如果没有符合条件的块则返回空字符串

        val fullContent = buildString {
            contents.forEach { content ->
                when (content) {
                    is EditorContent.TextBlock -> append(content.text.text)
                    is EditorContent.TodoBlock -> append(content.text.text)
                    is EditorContent.ImageBlock -> firstImageUri = firstImageUri ?: content.uri.toString()
                    is EditorContent.AudioBlock -> hasAudio = true
                    is EditorContent.RichTextV2 -> {}
                }
            }
        }

        return Note(
            title = title,
            content = fullContent,
            contents = contents,
            summary = summaryContent,
            firstPicture = firstImageUri,
            handwritingThumbnail = null,
            hasAudio = hasAudio,
            categoryId = categoryId,
            createTime = System.currentTimeMillis(),
            modifyTime = System.currentTimeMillis(),
            bgMode = bgMode,
            bgColor = bgColor
        )
    }

    private fun updateNoteData(
        existingNote: Note,
        newTitle: String,
        contents: List<EditorContent>,
        bgMode: BgMode,bgColor: Long,
        categoryId: Long
    ): Note {
        var firstImageUri: String? = null
        var hasAudio = false

        // 获取第一个文本块或待办事项的内容
        val summaryContent = contents.firstOrNull { content ->
            content is EditorContent.TextBlock || content is EditorContent.TodoBlock
        }?.let { content ->
            when (content) {
                is EditorContent.TextBlock -> content.text.text
                is EditorContent.TodoBlock -> content.text.text
                else -> "" // 理论上不会走到这个分支
            }
        }?.take(300) ?: "" // 如果没有符合条件的块则返回空字符串

        val fullContent = buildString {
            contents.forEach { content ->
                when (content) {
                    is EditorContent.TextBlock -> append(content.text.text)
                    is EditorContent.TodoBlock -> append(content.text.text)
                    is EditorContent.ImageBlock -> {
                        if (firstImageUri == null) {
                            firstImageUri = content.uri.toString()
                        }
                    }
                    is EditorContent.AudioBlock -> hasAudio = true
                    is EditorContent.RichTextV2 -> {}
                }
            }
        }
        return existingNote.copy(
            title = newTitle,
            content = fullContent,
            contents = contents,
            summary = summaryContent,
            firstPicture = firstImageUri,
            handwritingThumbnail = HandWritingThumbnailRepo.getBitmapPath(existingNote.noteId),
            hasAudio = hasAudio,
            modifyTime = System.currentTimeMillis(),
            bgMode = bgMode,
            bgColor = bgColor,
            categoryId = categoryId
        )
    }

    /**
     * 加载回显Note数据
     * @param isLandscape 是否横屏
     */
    private fun loadNoteContents(noteId: Long,isLandscape:Boolean) {
        viewModelScope.launch {
            initEditMode(noteId,isLandscape)
            NoteRepository.getNote(noteId)?.let { note->
                _state.update {
                    it.copy(
                        title = note.title,
                        note = note,
                        contents = note.contents,
                        bgMode = note.bgMode,
                        bgColor = note.bgColor,
                    )
                }
                _state.update { it.copy(newCategory = CategoryRepository.getCategory(note.categoryId)) }
                if(isLandscape && _state.value.editMode){
                    // 1. 找到第一个文本或TODO块
                    val editorIndex = note.contents.indexOfFirst { content ->
                        content is EditorContent.TextBlock || content is EditorContent.TodoBlock
                    }
                    // 2. 设置焦点到第一个可编辑的内容块
                    if (editorIndex != -1) {
                        val lastPos = when (val block = note.contents[editorIndex]) {
                            is EditorContent.TextBlock -> block.text.text.length
                            is EditorContent.TodoBlock -> block.text.text.length
                            else -> 0
                        }
                        // 更新焦点与光标位置
                        updateFocusedIndex(editorIndex)
                        updateCursorPosition(lastPos, isChange = false)
                    }
                }
            }
        }
    }

    private fun updateTextStyles(index: Int, spans: List<AnnotatedString.Range<SpanStyle>>) {
        val currentList = _state.value.contents
        if (index !in currentList.indices) return

        when (val content = currentList[index]) {
            is EditorContent.TextBlock -> {
                val updatedAnnotatedString = content.text.annotatedString.updateSpans(spans)

                val updatedBlock = content.copy(
                    text = content.text.copy(
                        annotatedString = updatedAnnotatedString
                    )
                )

                _state.update {
                    it.copy(contents = _state.value.contents.toMutableList().apply {
                        set(index, updatedBlock)
                    })
                }
                updateContent(index, updatedBlock)
            }
            // 其他类型处理...
            is EditorContent.ImageBlock -> {}
            is EditorContent.TodoBlock -> {
                val updatedAnnotatedString = content.text.annotatedString.updateSpans(spans)

                val updatedBlock = content.copy(
                    text = content.text.copy(
                        annotatedString = updatedAnnotatedString
                    )
                )
                _state.update {
                    it.copy(contents = _state.value.contents.toMutableList().apply {
                        set(index, updatedBlock)
                    })
                }
                updateContent(index, updatedBlock)
            }
            is EditorContent.AudioBlock -> {} //临时实现
            is EditorContent.RichTextV2 -> {}
        }
    }

    private fun AnnotatedString.updateSpans(
        spans: List<AnnotatedString.Range<SpanStyle>>
    ): AnnotatedString {
        return buildAnnotatedString {
            append(this@updateSpans) // 继承原有文本和样式
            spans.forEach { (style, start, end) ->
                addStyle(style, start, end)
            }
        }
    }

    /**
     * 解析选区范围内的字符样式，更新 isBoldActive/isItalicActive/isUnderlineActive 状态
     * 如果 range.collapsed，则使用 parseCurrentStyles(index) 的单点逻辑
     */
    private fun parseSelectionStyles(index: Int, range: TextRange?) {
        val block = _state.value.contents.getOrNull(index)
        if (block == null || range == null) {
            resetStyles()
            return
        }
        if (range.collapsed) {
            // 非选区时用之前的逻辑
            parseCurrentStyles(index)
            return
        }
        // 取选中范围内的所有 SpanStyle
        val annotatedString = when (block) {
            is EditorContent.TextBlock -> block.text.annotatedString
            is EditorContent.TodoBlock -> block.text.annotatedString
            else -> null
        } ?: return

        val safeStart = range.min
        val safeEnd = range.max.coerceAtMost(annotatedString.length)

        // 枚举每个字符，看某种样式是不是全都有
        fun isStyleActive(test: (SpanStyle?) -> Boolean): Boolean {
            if (safeStart >= safeEnd) return false
            for (i in safeStart until safeEnd) {
                // 查找包含i的span（多个span重叠的情况则最顶部那层就被认为是“覆盖”的）
                val span = annotatedString.spanStyles.lastOrNull { i in it.start until it.end }
                if (!test(span?.item)) {
                    return false
                }
            }
            return true
        }

        val boldActive = isStyleActive { it?.fontWeight == FontWeight.Bold }
        val italicActive = isStyleActive { it?.fontStyle == FontStyle.Italic }
        val underlineActive = isStyleActive { it?.textDecoration?.contains(TextDecoration.Underline) == true }

        _state.update {
            it.copy(
                isBoldActive = boldActive,
                isItalicActive = italicActive,
                isUnderlineActive = underlineActive,
            )
        }
        // 同步 currentTextStyle
        updateTextStyle(_currentTextStyle.value.copy(
            fontWeight = if (boldActive) FontWeight.Bold else FontWeight.Normal,
            fontStyle = if (italicActive) FontStyle.Italic else FontStyle.Normal,
            textDecoration = if (underlineActive) TextDecoration.Underline else TextDecoration.None
        ))
    }


    // 撤销栈和重做栈，限制最大50个操作
    private val undoStack = ArrayDeque<Operation>(50)
    private val redoStack = ArrayDeque<Operation>(50)


    // 定义操作类型
    sealed class Operation {
        data class UpdateTitle(val oldTitle: String, val newTitle: String) : Operation()
        data class Add(val index: Int, val content: EditorContent) : Operation()
        data class Remove(val index: Int, val content: EditorContent) : Operation()
        data class Update(val index: Int, val old: EditorContent, val new: EditorContent) : Operation()
        data class ReplaceAll(
            val oldContents: List<EditorContent>,
            val newContents: List<EditorContent>
        ) : Operation()
        data class Merge(
            val firstIndex: Int,
            val oldFirst: EditorContent,
            val secondIndex: Int,
            val oldSecond: EditorContent,
            val merged: EditorContent
        ) : Operation()
    }

    private var lastEditTimestamp = System.currentTimeMillis()
    // 记录操作（新增/修改/删除时调用）
    private fun recordOperation(op: Operation) {
        when (op) {
            // 处理批量替换操作
            is Operation.ReplaceAll -> {
                // 限制操作记录数量
                if (undoStack.size >= 50) undoStack.removeFirst()

                // 添加操作到撤销栈
                undoStack.addLast(op)

                // 清空重做栈（新操作使后续操作无效）
                redoStack.clear()

                // 更新按钮状态
                updateButtonStates()

                // 记录操作时间戳用于连续操作判断
                lastEditTimestamp = System.currentTimeMillis()
            }
            // 处理更新操作（合并连续编辑）
            is Operation.Update -> {
                val lastOp = undoStack.lastOrNull()
                if (lastOp is Operation.Update &&
                    lastOp.index == op.index &&
                    (System.currentTimeMillis() - lastEditTimestamp) < 500) {

                    // 合并到前一个更新操作
                    undoStack.removeLast()
                    undoStack.add(op.copy(old = lastOp.old))
                } else {
                    // 添加新操作
                    if (undoStack.size >= 50) undoStack.removeFirst()
                    undoStack.addLast(op)
                }
                redoStack.clear()
                lastEditTimestamp = System.currentTimeMillis()
            }
            // 处理其他操作类型
            else -> {
                // 常规操作记录逻辑
                if (undoStack.size >= 50) undoStack.removeFirst()
                //Modified by caili.zhao for TNOTE-2747 on 20250421 begin
                if (op is Operation.Remove && op.content is EditorContent.AudioBlock) {
                    //if (!File(op.content.audioPath).exists()) {
                        // 如果是音频块，直接删除
                        val size = undoStack.size
                        for (i in size - 1 downTo 0) {
                            val element = undoStack.elementAt(i)
                            if (element is Operation.Add && element.content is EditorContent.AudioBlock) {
                                if (element.content.audioPath == op.content.audioPath) {
                                    undoStack.removeAt(i)
                                }
                                break
                            }
                        }
                    /*} else {
                        undoStack.addLast(op)
                    }*/
                }else {
                    undoStack.addLast(op)
                }
                //Modified by caili.zhao for TNOTE-2747 on 20250421 end
                redoStack.clear()
                lastEditTimestamp = System.currentTimeMillis()
            }
        }
        // 更新状态中的撤销栈引用
        _state.update { it.copy(undoStack = undoStack) }

        // 更新按钮可用状态
        updateButtonStates()
    }

    private fun isContinuousEdit(oldOp: Operation.Update, newOp: Operation.Update): Boolean {
        // 判断两次编辑时间间隔小于1秒视为连续操作
        return (System.currentTimeMillis() - lastEditTimestamp) < 500
    }

    // 更新按钮状态
    private fun updateButtonStates() {
        val canUndo = undoStack.isNotEmpty()
        val canRedo = redoStack.isNotEmpty()
        Logger.d(TAG, "Updating buttons - Undo: $canUndo Redo: $canRedo")
        _state.update {
            it.copy(canUndo = canUndo, canRedo = canRedo)
        }
    }

    /**
     * 清除撤销重做操作记录
     */
    private fun resetHistory() {
        undoStack.clear()
        redoStack.clear()
        _state.update {
            it.copy(undoStack = undoStack)
        }
        updateButtonStates()
    }

    /**
     * 合并两个相邻内容块时的原子操作记录与处理。
     *
     * 该方法用于实现富文本编辑器中“将当前块与前一个块合并”的逻辑，并**将合并操作作为一个不可分割的撤销/重做原子操作**插入到撤销栈。
     *
     * 操作流程：
     * 1. 用合并后的新内容替换前一个块（prevIndex）。
     * 2. 删除当前块（curIndex）。
     * 3. 更新状态为前一个块获得焦点。
     * 4. 记录一次“合并”类型的撤销操作（包含合并前的两个块内容和合并后的内容），用于撤销时能够完整恢复撤销前的状态，包括文本、样式、段落类型等。
     * 5. 触发自动保存。
     *
     * @param prevIndex 前一个块（目标合并块）在内容列表中的索引
     * @param prevBlockOriginal 合并前的前一个块的完整数据（包括文本、样式、段落类型等）
     * @param curIndex 当前被合并并将被删除的块在内容列表中的索引
     * @param curBlockOriginal 合并前当前块的完整数据
     * @param mergedBlock 合并后的新块（用于替换prevIndex下的块）
     */
    fun handleMergeBlocks(
        prevIndex: Int,
        prevBlockOriginal: EditorContent,
        curIndex: Int,
        curBlockOriginal: EditorContent,
        mergedBlock: EditorContent
    ) {
        // 1. 用合并后的内容替换前一个块
        updateContentQuietly(prevIndex, mergedBlock)
        // 2. 删除当前块
        removeContentQuietly(curIndex)
        // 3. 更新焦点
        _state.update { it.copy(focusedIndex = prevIndex) }
        // 4. 记录到撤销栈，便于撤销时完整还原合并前状态
        recordOperation(
            Operation.Merge(
                prevIndex,        // 合并后块位置
                prevBlockOriginal,// 合并前的前块数据
                curIndex,         // 被合并并删除的块位置
                curBlockOriginal, // 合并前的当前块数据
                mergedBlock       // 合并后内容
            )
        )
        // 5. 触发保存
        requestSave()
    }

    // 执行撤销
    private fun performUndo() {
        if (undoStack.isEmpty()) {  // 添加前置空检查
            updateButtonStates()
            return
        }
        undoStack.removeLastOrNull()?.let { op ->
            // 添加操作有效性检查
            when (op) {
                is Operation.Add -> {
                    if (op.index > _state.value.contents.size) {
                        Logger.w(TAG, "Undo Add: Invalid index ${op.index} for list size ${_state.value.contents.size}")
                        return@let
                    }
                }
                is Operation.Remove -> {
                    if (op.index >= _state.value.contents.size) {
                        Logger.w(TAG, "Undo Remove: Invalid index ${op.index} for list size ${_state.value.contents.size}")
                        return@let
                    }
                }
                is Operation.Update -> {
                    if (op.index >= _state.value.contents.size) {
                        Logger.w(TAG, "Undo Update: Invalid index ${op.index} for list size ${_state.value.contents.size}")
                        return@let
                    }
                }
                is Operation.ReplaceAll -> {} // 始终允许替换操作
                is Operation.UpdateTitle -> {}
                is Operation.Merge -> {}
            }
            try {
                applyReverseOperation(op)
                redoStack.addLast(op)
                parseCurrentStyles(_state.value.focusedIndex)
            }catch (e: Exception){
                Logger.e(TAG, "Undo failed: ${e.message}")
                undoStack.addLast(op)  // 回滚操作
            }
        }
        updateButtonStates()
        _state.update {
            it.copy(undoStack = undoStack)
        }

        val selectionRange = selection.value
        parseSelectionStyles(_state.value.focusedIndex, selectionRange)
    }

    // 执行重做
    private fun performRedo() {
        redoStack.removeLastOrNull()?.let { op ->
            // 添加操作有效性检查
            when (op) {
                is Operation.Add -> {
                    if (op.index > _state.value.contents.size) {
                        Logger.w(TAG, "Redo Add: Invalid index ${op.index} for list size ${_state.value.contents.size}")
                        return@let
                    }
                }
                is Operation.Remove -> {
                    if (op.index >= _state.value.contents.size) {
                        Logger.w(TAG, "Redo Remove: Invalid index ${op.index} for list size ${_state.value.contents.size}")
                        return@let
                    }
                }
                is Operation.Update -> {
                    if (op.index >= _state.value.contents.size) {
                        Logger.w(TAG, "Redo Update: Invalid index ${op.index} for list size ${_state.value.contents.size}")
                        return@let
                    }
                }
                is Operation.ReplaceAll -> {} // 始终允许替换操作
                is Operation.UpdateTitle -> {}
                is Operation.Merge -> {}
            }

            applyForwardOperation(op)
            undoStack.addLast(op)
            updateButtonStates()
        }
        _state.update {
            it.copy(undoStack = undoStack)
        }

        val selectionRange = selection.value
        parseSelectionStyles(_state.value.focusedIndex, selectionRange)
    }

    // 应用逆向操作（撤销）
    private fun applyReverseOperation(op: Operation) {
        when (op) {
            is Operation.Add -> {
                if(op.index<_state.value.contents.size){
                    removeContentQuietly(op.index)
                    _state.update {
                        it.copy(focusedIndex = op.index - 1)
                    }
                }
            }
            is Operation.Remove -> {
                addContentQuietly(op.index, op.content)
                _state.update {
                    it.copy(focusedIndex = op.index)
                }
            }
            is Operation.Update -> {
                // 检查索引有效性
                if (op.index < _state.value.contents.size) {
                    updateContentQuietly(op.index, op.old)
                    _state.update {
                        it.copy(focusedIndex = op.index)
                    }
                    // 恢复光标位置
                    (op.old as? EditorContent.TextBlock)?.text?.selection?.start?.let { position ->
                        _state.update {
                            it.copy(cursorPosition = position)
                        }
                    }
                } else {
                    // 处理无效索引，例如重置焦点或跳过操作
                    _state.update { it.copy(focusedIndex = -1) }
                }
            }

            is Operation.ReplaceAll -> {
                _state.update { it.copy(contents = op.oldContents) }
            }

            is Operation.UpdateTitle -> {
                _state.update { it.copy(title = op.oldTitle, note = it.note?.copy(title = op.oldTitle)) }
            }

            is Operation.Merge -> {
                updateContentQuietly(op.firstIndex, op.oldFirst)   // 恢复前块的全部，包括paragraphStyle
                addContentQuietly(op.secondIndex, op.oldSecond)    // 恢复被合并块的全部，包括paragraphStyle
                _state.update { it.copy(focusedIndex = op.secondIndex) }
            }
        }

        updateNoteFromContents() // 新增：操作后更新Note数据
        requestSave() // 触发即时保存检查
    }

    // 应用正向操作（重做）
    private fun applyForwardOperation(op: Operation) {
        when (op) {
            is Operation.Add -> {
                addContentQuietly(op.index, op.content)
                _state.update {
                    it.copy(focusedIndex = op.index)
                }
            }
            is Operation.Remove -> {
                if(op.index<_state.value.contents.size){
                    removeContentQuietly(op.index)
                    _state.update {
                        it.copy(focusedIndex = op.index - 1)
                    }
                }
            }
            is Operation.Update -> {
                if(op.index<_state.value.contents.size){
                    updateContentQuietly(op.index, op.new)
                    _state.update {
                        it.copy(focusedIndex = op.index)
                    }
                    // 恢复光标位置
                    (op.new as? EditorContent.TextBlock)?.text?.selection?.start?.let {position->
                        _state.update {
                            it.copy(cursorPosition = position)
                        }
                    }
                }

            }

            is Operation.ReplaceAll -> {
                _state.update { it.copy(contents = op.newContents) }
            }

            is Operation.UpdateTitle -> _state.update { it.copy(title = op.newTitle, note = it.note?.copy(title = op.newTitle)) }
            is Operation.Merge -> {
                // 重做合并
                updateContentQuietly(op.firstIndex, op.merged)
                removeContentQuietly(op.secondIndex)
                _state.update { it.copy(focusedIndex = op.firstIndex) }
            }
        }

        updateNoteFromContents() // 新增：操作后更新Note数据
        requestSave() // 触发即时保存检查
    }

    /**
     * 根据当前内容更新Note对象
     */
    private fun updateNoteFromContents() {
        _state.value.note?.let { existingNote ->
            val updatedNote = updateNoteData(
                existingNote = existingNote,
                newTitle = _state.value.title,
                contents = _state.value.contents,
                bgMode = existingNote.bgMode,
                bgColor = existingNote.bgColor,
                categoryId = existingNote.categoryId
            )
            _state.update { it.copy(note = updatedNote) }
        } ?: run {
            // 新建Note的情况
            val newNote = createNewNote(
                title = _state.value.title,
                contents = _state.value.contents,
                bgMode = BgMode.none,
                bgColor = Skin.defColor,
                categoryId = 1L
            )
            _state.update { it.copy(note = newNote) }
        }
    }

    // 静默添加（不记录操作）
    private fun addContentQuietly(index: Int, content: EditorContent) {
        _state.update {
            it.copy(contents = it.contents.toMutableList().apply {
                add(index, content)
            })
        }
    }

    // 静默删除（不记录操作）
    private fun removeContentQuietly(index: Int) {
        _state.update {
            it.copy(contents = it.contents.toMutableList().apply {
                removeAt(index)
            })
        }
    }

    // 静默更新（不记录操作）
    private fun updateContentQuietly(index: Int, content: EditorContent) {
        _state.update {
            it.copy(contents = it.contents.toMutableList().apply {
                set(index, content)
            })
        }
    }

    /**
     * 批量替换内容
     */
    private fun replaceAllContents(newContents: List<EditorContent>) {
        val oldContents = _state.value.contents.toList()
        _state.update { it.copy(contents = newContents) }

        // 记录操作用于撤销
        recordOperation(Operation.ReplaceAll(oldContents, newContents))
        // 显式更新按钮状态
        updateButtonStates()
        // 保存当前数据
        requestSave()
    }

    /**
     * 重置手写转文本内容
     */
    private fun resetHandwritingToText(){
        _state.update {
            it.copy(aiInsertText = "")
        }
    }




    private fun saveLastBrushMenuType(brushType:String){

        if (lastBrushMenuType!=brushType && (brushType == BrushMenu.ERASER || brushType == BrushMenu.PEN)){
            viewModelScope.launch {
                AppDataStore.putStringData(APP_DATA_KEY_LAST_BRUSH_MENU_TYPE,brushType)
                lastBrushMenuType = brushType
            }
        }

    }
    fun lastCacheBrushMenuType(){
        if(lastBrushMenuType.isEmpty()){
            viewModelScope.launch {
                lastBrushMenuType = AppDataStore.getStringData(APP_DATA_KEY_LAST_BRUSH_MENU_TYPE,BrushMenu.PEN)
                handleIntent(
                    RichTextIntent.UpdateBrushMenuType(
                        lastBrushMenuType                    )
                )
            }
        }

    }


    /**
     * 校验插入当前输入的内容后长度是否超出限制
     * @return 返回实际允许输入的文本
     */
    fun validateContentCanAdd(input: String): Boolean {
        // 步骤1：获取当前焦点块索引
        val focusedIndex = _state.value.focusedIndex

        // 步骤2：计算排除焦点块后的总长度
        val otherBlocksLength = _state.value.contents
            .filterIndexed { index, _ -> index != focusedIndex }
            .sumOf { content ->
                when (content) {
                    is EditorContent.TextBlock -> content.text.text.length
                    is EditorContent.TodoBlock -> content.text.text.length
                    else -> 0
                }
            }

        // 步骤3：计算剩余可用长度
        val remaining = RichTextController.MAX_CONTENT_LENGTH - otherBlocksLength

        // 步骤4：处理新文本长度
        return remaining >= input.length

    }

    private fun updateAudioPath(oldAudioPath: String, newAudioPath: String) {
        _state.value.contents.forEachIndexed { index, content ->
            if (content is EditorContent.AudioBlock && content.audioPath == oldAudioPath) {
                val updatedContent = content.copy(audioPath = newAudioPath)
                updateContentQuietly(index, updatedContent)
                return@forEachIndexed
            }
        }
    }

    private fun deleteAudio(audioPath: String) {
        _state.value.contents.forEachIndexed { index, content ->
            if (content is EditorContent.AudioBlock && content.audioPath == audioPath) {
                removeContentQuietly(index)
                return@forEachIndexed
            }
        }
    }

    fun handleIntent(intent: RichTextIntent) {
        Logger.d(TAG, "intent : $intent")
        when (intent) {
            is RichTextIntent.GetCategories -> {
                getCategories()
            }

            is RichTextIntent.LoadNoteContents -> {
                loadNoteContents(intent.noteId,intent.isLandscape)
            }

            is RichTextIntent.AddCategory -> {
                addCategory(intent.category,intent.isPreviewMode)
            }

            is RichTextIntent.RenameCategory -> {
                renameCategory(intent.category)
            }

            is RichTextIntent.DeleteOneNote -> {
                deleteOneNote(intent.noteId)
            }

            is RichTextIntent.UpdateNoteCategoryId -> {
                updateNoteCategoryId(intent.noteId, intent.categoryId)
            }

            is RichTextIntent.AddContent -> {
                addContent(intent.index,intent.content)
            }

            is RichTextIntent.UpdateContent -> {
                updateContent(intent.index,intent.content)
            }

            is RichTextIntent.RemoveContent -> {
                removeContent(intent.index)
            }

            is RichTextIntent.UpdateTitle -> {
                updateTitle(intent.title)
            }

            is RichTextIntent.UpdateTextStyle -> {
                updateTextStyle(intent.style)
            }

            is RichTextIntent.UpdateTextStyles -> {
                updateTextStyles(intent.index,intent.spans)
            }

            is RichTextIntent.ParseCurrentStyles ->{
                parseCurrentStyles(intent.index)
            }

            is RichTextIntent.UpdateParagraphStyle -> {

            }

            is RichTextIntent.ToggleParagraphStyle -> {
                toggleParagraphStyle(intent.index,intent.targetStyle)
            }

            is RichTextIntent.UpdateMenuType -> {
                updateBottomMenuType(intent.type)
            }

            is RichTextIntent.UpdateBrushMenuType -> {
                updateBrushMenuType(intent.type)
                if(intent.type == BrushMenu.PEN || intent.type == BrushMenu.ERASER){
                    saveLastBrushMenuType(intent.type)
                }
            }

            is RichTextIntent.PerformSave -> {
                viewModelScope.launch {
                    performSave()
                }
            }
            is RichTextIntent.RequestSave -> {
                requestSave()
            }

            is RichTextIntent.UpdateCursorPosition -> {
                updateCursorPosition(intent.position,intent.isChange)
            }
            is RichTextIntent.UpdateTitleCursorPosition -> {
                updateTitleCursorPosition(intent.position)
            }
            is RichTextIntent.UpdateFocusedIndex -> {
                updateFocusedIndex(intent.index)
            }

            is RichTextIntent.Redo -> {
                performRedo()
            }
            is RichTextIntent.Undo -> {
                performUndo()
            }

            is RichTextIntent.ResetHistory -> {
                resetHistory()
            }

            is RichTextIntent.UpdateTitleFocus -> {
                updateTitleFocus(intent.hasFocus)
            }

            is RichTextIntent.UpdateEditMode -> {
                updateEditMode(intent.enabled)
            }

            is RichTextIntent.UpdateSaveState -> {
                updateSaveState(intent.canSave)
            }

            is RichTextIntent.UpdateCurrentFocusedType -> {
                updateCurrentFocusedType(intent.editorContent)
            }

            is RichTextIntent.UpdateCurrentParagraphStyle -> {
                updateCurrentParagraphStyle(intent.currentParagraphStyle)
            }

            is RichTextIntent.UpdateCurrentParagraphType ->{
                updateCurrentParagraphType(intent.currentParagraphType)
            }

            is RichTextIntent.ReplaceAllContents -> {
                replaceAllContents(intent.contents)
            }
            is RichTextIntent.UpdateSkinStyle ->{
                updateSkinStyle(intent.bgMode,intent.color)
            }

            is RichTextIntent.ResetHandwritingToText -> {
                resetHandwritingToText()
            }

            is RichTextIntent.UpdateBoldActive -> {
                updateBoldActive(intent.isBoldActive)
            }
            is RichTextIntent.UpdateItalicActive -> {
                updateItalicActive(intent.isItalicActive)
            }
            is RichTextIntent.UpdateUnderlineActive -> {
                updateUnderlineActive(intent.isUnderlineActive)
            }
            is RichTextIntent.UpdateAudioPath -> {
                updateAudioPath(intent.oldAudioPath, intent.newAudioPath)
            }
            is RichTextIntent.DeleteAudio -> {
                deleteAudio(intent.audioPath)
            }
        }
    }

    /**
     * 当用户从右向左选择文本时（这会创建一个反向的选择范围）
     * 避免 java.lang.IllegalArgumentException: Reversed range is not supported
     * 创建安全的 TextRange 对象，确保 start <= end
     * @param start 起始位置
     * @param end 结束位置（可选，默认与 start 相同）
     * @return 安全的 TextRange 对象
     */
    fun safeTextRange(start: Int, end: Int = start): TextRange {
        return if (start <= end) {
            TextRange(start, end)
        } else {
            TextRange(end, start) // 交换位置
        }
    }

    companion object {
        private const val TAG = "RichTextViewModel"
    }
}