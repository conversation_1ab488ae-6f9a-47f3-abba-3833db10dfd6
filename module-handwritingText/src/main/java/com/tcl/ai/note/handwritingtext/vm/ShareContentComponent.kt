package com.tcl.ai.note.handwritingtext.vm

import android.content.ClipData
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.graphics.pdf.PdfDocument
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import androidx.core.content.FileProvider
import coil.ImageLoader
import coil.request.ImageRequest
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.DrawStroke
import com.tcl.ai.note.handwritingtext.database.entity.*
import com.tcl.ai.note.handwritingtext.repo.DrawBoardRepository
import com.tcl.ai.note.handwritingtext.ui.richtext.component.TitleBlock
import com.tcl.ai.note.handwritingtext.ui.richtext.richtext.ShareTextBlock
import com.tcl.ai.note.handwritingtext.ui.richtext.richtext.ShareTodoBlock
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.MeshStyle
import com.tcl.ai.note.handwritingtext.utils.HtmlDocument
import com.tcl.ai.note.handwritingtext.utils.HtmlDocument.Companion.toHtmlText
import com.tcl.ai.note.handwritingtext.utils.HtmlTextStyle
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.px2dp
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.voicetotext.util.formatAudioTime
import com.tcl.ai.note.voicetotext.util.getAudioDuration
import com.tcl.ai.note.voicetotext.view.widget.AudioBlock
import com.tcl.ai.note.widget.capturable.GraphicsLayerController
import com.tcl.ai.note.widget.capturable.recordGraphicsLayer
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.first
import java.io.File
import java.io.FileOutputStream
import kotlin.math.max
import kotlin.math.min

@Deprecated("二期使用ShareContentController")
class ShareContentComponent {
    private var showPopupStateFlow = MutableStateFlow(false)
    private var popupDrawingStateFlow = MutableStateFlow(false)
    private val richTextGraphicsController: GraphicsLayerController = GraphicsLayerController()
    private val handwritingGraphicsLayerController: GraphicsLayerController = GraphicsLayerController()
    private var richTextSize = IntSize(0, 0)
    private var handwritingCanvasOffset = Offset(0f, 0f)

    /**
     * 执行图片分享流程
     *
     * @param context 上下文对象
     * @param coroutineScope 协程作用域（由调用方传入）
     * @param note 笔记对象，包含标题等信息
     * @param contentList 笔记内容列表，包括文本块、待办项、图片块等
     * @param screenWidthDp 屏幕宽度（单位：dp）
     * @param density 屏幕像素密度（dp 转换为 px 的系数）
     */
    suspend fun shareWithImage(
        context: Context,
        note: Note,
    ) = coroutineScope {
        try {
            showPopupStateFlow.value = true
            popupDrawingStateFlow.value = true
            popupDrawingStateFlow.filter { it == false }.first()
            Logger.d(TAG, "Popup draw end, ready shareWithImage")

            val drawStrokeList = DrawBoardRepository.getDrawByNoteIdBlock(note.noteId)?.strokes
            val handwritingSize = drawStrokeList?.getHandwritingBitmapSize() ?: IntSize(0, 0)
            val recordWidth = max(handwritingSize.width, richTextSize.width)
            val recordHeight = max(handwritingSize.height, richTextSize.height)
            val widthScale = if (recordWidth > 3000) 3000F / recordWidth else 1F
            val heightScale = if (recordHeight > 4000) 4000F / recordHeight else 1F
            val recordScale = min(widthScale, heightScale)

            val richTextBitmap = runCatching {
                richTextGraphicsController.toImageBitmap(size = richTextSize, scale = recordScale).asAndroidBitmap().asShared().asImageBitmap()
            }.getOrDefault(ImageBitmap(1, 1, ImageBitmapConfig.Argb8888))
            val handwritingBitmap = runCatching {
                handwritingGraphicsLayerController.toImageBitmap(size = handwritingSize, scale = recordScale, offset = handwritingCanvasOffset).asAndroidBitmap().asShared().asImageBitmap()
            }.getOrDefault(ImageBitmap(1, 1, ImageBitmapConfig.Argb8888))

            val width = max(richTextBitmap.width,handwritingBitmap.width)
            val height = max(richTextBitmap.height,handwritingBitmap.height)
            Logger.d(TAG, "shareWithImage, collect bitmap end, width: $width, height: $height")

            // 3. 创建 Bitmap 并设置白色背景
            val canvasBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(canvasBitmap)
            canvas.drawColor(Color.parseColor("#FFF5F6F7")) // 背景
            canvas.drawBitmap(richTextBitmap.asAndroidBitmap(), 0f, 0f, null)
            canvas.drawBitmap(handwritingBitmap.asAndroidBitmap(), 0f, 0f, null)
            // 6. 将图片保存到文件并启动分享流程
            saveAndShareImage(context, canvasBitmap)
        } catch (e: Exception) {
            // 捕获异常并在主线程提示用户生成图片失败
            withContext(Dispatchers.Main) {
                Logger.w(TAG, context.getString(R.string.failed_to_create_image) + ": ${e.stackTraceToString()}")
            }
        }
        showPopupStateFlow.value = false
    }

    /**
     * 保存图片并触发分享
     */
    private fun saveAndShareImage(context: Context, bitmap: Bitmap) {
        try {
            // 创建缓存目录
            val cacheDir = File(context.cacheDir, "shared_images").apply { mkdirs() }
            val outputFile = File(cacheDir, "note.png")

            // 保存Bitmap到文件
            FileOutputStream(outputFile).use { out ->
                bitmap.compress(Bitmap.CompressFormat.PNG, 90, out)
            }
            // 生成分享URI
            val contentUri = FileProvider.getUriForFile(context, "${context.packageName}.fileprovider", outputFile)
            // 构建分享Intent
            Intent(Intent.ACTION_SEND).apply {
                type = "image/png"
                clipData = ClipData.newRawUri("", contentUri)
                putExtra(Intent.EXTRA_STREAM, contentUri)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }.also { intent ->
                context.startActivity(Intent.createChooser(intent, context.getString(R.string.show_content_share)))
            }
        } catch (e: Exception) {
            Logger.w(TAG, "Sharing failed:${e.stackTraceToString()}")
        } finally {
            bitmap.recycle() // 释放Bitmap内存
        }
    }


    /**
     * 启动PDF生成和分享流程
     * @param context 上下文对象
     * @param coroutineScope 协程作用域
     * @param note 笔记元数据
     * @param contentList 笔记内容列表（支持文本/待办/图片）
     */
    suspend fun shareWithPdf(
        context: Context,
        note: Note,
    ) = coroutineScope {
        try {
            showPopupStateFlow.value = true
            popupDrawingStateFlow.value = true
            popupDrawingStateFlow.filter { it == false }.first()
            Logger.d(TAG, "Popup draw end, ready shareWithPdf")

            val drawStrokeList = DrawBoardRepository.getDrawByNoteIdBlock(note.noteId)?.strokes
            val handwritingSize = drawStrokeList?.getHandwritingBitmapSize() ?: IntSize(0, 0)
            val recordWidth = max(handwritingSize.width, richTextSize.width)
            val recordHeight = max(handwritingSize.height, richTextSize.height)
            val widthScale = if (recordWidth > 3000) 3000F / recordWidth else 1F
            val heightScale = if (recordHeight > 4000) 4000F / recordHeight else 1F
            val recordScale = min(widthScale, heightScale)

            val richTextBitmap = runCatching {
                    richTextGraphicsController.toImageBitmap(size = richTextSize, scale = recordScale).asAndroidBitmap().asShared().asImageBitmap()
                }.getOrDefault(ImageBitmap(1, 1, ImageBitmapConfig.Argb8888))
            val handwritingBitmap = runCatching {
                    handwritingGraphicsLayerController.toImageBitmap(size = handwritingSize, scale = recordScale, offset = handwritingCanvasOffset).asAndroidBitmap().asShared().asImageBitmap()
                }.getOrDefault(ImageBitmap(1, 1, ImageBitmapConfig.Argb8888))

            val width = max(richTextBitmap.width,handwritingBitmap.width)
            val height = max(richTextBitmap.height,handwritingBitmap.height)
            Logger.d(TAG, "shareWithPdf, collect bitmap end, width: $width, height: $height")

            // 初始化PDF文档
            val document = PdfDocument()
            val pageInfo = PdfDocument.PageInfo.Builder(width, height, 1).create()
            val page = document.startPage(pageInfo)
            val canvas = page.canvas
            canvas.drawColor(Color.parseColor("#FFF5F6F7")) // 背景
            canvas.drawBitmap(richTextBitmap.asAndroidBitmap(), 0f, 0f, null)
            canvas.drawBitmap(handwritingBitmap.asAndroidBitmap(), 0f, 0f, null)
            document.finishPage(page)
            // 分享
            saveAndSharePdfFile(context, document)
        } catch (e: Exception) {
            withContext(Dispatchers.Main) {
                Logger.w(TAG, context.getString(R.string.sharing_pdf_failure) + ": ${e.stackTraceToString()}")
            }
        }
        showPopupStateFlow.value = false
    }
// endregion

    /**
     * 分享PDF文件
     */
    private fun saveAndSharePdfFile(context: Context, document: PdfDocument) {
        // 缓存生成文件
        val pdfDir = File(context.cacheDir, "shared_pdfs").apply { mkdirs() }
        val file = File(pdfDir, "Notes.pdf")
        document.writeTo(FileOutputStream(file))
        document.close()

        val contentUri = FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            file
        )

        Intent(Intent.ACTION_SEND).apply {
            type = "application/pdf"
            putExtra(Intent.EXTRA_STREAM, contentUri)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }.also { intent ->
            context.startActivity(Intent.createChooser(intent, context.getString(R.string.show_content_share)))
        }
    }

    /**
     * 启动HTML生成和分享流程
     * @param context 上下文对象
     * @param coroutineScope 协程作用域
     * @param note 笔记元数据
     * @param contentList 笔记内容列表（支持文本/待办/图片）
     */
    suspend fun shareWithHtml(
        context: Context,
        note: Note,
    ) = coroutineScope {
        try {
            var htmlTextStyle : HtmlTextStyle = HtmlTextStyle.NONE
            val htmlDocument = HtmlDocument()
            htmlDocument.setTitle(note.title)
            note.contents.forEach {
                htmlTextStyle = when {
                    it is EditorContent.TextBlock && it.paragraphStyle == ParagraphStyle.NUMBERED -> {
                        if (htmlTextStyle is HtmlTextStyle.NUMBERED) {
                            htmlTextStyle.copy(lineNum = htmlTextStyle.lineNum + 1)
                        } else {
                            HtmlTextStyle.NUMBERED(1)
                        }
                    }

                    it is EditorContent.TextBlock && it.paragraphStyle == ParagraphStyle.BULLETED -> {
                        if (htmlTextStyle is HtmlTextStyle.BULLETED) {
                            htmlTextStyle.copy(lineNum = htmlTextStyle.lineNum + 1)
                        } else {
                            HtmlTextStyle.BULLETED(1)
                        }
                    }

                    else -> HtmlTextStyle.NONE
                }
                when(it){
                    is EditorContent.TextBlock -> htmlDocument.addText(it.text.toHtmlText(), htmlTextStyle)
                    is EditorContent.TodoBlock -> htmlDocument.addTodo(it.text.toHtmlText(), it.isDone)
                    is EditorContent.ImageBlock -> {
                        val imageRequest = ImageRequest.Builder(context).data(it.uri).build()
                        val drawable = ImageLoader(context).enqueue(imageRequest).job.await().drawable
                        val bitmap = (drawable as? BitmapDrawable)?.bitmap
                        if (bitmap != null) {
                            htmlDocument.addImage(bitmap)
                        }
                    }
                    else -> {}
                }
            }
            // 分享
            saveAndShareHtmlFile(context, htmlDocument.build())
        } catch (e: Exception) {
            withContext(Dispatchers.Main) {
                Logger.w(TAG, context.getString(R.string.action_share_text_file) + ": ${e.stackTraceToString()}")
            }
        }
    }
// endregion

    /**
     * 分享Html文件
     */
    private fun saveAndShareHtmlFile(context: Context, htmlString: String) {
        // 缓存生成文件
        val htmlDir = File(context.cacheDir, "shared_html").apply { mkdirs() }
        val file = File(htmlDir, "Note.html")
        FileOutputStream(file).use {
            it.write(htmlString.toByteArray())
        }

        val contentUri = FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            file
        )

        Intent(Intent.ACTION_SEND).apply {
            type = "text/html"
            putExtra(Intent.EXTRA_STREAM, contentUri)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }.also { intent ->
            context.startActivity(Intent.createChooser(intent, context.getString(R.string.show_content_share)))
        }
    }

    /**
     * 注入到composable函数中
     * 引入note信息，利用popup构建分享内容
     */
    @OptIn(ExperimentalFoundationApi::class)
    @Composable
    fun injectRecordBitmapComposable(note: Note) {
        val showPopup by showPopupStateFlow.collectAsState()
        if (showPopup) {
            Popup(onDismissRequest = { showPopupStateFlow.value = false }) {
                Box(Modifier
                    .size(1.dp)
                    .alpha(0F)) {
                    PreviewSection(note)
                }
            }
        }
    }

    /**
     * 列表预览区内容
     */
    @OptIn(ExperimentalFoundationApi::class)
    @Composable
    private fun PreviewSection(
        note: Note,
        modifier: Modifier = Modifier,
    ) {
        var isRichTextDrawFinish by remember { mutableStateOf(false) }
        var isHandwritingFinish by remember { mutableStateOf(false) }
        var drawStrokeList by remember { mutableStateOf<List<DrawStroke>?>(null) }
        var titleHeight by remember { mutableIntStateOf(0) }

        val dimens = getGlobalDimens()

        LaunchedEffect(isHandwritingFinish, isHandwritingFinish) {
            // 重置状态，并发送绘制结束信号
            if (isHandwritingFinish && isRichTextDrawFinish) {
                popupDrawingStateFlow.value = false
                isRichTextDrawFinish = false
                isHandwritingFinish = false
            }
        }

        DisposableEffect(Unit) {
            onDispose {
                isRichTextDrawFinish = false
                isHandwritingFinish = false
                Logger.d(TAG, "PreviewSection onDispose")
            }
        }

        LaunchedEffect(note) {
            if (note.noteId > -1) {
                drawStrokeList = DrawBoardRepository.getDrawByNoteIdBlock(note.noteId)?.strokes ?: emptyList()
            }
        }

        Box(
            modifier = modifier
        ) {
            CompositionLocalProvider(
                LocalOverscrollConfiguration provides null
            ) {


                // 文本内容
                Column(
                    modifier = Modifier
                        .invisibleSemantics()
                        .verticalScroll(rememberScrollState(), false)
                        .horizontalScroll(rememberScrollState(), false)
                        .onGloballyPositioned { richTextSize = it.size }
                        .recordGraphicsLayer(richTextGraphicsController)
                ) {
                    // 标题栏
                    TitleBlock(
                        darkTheme = (isSystemInDarkTheme() && note.bgColor == Skin.defColor),
                        onTitleChange = {},
                        onFocusChanged = {},
                        title = note.title,
                        modifier = Modifier
                            .padding(
                                start = 24.dp,
                                top =dimens.titleTopMargin,
                                bottom = dimens.titleBottomMargin
                            )
                            .onSizeChanged {size -> titleHeight = size.height},
                    )
                    note.contents.forEachIndexed { index, item ->
                        when (item) {
                            is EditorContent.TextBlock -> {
                                ShareTextBlock(item, index, note.contents)
                            }

                            is EditorContent.ImageBlock -> {
                                val bitmap = LocalContext.current.contentResolver.openFileDescriptor(item.uri, "r")?.use {
                                    BitmapFactory.decodeFileDescriptor(it.fileDescriptor).asShared()
                                }
                                if (bitmap == null) return@forEachIndexed

                                val measuredWidth = bitmap.width
                                val measuredHeight = bitmap.height
                                val shortEdge = max(GlobalContext.screenWidth, GlobalContext.screenHeight)
                                val scaleModifier = when (item.scaleMode) {
                                    ImageBlockScaleMode.Large -> Modifier.fillMaxWidth()
                                    ImageBlockScaleMode.Origin -> Modifier.width(measuredWidth.px2dp.dp)
                                    ImageBlockScaleMode.Small -> {
                                        val widthPx = when {
                                            measuredWidth > shortEdge -> shortEdge * 0.4f
                                            measuredWidth > shortEdge / 2 -> measuredWidth * 0.4f
                                            else -> measuredWidth * 0.6f
                                        }
                                        Modifier.width(widthPx.toInt().px2dp.dp)
                                    }
                                }
                                Logger.d("ShareImageBlock", "ImageBlockScaleMode: ${item.scaleMode}, measuredWidth: $measuredWidth, measuredWidth: $measuredHeight")


                                Image(
                                    bitmap = bitmap.asImageBitmap(),
                                    contentDescription = null,
                                    contentScale = ContentScale.FillWidth,
                                    modifier = Modifier
                                        .then(scaleModifier)
                                        .padding(horizontal = 7.dp)
                                        .padding(start = 17.dp, end = 27.dp, bottom = 8.dp)
                                )
                            }

                            is EditorContent.TodoBlock -> {
                                ShareTodoBlock(item)
                            }

                            is EditorContent.AudioBlock -> {
                                AudioBlock(
                                    focusRequester = FocusRequester(),
                                    audioPath = item.audioPath,
                                    durationText = formatAudioTime(getAudioDuration(item.audioPath)),
                                    onStopRecordClick = {},
                                    onMoreClick = {},
                                    onAudioToTextClick = {},
                                    onDeleteClick = {},
                                    onEnterKeyPressed = {},
                                    modifier = Modifier
                                        .defaultMinSize(GlobalContext.screenWidth.px2dp.dp)
                                        .fillMaxWidth()
                                        .padding(start = 24.dp, end = 24.dp),
                                    isTabletPreviewMode = true
                                )
                            }

                            is EditorContent.RichTextV2 -> {}
                        }
                    }
                    isRichTextDrawFinish = true
                    Logger.d(TAG, "isRichTextDrawFinish")
                }
            }

            // 手绘内容
            Canvas(
                modifier = Modifier
                    .recordGraphicsLayer(handwritingGraphicsLayerController)
            ) {
                drawStrokeList?.forEach {
                    it.toDrawPathDisplay().draw(this)
                }
                if (drawStrokeList != null) {
                    isHandwritingFinish = true
                    Logger.d(TAG, "isHandwritingFinish ${drawStrokeList?.size}")
                }
            }
        }
    }

    companion object {
        private const val TAG = "ShareContentComponent"
    }
}




