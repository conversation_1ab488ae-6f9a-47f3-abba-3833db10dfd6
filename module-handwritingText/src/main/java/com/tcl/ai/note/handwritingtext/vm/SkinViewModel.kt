package com.tcl.ai.note.handwritingtext.vm

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.graphics.Color
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.handwritingtext.database.entity.BgMode

import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.track.AnalyticsHandWritingTextModel
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.utils.toArgbLong
import com.tcl.ai.note.utils.toComposeColor


/**
 * 编辑皮肤管理
 */
class SkinViewModel : ViewModel() {




    var skinModel = Skin(color = Skin.defColor, bgMode= BgMode.none)

    private val _bgModeState = mutableStateOf(BgMode.none)
    val bgModeState: State<BgMode> = _bgModeState

    private val _bgColor = mutableStateOf(Color(Skin.defColor))
    val bgColorState: State<Color> = _bgColor

    init {
        AnalyticsHandWritingTextModel.loadSkinViewModel(this)
    }

    fun updateBgMode(bgMode: BgMode){
        _bgModeState.value = bgMode
        skinModel.bgMode =bgMode

    }

    fun updateBgColor(bgColor: Color){
        _bgColor.value = bgColor
        skinModel.color =bgColor.toArgbLong()

    }



    val bgColors = arrayListOf(
        Color(Skin.defColor),
        Color(0xffF3F9FF),
        Color(0xffECF7EC),
        Color(0xffFDFBF1),
        Color(0xffFFF1F1),
        Color(0xffF7EFFF)
    )


}