package com.tcl.ai.note.handwritingtext.vm

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import com.tcl.ai.note.base.R
import javax.inject.Inject

@HiltViewModel
class TitleEditViewModel @Inject constructor() : ViewModel() {

    private val _isEditingTitle = mutableStateOf(false)
    val isEditingTitle = _isEditingTitle

    private val _noteTitle = mutableStateOf("")
    val noteTitle = _noteTitle

    private val _forceExitEditing = mutableStateOf(false)
    val forceExitEditing = _forceExitEditing

    // 跟踪用户是否真正编辑过标题（区分是否为默认hint状态）
    private val _hasUserEditedTitle = mutableStateOf(false)
    val hasUserEditedTitle = _hasUserEditedTitle

    // 默认hint文本缓存
    private var defaultHintText: String? = null

    private var onTitleChangedCallback: ((String) -> Unit)? = null

    fun setEditingState(isEditing: Boolean) {
        _isEditingTitle.value = isEditing
    }

    fun updateTitle(title: String) {
        _noteTitle.value = title
        
        // 检查是否为默认hint状态
        val isDefaultHint = defaultHintText != null && title == defaultHintText
        
        // 如果是默认hint状态且用户没有真正编辑过，则回传空字符串
        val titleToSave = if (isDefaultHint && !_hasUserEditedTitle.value) {
            ""
        } else {
            title
        }
        
        // 触发标题变化回调，通知 RichTextViewModel2 进行保存
        onTitleChangedCallback?.invoke(titleToSave)
    }

    fun forceExitEditing() {
        _forceExitEditing.value = true
    }

    fun resetForceExitFlag() {
        _forceExitEditing.value = false
    }

    // 设置标题变化回调
    fun setOnTitleChangedListener(callback: (String) -> Unit) {
        onTitleChangedCallback = callback
    }

    // 初始化标题（从 RichTextViewModel2 同步）
    fun initTitle(title: String) {
        _noteTitle.value = title
        // 如果初始标题不为空，说明用户之前已经编辑过
        _hasUserEditedTitle.value = title.isNotEmpty()
    }
    
    // 设置默认hint文本
    fun setDefaultHintText(hintText: String) {
        defaultHintText = hintText
    }
    
    // 标记用户已经开始编辑标题
    fun markUserHasEditedTitle() {
        _hasUserEditedTitle.value = true
    }
    
    // 重置用户编辑状态（用于新建笔记等场景）
    fun resetUserEditedState() {
        _hasUserEditedTitle.value = false
    }
} 