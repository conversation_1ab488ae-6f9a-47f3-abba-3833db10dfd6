package com.tcl.ai.note.handwritingtext.vm.event

import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity

/**
 * 富文本操作事件
 */
sealed class RichTextOperateEvent {
    data class LoadNote(val content:String,val richTextStyleEntity: RichTextStyleEntity, val init: Boolean = false): RichTextOperateEvent()
    data object  Undo: RichTextOperateEvent()
    data object  Redo: RichTextOperateEvent()
}