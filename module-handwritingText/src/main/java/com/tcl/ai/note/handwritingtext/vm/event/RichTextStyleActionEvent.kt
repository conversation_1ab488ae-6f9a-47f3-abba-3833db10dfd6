package com.tcl.ai.note.handwritingtext.vm.event

import android.text.Layout

/**
 * 富文本样式事件
 */
sealed class RichTextStyleActionEvent {
    data class BoldToggled(val isActive: Boolean) : RichTextStyleActionEvent()
    data class ItalicToggled(val isActive: Boolean) : RichTextStyleActionEvent()
    data class UnderlineToggled(val isActive: Boolean) : RichTextStyleActionEvent()
    data class StrikethroughToggled(val isActive: Boolean) : RichTextStyleActionEvent()
    data class AlignmentApplied(val alignment: Layout.Alignment) : RichTextStyleActionEvent()
    data object IndentLeftApplied : RichTextStyleActionEvent()
    data object IndentRightApplied : RichTextStyleActionEvent()
    data class TodoToggled(val isActive: Boolean) : RichTextStyleActionEvent()
    data class NumberedListToggled(val isActive: Boolean) : RichTextStyleActionEvent()
    data class BulletedListToggled(val isActive: Boolean) : RichTextStyleActionEvent()
    data class FontSizeApplied(val size: Int) : RichTextStyleActionEvent()
    data class FontColorApplied(val color: Int) : RichTextStyleActionEvent()
    data class BackgroundColorApplied(val color: Int) : RichTextStyleActionEvent()
}