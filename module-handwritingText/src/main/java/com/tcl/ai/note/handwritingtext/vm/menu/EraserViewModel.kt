package com.tcl.ai.note.handwritingtext.vm.menu

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.popup.EraserMode
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.dp2px
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 橡皮擦工具ViewModel
 * 管理橡皮擦的模式选择和大小设置
 * 注意这个仅仅是针对菜单栏的橡皮擦预览指示器，不是针对绘制区的
 * EraserPressIndicator 按压指示器是直接读取 SuniaDrawViewModel 的属性，路线：
 * EraserViewModel（菜单栏） -> SuniaDrawViewModel（SDK） -> EraserPressIndicator（绘制）
 */
@HiltViewModel
class EraserViewModel @Inject constructor() : ViewModel() {

    // 选中的橡皮擦模式，0=Pixel eraser（ EraserMode.POINT ）, 1=Object eraser（EraserMode.STROKE）
    var selectedMode by mutableIntStateOf(0)
        private set

    // 橡皮擦大小，范围10~100px
    var eraserSize by mutableIntStateOf(55) // 中间数是55
        private set

    // SDK更新回调，用于初始化时同步设置到SDK
    private var sdkUpdateCallback: ((Float, EraserMode) -> Unit)? = null

    init {
        // 初始化时加载保存的设置
        loadEraserSettings()
    }

    /**
     * 获取当前选择的橡皮擦模式
     */
    val currentEraserMode: EraserMode
        get() = if (selectedMode == 0) EraserMode.AREA else EraserMode.STROKE

    /**
     * 选择橡皮擦模式
     * @param mode 0=Pixel eraser, 1=Object eraser
     */
    fun selectMode(mode: Int) {
        if (mode in 0..1) {
            selectedMode = mode
            Logger.d(TAG, "selectMode: $mode")
            saveEraserMode(mode)
        }
    }

    /**
     * 更新橡皮擦大小
     * @param size 大小，范围10~100px
     */
    fun updateSize(size: Int) {
        val newSize = size.coerceIn(10, 100)
        eraserSize = newSize
        Logger.d(TAG, "updateSize: $newSize")
        saveEraserSize(newSize)
    }

    /**
     * 获取当前橡皮擦配置
     */
    fun getCurrentConfig(): EraserConfig {
        return EraserConfig(
            mode = currentEraserMode,
            size = eraserSize.toFloat()
        )
    }

    /**
     * 设置SDK更新回调
     * @param callback SDK属性更新回调函数
     */
    fun setSdkUpdateCallback(callback: (Float, EraserMode) -> Unit) {
        sdkUpdateCallback = callback
        // 设置回调后立即同步当前设置到SDK
        syncToSdk()
    }

    /**
     * 同步当前设置到SDK
     */
    private fun syncToSdk() {
        sdkUpdateCallback?.let { callback ->
            val radiusPx = getEraserRadiusForSdk()
            callback(radiusPx, currentEraserMode)
            Logger.d(TAG, "Synced to SDK: radius=${radiusPx}px, mode=$currentEraserMode")
        }
    }

    /**
     * 加载保存的橡皮擦设置
     */
    private fun loadEraserSettings() {
        viewModelScope.launch {
            try {
                selectedMode = AppDataStore.getInt(KEY_ERASER_MODE, DEFAULT_ERASER_MODE)
                eraserSize = AppDataStore.getInt(KEY_ERASER_SIZE, DEFAULT_ERASER_SIZE)
                Logger.d(TAG, "Loaded eraser settings: mode=$selectedMode, size=$eraserSize")
                // 加载设置后同步到SDK
                syncToSdk()
            } catch (e: Exception) {
                Logger.e(TAG, "Failed to load eraser settings: $e")
            }
        }
    }

    /**
     * 保存橡皮擦模式
     */
    private fun saveEraserMode(mode: Int) {
        viewModelScope.launch {
            try {
                AppDataStore.putInt(KEY_ERASER_MODE, mode)
            } catch (e: Exception) {
                Logger.e(TAG, "Failed to save eraser mode: $e")
            }
        }
    }

    /**
     * 保存橡皮擦大小
     */
    private fun saveEraserSize(size: Int) {
        viewModelScope.launch {
            try {
                AppDataStore.putInt(KEY_ERASER_SIZE, size)
            } catch (e: Exception) {
                Logger.e(TAG, "Failed to save eraser size: $e")
            }
        }
    }

    companion object {
        private const val TAG = "EraserViewModel"
        
        // DataStore keys
        private const val KEY_ERASER_MODE = "eraser_mode"
        private const val KEY_ERASER_SIZE = "eraser_size"
        
        // Default values
        private const val DEFAULT_ERASER_MODE = 0
        private const val DEFAULT_ERASER_SIZE = 55
    }
}

/**
 * 橡皮擦配置数据类
 */
data class EraserConfig(
    val mode: EraserMode,
    val size: Float
)

/**
 * 获取橡皮擦在SDK中的实际显示大小（半径）
 * 统一处理橡皮擦大小的计算逻辑
 * @return 橡皮擦在SDK中使用的半径值（px）
 */
fun EraserViewModel.getEraserRadiusForSdk(): Float {
    return when (selectedMode) {
        1 -> 24f // Stroke eraser 固定 24px 直径
        else -> eraserSize.dp2px.toFloat() / 2 // Area eraser 10-100px 直径转换为半径
    }
}

/**
 * 同步橡皮擦设置到SDK
 * 使用扩展函数统一调用逻辑
 */
fun EraserViewModel.syncToSdk(suniaDrawViewModel: SuniaDrawViewModel) {
    suniaDrawViewModel.switchEraser(currentEraserMode, getEraserRadiusForSdk())
} 