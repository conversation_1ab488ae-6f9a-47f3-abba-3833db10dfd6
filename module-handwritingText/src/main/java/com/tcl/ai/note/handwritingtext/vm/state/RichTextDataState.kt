package com.tcl.ai.note.handwritingtext.vm.state

import com.tcl.ai.note.handwritingtext.database.entity.BgMode
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity

data class RichTextDataState(
    val noteId: Long? = null,
    val title: String = "", // 标题内容
    val content: String = "", // 富文本纯文本内容
    val richTextStyleEntity: RichTextStyleEntity = RichTextStyleEntity(), // 富文本样式结构
    val images: List<EditorContent.ImageBlock> = emptyList(), // 图片列表
    val audios: List<EditorContent.AudioBlock> = emptyList(),   // 音频列表
    var bgMode: BgMode = BgMode.none,
    var bgColor: Long = Skin.defColor,
    var categoryId: Long = 1, // 未分类
    val isNewNote: Boolean = false, // 标识是否为新建笔记
    val isSaved:Boolean = false // 是否执行过保存操作
){
    val displayBgColor: Long
        get() = if (bgColor == 0xFFF5F6F7) Skin.defColor else bgColor
}