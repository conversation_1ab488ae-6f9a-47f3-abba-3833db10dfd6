package com.tcl.ai.note.handwritingtext.vo

import android.graphics.Canvas
import android.graphics.RectF
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.asAndroidPath
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.nativeCanvas
import com.tcl.ai.note.handwritingtext.bean.*
import com.tcl.ai.note.handwritingtext.utils.bounds
import com.tcl.ai.note.handwritingtext.utils.forEachBezierPathSegmentWithWidth
import com.tcl.ai.note.handwritingtext.utils.radius

data class DrawPathDisplay(
    // 和DrawStroke的id保存一致
    val id: Long = System.currentTimeMillis(),
    val path: Path = Path(),
    val points: List<DrawPoint> = emptyList(),
    val style: StrokeStyle = StrokeStyle(),
) {
    val bounds: RectF = points.bounds()
    val radius: Float = bounds.radius()

    private fun drawBallPenDoodle(canvas: Canvas) {
        // 圆珠笔，默认效果
        val paint = style.toPaint()
        paint.blendMode = android.graphics.BlendMode.SRC_OVER
        canvas.drawPath(path.asAndroidPath(), paint)
    }

    private fun drawMarkPenDoodle(canvas: Canvas) {
        // 马克笔，叠加效果
        val paint = style.toPaint()
        paint.blendMode = android.graphics.BlendMode.SRC_OVER
        canvas.drawPath(path.asAndroidPath(), paint)
    }

    private fun drawFountainPenDoodle(canvas: Canvas) {
        // 钢笔
        val paint = style.toPaint()
        points.forEachBezierPathSegmentWithWidth(
            path = path.asAndroidPath(),
            baseWidth = style.width
        ) { path, width ->
            paint.strokeWidth = width
            canvas.drawPath(path, paint)
        }
    }

    private fun drawEraser(canvas: Canvas) {
        val paint = style.toPaint()
        paint.alpha = 0
        paint.blendMode = android.graphics.BlendMode.DST_IN
        paint.xfermode = android.graphics.PorterDuffXfermode(android.graphics.PorterDuff.Mode.DST_IN)
        canvas.drawPath(path.asAndroidPath(), paint)
    }

    fun draw(canvas: Canvas) {
        when (style.drawMode) {
            DrawMode.BEAUTIFICATION, DrawMode.PEN -> {
                when (style.doodlePen) {
                    DoodlePen.FountainPen -> {
                        drawFountainPenDoodle(canvas)
                    }

                    DoodlePen.Ballpen -> {
                        // 圆珠笔，默认效果
                        drawBallPenDoodle(canvas)
                    }

                    DoodlePen.Markpen -> {
                        // 马克笔，叠加效果
                        drawMarkPenDoodle(canvas)
                    }
                }
            }

            DrawMode.ERASER -> {
                drawEraser(canvas)
            }

            DrawMode.NONE -> {

            }
        }
    }

    fun draw(drawScope: DrawScope) {
        drawScope.drawIntoCanvas {
            draw(it.nativeCanvas)
        }
    }

    fun toDrawStroke() = DrawStroke(
        id = id,
        points = points,
        style = style,
    )

    companion object {
        private const val TAG = "DrawPathDisplay"
    }
}