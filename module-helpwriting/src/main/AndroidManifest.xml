<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application>
        <activity
            android:name=".ui.HelpMeWriteActivity"
            android:excludeFromRecents="true"
            android:resizeableActivity="true"
            android:exported="false"
            android:windowSoftInputMode="adjustResize"
            android:configChanges="orientation|screenSize|keyboardHidden|uiMode"
            android:screenOrientation="portrait"
            android:theme="@style/TransparentTheme" />

    </application>

</manifest>