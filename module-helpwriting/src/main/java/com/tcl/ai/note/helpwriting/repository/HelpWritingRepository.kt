package com.tcl.ai.note.helpwriting.repository

import android.util.Log
import com.tcl.ai.note.GlobalContext.Companion.applicationScope
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.state.ChartStreamingMsgUtils
import com.tcl.ai.note.state.ChatStreamingMsg
import com.tcl.ai.note.state.ERR_TRANSLATE_SENSITIVE_WORDS
import com.tcl.ai.note.state.ERR_USAGE_LIMITED
import com.tcl.ai.note.state.Result
import com.tcl.ai.note.state.StreamingStatus
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.systemLangTag
import com.tcl.ai.sdk.assistant.AssistantServiceManager
import com.tcl.ai.sdk.assistant.ICallResult
import com.tcl.ai.sdk.assistant.callback.IChatStreamingCallback
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.UUID
import javax.inject.Inject
import kotlin.concurrent.Volatile
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException


interface HelpWritingRepository {


    fun operateUserEssayFlow(
        msg: String?,
        type: String,
        quickAction: String?,
        errorCallBack: (String) -> Unit
    ): Flow<Result<ChatStreamingMsg>?>

    suspend fun sendUserMsg(
        msg: String?,
        type: String,
        quickAction: String?,
        errorCallBack: (String) -> Unit
    )

    /**
     * 取消流式消息
     */
    fun cancelStreamingMsg(onStopSuccess: (() -> Unit)?)

    fun reset(callback: (() -> Unit)?)

    fun getTemplateFlow(): MutableStateFlow<Result<ChatStreamingMsg>?>
    fun setOfflineState()
    fun clear()

}

class HelpWritingRepositoryImpl @Inject constructor() : HelpWritingRepository {

    companion object {
        private const val TAG = "HelpWritingRepositoryImpl"
    }

    private var lastText = ""
    private var _templateTextStr = MutableStateFlow<Result<ChatStreamingMsg>?>(null)
    private var streamingJob: Job? = null
    val stopSignal = MutableStateFlow(false) // 用于控制 Flow 是否继续执行

    //会话ID
    private var conversationId: String? = null


    private var currentTaskId = -1

    @Volatile
    private var assistObj = -1
    private var accumulatedText = StringBuilder()

    init {
        reset(null)
        // 初始化时连接账户控制器
        AccountController.connect()
    }

    private suspend fun getAssistObjId(): Int {
        if (assistObj != -1) {
            return assistObj
        }
        return suspendCancellableCoroutine { c ->
            AssistantServiceManager.instance.obtainAssistant(object : ICallResult.Stub() {
                override fun onSuccess(p0: Int) {
                    assistObj = p0
                    c.resume(p0)
                }

                override fun onFailure(p0: Int) {
                    Log.e(TAG, "obtainAssistant --> failure, errorCode: $p0")
                    c.resumeWithException(Exception("obtainAssistant failure"))
                }
            })
        }
    }

    /**
     * 发送原生消息的时候 quickAction 为 null
     * 发送指令的时候 msg 为指 null
     */
    override fun operateUserEssayFlow(
        msg: String?, type: String, quickAction: String?, errorCallBack: (String) -> Unit
    ): Flow<Result<ChatStreamingMsg>?> = callbackFlow {
        stopSignal.value = false
        lastText = ""
        accumulatedText = StringBuilder()

        // 发送加载状态
        send(Result.Loading)

        Logger.d(
            TAG, "operateUserEssay " +
                    "authorization: ${AccountController.token}, " +
                    "serverUrl: ${AccountController.baseHost}, " +
                    "preset: $type, " +
                    "originText: $msg, " +
                    "langCode: $systemLangTag, " +
                    "quickAction: $quickAction, " +
                    "countryCode: ${AccountController.countryCode}"
        )
        if (conversationId == null) {
            conversationId = UUID.randomUUID().toString()
        }
        // 创建一个协程作用域，用于在回调中启动协程
        val scope = CoroutineScope(coroutineContext)
        AssistantServiceManager.instance.helpWriting(
            callId = getAssistObjId(),
            authorization = AccountController.token,
            serverUrl = AccountController.baseHost,
            preset = type,
            conversationId = conversationId ?: "",
            message = msg,
            langCode = systemLangTag,
            countryCode = AccountController.countryCode,
            quickAction = quickAction,
            callback = object : IChatStreamingCallback {
                override fun onSuccess(streamingMsg: com.tcl.ai.sdk.assistant.entity.ChatStreamingMsg) {
                    Logger.i(TAG, "IChatStreamingCallback success: $streamingMsg")

                    if (stopSignal.value) {
                        Logger.i(TAG, "IChatStreamingCallback success: operation cancelled")
                        close() // 关闭 Flow
                        return
                    }

                    val myMsg = streamingMsg.toMyChatStreamingMsg() // 使用扩展函数转换
                    // 在协程中使用 send
                    scope.launch(Dispatchers.Default) {
                        try {
                            // 只在非停止状态下发送数据
                            if (!stopSignal.value) {
                                send(Result.Success(myMsg))

                                val tmpStatus = streamingMsg.status
                                if (tmpStatus is com.tcl.ai.sdk.assistant.entity.StreamingStatus.Stopped) {
                                    /**
                                     *Stopped 异常结束需要关注 errorCode 敏感词 在 StreamingStatus.Stopped 中
                                     */
                                    val errorCode = tmpStatus.errorCode
                                    if (errorCode == ERR_USAGE_LIMITED && !stopSignal.value) {
                                        send(Result.Error(Exception("SDK error"), errorCode))
                                    } else if (errorCode == ERR_TRANSLATE_SENSITIVE_WORDS && !stopSignal.value) {
                                        send(Result.Error(Exception("SDK error"), errorCode))
                                    }
                                }

                                // 如果是最后一条消息，关闭流
                                if (myMsg.status == StreamingStatus.COMPLETED) {
                                    close()
                                }
                            }
                        } catch (e: Exception) {
                            Logger.e(TAG, "Error sending to channel: ${e.message}")
                        }
                    }
                }

                override fun onFailure(errCode: Int) {
                    Logger.w(TAG, "asr fail !!! $errCode")
                    scope.launch(Dispatchers.Default) {
                        try {
                            if (!stopSignal.value) {
                                send(Result.Error(Exception("SDK error"), errCode))
                                close() // 关闭流
                            }
                        } catch (e: Exception) {
                            Logger.e(TAG, "Error sending error to channel: ${e.message}")
                        }
                    }
                }
            },
            object : ICallResult.Stub() {
                override fun onSuccess(jobId: Int) {
                    currentTaskId = jobId
                    Logger.i(TAG, "operateUserEssay --> success, jobId: $jobId")
                }

                override fun onFailure(errorCode: Int) {
                    Logger.i(TAG, "operateUserEssay --> failure, errorCode: $errorCode")
                }
            }
        )

        // 当Flow被取消时执行的代码
        awaitClose {
            Logger.i(TAG, "operateUserEssay flow closed")
        }
    }.flowOn(Dispatchers.IO)

    override fun cancelStreamingMsg(onStopSuccess: (() -> Unit)?) {
        stopSignal.value = true
        streamingJob?.cancel()
        onStopSuccess?.invoke()
        applicationScope.launch {
            val tmpTextStr = _templateTextStr.value
            if (tmpTextStr is Result.Success) {
                _templateTextStr.emit(
                    Result.Success(
                        ChatStreamingMsg(
                            status = StreamingStatus.STOPPED,
                            text = tmpTextStr.data.text,
                            threadId = tmpTextStr.data.threadId
                        )
                    )
                )
            } else if (tmpTextStr is Result.Loading) {
                _templateTextStr.emit(null)
            }
            launch {
                AssistantServiceManager.instance.cancelPresetCompletionsStreamingTask(
                    callId = getAssistObjId(),
                    jobId = currentTaskId,
                    callResult = object : ICallResult.Stub() {
                        override fun onSuccess(p0: Int) {
                            Logger.i(TAG, "cancelStreamingMsg --> success")
                        }

                        override fun onFailure(errCode: Int) {
                            Logger.w(TAG, "cancelStreamingMsg --> onFailure: $errCode")
                        }
                    }
                )
            }
        }
    }

    override fun reset(callback: (() -> Unit)?) {
        streamingJob?.cancel()
        applicationScope.launch {
            _templateTextStr.emit(null)
        }
    }

    override fun getTemplateFlow(): MutableStateFlow<Result<ChatStreamingMsg>?> {
        return _templateTextStr
    }

    override fun setOfflineState() {
        _templateTextStr.value = Result.Error(Exception("network error"))
    }


    override suspend fun sendUserMsg(
        msg: String?, type: String,
        quickAction: String?, errorCallBack: (String) -> Unit
    ) {
        _templateTextStr.value = Result.Loading
        operateUserEssayFlow(msg, type, quickAction, errorCallBack).onEach { delay(50) }
            .onCompletion { cause ->
                Logger.d(TAG, "sendMsgWithSate:OnCompletion: $cause")
            }.collect { result ->
            Logger.d(TAG, "sendMsgWithSate:collect: $result")
            if (result is Result.Success && !stopSignal.value) {
                accumulatedText.append(result.data.text)
                _templateTextStr.value =
                    result.copy(data = result.data.copy(text = accumulatedText.toString()))
            } else {
                if (result is Result.Error) {
                    val code = result.code
                    ChartStreamingMsgUtils.setErrorResultMsgState(_templateTextStr)
                    errorCallBack(code.toString())
                }
            }
        }
    }

    // 为 SDK 的类添加扩展函数
    fun com.tcl.ai.sdk.assistant.entity.ChatStreamingMsg.toMyChatStreamingMsg(): ChatStreamingMsg {
        return ChatStreamingMsg(
            status = when (status) { // 确保状态枚举值匹配
                is com.tcl.ai.sdk.assistant.entity.StreamingStatus.InProgress -> StreamingStatus.IN_PROGRESS
                is com.tcl.ai.sdk.assistant.entity.StreamingStatus.Completed -> StreamingStatus.COMPLETED
                is com.tcl.ai.sdk.assistant.entity.StreamingStatus.Stopped -> StreamingStatus.STOPPED
                else -> {
                    Logger.i(TAG, "toMyChatStreamingMsg status $status ")
                    throw IllegalArgumentException("Unknown status")
                }
            },
            //新的帮写 完成时会 带上全部的文本 会造成重复
            text = if (status is com.tcl.ai.sdk.assistant.entity.StreamingStatus.Completed) "" else this.text,
            threadId = this.threadId,
            userMessageId = this.userMessageId,
            errorCode =
            (status as? com.tcl.ai.sdk.assistant.entity.StreamingStatus.Stopped)?.errorCode ?: 0
        )
    }

    override fun clear() {
        accumulatedText.clear()
        stopSignal.value = true
        _templateTextStr.value = null
        currentTaskId = -1
        conversationId = null
    }
}

