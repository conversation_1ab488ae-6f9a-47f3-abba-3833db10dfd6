package com.tcl.ai.note.helpwriting.track

import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.helpwriting.viewmodel.HelpWritingViewModel
import com.tcl.ai.note.state.Result
import com.tcl.ai.note.state.StreamingStatus
import com.tcl.ai.note.track.AbsAnalyticsSubModel
import com.tcl.ai.note.track.TclAnalytics


/**
 * 分析AI帮写使用情况
 *
 * 用于上报埋点
 */
object AnalyticsHelpWritingModel : AbsAnalyticsSubModel() {
    // 记录发送命令的时间
    var startHelpWritingCmdMillis = 0L

    internal fun loadHelpWritingViewModel(helpWritingViewModel: HelpWritingViewModel) {
        reportAiWriteCompletionState(helpWritingViewModel)
    }

    /**
     * 上报帮写获取数据的状态
     */
    private fun reportAiWriteCompletionState(helpWritingViewModel: HelpWritingViewModel) {
        helpWritingViewModel.chatLoadingStatus.collectWithScope(helpWritingViewModel.viewModelScope) { result ->
            when {
                result is Result.Success && result.data.status == StreamingStatus.COMPLETED -> {
                    TclAnalytics.reportAiWriteCompletionState("0")
                }

                result is Result.Error -> {
                    TclAnalytics.reportAiWriteCompletionState("1")
                }

                else -> {}
            }
        }
    }
}