package com.tcl.ai.note.helpwriting.ui.compose

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tcl.ai.note.helpwriting.viewmodel.HelpWritingViewModel
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.widget.BottomSheetDialog
import com.tcl.ai.note.widget.components.ShowToastAndDialog
import kotlinx.coroutines.launch


@Composable
fun HelpMeWriteScreen(
    onFinish: () -> Unit,
    viewModel: HelpWritingViewModel = hiltViewModel()
) {
    val coroutineScope = rememberCoroutineScope()
    var dismissDialogCallback: (suspend () -> Unit)? = null
    val selectedTemplateId by viewModel.selectedTemplateId
    val effectState by viewModel.effect.collectAsStateWithLifecycle(null)
    val generateState by viewModel.generateState
    ShowToastAndDialog(effectState) {
        onFinish()
    }
    //这种关闭对话框的方式 才不会造成阴影背景往下移动的情况
    fun closeDialog() {
        coroutineScope.launch {
            dismissDialogCallback?.invoke()
        }
    }
    var expandDialogToFullScreen: (() -> Unit)? = null
    BottomSheetDialog(
        modifier = Modifier.fillMaxHeight().navigationBarsPadding(),
        canceledOnTouchOutside = false,
        visible = true,
        onDismissRequest = {
            onFinish()
        },
        isNeedAddNavigationBarsPadding = false,
        showIndicatorWhenFullScreen = true,
        showFullScreenCallBack = { isFullScreen ->
            if (isFullScreen) {
                TclAnalytics.reportAiWriteMax("0")
            }
        },
        backHandler = {},
        onDismissCallback = { callback ->
            dismissDialogCallback = callback
        },
        onHeightControllerReady = { expandToFullScreen ->
            // 自动展开到全屏
            expandDialogToFullScreen = expandToFullScreen
        }) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxSize()
        ) {


            if (!generateState) {
                HelpWriteCard(
                    selectedTemplateId = selectedTemplateId,
                    onFinish = {
                        closeDialog()
                    },
                    onTemplateClick = viewModel::setTemplatedId,
                    onSendClick =  {
                        viewModel.onSendClick(it,true)
                    },
                    gotoResultPage = viewModel::gotoResultPage,
                    clearTemplateSelected = {
                        viewModel.setTemplatedId(-1)
                    }
                )
            } else {
                // 点击发送按钮时，调用展开到全屏的函数
                expandDialogToFullScreen?.invoke()
                HelpWriteGenerateContentCard(
                    viewModel = viewModel,
                    selectTemplateId = selectedTemplateId,
                    onCloseClick = {
                        onFinish()
                        closeDialog()
                    },
                    onFinish = {
                        closeDialog()
                    }
                )
            }
        }
    }
}


