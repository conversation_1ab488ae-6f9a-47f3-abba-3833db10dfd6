package com.tcl.ai.note.helpwriting.ui.compose

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.helpwriting.utils.templateList
import com.tcl.ai.note.theme.AI_TITLE_BOTTOM_PADDING
import com.tcl.ai.note.theme.MENU_VERTICAL_PADDING
import com.tcl.ai.note.widget.MarqueeText
import com.tcl.ai.note.widget.components.AIHeaderCloseImage
import com.tcl.ai.note.widget.testUIBorder

@Composable
fun HelpMeWriteTitle(
    selectedTemplateId: Int,
    onClose: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .padding(bottom = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Row(
            modifier = Modifier
                .weight(1f),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Box(
                modifier = Modifier
                    .weight(1f, fill = false)
            ) {
                MarqueeText(
                    text = stringResource(id = R.string.ai_writing_assistant),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier,
                    wrapContentWidth = true
                )
            }
            if (selectedTemplateId != -1) {
                Box(
                    modifier = Modifier
                        .padding(horizontal = 8.dp)
                        .clip(RoundedCornerShape(20.dp))
                        .background(
                            brush = Brush.linearGradient(
                                colors = listOf(
                                    colorResource(id = R.color.help_writing_template_bg_gradient_left_color),
                                    colorResource(id = R.color.help_writing_template_bg_gradient_right_color)
                                )
                            )
                        )
                        .widthIn(max = 100.dp)
                        .padding(horizontal = 12.dp, vertical = MENU_VERTICAL_PADDING)

                ) {
                    MarqueeText(
                        text = stringResource(id = templateList[selectedTemplateId]),
                        modifier = Modifier.align(Alignment.Center),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        wrapContentWidth = true,
                        style = TextStyle(
                            brush = Brush.linearGradient(
                                colors = listOf(
                                    colorResource(id = R.color.help_writing_template_gradient_left_color),
                                    colorResource(id = R.color.help_writing_template_gradient_right_color)
                                ),
                            ),
                            textAlign = TextAlign.Center
                        )
                    )
                }
            }
        }
        AIHeaderCloseImage(onClose)
    }
}
@Composable
@Preview(showBackground = true, locale = "mk", fontScale = 1.3f,
    device = "spec:width=392.7dp,height=850.9dp,dpi=440"
)
fun HelpMeWriteTitlePreview() {
    HelpMeWriteTitle(
        selectedTemplateId = 0,
        onClose = {}
    )
}