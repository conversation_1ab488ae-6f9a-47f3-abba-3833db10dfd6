package com.tcl.ai.note.helpwriting.ui.compose

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.helpwriting.ui.compose.widget.HelpWritingBottomBar
import com.tcl.ai.note.helpwriting.utils.templateIconList
import com.tcl.ai.note.helpwriting.utils.templateList
import com.tcl.ai.note.theme.AI_CONTENT_PADDING_HORIZONTAL
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.widget.MarqueeText

@Composable
fun HelpWriteCard(
    selectedTemplateId: Int,
    onFinish: () -> Unit,
    onTemplateClick: (Int) -> Unit,
    onSendClick: (String) -> Unit,
    gotoResultPage: (String) -> Unit,
    clearTemplateSelected: () -> Unit
) {


    Column(
        modifier = Modifier
            .fillMaxSize()
            .navigationBarsPadding(),
    ) {
        HelpMeWriteTitle(selectedTemplateId = -1, onClose = onFinish)

        Box(
            modifier = Modifier
                .padding(top = 10.dp)
                .weight(1f) // 改为true，使其填充所有可用空间
                .fillMaxWidth()
        ) {
            HelpWritingContentScreen(
                onTemplateClick = onTemplateClick,
                selectedTemplateId = selectedTemplateId
            )
        }
        HelpWritingBottomBar(
            gotoResultPage = { index, message ->
                gotoResultPage.invoke(message)
            },
            onSendClicked = onSendClick,
            isTemplateClicked = selectedTemplateId != -1,
            onStopClick = { },
            templateId = selectedTemplateId,
            clearTemplateSelected = clearTemplateSelected
        )
    }
}


@Composable
private fun HelpWritingContentScreen(
    onTemplateClick: (templateId: Int) -> Unit,
    selectedTemplateId: Int
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .clip(
                RoundedCornerShape(20.dp)
            )
            .background(
                color = colorResource(id = R.color.tct_result_bg_color),
            )
            .padding(horizontal = 16.dp, vertical = 16.dp)

    ) {
        items(templateList.size) { index ->
            Row(
                modifier = Modifier
                    .padding(horizontal = 4.dp, vertical = 4.dp)
                    .clip(
                        RoundedCornerShape(8.dp)
                    )
                    .clickable {
                        onTemplateClick.invoke(index)
                    }
                    .background(
                        brush = Brush.Companion.linearGradient(
                            colors = if (index != selectedTemplateId) listOf(
                                colorResource(id = R.color.writing_gradient_left_color),
                                colorResource(id = R.color.writing_gradient_right_color)
                            ) else listOf(
                                colorResource(id = R.color.help_writing_template_selected_gradient_left),
                                colorResource(id = R.color.help_writing_template_selected_gradient_right)
                            ),
                        )
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (index == selectedTemplateId) {
                    Icon(
                        painter = painterResource(id = templateIconList[index]),
                        contentDescription = stringResource(id = templateList[index]),
                        Modifier.padding(horizontal = 8.dp, vertical = 6.dp),
                        tint = TclTheme.colorScheme.cardSelectHint
                    )
                } else {
                    Image(
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 6.dp),
                        painter = painterResource(id = templateIconList[index]),
                        contentDescription = stringResource(id = templateList[index])
                    )
                }
                MarqueeText(
                    modifier = Modifier.padding(vertical = 10.dp),
                    text = stringResource(id = templateList[index]),
                    style = TextStyle(
                        color = if (index == selectedTemplateId) colorResource(id = R.color.tct_alert_dialog_bg_color) else colorResource(
                            id = R.color.tct_stander_text_primary
                        ),
                        fontSize = 14.sp
                    )
                )
            }
        }
    }
}

@Preview
@Composable
private fun HelpWritingContentScreenPreview() {
    HelpWriteCard(
        selectedTemplateId = 1,
        onFinish = {},
        onTemplateClick = {},
        onSendClick = {},
        gotoResultPage = {},
        clearTemplateSelected = {})
}