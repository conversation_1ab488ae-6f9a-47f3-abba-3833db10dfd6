package com.tcl.ai.note.helpwriting.ui.compose

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.input.TextFieldState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInParent
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tcl.ai.note.base.R
import com.tcl.ai.note.helpwriting.ui.compose.widget.HelpWritingBottomBar
import com.tcl.ai.note.helpwriting.ui.compose.widget.markdown.MarkdownText
import com.tcl.ai.note.helpwriting.utils.operationFunctionIconList
import com.tcl.ai.note.helpwriting.utils.operationFunctionList
import com.tcl.ai.note.helpwriting.viewmodel.HelpWritingViewModel
import com.tcl.ai.note.state.ChatStreamingMsg
import com.tcl.ai.note.state.Result
import com.tcl.ai.note.state.StreamingStatus
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.widget.components.AIServiceStateHandler
import com.tcl.ai.note.widget.components.checkLoginAndExecute
import com.tcl.ai.note.widget.components.AIBottomOperateComponent
import com.tcl.ai.note.widget.components.AIBottomPrompt
import com.tcl.ai.note.widget.components.AICommonResultPanelColumn
import com.tcl.ai.note.widget.components.AIStateLoading
import com.tcl.ai.note.widget.components.BottomFadeBox
import com.tcl.ai.note.widget.components.ExpandedTextContent
import com.tcl.ai.note.widget.drawFadeOutRegionHorizontally
import com.tcl.ai.note.widget.verticalScrollbar
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 生成内容的卡片
 */
@Composable
fun HelpWriteGenerateContentCard(
    viewModel: HelpWritingViewModel,
    selectTemplateId: Int,
    onCloseClick: () -> Unit,
    onFinish: () -> Unit
) {
    val chatLoadingState by viewModel.chatLoadingStatus.collectAsStateWithLifecycle()
    val originalText by viewModel.originalTextState.collectAsStateWithLifecycle()
    var paddingTop by rememberSaveable {
        mutableFloatStateOf(0f)
    }
    var textExpanded by rememberSaveable {
        mutableStateOf(false)
    }
    var showExpandIcon by rememberSaveable {
        mutableStateOf(false)
    }
    val coroutineScope = rememberCoroutineScope()
    AIServiceStateHandler(stringResource(id = R.string.ai_writing_assistant)) { loginHandler ->
        Box(modifier = Modifier.fillMaxSize()) {
            Column(modifier = Modifier.fillMaxSize()) {
                HelpMeWriteTitle(
                    selectedTemplateId = selectTemplateId,
                    onClose = {
                        viewModel.stopAIAssistant()
                        onFinish.invoke()
                    }
                )
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .onGloballyPositioned {
                            paddingTop = if (isTablet) {
                                (it.positionInParent().y + it.size.height) / 2.2f
                            } else {
                                (it.positionInParent().y + it.size.height) / 3.3f
                            }
                        }
                        .padding(start = 20.dp, end = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {

                    Text(
                        text = originalText,
                        overflow = TextOverflow.Ellipsis,
                        fontSize = 14.sp,
                        maxLines = 1,
                        color = TclTheme.tclColorScheme.tctStanderTextSecondary,
                        modifier = Modifier
                            .weight(1f),
                        onTextLayout = {
                            showExpandIcon = it.hasVisualOverflow
                        }
                    )

                    val focusManager = LocalFocusManager.current
                    if (showExpandIcon) {
                        Image(
                            painter = painterResource(id = if (textExpanded) R.drawable.fold else R.drawable.unfold),
                            contentDescription = "",
                            modifier = Modifier.clickable(
                                interactionSource = null,
                                indication = ripple(bounded = false)
                            ) {
                                focusManager.clearFocus()
                                textExpanded = !textExpanded
                                Logger.d(
                                    tag = "ReWriteContent",
                                    message = "textExpanded:$textExpanded"
                                )
                            })
                    }
                }
                Spacer(modifier = Modifier.height(8.dp))
                AiResultArea(
                    chatLoadingState = chatLoadingState,
                    onStopClick = viewModel::stopAIAssistant,
                    onRetryClick = {
                        coroutineScope.launch {
                            checkLoginAndExecute(loginHandler, viewModel::retry)
                        }
                    },
                    onInsertClick = {
                        coroutineScope.launch {
                            viewModel.insert()
                            onCloseClick()
                        }
                    },
                    onCopyClick = viewModel::copy,
                    onOutput = viewModel::output,
                    modifier = Modifier
                        .weight(1f, fill = false)
                        .fillMaxWidth()
                )
                if (!(chatLoadingState is Result.Loading
                            || ((chatLoadingState is Result.Success)
                            && (chatLoadingState as? Result.Success<ChatStreamingMsg>)?.data?.status == StreamingStatus.IN_PROGRESS))
                ) {
                    BottomMenuListAndInputArea(
                        onSendClick = {
                            coroutineScope.launch {
                                checkLoginAndExecute(loginHandler) {
                                    viewModel.onSendClick(it, false)
                                }
                            }
                        },
                        onAiOperationClick = { type, callback ->
                            coroutineScope.launch {
                                checkLoginAndExecute(loginHandler) {
                                    viewModel.onBottomMenuItemClick(type, callback)
                                }
                            }
                        },
                        onDismissRequest=onCloseClick,
                        isResultPage = true,
                        chatLoadingState
                    )
                }
            }


//        if (isShowOriginalText) {
            ExpandedTextContent(textExpanded, changeTextExpanded = {
                textExpanded = !textExpanded
            }, paddingTop, originalText)
//        }
        }
    }
}

@Composable
private fun AiResultArea(
    chatLoadingState: Result<ChatStreamingMsg>?,
    onStopClick: (() -> Unit) -> Unit,
    onRetryClick: () -> Unit,
    onInsertClick: () -> Unit,
    onCopyClick: () -> Unit,
    onOutput: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    fun topAIAndToast() {
        onStopClick {}
        ToastUtils.makeWithCancel(
            R.string.stopped_answer,
        )
    }
    LaunchedEffect(key1 = chatLoadingState) {
        if (chatLoadingState is Result.Success && chatLoadingState.data.status != StreamingStatus.IN_PROGRESS) {
            onOutput(chatLoadingState.data.text)
        }
    }

    val density = LocalDensity.current
    val content = remember(chatLoadingState) {
        when (chatLoadingState) {
            is Result.Success -> chatLoadingState.data.text
            else -> ""
        }
    }
    Logger.d("GenerateContentCard", "chatLoadingState: $chatLoadingState")
    AICommonResultPanelColumn(
        modifier = modifier,
        bottom = 16.dp
    ) {
        when (chatLoadingState) {
            is Result.Loading -> {
                AIStateLoading(isOffline = false, onStopClick = {
                    topAIAndToast()
                }
                )
            }

            is Result.Success -> {
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxWidth()
                    ) {

                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxWidth()
                        ) {
                            AIShowContentText(chatLoadingState, density)
                            if (chatLoadingState.data.status == StreamingStatus.IN_PROGRESS) {
                                AIStateLoading(
                                    isOffline = false,
                                    false,
                                    onStopClick = { topAIAndToast() })
                            }
                        }
                        if (chatLoadingState.data.status != StreamingStatus.IN_PROGRESS) {

                            AIBottomOperateComponent(
                                content = content,
                                modifier = Modifier.fillMaxWidth(),
                                onRetryClick = onRetryClick,
                                onCopyClick = onCopyClick,
                                onInsertClick = {
                                    onInsertClick()
                                }
                            )
                        }
                    }
                }
            }

            else -> {
                AIBottomOperateComponent(
                    modifier = Modifier.weight(1f),
                    content = content,
                    onRetryClick = onRetryClick,
                    onInsertClick = {
                        onInsertClick()
                    },
                    onCopyClick = onCopyClick,
                )
            }
        }
        if (!(chatLoadingState is Result.Loading ||
                    (chatLoadingState is Result.Success
                            && chatLoadingState.data.status == StreamingStatus.IN_PROGRESS))
        ) {
            AIBottomPrompt()
        }
    }
}


@Composable
private fun AIShowContentText(
    chatLoadingState: Result.Success<ChatStreamingMsg>,
    density: Density
) {
    val scrollState = rememberScrollState()
    LaunchedEffect(key1 = chatLoadingState) {
        scrollState.animateScrollTo(scrollState.maxValue)
    }
    val isScrollEnd = remember {
        derivedStateOf {
            scrollState.value == scrollState.maxValue
        }
    }
    BottomFadeBox(
        modifier = Modifier
            .fillMaxWidth()
            .verticalScrollbar(
                state = scrollState,
                offsetX = -with(density) { (5.dp).toPx() })
            .verticalScroll(scrollState), isDrawGradient = !isScrollEnd.value
    ) {
        MarkdownText(
            markdown = chatLoadingState.data.text,
            style = TextStyle(
                color = colorResource(id = R.color.tct_stander_text_primary),
                fontSize = 15.sp
            ),
            modifier = Modifier
                .fillMaxWidth(),
            useTablePlugin = chatLoadingState.data.status != StreamingStatus.IN_PROGRESS,
            isTextSelectable = true
        )
    }
}

/**
 * 底部横向菜单栏和输入框
 */
@Composable
fun BottomMenuListAndInputArea(
    onSendClick: (name: String) -> Unit,
    onAiOperationClick: (Int, () -> Unit) -> Unit,
    onDismissRequest: () -> Unit,
    isResultPage: Boolean = false,
    chatLoadingState: Result<ChatStreamingMsg>? = null
) {
    val isNormalSate = chatLoadingState != null
    //如果是敏感词停止状态，也不能点击
    val isMenuListCanClickable =
        chatLoadingState != null && (chatLoadingState is Result.Success && chatLoadingState.data.errorCode == 0)
    Column(
        modifier = Modifier
            .shadow(
                elevation = 10.dp,
                shape = RoundedCornerShape(20.dp, 20.dp, 0.dp, 0.dp),
                clip = false
            )
            .clip(RoundedCornerShape(20.dp, 20.dp, 0.dp, 0.dp))
    ) {
        LazyRow(
            modifier = Modifier
                .background(color = colorResource(id = R.color.chat_bottom_background))
                .padding(top = 16.dp, start = 8.dp, end = 8.dp)
                .fillMaxWidth()
                .clip(RoundedCornerShape(8.dp))
                .drawFadeOutRegionHorizontally(
                    containColor = colorResource(id = R.color.chat_bottom_background).copy(alpha = 0f),
                    regionColor = colorResource(id = R.color.chat_bottom_background).copy(alpha = 1f),
                    fadeSize = 35.dp
                )
        ) {
            item {
                Spacer(modifier = Modifier.width(8.dp))
            }
            items(operationFunctionList.size) {
                Row(
                    modifier = Modifier
                        .alpha(
                            if (isMenuListCanClickable) 1f else 0.5f
                        )
                        .padding(horizontal = 6.dp)
                        .heightIn(min = 32.dp)
                        .clip(RoundedCornerShape(25.dp))
                        .background(
                            brush = Brush.linearGradient(
                                colors = listOf(
                                    colorResource(id = R.color.writing_gradient_left_color),
                                    colorResource(id = R.color.writing_gradient_right_color)
                                ),
                            )
                        )
                        .clickable(enabled = isMenuListCanClickable) {
                            onAiOperationClick(it + 6) { }
                        }
                        .padding(horizontal = 12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.CenterVertically),
                        painter = painterResource(id = operationFunctionIconList[it]),
                        contentDescription = stringResource(id = operationFunctionList[it])
                    )
                    Text(
                        modifier = Modifier.padding(start = 5.dp),
                        text = stringResource(id = operationFunctionList[it]),
                        fontSize = 14.sp
                    )
                }
            }
            item {
                Spacer(modifier = Modifier.width(8.dp))
            }
        }
        HelpWritingBottomBar(
            onSendClicked = onSendClick,
            onStopClick = {},
            isResultPage = isResultPage,
            sendEnable = isNormalSate
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun CommandAreaPre() {
    BottomMenuListAndInputArea(
        onSendClick = {},
        onAiOperationClick = { _, _ -> /*TODO*/ },
        onDismissRequest = {},
        isResultPage = true,
        chatLoadingState = Result.Success(
            ChatStreamingMsg(
                status = StreamingStatus.IN_PROGRESS,
                text = "Hello, I am a chatbot. I can help you with anything.",
                threadId = "",
                userMessageId = ""
            )
        )
    )
}

@Preview
@Composable
private fun AiResultAreaPreview() {

    AiResultArea(
        chatLoadingState = Result.Success(
            ChatStreamingMsg(
                status = StreamingStatus.IN_PROGRESS,
                text = "Hello, I am a chatbot. I can help you with anything.",
                threadId = "",
                userMessageId = ""
            )
        ),
        onStopClick = {},
        onRetryClick = {},
        onInsertClick = {},
        onCopyClick = {},
        onOutput = {}
    )
}
