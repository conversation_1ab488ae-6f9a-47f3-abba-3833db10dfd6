package com.tcl.ai.note.helpwriting.ui.compose.widget

import android.widget.Toast
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.TweenSpec
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.input.key.onKeyEvent
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.CursorColor
import com.tcl.ai.note.theme.textFieldBordColor
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.widget.MarqueeText
import kotlin.text.isNotBlank
import kotlin.text.isBlank
import kotlin.text.isEmpty

// 输入框最大长度
const val InputMaxLength = 10000

@Composable
internal fun HelpWritingBottomBar(
    modifier: Modifier = Modifier,
    gotoResultPage: (template: Int, command: String) -> Unit = { _, _ -> },
    onSendClicked: (msg: String) -> Unit,
    isTemplateClicked: Boolean = false,
    showStopIcon: Boolean = false,
    onStopClick: () -> Unit,
    templateId: Int = -1,
    isResultPage: Boolean = false,
    sendEnable: Boolean = true,
    clearTemplateSelected: () -> Unit = {}
) {
    // 在组件内部管理文本状态
    var text by rememberSaveable { mutableStateOf("") }
    var sendState by rememberSaveable { mutableStateOf(text.isNotBlank() && sendEnable) }
    val rightIcon = if (showStopIcon) {
        painterResource(id = R.drawable.icon_stop_generate)
    } else {
        Logger.d("HelpWritingBottomBar", "sendState: $sendState")
        if (sendState) {
            painterResource(id = R.drawable.icon_ai_send_enable)
        } else {
            painterResource(id = R.drawable.icon_ai_send_disabled)
        }
    }
    var isFocus by rememberSaveable { mutableStateOf(false) }
    val borderColor = animateColorAsState(
        if (!isFocus) textFieldBordColor else textFieldBordColor,
        TweenSpec(300),
        label = ""
    )

    val promptResId = if (isTemplateClicked)
        R.string.help_writing_tag_prompt
    else if (isResultPage) {
        R.string.help_writing_continue_ask
    } else {
        R.string.help_writing_default_prompt
    }
    val focusManager = LocalFocusManager.current

    // 计算当前输入的字符数
    val currentLength = text.length
    val pleaseInput = stringResource(id = R.string.enter)
    Row(
        modifier = Modifier.bottomInputModifier(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        BasicTextField(
            value = text,
            onValueChange = { newText ->
                // 限制输入长度
                if (newText.length <= InputMaxLength) {
                    text = newText
                } else {
                    ToastUtils.makeWithCancel(
                        R.string.exceed_limit,
                        length = Toast.LENGTH_SHORT
                    )
                }
            },
            enabled = sendEnable,
            maxLines = 3,
            textStyle = TextStyle(
                textAlign = TextAlign.Start,
                fontSize = 14.sp,
                color = colorResource(id = R.color.tct_stander_text_primary),
                lineHeight = 20.sp,
                platformStyle = PlatformTextStyle(
                    includeFontPadding = false
                ),
                lineHeightStyle = LineHeightStyle(
                    alignment = LineHeightStyle.Alignment.Center,
                    trim = LineHeightStyle.Trim.None
                )
            ),
            cursorBrush = SolidColor(CursorColor),
            modifier = Modifier
                .weight(1f, false)
                .onFocusChanged { isFocus = it.isFocused }
                .semantics {
                    contentDescription = pleaseInput
                }
                .onKeyEvent {
                    false
                },
            decorationBox = { innerTextField ->
                Box(
                    modifier = Modifier
                        .border(
                            1.dp,
                            borderColor.value,
                            RoundedCornerShape(12.dp)
                        )
                        .clip(RoundedCornerShape(12.dp))
                        .background(color = colorResource(id = R.color.tct_stander_bg_basic2))
                        .padding(horizontal = 8.dp)
                        .heightIn(min = 40.dp)
                        .fillMaxWidth(),
                    contentAlignment = Alignment.Center,
                ) {
                    sendState = if (text.isBlank()) {
                        false
                    } else {
                        sendEnable
                    }
                    InputDecorator(innerTextField, text, promptResId, currentLength)
                }
            }
        )

        Column(
            modifier = Modifier.padding(start = 4.dp),
            verticalArrangement = Arrangement.Center
        ) {
            IconButton(
                modifier = Modifier.size(40.dp),
                enabled = sendState || showStopIcon,
                onClick = {
                    focusManager.clearFocus()
                    if (showStopIcon) {
                        onStopClick.invoke()
                        return@IconButton
                    }
                    if (sendState) {
                        if (isResultPage) {
                            onSendClicked.invoke(text)
                        } else {
                            onSendClicked.invoke(text)
                            gotoResultPage.invoke(templateId, text)
                        }
                        text = "" // 发送后清空输入框
                        sendState = false
                    }
                }
            ) {
                Image(
                    painter = rightIcon,
                    contentDescription = if (showStopIcon) stringResource(id = R.string.ai_stop) else stringResource(
                        id = R.string.ai_send
                    ),
                )
            }
        }
    }
}

@Composable
private fun InputDecorator(
    innerTextField: @Composable () -> Unit,
    text: String,
    promptResId: Int,
    currentLength: Int,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 5.dp, bottom = 5.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 输入框区域，占据剩余空间
        Box(
            modifier = Modifier
                .weight(1f),
            contentAlignment = Alignment.CenterStart
        ) {
            innerTextField()
            // 占位符文本
            if (text.isEmpty()) {
                MarqueeText(
                    style = TextStyle(
                        color = colorResource(id = R.color.chat_prompt_text_color),
                        fontSize = 14.sp,
                        lineHeight = 20.sp,
                        platformStyle = PlatformTextStyle(
                            includeFontPadding = false
                        )
                    ),
                    text = stringResource(id = promptResId)
                )
            }
        }

        // 字数统计，固定在右侧
        Text(
            text = "$currentLength/$InputMaxLength",
            style = TextStyle(
                color = colorResource(id = R.color.chat_prompt_text_color),
                fontSize = 10.sp
            ),
            modifier = Modifier.padding()
        )
    }
}


@Composable
fun Modifier.bottomInputModifier() = this
    .background(color = colorResource(id = R.color.chat_bottom_background))
    .padding(bottom = 10.dp, top = 12.dp, start = 20.dp, end = 12.dp)
@Preview
@Composable
private fun HelpWritingBottomBarTagPreview() {
    Surface {
        HelpWritingBottomBar(
            onSendClicked = {},
            isTemplateClicked = true,
            onStopClick = { /*TODO*/ },
            templateId = -1,
            isResultPage = false,
        )
    }
}
@Preview
@Composable
private fun HelpWritingBottomBarPreview() {
    Surface {
        HelpWritingBottomBar(
            onSendClicked = {},
            onStopClick = { /*TODO*/ })
    }
}