package com.tcl.ai.note.helpwriting.ui.compose.widget.floating;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Resources;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;

import com.tcl.ai.note.helpwriting.R;

import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Operate windows : copy, select all
 */
public class OperateWindow {

    private PopupWindow mWindow;
//        private int[] mTempCoors = new int[2];

    private int mWidth;
    private int mHeight;

    private Context mContext;
    private View mView;

    private int screenWidth = Resources.getSystem().getDisplayMetrics().widthPixels;
    private int screenHeight = Resources.getSystem().getDisplayMetrics().heightPixels;

    @SuppressLint("MissingInflatedId")
    public OperateWindow(View view) {
        this.mView = view;
        this.mContext = view.getContext();
        View contentView = LayoutInflater.from(view.getContext()).inflate(com.tcl.ai.note.helpwriting.R.layout.layout_operate_windows, null);
        contentView.measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
        mWidth = contentView.getMeasuredWidth();
        mHeight = contentView.getMeasuredHeight();
        mWindow =
                new PopupWindow(contentView, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, false);
            mWindow.setWindowLayoutType(2045);
//        mWindow.setWindowLayoutType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
        mWindow.setClippingEnabled(false);

        contentView.findViewById(com.tcl.ai.note.helpwriting.R.id.tv_copy).setVisibility(View.GONE);
        contentView.findViewById(com.tcl.ai.note.helpwriting.R.id.tv_paste).setVisibility(View.GONE);
        contentView.findViewById(com.tcl.ai.note.helpwriting.R.id.tv_cut).setVisibility(View.GONE);
        contentView.findViewById(com.tcl.ai.note.helpwriting.R.id.tv_select_all).setVisibility(View.GONE);
        contentView.findViewById(com.tcl.ai.note.helpwriting.R.id.iv_assist).setVisibility(View.GONE);
    }


    /**
     * 在View系统中调用
     * @param menuItems
     * @param menuItemClickListener
     * @param contentRect
     */
    public void show(List<MenuItem> menuItems, MenuItem.OnMenuItemClickListener menuItemClickListener, android.graphics.Rect contentRect) {
        View tvCopy = mWindow.getContentView().findViewById(R.id.tv_copy);
        View tvPaste = mWindow.getContentView().findViewById(R.id.tv_paste);
        View tvCut = mWindow.getContentView().findViewById(R.id.tv_cut);
        View tvSelectAll = mWindow.getContentView().findViewById(R.id.tv_select_all);
        tvCopy.setVisibility(View.GONE);
        tvPaste.setVisibility(View.GONE);
        tvCut.setVisibility(View.GONE);
        tvSelectAll.setVisibility(View.GONE);

        menuItems.forEach(menuItem -> {
            if (menuItem.getItemId() == android.R.id.copy) {
                tvCopy.setOnClickListener(v -> {
                    menuItemClickListener.onMenuItemClick(menuItem);
                    dismiss();
                });
                tvCopy.setVisibility(View.VISIBLE);
            } else if (menuItem.getItemId() == android.R.id.selectAll) {
                tvSelectAll.setOnClickListener(v -> {
                    menuItemClickListener.onMenuItemClick(menuItem);
                    dismiss();
                });
                tvSelectAll.setVisibility(View.VISIBLE);
            } else if (menuItem.getItemId() == android.R.id.paste) {
                tvPaste.setOnClickListener(v -> {
                    menuItemClickListener.onMenuItemClick(menuItem);
                    dismiss();
                });
                tvPaste.setVisibility(View.VISIBLE);
            } else if (menuItem.getItemId() == android.R.id.cut) {
                tvCut.setOnClickListener(v -> {
                    menuItemClickListener.onMenuItemClick(menuItem);
                    dismiss();
                });
                tvCut.setVisibility(View.VISIBLE);
            }
        });
        mWindow.setElevation(8f);
        int x = contentRect.left;

        // 显示在屏幕外面，需要矫正位置
        View contentView = mWindow.getContentView();
        contentView.measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
        int measuredWidth = contentView.getMeasuredWidth();
        if (x + measuredWidth > screenWidth) {
            x = screenWidth - measuredWidth - dpToPx(12);
        }
        mWindow.showAtLocation(mView, Gravity.NO_GRAVITY, x, (int) (contentRect.top - mHeight * 2f));
    }

    public void dismiss() {
        mWindow.dismiss();
    }

    public boolean isShowing() {
        return mWindow.isShowing();
    }

//    public void show(View view, @NotNull Rect rect) {
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//            mWindow.setElevation(8f);
//        }
//        view.getLocationInWindow(mTempCoors);
////            mWindow.showAtLocation(view, Gravity.NO_GRAVITY, posX, posY);
//        Logger.d("OperateWindow", "show: " + rect);
//        Logger.d("OperateWindow", "show view: " + view + "  mTempCoors=" + mTempCoors[0] + "  " + mTempCoors[1]);
//        mWindow.showAtLocation(view, Gravity.NO_GRAVITY, (int) rect.getLeft() + mTempCoors[0],
//                (int) ((int) rect.getTop() - mHeight*1.7) + + mTempCoors[1]);
//    }

    public void setOutsideTouchable(boolean outsideTouchable, PopupWindow.OnDismissListener onDismiss) {
        if (mWindow.isOutsideTouchable() ^ outsideTouchable) {
            mWindow.setOutsideTouchable(outsideTouchable);
            mWindow.setFocusable(!outsideTouchable);
            mWindow.update();
        }
        mWindow.setOnDismissListener(onDismiss);
    }


    /**
     * 在 Compose 中调用
     *
     * @param onAssistAction
     * @param onCopyRequested
     * @param onPasteRequested
     * @param onCutRequested
     * @param onSelectAllRequested
     * @param rect
     */
    public void show(Function0<Unit> onAssistAction,
                     Function0<Unit> onCopyRequested,
                     Function0<Unit> onPasteRequested,
                     Function0<Unit> onCutRequested,
                     Function0<Unit> onSelectAllRequested,
                     androidx.compose.ui.geometry.Rect rect, int xOffset, int yOffset) {
        View tvCopy = mWindow.getContentView().findViewById(R.id.tv_copy);
        View tvPaste = mWindow.getContentView().findViewById(R.id.tv_paste);
        View tvCut = mWindow.getContentView().findViewById(R.id.tv_cut);
        View tvSelectAll = mWindow.getContentView().findViewById(R.id.tv_select_all);
        View ivAssist = mWindow.getContentView().findViewById(R.id.iv_assist);

        if (onAssistAction != null) {
            ivAssist.setVisibility(View.VISIBLE);
            ivAssist.setOnClickListener(v -> {
                onAssistAction.invoke();
                dismiss();
            });
        } else {
            ivAssist.setVisibility(View.GONE);
        }

        if (onCopyRequested != null) {
            tvCopy.setVisibility(View.VISIBLE);
            tvCopy.setOnClickListener(v -> {
                onCopyRequested.invoke();
                dismiss();
            });
        } else {
            tvCopy.setVisibility(View.GONE);
        }

        if (onPasteRequested != null) {
            tvPaste.setVisibility(View.VISIBLE);
            tvPaste.setOnClickListener(v -> {
                onPasteRequested.invoke();
                dismiss();
            });
        } else {
            tvPaste.setVisibility(View.GONE);
        }


        if (onCutRequested != null) {
            tvCut.setVisibility(View.VISIBLE);
            tvCut.setOnClickListener(v -> {
                onCutRequested.invoke();
                dismiss();
            });
        } else {
            tvCut.setVisibility(View.GONE);
        }

        if (onSelectAllRequested != null) {
            tvSelectAll.setVisibility(View.VISIBLE);
            tvSelectAll.setOnClickListener(v -> {
                onSelectAllRequested.invoke();
                dismiss();
            });
        } else {
            tvSelectAll.setVisibility(View.GONE);
        }

        mWindow.setElevation(8f);

        int x = (int) rect.getLeft() + xOffset;
        int y = (int) (rect.getTop() + yOffset);

        // 显示在屏幕外面，需要矫正位置
        View contentView = mWindow.getContentView();
        contentView.measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
        int measuredWidth = contentView.getMeasuredWidth();
        if (x + measuredWidth > screenWidth) {
            x = screenWidth - measuredWidth - dpToPx(12);
        }
        mWindow.showAtLocation(mView, Gravity.NO_GRAVITY, x, y);
    }

    public int dpToPx(int dp) {
        float density = mView.getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }
}