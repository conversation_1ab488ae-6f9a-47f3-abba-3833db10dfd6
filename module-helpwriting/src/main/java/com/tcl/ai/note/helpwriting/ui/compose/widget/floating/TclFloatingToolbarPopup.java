package com.tcl.ai.note.helpwriting.ui.compose.widget.floating;

import android.graphics.Rect;
import android.view.MenuItem;
import android.view.View;
import android.widget.PopupWindow;

import java.util.List;

public class TclFloatingToolbarPopup implements FloatingToolbarPopup {

    private final OperateWindow operateWindow;

    public TclFloatingToolbarPopup(View view) {
        operateWindow = new OperateWindow(view);
    }


    @Override
    public void setSuggestedWidth(int suggestedWidth) {

    }

    @Override
    public void setWidthChanged(boolean widthChanged) {

    }

    @Override
    public void show(List<MenuItem> menuItems, MenuItem.OnMenuItemClickListener menuItemClickListener, Rect contentRect) {

        operateWindow.show(menuItems, menuItemClickListener, contentRect);
    }

    @Override
    public void dismiss() {
        operateWindow.dismiss();
    }

    @Override
    public void hide() {
        operateWindow.dismiss();
    }

    @Override
    public boolean isShowing() {
        return operateWindow.isShowing();
    }

    @Override
    public boolean isHidden() {
        return operateWindow.isShowing();
    }

    @Override
    public boolean setOutsideTouchable(boolean outsideTouchable, PopupWindow.OnDismissListener onDismiss) {
        operateWindow.setOutsideTouchable(outsideTouchable, onDismiss);
        return true;
    }
}
