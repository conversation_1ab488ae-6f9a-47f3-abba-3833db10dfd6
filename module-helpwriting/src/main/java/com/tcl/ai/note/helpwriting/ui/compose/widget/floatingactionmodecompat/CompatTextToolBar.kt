package com.tcl.ai.note.helpwriting.ui.compose.widget.floatingactionmodecompat

import android.view.View
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.platform.TextToolbar
import androidx.compose.ui.platform.TextToolbarStatus
import com.tcl.ai.note.helpwriting.ui.compose.widget.floating.OperateWindow


typealias ActionCallback = () -> Unit

class CompatTextToolbar(
    view: View,
    private val onAssistAction: (() -> Unit)? = null
) : TextToolbar {


    // 需要在写作和问答模式共享偏移量
    companion object {
        var xOffset = 0
        var yOffset = 0
    }

    private val operateWindow = OperateWindow(view)


    override var status: TextToolbarStatus = TextToolbarStatus.Hidden
        private set

    override fun hide() {
        status = TextToolbarStatus.Hidden
        operateWindow.dismiss()
    }

    override fun showMenu(
        rect: Rect,
        onCopyRequested: ActionCallback?,
        onPasteRequested: ActionCallback?,
        onCutRequested: ActionCallback?,
        onSelectAllRequested: ActionCallback?
    ) {

        status = TextToolbarStatus.Shown
        operateWindow.show(
            onAssistAction,
            onCopyRequested,
            onPasteRequested,
            onCutRequested,
            onSelectAllRequested,
            rect,
            xOffset,
            yOffset
        )

    }

}