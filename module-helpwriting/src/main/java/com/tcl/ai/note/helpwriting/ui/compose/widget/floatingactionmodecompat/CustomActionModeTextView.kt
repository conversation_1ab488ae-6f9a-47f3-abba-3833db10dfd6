package com.tcl.ai.note.helpwriting.ui.compose.widget.floatingactionmodecompat

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.ActionMode
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewTreeObserver.OnPreDrawListener
import android.widget.TextView
import com.tcl.ai.note.helpwriting.ui.compose.widget.floating.FloatingActionMode
import com.tcl.ai.note.helpwriting.ui.compose.widget.floating.FloatingToolbar

@SuppressLint("AppCompatCustomView")
class CustomActionModeTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
)  : TextView(context, attrs, defStyleAttr) {
    private var mFloatingActionMode: ActionMode? = null
    private var mFloatingToolbarPreDrawListener: OnPreDrawListener? = null
    private var mFloatingToolbar: FloatingToolbar? = null

    override fun startActionMode(callback: ActionMode.Callback, type: Int): ActionMode {
        var mode = createActionMode(callback)
        callback.onCreateActionMode(mode, mode.menu)
        setHandledActionMode(mode)
        return mode
    }


    private fun setHandledActionMode(mode: ActionMode) {
        mFloatingActionMode = mode
        mFloatingActionMode!!.invalidate() // Will show the floating toolbar if necessary.
        getViewTreeObserver().addOnPreDrawListener(mFloatingToolbarPreDrawListener)
    }

    private fun createActionMode(callback: ActionMode.Callback): ActionMode {
        cleanupFloatingActionModeViews()
        mFloatingToolbar = FloatingToolbar(this)
        val mode =
            FloatingActionMode(
                context,
                ActionModeCallback2Wrapper(callback),
                this,
                mFloatingToolbar
            )
        mode.setOutsideTouchable(true) {
//            mFloatingToolbar?.dismiss()
            isSelected = false
        }
        mFloatingToolbarPreDrawListener =
            OnPreDrawListener {
                mode.updateViewLocationInWindow()
                true
            }
        return mode

    }

    override fun onWindowFocusChanged(hasWindowFocus: Boolean) {
        super.onWindowFocusChanged(hasWindowFocus)
        if (mFloatingActionMode != null) {
            mFloatingActionMode?.onWindowFocusChanged(hasWindowFocus)
        }
    }

    private fun cleanupFloatingActionModeViews() {
        if (mFloatingToolbar != null) {
            mFloatingToolbar!!.dismiss()
            mFloatingToolbar = null
        }
        if (mFloatingToolbarPreDrawListener != null) {
            getViewTreeObserver()
                .removeOnPreDrawListener(mFloatingToolbarPreDrawListener)
            mFloatingToolbarPreDrawListener = null
        }
    }

    private class ActionModeCallback2Wrapper(private val mWrapped: ActionMode.Callback) :
        ActionMode.Callback2() {
        override fun onCreateActionMode(mode: ActionMode, menu: Menu): Boolean {
            return mWrapped.onCreateActionMode(mode, menu)
        }

        override fun onPrepareActionMode(mode: ActionMode, menu: Menu): Boolean {
            return mWrapped.onPrepareActionMode(mode, menu)
        }

        override fun onActionItemClicked(mode: ActionMode, item: MenuItem): Boolean {
            return mWrapped.onActionItemClicked(mode, item)
        }

        override fun onDestroyActionMode(mode: ActionMode) {
            mWrapped.onDestroyActionMode(mode)
        }

        override fun onGetContentRect(mode: ActionMode, view: View, outRect: Rect) {
            if (mWrapped is ActionMode.Callback2) {
                mWrapped.onGetContentRect(mode, view, outRect)
            } else {
                super.onGetContentRect(mode, view, outRect)
            }
        }
    }
}
