package com.tcl.ai.note.helpwriting.ui.compose.widget.markdown

import android.content.Context
import coil.ImageLoader
import coil.imageLoader
import com.tcl.ai.note.helpwriting.ui.compose.widget.markdown.plugin.CoilImagesPlugin
import com.tcl.ai.note.helpwriting.ui.compose.widget.markdown.plugin.LinkifyPlugin
import io.noties.markwon.AbstractMarkwonPlugin
import io.noties.markwon.Markwon
import io.noties.markwon.MarkwonConfiguration
import io.noties.markwon.SoftBreakAddsNewLinePlugin
import io.noties.markwon.core.MarkwonTheme
import io.noties.markwon.ext.strikethrough.StrikethroughPlugin
import io.noties.markwon.ext.tables.TablePlugin
import io.noties.markwon.html.HtmlPlugin
import com.tcl.ai.note.base.R

internal object MarkdownRender {

    fun create(
        context: Context,
        imageLoader: ImageLoader?,
        linkifyMask: Int,
        enableSoftBreakAddsNewLine: Boolean,
        onLinkClicked: ((String) -> Unit)? = null,
        useTablePlugin: Boolean = true
    ): Markwon {
        val coilImageLoader = imageLoader ?: context.imageLoader
        val markwonBuilder =  Markwon.builder(context)
            .usePlugin(HtmlPlugin.create())
            .usePlugin(CoilImagesPlugin.create(context, coilImageLoader))
            .usePlugin(StrikethroughPlugin.create())
            .usePlugin(LinkifyPlugin.create(linkifyMask, true))
            .apply {
                if (enableSoftBreakAddsNewLine) {
                    usePlugin(SoftBreakAddsNewLinePlugin.create())
                }
            }
            .usePlugin(object : AbstractMarkwonPlugin() {
                override fun configureConfiguration(builder: MarkwonConfiguration.Builder) {
                    // Setting [MarkwonConfiguration.Builder.linkResolver] overrides
                    // Markwon's default behaviour - see Markwon's [LinkResolverDef]
                    // and how it's used in [MarkwonConfiguration.Builder].
                    // Only use it if the client explicitly wants to handle link clicks.
                    onLinkClicked ?: return
                    builder.linkResolver { _, link ->
                        // handle individual clicks on Textview link
                        onLinkClicked.invoke(link)
                    }
                }

                override fun configureTheme(builder: MarkwonTheme.Builder) {
                    builder.codeBackgroundColor(context.getColor(R.color.tct_alert_dialog_bg_color))
                    builder.thematicBreakHeight(3)
                }
            })
        if (useTablePlugin){
            markwonBuilder.usePlugin(TablePlugin.create(context))
        }
        return markwonBuilder.build()
    }
}
