package com.tcl.ai.note.helpwriting.utils

import com.tcl.ai.note.base.R

internal val templateList = listOf(
    R.string.help_writing_email,
    R.string.help_writing_work_summary,
    R.string.help_writing_event_program,
    R.string.help_writing_invitation,
    R.string.help_writing_outLine,
    R.string.help_writing_meeting_notice
)

 val templateIconList = listOf(
    R.drawable.email,
    R.drawable.work_summary,
    R.drawable.event_program,
    R.drawable.invitation,
    R.drawable.outline,
    R.drawable.meeting_notice,
)

 val operationFunctionList = listOf(
    R.string.help_writing_shorter,
    R.string.help_writing_longer,
    R.string.professional,
    R.string.casual
)

 val operationFunctionIconList = listOf(
    R.drawable.shorter,
    R.drawable.longer,
    R.drawable.professional,
    R.drawable.casual,
)

 val templateSummaryList = listOf(
    R.string.help_writing_default_prompt,
    R.string.help_writing_work_summary_description,
    R.string.help_writing_event_program_description,
    R.string.help_writing_invitation_description,
    R.string.help_writing_outLine_description,
    R.string.help_writing_meeting_notice_description
)