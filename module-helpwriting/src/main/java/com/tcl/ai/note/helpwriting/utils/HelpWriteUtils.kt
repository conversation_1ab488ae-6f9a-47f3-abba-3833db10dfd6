package com.tcl.ai.note.helpwriting.utils

import com.tcl.ai.sdk.assistant.HelpWritingCmd
import com.tcl.ai.sdk.assistant.PresetCmd

object HelpWriteUtils {
    /**
     * 获取预设指令
     */
     fun getPresetCmd(type: Int): String {
        val presetCmd = when (type) {
            0 -> {
                HelpWritingCmd.EMAIL
            }

            1 -> {
                HelpWritingCmd.WORK_SUMMARY
            }

            2 -> {
                HelpWritingCmd.ACTIVITY_PLAN
            }

            3 -> {
                HelpWritingCmd.INVITATION
            }

            4 -> {
                HelpWritingCmd.OUTLINE
            }

            5 -> {
                HelpWritingCmd.MEETING_NOTIFICATION
            }

            6 -> {
                HelpWritingCmd.MAKE_SHORTER
            }

            7 -> {
                HelpWritingCmd.MAKE_LONGER
            }

            8 -> {
                HelpWritingCmd.PROFESSIONALIZE
            }

            9 -> {
                HelpWritingCmd.MAKE_CASUAL
            }

            else -> {
                HelpWritingCmd.CUSTOM
            }
        }
        return presetCmd
    }

}