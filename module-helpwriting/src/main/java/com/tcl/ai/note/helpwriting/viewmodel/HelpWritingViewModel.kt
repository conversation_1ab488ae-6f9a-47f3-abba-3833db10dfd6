package com.tcl.ai.note.helpwriting.viewmodel

import android.content.Context
import android.util.Log
import androidx.annotation.StringRes
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.base.R
import com.tcl.ai.note.event.AIReplaceEvent
import com.tcl.ai.note.handwritingtext.repo.NoteRepository2
import com.tcl.ai.note.handwritingtext.richtext.utils.NoteContentUtil
import com.tcl.ai.note.helpwriting.repository.HelpWritingRepository
import com.tcl.ai.note.helpwriting.track.AnalyticsHelpWritingModel
import com.tcl.ai.note.helpwriting.utils.HelpWriteUtils
import com.tcl.ai.note.net.NetworkUseCase
import com.tcl.ai.note.state.ChartStreamingMsgUtils
import com.tcl.ai.note.state.ChatEffect
import com.tcl.ai.note.state.Result
import com.tcl.ai.note.state.StreamingStatus
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.sdk.assistant.PresetCmd
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 帮写的ViewModel
 * 原文只能显示初始的问题，后续的继续提问只是对生成内容的继续提问
 */
@HiltViewModel
class HelpWritingViewModel @Inject constructor(
    @ApplicationContext val context: Context,
    private val networkUseCase: NetworkUseCase,
    private val repository: HelpWritingRepository
) : ViewModel() {

    companion object {
        private const val TAG = "HelpWritingViewModel"
    }

    private var _selectedTemplateId = mutableIntStateOf(-1)
    val selectedTemplateId: State<Int> = _selectedTemplateId
    var generateState = mutableStateOf(false)
        private set

    private val isOffline = networkUseCase.isOffline(viewModelScope)
    private val _effect = MutableStateFlow<ChatEffect?>(
        null
    )
    val effect = _effect.asStateFlow()

    // for retry
    private var lastOperationType = -1
    private var lastCommand = PresetCmd.CUSTOM
    private var lastOriginalText = ""

    // ai返回结果的流
    private val _chatLoadingStatus = repository.getTemplateFlow()
    val chatLoadingStatus = _chatLoadingStatus.asStateFlow()

    private var latestAiResult = ""
    private var _originalTextState = MutableStateFlow("")
    var originalTextState = _originalTextState.asStateFlow()
    private var sendMsgJob: Job? = null
    private var noteId: Long? = null
    private var mContent: String? = null

    init {
        AnalyticsHelpWritingModel.loadHelpWritingViewModel(this)
        viewModelScope.launch {
            isOffline.collectLatest {
                if (it) {
                    (chatLoadingStatus.value as? Result.Success)?.let { state ->
                        val successData = state.data
                        if (successData.status == StreamingStatus.IN_PROGRESS) {
                            _effect.value = (ChatEffect.ShowToastRes(R.string.network_error))
                        }
                    }
                }
            }
        }
    }

    private val errorCallback: (String) -> Unit = { message ->
        viewModelScope.launch {
            when (message) {
                "network error" -> _effect.value = (ChatEffect.ShowToastRes(R.string.network_error))
                "ai_error" -> _effect.value = (ChatEffect.ShowToastRes(R.string.ai_error_code))
                else -> {
                    val errorResult =
                        ChartStreamingMsgUtils.dealWithErrorResult(message.toIntOrNull(), null)
                    _effect.value = (errorResult)
                }
            }
        }
    }

    fun initNote(noteId: Long?) {
        this.noteId = noteId
        viewModelScope.launch {
            mContent = NoteRepository2.get(noteId)?.content
        }
    }

    fun setTemplatedId(templateId: Int) {
        if (_selectedTemplateId.intValue == templateId) {
            _selectedTemplateId.intValue = -1
        } else {
            _selectedTemplateId.intValue = templateId
        }
    }

    /**
     * 输入框的发送指令
     */
    fun onSendClick(command: String, isFromFirstPage: Boolean) {
        Log.d("HelpWritingResultViewModel", "onSendClick command: $command")
        val type = if (_selectedTemplateId.intValue == -1) 10 else _selectedTemplateId.intValue
        if (isFromFirstPage) {//起始页记住原文本
            _originalTextState.value = command
        }
        sendWritingQuickCommand(type, command, {})
        AnalyticsHelpWritingModel.startHelpWritingCmdMillis = System.currentTimeMillis()
    }

    /**
     * * 底部菜单点击事件
     */
    fun onBottomMenuItemClick(
        type: Int,
        afterSendCallback: () -> Unit
    ) {
        sendWritingQuickCommand(type, "", afterSendCallback)
    }

    /**
     *
     * type：指定的指令类型
     * command: 自定义指令 输入框 点发送为原始文本
     * afterSendCallback：发送后的回调
     */
    private fun sendWritingQuickCommand(
        type: Int,
        command: String,
        afterSendCallback: () -> Unit,
        isRetry: Boolean = false
    ) {
        if (isOffline.value) {//有网才往下走
            viewModelScope.launch {
                _effect.value = (ChatEffect.ShowToastRes(R.string.network_error))
            }
            return
        }

        val presetCmd = HelpWriteUtils.getPresetCmd(type)
        //保留上一次的回复文本

        val text = (chatLoadingStatus.value as? Result.Success)?.let { state ->
            val successData = state.data
            if (successData.status == StreamingStatus.COMPLETED || successData.status == StreamingStatus.STOPPED) {
                latestAiResult = successData.text
            }
            latestAiResult
        } ?: ""

        //文本长度判断
        if (text.length > 10000) {
            viewModelScope.launch {
                _effect.value = (ChatEffect.ShowToastRes(R.string.operate_text_override))
            }
            return
        }
        lastOperationType = type
        afterSendCallback.invoke()
        sendMsgJob = viewModelScope.launch {
            //设置AI返回结果的Title
            Logger.i(
                TAG,
                "sendWritingQuickCommand --> type: $type, command: $command presetCmd: $presetCmd "
                        + "lastCommand: $lastCommand" +
                        "text: $text"
            )

            if (type == 10 || type in 0..5) {//自定义指令和预设指令
                lastCommand = presetCmd //自定义指令
                lastOriginalText = command//command是输入的文本 记住 给重试
                repository.sendUserMsg(command, presetCmd, quickAction = null, errorCallback)
            } else {
                //预设指令 command为空  presetCmd 是 quick_action
                repository.sendUserMsg(null, lastCommand, quickAction = presetCmd, errorCallback)
            }
        }

        // 上报埋点
        if (isRetry) {
            TclAnalytics.reportAiWriteResentTopic(command)
        } else {
            TclAnalytics.reportAiWriteTopic(command)
        }
    }


    // 重试功能
    fun retry() {
        //重试上一次的指令 + 文本
        Logger.i(TAG, "retry !!!")
        sendWritingQuickCommand(lastOperationType, lastOriginalText, {}, isRetry = true)
        TclAnalytics.reportAiWriteReset("0")
        AnalyticsHelpWritingModel.startHelpWritingCmdMillis = System.currentTimeMillis()
    }

    fun insert() {
        val successSate = chatLoadingStatus.value as? Result.Success
        successSate?.let { state ->
            val needInsertText = state.data.text
            AIReplaceEvent.sendHelpWriteReplaceEvent(needInsertText)
            TclAnalytics.reportAiWriteInset("0")
        }
        successSate?.let { state ->
            val needInsertText = state.data.text
            if (needInsertText.isEmpty()) {
                return
            }
            mContent?.let { content ->
                Logger.d(TAG, "insert -->")
                if (NoteContentUtil.isInsertContentLengthValid(content, needInsertText)) {
                    showToast(R.string.inserted)
                }
            }
        }
    }

    fun copy() {
        TclAnalytics.reportAiWriteCopy("0")
        viewModelScope.launch {
            showToast(R.string.copied)
        }
    }

    fun output(outputText: String) {
    }

    fun stopAIAssistant(onStopSuccess: (() -> Unit)? = null) {
        Logger.d(TAG, "cancelAiOutput !!!")
        repository.cancelStreamingMsg(onStopSuccess)
        sendMsgJob?.cancel()
        if (AnalyticsHelpWritingModel.startHelpWritingCmdMillis > 0) {
            TclAnalytics.reportAiWriteStop(
                "0",
                (System.currentTimeMillis() - AnalyticsHelpWritingModel.startHelpWritingCmdMillis).toString()
            )
        }
        AnalyticsHelpWritingModel.startHelpWritingCmdMillis = 0
    }

    private fun showToast(@StringRes toastId: Int) {
        Logger.d(TAG, "showToast: $toastId thread: ${Thread.currentThread().name}")
        _effect.value = (ChatEffect.ShowToastRes(toastId))
        viewModelScope.launch {
            delay(1000)
            _effect.value = (null)
        }
    }

    fun gotoResultPage(state: String) {
        if (!isOffline.value) {//离线状态下不跳转
            generateState.value = true
        }
    }

    /**
     * 重置原始文本
     * 在每次打开弹窗时调用，确保每次都使用新的文本
     */
    fun resetOriginalText() {
        _originalTextState.value = ""
        repository.reset(null)
    }
}