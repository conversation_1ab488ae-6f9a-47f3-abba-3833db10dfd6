<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_operate_window"
    android:orientation="horizontal"
    android:paddingLeft="5dp"
    android:gravity="center_vertical"
    android:paddingRight="5dp">

    <ImageView
        android:id="@+id/iv_assist"
        android:layout_width= "44dp"
        android:layout_height="44dp"
        android:padding="12dp"
        android:src="@drawable/writing_logo"/>

    <TextView
        android:id="@+id/tv_copy"
        android:text="@android:string/copy"
        style="@style/OperateTextView"/>

    <TextView
        android:id="@+id/tv_paste"
        android:text="@android:string/paste"
        style="@style/OperateTextView"/>

    <TextView
        android:id="@+id/tv_cut"
        android:text="@android:string/cut"
        style="@style/OperateTextView"/>

    <TextView
        android:id="@+id/tv_select_all"
        android:text="@android:string/selectAll"
        style="@style/OperateTextView"/>

</LinearLayout>