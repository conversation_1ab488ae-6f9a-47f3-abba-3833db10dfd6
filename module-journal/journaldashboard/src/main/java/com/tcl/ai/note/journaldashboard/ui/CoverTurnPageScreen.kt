package com.tcl.ai.note.journaldashboard.ui

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateDp
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateOffset
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.updateTransition
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.database.entity.Journal
import com.tcl.ai.note.journaldashboard.utils.CoverDataList
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.utils.setJournalClickTime
import com.tcl.ai.note.utils.toPx

enum class AnimationStage {
    INITIAL, // 初始状态
    EXPAND, // 居中且放大状态
    TURNPAGE // 翻页状态
}

enum class AnimationType {
    ENTER,
    EXIT,
}

/**
 * 封面翻页动效组件
 */
@Composable
fun CoverTurnPageScreen(
    navController: NavController,
    journal: Journal,
    bounds: Rect,
    isEnter: Boolean = false, //区分是enter还是popEnter
    previousRoute: String? = null,
    content: @Composable (isFullMode: Boolean, onExit: () -> Unit) -> Unit
) {
    // 动画阶段
    var stage by remember {
        mutableStateOf(
            if (isEnter) {
                AnimationStage.INITIAL
            } else {
                AnimationStage.TURNPAGE
            }
        )
    }
    // 动画类型
    var animationType by remember { mutableStateOf(AnimationType.ENTER) }
    val density = LocalDensity.current
    val animDuration = 250

    // 动画尺寸参数
    val offsetX = remember { 60.dp.toPx }
    val targetWidth = remember { GlobalContext.screenWidth - offsetX * 2 }
    val targetHeight = remember { targetWidth / bounds.width * bounds.height }
    val screenWidth = LocalConfiguration.current.screenWidthDp
    val screenHeight = LocalConfiguration.current.screenHeightDp

    // 翻开封面动画参数
    val transition = updateTransition(targetState = stage, label = "multiStage")
    val scale by transition.animateOffset(
        transitionSpec = { tween(animDuration, easing = LinearEasing) },
        label = "scale"
    ) {
        when (it) {
            AnimationStage.INITIAL -> Offset(
                bounds.width / GlobalContext.screenWidth,
                bounds.height / GlobalContext.screenHeight
            )

            AnimationStage.EXPAND -> Offset(
                targetWidth / GlobalContext.screenWidth,
                targetHeight / GlobalContext.screenHeight
            )

            else -> Offset(1f, 1f)
        }
    }
    val translation by transition.animateOffset(
        transitionSpec = { tween(animDuration, easing = LinearEasing) },
        label = "translation"
    ) {
        when (it) {
            AnimationStage.INITIAL -> Offset(
                (bounds.left + bounds.width / 2) - (GlobalContext.screenWidth / 2),
                (bounds.top + bounds.height / 2) - (GlobalContext.screenHeight / 2),
            )

            else -> Offset(0f, 0f)
        }
    }
    val rotation by transition.animateFloat(
        transitionSpec = { tween(animDuration, easing = LinearEasing) },
        label = "rotation"
    ) {
        when (it) {
            AnimationStage.TURNPAGE -> -90f
            else -> 0f
        }
    }
    val cornerSize by transition.animateDp(
        transitionSpec = { tween(animDuration, easing = LinearEasing) },
        label = "cornerSize"
    ) {
        when (it) {
            AnimationStage.TURNPAGE -> 0.dp
            else -> 40.dp
        }
    }

    // 自动推进分段
    LaunchedEffect(animationType, transition.currentState, transition.targetState) {
        if (transition.currentState == transition.targetState) {
            stage = if (animationType == AnimationType.ENTER) {
                when (transition.currentState) {
                    AnimationStage.INITIAL -> AnimationStage.EXPAND
                    else -> AnimationStage.TURNPAGE
                }
            } else {
                when (transition.currentState) {
                    AnimationStage.TURNPAGE -> AnimationStage.EXPAND
                    else -> AnimationStage.INITIAL
                }
            }
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        if (transition.targetState == AnimationStage.TURNPAGE) {
            Box(
                modifier = Modifier
                    .size(screenWidth.dp, screenHeight.dp)
                    .graphicsLayer(
                        scaleX = scale.x,
                        scaleY = scale.y,
                        translationX = translation.x,
                        translationY = translation.y,
                        shape = RoundedCornerShape(cornerSize),
                        clip = true,
                    )
            ) {
                content(transition.currentState == AnimationStage.TURNPAGE) {
                    if (bounds != Rect.Zero
                        && (previousRoute == "main_screen"
                                || navController.currentDestination?.route?.contains(ROUTE_JOURNAL_CONTENT_SCREEN) == true)) {
                        setJournalClickTime()
                        animationType = AnimationType.EXIT
                    }
                }
            }
        }

        if (rotation != -90f) {
            CoverItem(
                modifier = Modifier
                    .size(screenWidth.dp, screenHeight.dp)
                    .graphicsLayer(
                        scaleX = scale.x,
                        scaleY = scale.y,
                        translationX = translation.x,
                        translationY = translation.y,
                    ),
                journal = journal,
                rotation = rotation,
                distance = 12f * density.density
            )
        }
    }
}

/**
 * 封面视图
 */
@Composable
fun CoverItem(
    journal: Journal,
    rotation: Float,
    distance: Float,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        val coverRes = if (journal.journalId == 1L) {
            R.drawable.cover_sample
        } else {
            CoverDataList.coverList[journal.coverId.toInt()]?.resId
                ?: R.drawable.cover_1
        }
        Image(
            painter = painterResource(coverRes),
            contentDescription = null,
            modifier = Modifier
                .fillMaxSize()
                .graphicsLayer {
                    transformOrigin =
                        TransformOrigin(pivotFractionX = 0.0f, pivotFractionY = 0.5f)
                    rotationY = rotation
                    cameraDistance = distance
                },
            contentScale = ContentScale.FillBounds
        )
    }
}