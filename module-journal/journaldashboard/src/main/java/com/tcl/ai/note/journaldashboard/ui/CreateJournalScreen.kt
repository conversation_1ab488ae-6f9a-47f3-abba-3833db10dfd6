package com.tcl.ai.note.journaldashboard.ui

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Surface
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInWindow
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.google.gson.Gson
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.database.entity.Journal
import com.tcl.ai.note.database.entity.JournalCategory
import com.tcl.ai.note.database.repository.HomeRepository
import com.tcl.ai.note.journalbase.BottomSheetDialog
import com.tcl.ai.note.journaldashboard.intent.ConfigIntent
import com.tcl.ai.note.journaldashboard.intent.JournalIntent
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.journaldashboard.utils.CoverDataList
import com.tcl.ai.note.journaldashboard.vm.CreateJournalViewModel
import com.tcl.ai.note.journalhome.entity.HomeCategoryItemEntity
import com.tcl.ai.note.journalhome.entity.HomeNoteItemEntity
import com.tcl.ai.note.journalhome.vm.state.HomeCategoryUiState
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.deviceDensity
import com.tcl.ai.note.utils.getNavBarHeight
import com.tcl.ai.note.utils.isDensity440
import com.tcl.ai.note.utils.judge
import com.tct.theme.core.designsystem.component.TclButton
import com.tct.theme.core.designsystem.component.TclTextButton
import com.tct.theme.core.designsystem.theme.TclTheme
import kotlinx.coroutines.launch

@SuppressLint("DesignSystem")
@Composable
fun CreateJournalScreen(
    navController: NavController,
    srcJournal: HomeNoteItemEntity? = null,
    homeCategoryUiState: HomeCategoryUiState,
    onDismissRequest: (Long?, String?, Long?, Boolean?) -> Unit,
    viewModel: CreateJournalViewModel = hiltViewModel(),
    onAddNewCategoryClick: () -> Unit = {},
) {
    val context = LocalContext.current
    val isDarkMode = com.tcl.ai.note.utils.isDarkMode(context)
    val coroutineScope = rememberCoroutineScope()
    var lastSelectedCoverId by remember { mutableIntStateOf((srcJournal?.coverId?: 1).toInt()) } // 上次选择的封面ID
    var selectedCoverId by remember { mutableIntStateOf((srcJournal?.coverId?: 1).toInt()) } // 当前选择的封面ID
    var isSelectCoverState by remember { mutableStateOf(false) } // 是否选择封面状态
    var showChangeJournalNameDialog by remember { mutableStateOf(false) } // 是否显示修改日记名称对话框
    var showJournalTypeDropdown by remember { mutableStateOf(false) } // 是否显示日记类型下拉框
    var rowPosition by remember { mutableStateOf(Offset.Zero) } // 日记类型下拉框位置
    var customJournalName by remember { mutableStateOf("") } // 自定义日记名称
    var isNewCategory by remember { mutableStateOf(false) } // 是否新建日记类型
    val allJournals by viewModel.allJournals.collectAsState()
    val navigationBarHeight = getNavBarHeight()
    var isConfirmAddACategory by remember { mutableStateOf(false) } // 是否确认添加一个新分类
    val createdNewCategory by viewModel.createdCategory.collectAsState()

    var curSelectedCategoryId by remember { mutableStateOf(0L) }
    var curSelectedCategoryName by remember { mutableStateOf("") }

    val contentTopPadding = remember {
        if (GlobalContext.densityDpi <= deviceDensity) {
            isDensity440.judge(58.dp, 49.dp)
        } else {
            isDensity440.judge(10.dp, 12.dp)
        }
    }
    val coverTopPadding = remember {
        if (GlobalContext.densityDpi <= deviceDensity) {
            isDensity440.judge(18.dp, 15.dp)
        } else {
            isDensity440.judge(4.dp, 8.dp)
        }
    }

    LaunchedEffect(createdNewCategory) {
        if (createdNewCategory.first > 0) {
            curSelectedCategoryId = createdNewCategory.first
            curSelectedCategoryName = createdNewCategory.second
        }
    }

    LaunchedEffect(srcJournal) {
        val category = srcJournal?.let {
            HomeRepository.getCategory(it.categoryId.toLong())
        }?: run {
            if (homeCategoryUiState.selectedCategoryId.isEmpty() || homeCategoryUiState.selectedCategoryId.toLong() == 0L) {
                HomeRepository.getDefaultJournalCategory()
            } else {
                HomeRepository.getCategory(homeCategoryUiState.selectedCategoryId.toLong())?: HomeRepository.getDefaultJournalCategory()
            }
        }
        curSelectedCategoryId = category.categoryId
        curSelectedCategoryName = category.name
    }

    val journalTitle = customJournalName.ifEmpty {
        srcJournal?.title ?: run {
            if (curSelectedCategoryId > 6)
                generateNewJournalTitle(curSelectedCategoryName, allJournals)
            else
                generateNewJournalTitle(String.format(context.getString(R.string.default_journal_title), curSelectedCategoryName), allJournals)
        }
    }
    val journal by viewModel.journal.collectAsState()
    LaunchedEffect(journal) {
        if (journal != null) {
            viewModel.setJournal(null)
            onDismissRequest(journal?.journalId, journalTitle, selectedCoverId.toLong(), isConfirmAddACategory)
        }
    }


    BottomSheetDialog(
        modifier = Modifier,
        isDarkMode = isDarkMode,
        visible = true,
        onDismissRequest = {
            onDismissRequest(null, null, null, isConfirmAddACategory)
        },
        canceledOnTouchOutside = false,
        showFullScreenCallBack = {

        },
        extraTopPadding = true,
        isNavigationBarsPaddingNeeded=true,
        onDismissCallback = { callback ->

        },
    ) {
        val scrollState = rememberScrollState()
        Column(modifier = Modifier
            .fillMaxSize()
            //.navigationBarsPadding()
            .verticalScroll(scrollState)
        ) {
            Text(text = stringResource(id = R.string.create_a_journal),
                fontSize = isDensity440.judge(22.sp, 20.sp),
                fontWeight = FontWeight.Medium,
                modifier = Modifier
                    .padding(horizontal = isDensity440.judge(26.dp, 24.dp), vertical = isDensity440.judge(22.dp, 20.dp))
            )
            Row(modifier = Modifier
                .fillMaxWidth()
                .semantics {
                    contentDescription =
                        GlobalContext.instance.getString(R.string.journal_cover_description)
                }
                .padding(
                    top = isDensity440.judge(
                        (GlobalContext.densityDpi <= deviceDensity).judge(
                            16.dp,
                            8.dp
                        ), 19.dp
                    ),
                ), horizontalArrangement = Arrangement.Center) {
                Surface(elevation = 4.dp,
                    color = Color.Transparent,
                    shape = RoundedCornerShape(topStart = 6.dp, topEnd = 12.dp, bottomStart = 6.dp, bottomEnd = 12.dp),
                    modifier = Modifier
                        .width(
                            isDensity440.judge(
                                (GlobalContext.densityDpi <= deviceDensity).judge(
                                    164.dp,
                                    142.dp
                                ), 150.dp
                            )
                        )
                        .height(
                            isDensity440.judge(
                                (GlobalContext.densityDpi <= deviceDensity).judge(
                                    237.dp,
                                    206.dp
                                ), 218.dp
                            )
                        )) {
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        painter = painterResource(id = CoverDataList.coverList[selectedCoverId]?.resId?:
                        R.drawable.cover_1),
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                    )
                }
            }
            if (isSelectCoverState) {
                Box(modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = isDensity440.judge(8.dp, 12.dp),
                        end = isDensity440.judge(8.dp, 12.dp),
                        top = coverTopPadding
                    )) {
                    IconButton(
                        modifier = Modifier.align(Alignment.CenterStart),
                        onClick = {
                            isSelectCoverState = false
                            selectedCoverId = lastSelectedCoverId
                        },
                    ) {
                        Icon(
                            Icons.Filled.Close,
                            contentDescription = stringResource(id = com.tcl.ai.note.base.R.string.edit_top_menu_back_icon),
                        )
                    }
                    TclTextButton(
                        modifier = Modifier.align(Alignment.CenterEnd),
                        onClick = {
                            isSelectCoverState = false
                            lastSelectedCoverId = selectedCoverId
                        }
                    ) {
                        Text(text = stringResource(id = com.tcl.ai.note.base.R.string.done))
                    }
                }
                CoverSelectorView(
                    modifier = Modifier
                        .fillMaxWidth()
                        .fillMaxHeight(),
                    coverId = selectedCoverId,
                    onCoverSelected = {
                        selectedCoverId = it
                    }
                )
            } else {
                Row(verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .padding(top = contentTopPadding)
                        .clickable {
                            showChangeJournalNameDialog = true
                        }) {
                    Text(text = stringResource(id = com.tcl.ai.note.base.R.string.title),
                        fontSize = isDensity440.judge(16.sp, 14.sp),
                        color = TclTheme.colorScheme.tctStanderTextSecondary,
                        modifier = Modifier
                            .padding(
                                start = isDensity440.judge(25.dp, 24.dp),
                                top = 18.dp,
                                bottom = 18.dp
                            )
                    )
                    Spacer(modifier = Modifier.weight(1f))
                    Text(text = journalTitle,
                        fontSize = isDensity440.judge(17.sp, 16.sp),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier
                            .widthIn(
                                max = (GlobalContext.densityDpi <= deviceDensity).judge(
                                    300.dp,
                                    250.dp
                                )
                            )
                            .padding(end = 24.dp)
                    )
                }
                Row(verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .clickable {
                            isSelectCoverState = true
                        }
                        .onGloballyPositioned { coordinates ->
                            rowPosition = coordinates.positionInWindow()
                        }) {
                    Text(text = stringResource(id = R.string.text_journal_cover),
                        fontSize = isDensity440.judge(16.sp, 14.sp),
                        color = TclTheme.colorScheme.tctStanderTextSecondary,
                        modifier = Modifier
                            .weight(1f)
                            .padding(
                                start = isDensity440.judge(25.dp, 24.dp),
                                top = 18.dp,
                                bottom = 18.dp
                            )
                    )

                    Image(
                        painter = painterResource(id = CoverDataList.coverList[selectedCoverId]?.resId?:
                        R.drawable.cover_1),
                        contentDescription = stringResource(id = R.string.text_journal_cover),
                        modifier = Modifier
                            .padding(end = 24.dp)
                            .width(24.dp)
                            .height(24.dp)
                            .clip(RoundedCornerShape(8.dp)),
                        contentScale = ContentScale.Crop
                    )
                }
                Row(verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .clickable {
                            showJournalTypeDropdown = true
                        }
                        /*.onGloballyPositioned { coordinates ->
                            rowPosition = coordinates.positionInWindow()
                        }*/,) {
                    Text(text = stringResource(id = R.string.text_journal_type),
                        color = TclTheme.colorScheme.tctStanderTextSecondary,
                        fontSize = isDensity440.judge(16.sp, 14.sp),
                        modifier = Modifier
                            .padding(
                                start = isDensity440.judge(25.dp, 24.dp),
                                top = 18.dp,
                                bottom = 18.dp
                            )
                    )
                    Spacer(modifier = Modifier.weight(1f))
                    Text(text = curSelectedCategoryName,
                        fontSize = isDensity440.judge(17.sp, 16.sp),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier
                            .padding(end = 8.dp)
                            .widthIn(
                                max = (GlobalContext.densityDpi <= deviceDensity).judge(
                                    250.dp,
                                    200.dp
                                )
                            )
                    )
                    Image(painter = painterResource(id = com.tcl.ai.note.journaldashboard.R.drawable.ic_type), contentDescription = null,
                        modifier = Modifier.padding(end = 24.dp))
                }

                if (showJournalTypeDropdown) {
                    JournalTypeDropdown(
                        currentCategoryId = curSelectedCategoryId,
                        rowPosition = rowPosition,
                        onDismissRequest = {
                            showJournalTypeDropdown = false
                        },
                        onTypeSelected = {
                            curSelectedCategoryId = it.id.toLong()
                            curSelectedCategoryName = it.name?: ""
                            showJournalTypeDropdown = false
                        },
                        onNewCategoryClicked = {
                            showJournalTypeDropdown = false
                            isNewCategory = true
                        },
                        homeCategoryUiState = homeCategoryUiState,
                    )
                }

                Spacer(modifier = Modifier.weight(1f))
                Box(modifier = Modifier
                    .fillMaxSize()
                    .padding(bottom = navigationBarHeight + 24.dp),
                    contentAlignment = Alignment.BottomCenter
                ) {
                    TclButton(modifier = Modifier
                        .padding(horizontal = 24.dp)
                        .fillMaxWidth()
                        .heightIn(min = isDensity440.judge(48.dp, 44.dp)),
                        colors = ButtonColors(
                            containerColor = com.tcl.ai.note.theme.TclTheme.colorScheme.highlightColor,
                            contentColor = TclTheme.colorScheme.tctStanderTextButton,
                            disabledContainerColor = com.tcl.ai.note.theme.TclTheme.colorScheme.highlightColor,
                            disabledContentColor = TclTheme.colorScheme.tctStanderTextButton
                        ),
                        onClick = {
                            createJournal(srcJournal, journalTitle, coverId = selectedCoverId, categoryId = curSelectedCategoryId, viewModel = viewModel)
                        }) {
                        Text(text = stringResource(id = R.string.btn_text_confirm),
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }

    if (showChangeJournalNameDialog) {
        InputDialog(
            title = stringResource(id = com.tcl.ai.note.base.R.string.title),
            text = journalTitle,
            placeholder = null,
            maxLength = 50,
            onValueChange = {

            },
            onConfirm = {
                customJournalName = it
                showChangeJournalNameDialog = false
            },
            onDismissRequest = {
                showChangeJournalNameDialog = false
            }
        )
    }

    if (isNewCategory) {
        onAddNewCategoryClick.invoke()
        isNewCategory = false
    }
}

private fun createJournal(
    srcJournal: HomeNoteItemEntity? = null,
    journalTitle: String,
    coverId: Int,
    categoryId: Long,
    viewModel: CreateJournalViewModel,
) {
    srcJournal?.let {
        val journal = Journal(
            journalId = it.id.toLong(),
            title = journalTitle,
            coverId = coverId.toLong(),
            categoryId = categoryId,
            createTime = it.createTime,
            modifyTime = System.currentTimeMillis(),
            lastViewPage = it.lastViewPage
        )
        viewModel.handleEvent(JournalIntent.EditJournal(journal))
    }?: run {
        val journal = Journal(
            title = journalTitle,
            coverId = coverId.toLong(),
            categoryId = categoryId,
            createTime = System.currentTimeMillis(),
            modifyTime = System.currentTimeMillis(),
            lastViewPage = 0
        )
        viewModel.handleEvent(JournalIntent.CreateJournal(journal))
    }
}

private fun generateNewJournalTitle(
    title: String,
    journals: List<Journal>
): String {
    if (journals.isEmpty() || !journals.any { it.title == title }) {
        return title
    }
    var index = 1
    while (true) {
        if (journals.any { it.title == "$title($index)" }) {
            index++
        } else {
            return "$title($index)"
        }
    }
}

@Preview(
    widthDp = 360,// 360 // 设置宽度以模拟横屏
    heightDp = 820,// 820 // 设置高度
//    widthDp = 1100, // 设置宽度以模拟横屏
//    heightDp = 720, // 设置高度
    showBackground = true
)
@Composable
fun PreviewCreateJournalScreen() {
    //CreateJournalScreen(srcJournal = null, navController = rememberNavController(),  onDismissRequest = { _, _, _, _ -> })
}
