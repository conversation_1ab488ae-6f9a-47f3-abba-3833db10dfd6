package com.tcl.ai.note.journaldashboard.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.window.Popup
import com.tcl.ai.note.drawboard.menubar.data.JournalMenuBarDataProvider
import com.tcl.ai.note.drawboard.menubar.handler.JournalMenuBarEventHandler
import com.tcl.ai.note.handwritingtext.ui.edit.container.RichTextToolbarContainer
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.data.MenuBarConfig
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.data.MenuBarDependencies
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.handler.MenuBarEventHandler
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.provider.MenuBarDataProvider
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.renderer.JournalMenuBarUIRenderer
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.renderer.MenuBarUIRenderer
import com.tcl.ai.note.handwritingtext.ui.richtext.rememberAIRouteState
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.EditBottomAIMenuPop
import com.tcl.ai.note.handwritingtext.vm.TextAndDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.widget.HorizontalLine


/**
 * 手机编辑页的菜单栏（位于页面底部）
 * -> 修改菜单项看场景需要修改下列 3项：
 * - 事件处理逻辑抽离到 MenuBarEventHandler
 * - 数据源生成逻辑抽离到 MenuBarDataProvider
 * - 状态管理逻辑抽离到 MenuBarStateManager
 * - 本组件只负责组装和UI渲染
 */
@Composable
fun JournalBottomMenuBar(
    modifier: Modifier = Modifier,
    config: MenuBarConfig = MenuBarConfig(),
    suniaDrawViewModel: SuniaDrawViewModel,
    textAndDrawViewModel: TextAndDrawViewModel
) {
    // 创建依赖组合
    val dependencies = MenuBarDependencies.createJournal(viewModel = suniaDrawViewModel, textAndDrawViewModel)

    // 收集所有状态
    val states = dependencies.stateManager.collectStates()
    val aiRouteState = rememberAIRouteState(
        viewModel = dependencies.richTextToolBarViewModel
    )
    // 创建事件处理器
    val eventHandler = remember(dependencies, config) {
        JournalMenuBarEventHandler(
            dependencies = dependencies,
            config = config
        )
    }

    // 创建数据提供者
    val dataProvider = remember(eventHandler, dependencies) {
        JournalMenuBarDataProvider(
            eventHandler = eventHandler,
            dependencies = dependencies
        )
    }

    // 初始化笔刷设置
    LaunchedEffect(states.initCount) {
        eventHandler.initializeBrush()
        eventHandler.handleBrushSwitchFromText()
    }
    
    // 清理 MenuBarEventHandler，避免内存泄露
    DisposableEffect(eventHandler) {
        onDispose {
            eventHandler.cleanup()
        }
    }

    DisposableEffect (Unit) {
        onDispose {
            eventHandler.handleKeyboardSwitchFromDraw()
        }
    }

    // 获取菜单项数据源
    val menuToolItems = remember(
        states.editMode, states.menuBarState, states.fingerDrawEnabled
    ) {
        if (config.enableLogging) {
            Logger.d("Lopez", "editMode： ${states.editMode}")
        }
        dataProvider.getDrawMenuItems(
            menuBarState = states.menuBarState,
            states.fingerDrawEnabled
        )
    }

    // Box在外面，切换EditMode时无法触发重组，因此这里再写一个Box以实现键盘自适应
    Popup(alignment = Alignment.BottomCenter,) {
        Box(modifier = modifier
            .fillMaxWidth()
            .height(TclTheme.dimens.menuBarHeight)) {
            RichTextToolbarContainer(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .imePadding(),
                isFixed = false,
                textAndDrawViewModel = dependencies.textAndDrawViewModel
            ) {
                JournalMenuBarUIRenderer(modifier, menuToolItems, states.selectedPen, states.editMode)
            }
//            HorizontalLine(modifier = Modifier.align(Alignment.BottomCenter))
        }
    }
    EditBottomAIMenuPop(
        expanded = states.isShowBottomAIPop,
        onDismissRequest = {
            dependencies.richTextToolBarViewModel.operateBottomAIPop()
        },
        onUpdateBottomMenuType = { bottomMenuType ->

        },
        onBottomClick ={
            aiRouteState.handleRoute(states.noteUIState.content,states.noteUIState.noteId,it)
        }
    )
}
