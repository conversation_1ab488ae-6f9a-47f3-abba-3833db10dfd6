package com.tcl.ai.note.journaldashboard.ui

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.BitmapFactory
import android.os.Build
import android.view.View
import android.view.Window
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.widget.Toast
import android.view.WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
import android.view.accessibility.AccessibilityManager
import androidx.activity.compose.BackHandler
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.gestures.detectHorizontalDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBackIosNew
import androidx.compose.material.icons.filled.ArrowForwardIos
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MenuDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.focusProperties
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import androidx.compose.ui.zIndex
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.preferencesDataStore
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.navigation.NavController
import coil3.compose.AsyncImage
import coil3.imageLoader
import coil3.memory.MemoryCache.Key
import com.google.gson.Gson
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.database.entity.JournalContent
import com.tcl.ai.note.database.entity.PageInfo
import com.tcl.ai.note.drawboard.SuniaDrawBoard
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.ui.edit.EraserPressIndicator
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.data.MenuBarConfig
import com.tcl.ai.note.handwritingtext.ui.utils.scale.MatrixInfo
import com.tcl.ai.note.handwritingtext.ui.widget.ScaleIndicator
import com.tcl.ai.note.handwritingtext.utils.FileUtils.getJournalContentThumbnailPath
import com.tcl.ai.note.handwritingtext.vm.TextAndDrawViewModel
import com.tcl.ai.note.journalbase.widget.pageflip.PageFlipView
import com.tcl.ai.note.journalbase.widget.pageflip.PageFlipViewState
import com.tcl.ai.note.journaldashboard.intent.JournalContentIntent
import com.tcl.ai.note.journaldashboard.utils.ImmersiveModeUtils
import com.tcl.ai.note.journaldashboard.utils.PhotosPermissionHelper
import com.tcl.ai.note.journaldashboard.vm.JournalContentViewModel
import com.tcl.ai.note.picturetotext.AiWritingAssistantScreen
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.template.bean.JournalContentInfo
import com.tcl.ai.note.template.bean.PageConfigInfo
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getStatusBarHeight
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.px2dp
import com.tcl.ai.note.utils.screenSizeMax
import com.tcl.ai.note.utils.screenSizeMin
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.widget.HorizontalLine
import com.tcl.ai.note.widget.HoverProofIconButton
import com.tcl.ai.note.widget.IconThemeSwitcher
import com.tct.theme.core.designsystem.component.TclTopAppBar
import com.tct.theme.core.designsystem.theme.TclTheme
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.io.File
import java.net.URLDecoder
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

/**
 * @param configInfo 模板配置信息
 */
@Composable
fun JournalContentScreen(
    modifier: Modifier = Modifier,
    navController: NavController,
    journalId: Long,
    journalTitle: String,
    coverId: Long,
    textAndDrawViewModel: TextAndDrawViewModel = hiltViewModel(),
    viewModel: JournalContentViewModel = hiltViewModel(),
    isFullMode: Boolean,
    configInfo: PageConfigInfo? = null,
    onExit: () -> Unit = {
        navController.navigateUp()
    }
) {
    val coroutineScope = rememberCoroutineScope()
    // 编辑模式
    var editMode by remember { mutableStateOf(false) }
    var drawMode by remember { mutableStateOf(false) }
    val drawBoardMode by textAndDrawViewModel.editModeState.collectAsState()
    // 是否显示更多菜单弹窗
    var isDropdownMenuExpanded by remember { mutableStateOf(false) }
    // 是否显示全部页面弹层
    var showManagePagesScreen by remember { mutableStateOf(false) }
    // 是否显示删除page数据弹框
    var showDeletePageDialog by remember { mutableStateOf(false) }
    // 是否显示分享page数据弹框
    var showSharePageDialog by remember { mutableStateOf(false) }
    // 是否显示停止AI智能生成弹框
    var showStopAIGenerationDialog by remember { mutableStateOf(false) }
    // 是否显示AI帮写弹窗
    var showAiWritingAssistantDialog by remember { mutableStateOf(false) }
    // 是否显示完整的标题
    var showFullTitleScreen by remember { mutableStateOf(false) }
    // 是否显示修改日记名称对话框
    var showChangeJournalNameDialog by remember { mutableStateOf(false) }
    // 是否显示模板创建弹框
    var showTemplateCreateDialog by remember { mutableStateOf(false) }
    // 是否显示悬浮按钮“AI重新生成文案"
    val showFloatingButton by viewModel.showFloatingButton.collectAsState()
    // 日记内容数据
    val content by viewModel.content.collectAsState()
    // 总页数
    val totalPages by remember(content) { mutableIntStateOf(content.pageInfos.size) }
    // 当前页面下标
    var pageIndex by remember(content) { mutableIntStateOf(content.lastPageIndex) }
    // 当前页面信息
    val curPageInfo by remember(content, pageIndex) {
        mutableStateOf(content.pageInfos.getOrNull(pageIndex))
    }
    // 模板数据
    val templateData by viewModel.templateData.collectAsState()
    val aiCreating by viewModel.aiCreating.collectAsState()
    val drawFinish by viewModel.drawFinish.collectAsState()
    val canUndo by viewModel.canUndoState.collectAsState()
    val canRedo by viewModel.canRedoState.collectAsState()
    val isContentEmpty by viewModel.isContentEmpty.collectAsState()
    val latestPageIndex by rememberUpdatedState(pageIndex)
    val statusBarHeight = getStatusBarHeight()
    val topBarHeight = remember { 56.dp }
    val context = LocalContext.current
    // 是否展示翻页组件
    var showFlip by remember { mutableStateOf(isFullMode) }
    val lifecycleOwner = LocalLifecycleOwner.current
    val imageLoader = context.imageLoader
    var forceUpdatePageIndex by remember { mutableStateOf<Int?>(null) }
    var forceUpdateTimeMillis by remember { mutableStateOf<Long?>(null) }
    val engineInitComplete by viewModel.engineInitComplete.collectAsState()

    var popupContent: (@Composable (areaHeight:Int) -> Unit)? by remember { mutableStateOf(null) }

    var editpageflippingDialog by remember { mutableStateOf(false) }
    // 新手指引
    var beginnerGuide by remember { mutableStateOf(false) }
    // 第一次启动时新手指引是否展示过
    LaunchedEffect(Unit) {
//        val alreadyShown = isGuideShown(context)
//        beginnerGuide = !alreadyShown
    }
    val immersiveActivity = remember { context as? Activity }
    // 是否处于沉浸式，默认为true
    var immersiveMode by remember { mutableStateOf(true) }
    val configuration = LocalConfiguration.current
    val screenWidthDp = configuration.screenWidthDp
    // 模板创建弹窗操作类型 新建/重新生成
    var operationType by remember { mutableStateOf(DataStoreParam.OPERATION_TYPE_ADD) }
    // 是否显示敏感图片提示弹框
    val showSensitiveImageDialog by viewModel.showSensitiveImageDialog.collectAsState()
    val sensitiveImagePath by viewModel.sensitiveImagePath.collectAsState()
    // 首次进页面自动沉浸
//    LaunchedEffect(Unit) {
//        immersiveActivity?.let {
//            ImmersiveModeUtils.enableImmersive(it)
//            immersiveMode = true
//        }
//    }
    fun isTalkBackEnabled(context: Context): Boolean {
        val manager = context.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
        return manager.isTouchExplorationEnabled
    }
    val isTalkBackOn = remember { isTalkBackEnabled(context) }
    // 沉浸状态变化时实际切换
    LaunchedEffect(immersiveMode) {
        immersiveActivity?.let {
            if (immersiveMode) {
                ImmersiveModeUtils.enableImmersive(it)
            } else {
                ImmersiveModeUtils.disableImmersive(it)
            }
        }
    }
    // 根据 totalPages 控制沉浸式模式
    LaunchedEffect(totalPages, isTalkBackOn) {
        if (!aiCreating && !showFloatingButton && totalPages > 0){
            val alreadyShown = isGuideShown(context)
            beginnerGuide = !alreadyShown
        }
        immersiveActivity?.let {
            if (totalPages == 0 || isTalkBackOn) {
                immersiveMode = false
                ImmersiveModeUtils.disableImmersive(it) // 禁用沉浸式模式
            } else {
                immersiveMode = true // 启用沉浸式模式
                ImmersiveModeUtils.enableImmersive(it) // 启用沉浸式模式
            }
        }
    }
    // 监听返回时的刷新标志
    val savedStateHandle = navController.currentBackStackEntry?.savedStateHandle
    LaunchedEffect(Unit) {
        savedStateHandle?.get<String>("configInfoJson")?.let { configInfoJson ->
            Logger.d("configInfoJson:", configInfoJson)
            val json = URLDecoder.decode(configInfoJson, "UTF-8")
            val info = Gson().fromJson(json, PageConfigInfo::class.java)
            viewModel.initData(context, journalId, info)
            savedStateHandle.remove<String>("configInfoJson") // 清除标志
        } ?: run {
            viewModel.customJournalName = journalTitle
            viewModel.initData(context, journalId, if (viewModel.hasInit) null else configInfo)
            if (!viewModel.hasInit) {
                viewModel.hasInit = true
            }
        }
    }
    DisposableEffect(Unit) {
        onDispose {
            viewModel.resetData()
            immersiveActivity?.let {
                if (immersiveMode) {
                    ImmersiveModeUtils.disableImmersive(it)
                }
            }
        }
    }
    LaunchedEffect(isFullMode) {
//        delay(100)
        showFlip = isFullMode
    }
    LaunchedEffect(editMode, pageIndex) {
        viewModel.updateUndoRedoState(canUndo = false, canRedo = false)
    }
    LaunchedEffect(Unit) {
        viewModel.forceUpdatePageIndex.collect {
            // 清除内存缓存
            val path = getJournalContentThumbnailPath(journalId, it)
            imageLoader.memoryCache?.remove(Key(path))
            // 清除磁盘缓存（要用协程！）
            imageLoader.diskCache?.remove(path)
            forceUpdatePageIndex = it
            forceUpdateTimeMillis = System.currentTimeMillis()
        }
    }
    LaunchedEffect(journalId, curPageInfo, templateData) {
        if (curPageInfo == null) {
            viewModel.setJournalContentInfo(null)
            viewModel.replaceText(emptyList())
        } else {
            val isEntFileExist = File(curPageInfo?.entFilePath ?: "").exists()
            if (!isEntFileExist && templateData != null) {
                viewModel.setAICreatingState(true)
                viewModel.handleEvent(
                    JournalContentIntent.UpdateJournalModifyTime(
                        journalId,
                        System.currentTimeMillis()
                    )
                )
            }
            curPageInfo?.entFilePath?.let {
                viewModel.setJournalContentInfo(
                    JournalContentInfo(
                        journalId = journalId,
                        pageIndex = curPageInfo?.pageId ?: 0,
                        statusBarHeight = statusBarHeight.value,
                        topBarHeight = topBarHeight.value,
                        templateData = isEntFileExist.judge(null, templateData),
                        entFile = curPageInfo?.entFilePath
                    )
                )
            }
        }
    }
    fun saveData() {
        Logger.d(
            "tcl",
            "onDispose: journalId = $journalId, content.contentId = ${content.contentId}, pageIndex = $latestPageIndex"
        )
        if (content.contentId == 0L) {
            viewModel.handleEvent(
                JournalContentIntent.InsertContent(
                    content.copy(
                        journalId = journalId,
                        contentId = 0L,
                        lastPageIndex = latestPageIndex
                    )
                )
            )
        } else {
            viewModel.handleEvent(JournalContentIntent.UpdateContent(content.copy(lastPageIndex = latestPageIndex)))
        }
    }

    DisposableEffect(lifecycleOwner) {
        // 生命周期回调
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_PAUSE) {
                saveData()
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    LaunchedEffect(drawBoardMode) {
        if (drawBoardMode != EditMode.DRAW) {
            drawMode = false
        }
    }
    if (showSensitiveImageDialog) {
        SensitiveImageDialog(
            imagePath = sensitiveImagePath,
            onDismissRequest = { viewModel.dismissSensitiveImageDialog() },
            onRegenerate = {
                viewModel.dismissSensitiveImageDialog()
                operationType = DataStoreParam.OPERATION_TYPE_SENSITIVE
                showTemplateCreateDialog = true
            }
        )
    }
    fun backAction() {
        if (aiCreating && viewModel.curConfigInfo != null) {
            showStopAIGenerationDialog = true
        } else {
            Logger.d("tcl", "engineInitComplete: $engineInitComplete")
            if (engineInitComplete) {
                viewModel.closeFloatingButton(closeByUser = true)
                onExit()
            }
        }
    }

    val photoPermissionHelper = PhotosPermissionHelper(onPermissionResult = {
        //新建页面时把现有的数据内容先保存
        if (operationType == DataStoreParam.OPERATION_TYPE_ADD) {
            saveData()
        }
        showTemplateCreateDialog = true
    })

    Box(modifier = modifier) {
        BackHandler {
            backAction()
        }

        ContentScreen(
            pageInfo = curPageInfo,
            modifier = Modifier
                .fillMaxSize()
        ) {
            Box(
                modifier = Modifier
                    .align(Alignment.Center)
                    .width((screenSizeMin * textAndDrawViewModel.scale).toInt().px2dp.dp)
                    .height((screenSizeMax * textAndDrawViewModel.scale).toInt().px2dp.dp)
            ) {
                curPageInfo?.bgImageResId?.let {
                    runCatching {
                        Image(
                            painter = painterResource(id = it),
                            contentDescription = null,
                            modifier = Modifier.fillMaxSize(),
                            contentScale = ContentScale.Crop,
                            alignment = Alignment.Center // 确保顶部对齐，优先显示顶部内容
                        )
                    }
                }
            }

            val pathName = curPageInfo?.bitmapFilePath.orEmpty()
            if (File(pathName).exists()) {
                AsyncImage(
                    model = pathName,
                    contentDescription = null,
                    modifier = Modifier
                        .fillMaxSize(),
                    contentScale = ContentScale.FillWidth,
                    alignment = Alignment.TopCenter
                )
            }

            if (isFullMode) {
                SuniaDrawBoard(
                    modifier = Modifier
                        .fillMaxSize(),
                    textAndDrawViewModel = textAndDrawViewModel,
                    suniaDrawViewModel = viewModel,
                )
                if (editMode) {
                    val matrixInfo by textAndDrawViewModel.matrixInfoState.collectAsState()
                    // 缩放指示，不能使用popup，会导致手绘层bug
                    ScaleIndicator(
                        scaleState = matrixInfo.scale,
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(top = getStatusBarHeight() + 56.dp),
                    )
                }
            }
        }

        if (!editMode && totalPages > 0 && drawFinish && showFlip && !aiCreating && !showFloatingButton) {
            var state by remember(totalPages, pageIndex) {
                mutableStateOf(
                    PageFlipViewState(
                        pageCount = totalPages,
                        currentPage = pageIndex
                    )
                )
            }
            // 首次进入如果处于新手指引页面
            if (beginnerGuide && !showFloatingButton && !aiCreating && totalPages > 0) {
                BeginnerGuideOverlay(
                    onDismiss = {
                        // 关闭时写入永久不再展示
                        coroutineScope.launch {
                            setGuideShown(context, true)
                            beginnerGuide = false
                        }
                    },
                    leftDrawable = R.drawable.previous_page,
                    centerDrawable = R.drawable.call_out_toolbar,
                    rightDrawable = R.drawable.next_page
                )
            } else {
                PageFlipView(
                    modifier = Modifier.fillMaxSize(),
                    state = state
                ) {
                    onTurnPageRequest { eventType, currentPage, isNextOrPrevious, success ->
                        if (!success) {
                            if (isNextOrPrevious) {
                                Toast.makeText(
                                    context,
                                    context.getString(R.string.already_last_page),
                                    Toast.LENGTH_SHORT
                                ).show()
                                Logger.d("tcl", "已经是最后一页啦")
                                /*if (eventType != EventType.Tap) {
                                    photoPermissionHelper.invoke { }
                                }*/
                            } else {
                                Toast.makeText(
                                    context,
                                    context.getString(R.string.already_first_page),
                                    Toast.LENGTH_SHORT
                                ).show()
                                Logger.d("tcl", "已经是第一页啦")
                            }
                        } else {
                            state =
                                state.copy(currentPage = if (isNextOrPrevious) currentPage + 1 else currentPage - 1)
                            viewModel.clearTemplateData()
                            pageIndex = state.currentPage ?: 0
                        }
                    }

                    contents { currentPage, refresh ->
                        PageContent(
                            pageIndex = currentPage,
                            content = content,
                        )

                        refresh()
                    }

                    onCenterTap {
                        if (!editMode && !beginnerGuide) {
                            immersiveMode = !immersiveMode
                        }
                    }
                }
            }
        }

        if (!immersiveMode) {
            Image(
                painter = painterResource(id = R.drawable.bg_titlebar),
                contentDescription = null,
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.Crop,
                alignment = Alignment.TopCenter // 确保顶部对齐，优先显示顶部内容
            )
        }

        if (!editMode) {
            // 如果不处于沉浸式模式
            if (!immersiveMode && totalPages > 0) {
                val normalTitleModifier = Modifier
                    .fillMaxWidth()
                    .height(topBarHeight + statusBarHeight)
                    .zIndex(2f)
                NormalModeTitleBar(
                    modifier = (showManagePagesScreen || showTemplateCreateDialog).judge(normalTitleModifier.clearAndSetSemantics {  }, normalTitleModifier),
                    backClick = {
                        backAction()
                    },
                    addBlankPage = {
                        if (aiCreating && viewModel.curConfigInfo != null) {
                            showStopAIGenerationDialog = true
                        } else {
                            photoPermissionHelper.invoke { }
                        }
                    },
                    editThisPage = {
                        if (aiCreating && viewModel.curConfigInfo != null) {
                            showStopAIGenerationDialog = true
                        } else {
                            editMode = true
                            drawMode = true
                            textAndDrawViewModel.editMode = EditMode.DRAW
                            viewModel.setAutoSave(!showManagePagesScreen)
                            viewModel.closeFloatingButton(closeByUser = true)
                        }
                    },
                    onManagePages = {
                        isDropdownMenuExpanded = false
                        showManagePagesScreen = true
                        viewModel.setAutoSave(false)
                    }
                )
            }
        } else {
            EditModeTitleBar(
                modifier = (showManagePagesScreen || showTemplateCreateDialog).judge(Modifier.clearAndSetSemantics {  }, Modifier),
                title = viewModel.customJournalName,
                menuExpanded = isDropdownMenuExpanded,
                canDelete = totalPages >= 1,
                canUndo = canUndo,
                canRedo = canRedo,
                isContentEmpty = isContentEmpty,
                backClick = {
                    backAction()
                },
                titleClick = {
                    showFullTitleScreen = true
                },
                undo = {
                    viewModel.undo()
                },
                redo = {
                    viewModel.redo()
                },
                previewClick = {
                    curPageInfo?.entFilePath?.let {
                        viewModel.changeScaleRelative(MatrixInfo())
                        viewModel.changeScaleEnd(MatrixInfo())
                        coroutineScope.launch {
                            delay(200)
                            viewModel.saveJournalContent(journalId, curPageInfo?.pageId ?: 0)
                        }
                    }
                    editMode = false
                    drawMode = false
                    viewModel.setAutoSave(false)
                },
                menuDismissRequest = {
                    isDropdownMenuExpanded = false
                },
                onAddAPage = {
                    isDropdownMenuExpanded = false
                    photoPermissionHelper.invoke { }
                },
                onManagePages = {
                    isDropdownMenuExpanded = false
                    showManagePagesScreen = true
                    viewModel.setAutoSave(false)
                },
                onClearThisPage = {
                    isDropdownMenuExpanded = false
                    viewModel.clearAll()
                },
                onDeleteThisPage = {
                    isDropdownMenuExpanded = false
                    showDeletePageDialog = true
                },
                onShareThisPage = {
                    isDropdownMenuExpanded = false
                    showSharePageDialog = true
                }
            )
        }
        if (totalPages > 0 && !editMode) {
            PageNumber(pageIndex, totalPages, modifier = Modifier.align(Alignment.BottomEnd))
        } else {
            if (!aiCreating && !editMode && journalId > 0) {
                JournalEmptyContentScreen(
                    navController = navController,
                    addPageByTemplate = {
                        photoPermissionHelper.invoke { }
                    },
                    addBlankPage = {

                    },
                    onBack = {
                        backAction()
                    }
                )
            }
        }

        //IFA展只有手写功能，暂时只展示手写属性菜单
//        if (editMode) {
//            BottomToolbar(
//                modifier = Modifier.align(Alignment.BottomCenter),
//                onAddText = {},
//                onDrawClicked = { drawMode = true },
//                onAddPictureClicked = {},
//                onBackgroundClicked = {},
//                onTemplateClicked = {},
//                onPicToTextClicked = {}
//            )
//        }

        if (drawMode && !showManagePagesScreen && !showTemplateCreateDialog) {
            Box(modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .background(com.tcl.ai.note.theme.TclTheme.colorScheme.secondaryBackground)
                .navigationBarsPadding()) {
                JournalBottomMenuBar(
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.BottomCenter),
                    config = MenuBarConfig(
                        setPopupComposable = { popup -> popupContent = popup }
                    ),
                    suniaDrawViewModel = viewModel,
                    textAndDrawViewModel = textAndDrawViewModel
                )
            }
            EraserPressIndicator(suniaDrawViewModel = viewModel)
            popupContent?.let { popupComposable ->
                var containerHeight by remember { mutableIntStateOf(0) }
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .navigationBarsPadding()
                        .onSizeChanged { size ->
                            containerHeight = size.height
                        }
                ){
                    if(containerHeight > 0){
                        popupComposable((containerHeight - com.tcl.ai.note.theme.TclTheme.dimens.menuBarHeight.toPx).toInt())
                    }
                }
            }
        }

        if (aiCreating) {
            // 沉浸式展示
            if (!editMode) {
                if (!beginnerGuide) {
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .width((screenWidthDp / 3f).dp)
                            .align(Alignment.CenterStart)
                            .offset(x = (screenWidthDp / 3f).dp)
                            .pointerInput(Unit) {
                                detectTapGestures {
                                    immersiveMode = !immersiveMode
                                }
                            }
                            .background(Color.Transparent)
                    )
                }
            }
            AICreating(modifier = Modifier.align(Alignment.BottomCenter))
        }

        if (showFloatingButton) {
            val screenWidth = LocalConfiguration.current.screenWidthDp

            // 中间1/3 用于沉浸式
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .width((screenWidth / 3f).dp)
                    .align(Alignment.CenterStart)
                    .offset(x = (screenWidth / 3f).dp)
                    .pointerInput(immersiveMode) {
                        detectTapGestures {
                            immersiveMode = !immersiveMode
                        }
                    }
                    .pointerInput(showFloatingButton) {
                        detectHorizontalDragGestures { _, _ ->
                            editpageflippingDialog = true
                        }
                    }
                    .background(Color.Transparent)
                    .semantics {
                        contentDescription = ""
                    },
            )

            // 左1/3
            Box(
                Modifier
                    .fillMaxHeight()
                    .width((screenWidth / 3f).dp)
                    .align(Alignment.CenterStart)
                    .pointerInput(showFloatingButton) {
                        detectTapGestures {
                            editpageflippingDialog = true
                        }
                    }
                    .pointerInput(showFloatingButton) {
                        detectHorizontalDragGestures { _, _ ->
                            editpageflippingDialog = true
                        }
                    }
                    .background(Color.Transparent)
                    .semantics {
                        contentDescription = context.getString(R.string.previous_page)
                    },
            )
            // 右1/3
            Box(
                Modifier
                    .fillMaxHeight()
                    .width((screenWidth / 3f).dp)
                    .align(Alignment.CenterEnd)
                    .pointerInput(showFloatingButton) {
                        detectTapGestures {
                            editpageflippingDialog = true
                        }
                    }
                    .pointerInput(showFloatingButton) {
                        detectHorizontalDragGestures { _, _ ->
                            editpageflippingDialog = true
                        }
                    }
                    .background(Color.Transparent)
                    .semantics {
                        contentDescription = context.getString(R.string.next_page)
                    },
            )

            AIRegenerate(
                modifier = Modifier.align(Alignment.BottomCenter),
                onBtnClick = {
                    operationType = DataStoreParam.OPERATION_TYPE_REGENERATE
                    photoPermissionHelper.invoke { }
                    viewModel.triggerAiWritingReport()
                },
                onXClick = {
                    viewModel.triggerReport()
                    viewModel.closeFloatingButton(closeByUser = true)
                }
            )
        }
        if (editpageflippingDialog) {
            PageTurningDialog(
                text = stringResource(R.string.button_disappears),
                onCancel = { editpageflippingDialog = false },
                onYes = {
                    editpageflippingDialog = false
                    viewModel.closeFloatingButton(closeByUser = true)
                    viewModel.triggerReport()
                    editMode = false
                    drawMode = false
                    viewModel.setAutoSave(false)
                }
            )
        }

        if (showDeletePageDialog) {
            DeletePageDialog(
                text = stringResource(R.string.text_delete_page_instructions),
                onDelete = {
                    showDeletePageDialog = false
                    val list = content.pageInfos.filterNot {
                        curPageInfo?.pageId == it.pageId
                    }
                    viewModel.handleEvent(
                        JournalContentIntent.UpdateContent(
                            content.copy(
                                pageInfos = list,
                                lastPageIndex = 0
                            )
                        ) {
                            editMode = false
                            drawMode = false
                            viewModel.setAutoSave(false)
                        }
                    )
                },
                onDismiss = {
                    showDeletePageDialog = false
                }
            )
        }

        if (showSharePageDialog) {
            ShowShareMethodDialog(
                onDismiss = {
                    showSharePageDialog = false
                },
                onShareAsImage = {
                    showSharePageDialog = false
                    curPageInfo?.let {
                        viewModel.shareImages(context, listOf(it))
                    }
                },
                onShareAsPdf = {
                    showSharePageDialog = false
                    curPageInfo?.let {
                        viewModel.sharePdfs(context, viewModel.customJournalName, listOf(it))
                    }
                }
            )
        }

        if (showStopAIGenerationDialog) {
            TipDialog(
                text = stringResource(R.string.text_stop_ai_generation),
                onDismiss = {
                    showStopAIGenerationDialog = false
                },
                onOk = {
                    showStopAIGenerationDialog = false
                    viewModel.stopTravelDiaryGeneration(context)
                },
            )
        }

        if (showAiWritingAssistantDialog) {
            AiWritingAssistantScreen(
                navController = navController,
                journalId = journalId,
                journalTitle = viewModel.customJournalName,
                coverId = coverId,
                configInfoJson = viewModel.configInfoJson,
                imageGroups = viewModel.imageGroups,
                needNavigate = false,
            ) {
                showAiWritingAssistantDialog = false
            }
        }

        if (showFullTitleScreen) {
            FullTitleScreen(
                topPadding = topBarHeight + statusBarHeight + 8.dp,
                journalName = viewModel.customJournalName,
                containerClick = {
                    showFullTitleScreen = false
                },
                editClick = {
                    showFullTitleScreen = false
                    showChangeJournalNameDialog = true
                }
            )
        }

        if (showChangeJournalNameDialog) {
            InputDialog(
                title = stringResource(id = com.tcl.ai.note.base.R.string.title),
                text = viewModel.customJournalName,
                maxLength = 50,
                onValueChange = {

                },
                onConfirm = {
                    viewModel.customJournalName = it
                    viewModel.handleEvent(
                        JournalContentIntent.UpdateJournalTitle(
                            journalId,
                            viewModel.customJournalName
                        )
                    )
                    showChangeJournalNameDialog = false
                },
                onDismissRequest = {
                    showChangeJournalNameDialog = false
                }
            )
        }

        if (showTemplateCreateDialog) {
            TemplateCreateScreen(
                navController = navController,
                operationType = operationType,
                generateClick = { type, configInfoJson ->
                    showTemplateCreateDialog = false
                    val json = URLDecoder.decode(configInfoJson, "UTF-8")
                    val info = Gson().fromJson(json, PageConfigInfo::class.java)
                    viewModel.replaceText(emptyList())
                    if (operationType == DataStoreParam.OPERATION_TYPE_REGENERATE) {
                        val curEntFile = File(curPageInfo?.entFilePath ?: "")
                        if (curEntFile.exists()) {
                            curEntFile.delete()
                        }
                        if (viewModel.curConfigInfo != info) {
                            viewModel.setJournalContentInfo(null)
                            viewModel.initData(context, journalId, info, type)
                        }
                    } else {
                        viewModel.initData(context, journalId, info, type)
                    }
                    operationType = DataStoreParam.OPERATION_TYPE_ADD
                },
                backHandler = {
                    showTemplateCreateDialog = false
                },
                onDismissRequest = {
                    showTemplateCreateDialog = false
                }
            )
        }
    }
    if (showManagePagesScreen) {
        ManagePagesScreen(
            curPageIndex = pageIndex,
            pageInfos = content.pageInfos,
            forceUpdatePageIndex = forceUpdatePageIndex,
            forceUpdateTimeMillis = forceUpdateTimeMillis,
            onDismissRequest = {
                showManagePagesScreen = false
                viewModel.setAutoSave(editMode)
            },
            onPageClick = { index ->
                if (index != latestPageIndex) {
                    viewModel.clearTemplateData()
                    pageIndex = index
                }
            },
            deletePages = { pageInfos ->
                val list = content.pageInfos.filterNot {
                    pageInfos.contains(it)
                }
                viewModel.handleEvent(
                    JournalContentIntent.UpdateContent(
                        content.copy(
                            pageInfos = list,
                            lastPageIndex = 0
                        )
                    ) {
                        if (list.isEmpty()) {
                            editMode = false
                            drawMode = false
                            viewModel.setAutoSave(false)
                        }
                    }
                )
            },
            shareAsImagePages = { pageInfos ->
                viewModel.shareImages(context, pageInfos)
            },
            shareAsPdfPages = { pageInfos ->
                viewModel.sharePdfs(context, viewModel.customJournalName, pageInfos)
            }
        )
    }
}

@Composable
fun PageContent(
    pageIndex: Int,
    content: JournalContent,
) {
    if (pageIndex < content.pageInfos.size) {
        val pathName = content.pageInfos[pageIndex].bitmapFilePath
        if (File(pathName).exists()) {
            val bitmap = BitmapFactory.decodeFile(pathName)
            bitmap?.let {
                ContentScreen(
                    pageInfo = content.pageInfos[pageIndex],
                    modifier = Modifier
                        .fillMaxSize()
                ) {
                    content.pageInfos[pageIndex].bgImageResId?.let {
                        runCatching {
                            Image(
                                painter = painterResource(id = it),
                                contentDescription = null,
                                modifier = Modifier.fillMaxSize(),
                                contentScale = ContentScale.Crop,
                                alignment = Alignment.Center // 确保顶部对齐，优先显示顶部内容
                            )
                        }
                    }
                    Image(
                        bitmap = it.asImageBitmap(),
                        contentDescription = null,
                        modifier = Modifier
                            .fillMaxSize(),
                        contentScale = ContentScale.FillWidth,
                        alignment = Alignment.TopCenter
                    )
                }
            }
        }
    }
}

@SuppressLint("DesignSystem")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun NormalModeTitleBar(
    modifier: Modifier = Modifier,
    backClick: () -> Unit,
    addBlankPage: () -> Unit,
    editThisPage: () -> Unit,
    onManagePages: () -> Unit,
) {
    TclTopAppBar(
        title = "",
        modifier = modifier,
        navigationIcon = {
            IconButton(onClick = backClick) {
                Icon(
                    imageVector = when (LocalLayoutDirection.current) {
                        LayoutDirection.Ltr -> Icons.Filled.ArrowBackIosNew
                        LayoutDirection.Rtl -> Icons.Filled.ArrowForwardIos
                    },
                    contentDescription = stringResource(com.tct.theme.core.designsystem.R.string.back),
                    tint = Color(0xE6000000)
                )
            }
        },
        actions = {
            IconButton(onClick = { addBlankPage() }) {
                Icon(
                    painter = painterResource(id = com.tcl.ai.note.base.R.drawable.add_button_icon),
                    contentDescription = stringResource(R.string.title_add_blank_page),
                    tint = Color(0xE6000000)
                )
            }
            IconButton(onClick = { editThisPage() }) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_edit_page),
                    contentDescription = stringResource(R.string.text_edit_page),
                    tint = Color(0xE6000000)
                )
            }
            IconButton(onClick = { onManagePages() }) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_manage_pages),
                    contentDescription = stringResource(R.string.text_manage_pages),
                    tint = Color(0xE6000000)
                )
            }
        },
        isBackIcon = true,
    )
}

@SuppressLint("DesignSystem")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EditModeTitleBar(
    title: String,
    menuExpanded: Boolean,
    canDelete: Boolean,
    canUndo: Boolean,
    canRedo: Boolean,
    isContentEmpty: Boolean,
    modifier: Modifier = Modifier,
    backClick: () -> Unit,
    titleClick: () -> Unit,
    undo: () -> Unit,
    redo: () -> Unit,
    previewClick: () -> Unit,
    menuDismissRequest: () -> Unit,
    onAddAPage: () -> Unit,
    onManagePages: () -> Unit,
    onClearThisPage: () -> Unit,
    onDeleteThisPage: () -> Unit,
    onShareThisPage: () -> Unit,
) {
    Popup(properties = PopupProperties(
        dismissOnBackPress = false,
        dismissOnClickOutside = false,
        usePlatformDefaultWidth = false)
    ) {
        TclTopAppBar(
            title = "",
            modifier = modifier,
            navigationIcon = {
                IconButton(onClick = backClick) {
                    Icon(
                        imageVector = when (LocalLayoutDirection.current) {
                            LayoutDirection.Ltr -> Icons.Filled.ArrowBackIosNew
                            LayoutDirection.Rtl -> Icons.Filled.ArrowForwardIos
                        },
                        contentDescription = stringResource(com.tct.theme.core.designsystem.R.string.back),
                        tint = Color(0xE6000000)
                    )
                }
            },
            actions = {
                Box(modifier = Modifier
                    .padding(start = 56.dp)
                    .weight(1f)
                    .clickable {
                        titleClick()
                    }
                ) {
                    Text(
                        title,
                        maxLines = 1,
                        fontSize = 20.sp,
                        color = Color(0xE6000000),
                        overflow = TextOverflow.Ellipsis
                    )
                }

                IconButton(enabled = canUndo, onClick = { undo() }) {
                    Icon(
                        painter = painterResource(
                            id = if (canUndo) {
                                com.tcl.ai.note.base.R.drawable.ic_edit_undo
                            } else {
                                com.tcl.ai.note.base.R.drawable.ic_edit_undo_enable
                            }
                        ),
                        contentDescription = stringResource(com.tcl.ai.note.base.R.string.edit_top_menu_undo),
                        tint = Color(0xE6000000)
                    )
                }
                IconButton(enabled = canRedo, onClick = { redo() }) {
                    Icon(
                        painter = painterResource(
                            id = if (canRedo) {
                                com.tcl.ai.note.base.R.drawable.ic_edit_redo
                            } else {
                                com.tcl.ai.note.base.R.drawable.ic_edit_redo_enable
                            }
                        ),
                        contentDescription = stringResource(com.tcl.ai.note.base.R.string.edit_top_menu_redo),
                        tint = Color(0xE6000000)
                    )
                }
                IconButton(onClick = { previewClick() }) {
                    Icon(
                        painter = painterResource(id = com.tcl.ai.note.base.R.drawable.save_button_icon),
                        contentDescription = "Preview icon",
                        tint = Color(0xE6000000)
                    )
                }

                MoreDropdownMenu(
                    expanded = menuExpanded,
                    canDelete = canDelete,
                    isContentEmpty = isContentEmpty,
                    onDismissRequest = menuDismissRequest,
                    onAddAPage = onAddAPage,
                    onManagePages = onManagePages,
                    onClearThisPage = onClearThisPage,
                    onDeleteThisPage = onDeleteThisPage,
                    onShareThisPage = onShareThisPage
                )
            },
            isBackIcon = true,
        )
    }
}

@Composable
fun FullTitleScreen(
    topPadding: Dp,
    journalName: String,
    containerClick: () -> Unit,
    editClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .clickable { containerClick() },
        contentAlignment = Alignment.TopCenter
    ) {
        Row(
            modifier = Modifier
                .padding(
                    horizontal = 20.dp,
                    vertical = topPadding
                )
                .shadow(
                    elevation = 8.dp,
                    shape = RoundedCornerShape(20.dp), // 圆角可选
                    ambientColor = Color.Black.copy(alpha = 0.85f), // 阴影色
                    spotColor = Color.Black.copy(alpha = 0.85f) // 阴影色（Compose 1.2+ 支持）
                )
                .background(Color.White, shape = RoundedCornerShape(20.dp))
                .padding(start = 16.dp, end = 8.dp, top = 4.dp, bottom = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                journalName,
                modifier = Modifier.weight(1f),
                fontSize = 14.sp,
                color = Color.Black,
            )

            IconButton(modifier = Modifier
                .padding(start = 4.dp),
                onClick = {
                    editClick()
                }
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_edit_page),
                    contentDescription = stringResource(R.string.text_edit_page),
                    tint = Color(0xE6000000)
                )
            }
        }
    }
}

@SuppressLint("DesignSystem")
@Composable
fun MoreDropdownMenu(
    expanded: Boolean,
    canDelete: Boolean,
    isContentEmpty: Boolean,
    onDismissRequest: () -> Unit,
    onAddAPage: () -> Unit,
    onManagePages: () -> Unit,
    onClearThisPage: () -> Unit,
    onDeleteThisPage: () -> Unit,
    onShareThisPage: () -> Unit,
) {
    DropdownMenu(
        expanded = expanded,
        offset = DpOffset(x = (-24).dp, y = 12.dp),
        onDismissRequest = onDismissRequest,
        modifier = Modifier
            .width(226.dp)
            .clip(RoundedCornerShape(20.dp))
            .background(
                color = colorResource(com.tcl.ai.note.base.R.color.bg_dialog),
                shape = RoundedCornerShape(20.dp)
            ),
        shape = RoundedCornerShape(20.dp)
    ) {
        // 新增
        CustomDropdownMenuItem(
            painter = painterResource(id = com.tcl.ai.note.base.R.drawable.ic_category_add),
            contentDescription = stringResource(id = R.string.text_add_page),
            text = stringResource(id = R.string.text_add_page),
        ) {
            onAddAPage()
        }

        // 管理页面
        CustomDropdownMenuItem(
            painter = painterResource(id = R.drawable.ic_manage_pages),
            contentDescription = stringResource(id = R.string.text_manage_pages),
            text = stringResource(id = R.string.text_manage_pages),
        ) {
            onManagePages()
        }

        HorizontalLine()

        // 清空本页
        CustomDropdownMenuItem(
            enabled = !isContentEmpty,
            painter = painterResource(id = com.tcl.ai.note.base.R.drawable.ic_cancel_selected),
            contentDescription = stringResource(id = R.string.text_clear_page),
            text = stringResource(id = R.string.text_clear_page),
        ) {
            onClearThisPage()
        }

        // 删除本页
        CustomDropdownMenuItem(
            enabled = canDelete,
            painter = painterResource(id = com.tcl.ai.note.base.R.drawable.ic_delete_selected),
            contentDescription = stringResource(id = R.string.text_delete_page),
            text = stringResource(id = R.string.text_delete_page),
        ) {
            onDeleteThisPage()
        }

        HorizontalLine()

        // 分享本页
        CustomDropdownMenuItem(
            painter = painterResource(id = com.tcl.ai.note.base.R.drawable.ic_menu_share),
            contentDescription = stringResource(id = R.string.text_share_page),
            text = stringResource(id = R.string.text_share_page),
        ) {
            onShareThisPage()
        }
    }
}

@SuppressLint("DesignSystem")
@Composable
fun CustomDropdownMenuItem(
    enabled: Boolean = true,
    painter: Painter,
    contentDescription: String,
    text: String,
    onClick: () -> Unit,
) {
    DropdownMenuItem(
        enabled = enabled,
        colors = MenuDefaults.itemColors(
            leadingIconColor = TclTheme.colorScheme.tctStanderTextPrimary,
            textColor = TclTheme.colorScheme.tctStanderTextPrimary,
            disabledLeadingIconColor = TclTheme.colorScheme.tctStanderTextDisabled,
            disabledTextColor = TclTheme.colorScheme.tctStanderTextDisabled,
        ),
        modifier = Modifier.padding(horizontal = 4.dp),
        leadingIcon = {
            Icon(
                painter = painter,
                modifier = Modifier.size(24.dp),
                contentDescription = contentDescription,
            )
        },
        text = {
            Text(
                text,
                modifier = Modifier.padding(horizontal = 4.dp),
                fontSize = 14.sp,
                fontWeight = FontWeight.Normal,
            )
        },
        onClick = onClick
    )
}

@Composable
fun ContentScreen(
    pageInfo: PageInfo?,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(color = colorResource(pageInfo?.bgColorResId ?: R.color.white)),
    ) {
        content()
    }
}

@Composable
fun PageNumber(
    pageIndex: Int = 0,
    totalPages: Int = 0,
    modifier: Modifier = Modifier
) {
    Box(
        modifier
            .padding(end = 24.dp, bottom = 32.dp)
            .background(Color.Black.copy(alpha = 0.3f), RoundedCornerShape(254.dp))
            .clip(RoundedCornerShape(254.dp))
            .focusable(false)
            .clearAndSetSemantics {  }
    ) {
        Text(
            "${pageIndex + 1}/$totalPages",
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            fontSize = 12.sp,
            fontWeight = FontWeight.Normal,
            color = Color.White
        )
    }
}

@Composable
fun BottomToolbar(
    modifier: Modifier = Modifier,
    onAddText: () -> Unit,
    onDrawClicked: () -> Unit,
    onBackgroundClicked: () -> Unit,
    onAddPictureClicked: () -> Unit,
    onTemplateClicked: () -> Unit,
    onPicToTextClicked: () -> Unit
) {
    val dimens = getGlobalDimens()
    Box(
        modifier = modifier
            .fillMaxWidth()
            .background(color = com.tcl.ai.note.theme.TclTheme.colorScheme.secondaryBackground)
            .navigationBarsPadding()
            .height(dimens.menuBarHeight)
    ) {
        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.TopStart)
                .height(1.dp)
                .background(Color.Black.copy(alpha = 0.05f))
        )
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 12.dp),
            horizontalArrangement = Arrangement.SpaceAround, // 设置间距相等
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 文本框按钮
            IconThemeSwitcher(
                btnSize = dimens.btnSize,
                iconSize = dimens.iconSize,
                painter = painterResource(
                    false.judge(
                        R.drawable.ic_text_box_selected,
                        R.drawable.ic_text_box
                    )
                ),
                contentDescription = null,
                onClick = {
                    onAddText.invoke()
                }
            )
            // 手写按钮
            IconThemeSwitcher(
                btnSize = dimens.btnSize,
                iconSize = dimens.iconSize,
                painter = painterResource(R.drawable.ic_handwritten),
                contentDescription = null,
                onClick = {
                    onDrawClicked.invoke()
                }
            )
            // 背景按钮
            IconThemeSwitcher(
                btnSize = dimens.btnSize,
                iconSize = dimens.iconSize,
                painter = painterResource(
                    false.judge(
                        R.drawable.ic_background_selected,
                        R.drawable.ic_background
                    )
                ),
                contentDescription = null,
                onClick = {
                    onBackgroundClicked.invoke()
                }
            )
            // 图片按钮
            IconThemeSwitcher(
                btnSize = dimens.btnSize,
                iconSize = dimens.iconSize,
                painter = painterResource(
                    false.judge(
                        R.drawable.ic_picture_selected,
                        R.drawable.ic_picture
                    )
                ),
                contentDescription = null,
                onClick = {
                    onAddPictureClicked.invoke()
                }
            )
            // 模板按钮
            IconThemeSwitcher(
                btnSize = dimens.btnSize,
                iconSize = dimens.iconSize,
                painter = painterResource(
                    false.judge(
                        R.drawable.ic_template_selected,
                        R.drawable.ic_template
                    )
                ),
                contentDescription = null,
                onClick = {
                    onTemplateClicked.invoke()
                }
            )
            // 图生文按钮
            HoverProofIconButton(
                onClick = {
                    onPicToTextClicked.invoke()
                },
                enabled = true,
                modifier = modifier.size(dimens.btnSize)
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_image_to_text),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                )
            }
        }
        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(1.dp)
                .align(Alignment.BottomStart)
                .background(Color.Black.copy(alpha = 0.05f))
        )
    }
}

/**
 * AI 生成提示框
 */
@Composable
fun AICreating(modifier: Modifier = Modifier) {
    val viewHeight = LocalConfiguration.current.screenHeightDp.dp / 2
    // 创建一个无限动画
    val infiniteTransition = rememberInfiniteTransition(label = "rotate")
    val angle by infiniteTransition.animateFloat(
        initialValue = 0f,       // 起始角度
        targetValue = 360f,      // 终止角度（一圈）
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = LinearEasing), //2秒转一圈，匀速
            repeatMode = RepeatMode.Restart
        ),
        label = "angle"
    )

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(viewHeight)
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color(0x00FFFFFF),
                        Color(0x66FFFFFF),
                    ),
                    start = Offset(0f, 0f),
                    end = Offset(0f, viewHeight.toPx),
                )
            )
            .navigationBarsPadding(),
        contentAlignment = Alignment.BottomCenter
    ) {
        Box(
            modifier = Modifier
                .padding(bottom = 20.dp)
                .clip(RoundedCornerShape(313.dp))
        ) {
            Image(
                painter = painterResource(id = R.drawable.bg_ai_creating),
                contentDescription = null,
                modifier = Modifier.matchParentSize(),
                contentScale = ContentScale.Crop,
            )
            Row(
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 6.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_ai_creating),
                    modifier = Modifier
                        .size(36.dp)
                        .rotate(angle),
                    contentDescription = "Loading icon"
                )
                Text(
                    text = stringResource(id = R.string.ai_creating),
                    modifier = Modifier.padding(start = 12.dp),
                    color = colorResource(id = com.tcl.ai.note.base.R.color.image_menu_scale_selected),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal,
                )
            }
        }
    }
}

/**
 * 悬浮按钮“AI重新生成文案”
 */
@Composable
fun AIRegenerate(
    modifier: Modifier = Modifier,
    onBtnClick: () -> Unit,
    onXClick: () -> Unit
) {
    Row(
        modifier
            .navigationBarsPadding()
            .padding(bottom = 20.dp),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .padding(start = 10.dp)
                .weight(1f, false)
                .wrapContentHeight()
                .shadow(
                    elevation = 4.dp,
                    shape = RoundedCornerShape(313.dp), // 圆角可选
                    ambientColor = Color.Black.copy(alpha = 0.85f), // 阴影色
                    spotColor = Color.Black.copy(alpha = 0.85f) // 阴影色（Compose 1.2+ 支持）
                )
                .clip(RoundedCornerShape(313.dp))
                .clickable { onBtnClick() },
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = R.drawable.bg_ai_creating),
                contentDescription = null,
                modifier = Modifier.matchParentSize(),
                contentScale = ContentScale.Crop,
            )

            Text(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp),
                text = stringResource(id = R.string.ai_regenerate_the_copy),
                color = colorResource(id = com.tcl.ai.note.base.R.color.image_menu_scale_selected),
                fontSize = 14.sp,
                fontWeight = FontWeight.Normal,
            )
        }
    }
}

@Composable
fun BeginnerGuideOverlay(
    onDismiss: () -> Unit,
    leftDrawable: Int,
    centerDrawable: Int,
    rightDrawable: Int
) {
    val dividerWidth = 1.dp
    Box(
        Modifier
            .fillMaxSize()
            .background(Color(0x99000000))
            .clickable { onDismiss() }
    ) {
        // 分割线：绝对定位
        Box(Modifier.fillMaxSize()) {
            val screenWidth = LocalConfiguration.current.screenWidthDp.dp
            val density = LocalDensity.current

            val offset1 = with(density) { (screenWidth * 1 / 3).toPx() }
            val offset2 = with(density) { (screenWidth * 2 / 3).toPx() }

            // 第一根竖线，1/3屏宽
            Box(
                Modifier
                    .fillMaxHeight()
                    .width(dividerWidth)
                    .offset { IntOffset(offset1.toInt(), 0) }
                    .background(Color(0x99FFFFFF))
            )
            // 第二根竖线，2/3屏宽
            Box(
                Modifier
                    .fillMaxHeight()
                    .width(dividerWidth)
                    .offset { IntOffset(offset2.toInt(), 0) }
                    .background(Color(0x99FFFFFF))
            )
        }

        Row(
            Modifier.fillMaxSize(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            BeginnerGuideItem(
                painter = painterResource(leftDrawable),
                text = "Previous page",
                modifier = Modifier.weight(1f)
            )
            Spacer(Modifier.width(dividerWidth))
            BeginnerGuideItem(
                painter = painterResource(centerDrawable),
                text = "Call out toolbar",
                modifier = Modifier.weight(1f)
            )
            Spacer(Modifier.width(dividerWidth))
            BeginnerGuideItem(
                painter = painterResource(rightDrawable),
                text = "Next page",
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
private fun BeginnerGuideItem(
    painter: Painter,
    text: String,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painter,
                contentDescription = null,
                modifier = Modifier.size(60.dp)
            )
            Spacer(modifier = Modifier.height(12.dp))
            Text(
                text = text,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
        }
    }
}

@Composable
private fun VerticalDivider() {
    Box(
        Modifier
            .fillMaxHeight()
            .width(1.dp)
            .background(Color.White.copy(alpha = 0.25f))
    )
}
val Context.dataStore by preferencesDataStore(name = "guide_prefs")

object GuideKeys {
    val GUIDE_SHOWN = booleanPreferencesKey("guide_shown")
}

suspend fun setGuideShown(context: Context, shown: Boolean) {
    context.dataStore.edit { settings ->
        settings[GuideKeys.GUIDE_SHOWN] = shown
    }
}

suspend fun isGuideShown(context: Context): Boolean {
    val prefs = context.dataStore.data.first()
    return prefs[GuideKeys.GUIDE_SHOWN] ?: false
}
