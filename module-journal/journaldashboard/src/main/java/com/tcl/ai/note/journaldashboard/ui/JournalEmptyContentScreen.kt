package com.tcl.ai.note.journaldashboard.ui

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBackIosNew
import androidx.compose.material.icons.filled.ArrowForwardIos
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.isDensity440
import com.tcl.ai.note.utils.judge

@Composable
fun JournalEmptyContentScreen(
    navController: NavController,
    addPageByTemplate: () -> Unit = {},
    addBlankPage: () -> Unit = {},
    onBack: () -> Unit = {
        navController.navigateUp()
    }
) {
    val scrollState = rememberScrollState()
    Box(
        modifier = Modifier
            .background(color = TclTheme.colorScheme.tctGlobalBgColor)
            .fillMaxSize()
    ) {
        BackHandler { onBack() }
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(color = TclTheme.colorScheme.tctGlobalBgColor)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(scrollState),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                OperateLargeImage()
                CreationMethodView()
                AddPageByTemplate(addPageByTemplate = addPageByTemplate)
                if (!DataStoreParam.IS_IFA) {
                    AddBlankPage(addBlankPage = addBlankPage)
                }
            }

            TitleBar(backPress = { onBack() })
        }
    }
}

@SuppressLint("DesignSystem")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TitleBar(
    backPress: () -> Unit,
) {
    TopAppBar(
        title = {
            Box(modifier = Modifier.offset(x = (-8).dp)) {
                Text(
                    stringResource(R.string.title_add_a_page), maxLines = 1, overflow = TextOverflow.Ellipsis,
                    color = TclTheme.colorScheme.tctStanderTextPrimary,
                    fontSize = isDensity440.judge(22.sp, 20.sp),
                    fontWeight = FontWeight.Medium,
                )
            }
        },
        expandedHeight = isDensity440.judge(70.dp, 56.dp),
        navigationIcon = {
            IconButton(onClick = { backPress.invoke() }) {
                Icon(
                    imageVector = when (LocalLayoutDirection.current) {
                        LayoutDirection.Ltr -> Icons.Filled.ArrowBackIosNew
                        LayoutDirection.Rtl -> Icons.Filled.ArrowForwardIos
                    },
                    contentDescription = stringResource(com.tct.theme.core.designsystem.R.string.back),
                    tint = TclTheme.tclColorScheme.tctStanderTextPrimary
                )
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = Color.Transparent,
        ),
    )
}

@Composable
private fun OperateLargeImage() {
    Image(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(min = 440.dp)
            .aspectRatio(360f / 440f),
        painter = painterResource(id = R.drawable.img_operation_photo),
        contentDescription = null
    )
}

@Composable
private fun CreationMethodView() {
    Text(text = stringResource(id = R.string.creation_method), fontSize = 20.sp, modifier = Modifier.padding(top = 32.dp, bottom = 18.dp))
}

@Composable
private fun AddPageByTemplate(addPageByTemplate: () -> Unit = {},) {
    AddPageItem(
        headRes = com.tcl.ai.note.journaldashboard.R.drawable.ic_head_template,
        titleRes = R.string.add_page_by_template_title,
        summaryRes = R.string.add_page_by_template_summary,
        onItemClick = {
            addPageByTemplate.invoke()
        },
        tailRes = com.tcl.ai.note.journaldashboard.R.drawable.ic_tail_template,
    )
}

@Composable
private fun AddBlankPage(addBlankPage: () -> Unit = {},) {
    AddPageItem(
        headRes = com.tcl.ai.note.journaldashboard.R.drawable.ic_head_blank,
        titleRes = R.string.add_blank_page_title,
        summaryRes = R.string.add_blank_page_summary,
        onItemClick = {
            addBlankPage.invoke()
        },
        tailRes = com.tcl.ai.note.journaldashboard.R.drawable.ic_tail_blank
    )
}

@Composable
private fun AddPageItem(headRes: Int, titleRes: Int, summaryRes: Int, onItemClick: () -> Unit = {}, tailRes: Int) {
    val isDarkTheme = isSystemInDarkTheme()
    Row(
        modifier = Modifier
            .padding(horizontal = 12.dp, vertical = 6.dp)
            .fillMaxWidth()
            .heightIn(min = 72.dp)
            //.shadow(elevation = 1.dp, shape = RoundedCornerShape(20.dp))
            .clip(RoundedCornerShape(20.dp))
            .background(
                color = com.tct.theme.core.designsystem.theme.TclTheme.colorScheme.tctStanderBgLayout,
                shape = RoundedCornerShape(20.dp)
            )
            .clickable { onItemClick.invoke() },
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Image(
            modifier = Modifier
                .padding(start = 12.dp)
                .size(40.dp),
            painter = painterResource(id = headRes),
            contentDescription = null
        )

        Column(modifier = Modifier
            .padding(start = 16.dp, end = 12.dp)
            .weight(1f)) {
            Text(
                text = stringResource(titleRes),
                color = TclTheme.colorScheme.tctStanderTextPrimary,
                fontSize = isDensity440.judge(17.sp, 16.sp),
                lineHeight = isDensity440.judge(22.sp, 20.sp),
            )
            Text(
                text = stringResource(summaryRes),
                color = TclTheme.colorScheme.tctStanderTextSecondary,
                fontSize = isDensity440.judge(11.sp, 10.sp),
                lineHeight = isDensity440.judge(25.sp, 14.sp),
            )
        }

        Image(
            modifier = Modifier
                .padding(start = 12.dp, end = 12.dp)
                .size(32.dp),
            painter = painterResource(id = tailRes),
            contentDescription = null,
            alpha = if (isDarkTheme) 0.8f else 1f
        )
    }
}

@Preview
@Composable
private fun JournalEmptyContentScreenPreview() {
    JournalEmptyContentScreen(
        navController = NavController(context = androidx.compose.ui.platform.LocalContext.current)
    )
}