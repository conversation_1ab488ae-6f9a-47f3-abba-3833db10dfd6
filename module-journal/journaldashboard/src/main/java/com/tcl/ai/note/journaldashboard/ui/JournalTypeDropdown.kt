package com.tcl.ai.note.journaldashboard.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import com.tcl.ai.note.base.R
import com.tcl.ai.note.journalhome.entity.HomeCategoryItemEntity
import com.tcl.ai.note.journalhome.vm.state.HomeCategoryUiState
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.isDensity440
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.toPx

@Composable
internal fun JournalTypeDropdown(
    rowPosition: Offset,
    currentCategoryId: Long,
    onDismissRequest: () -> Unit,
    onTypeSelected:(HomeCategoryItemEntity) -> Unit = {},
    onNewCategoryClicked: () -> Unit,
    //dashboardModel: DashboardModel = hiltViewModel(),
    homeCategoryUiState: HomeCategoryUiState,
) {
    //val listNoteCategoryState by dashboardModel.listNoteCategoryState.collectAsState()
    var popupHeight by remember { mutableIntStateOf(960) }

    Popup(
        alignment = Alignment.TopEnd,
        offset = IntOffset(
            isDensity440.judge(-24.dp.toPx.toInt(), -24.dp.toPx.toInt()),
            (rowPosition.y - popupHeight).toInt()
        ),
        onDismissRequest = { onDismissRequest.invoke() },
        properties = PopupProperties(focusable = true),
    ) {
        Surface(
            elevation = 4.dp,
            shape = RoundedCornerShape(isDensity440.judge(22.dp, 20.dp)),
            modifier = Modifier.width(isDensity440.judge(190.dp, 174.dp)),
            color = Color.Transparent
        ) {
            Box(modifier = Modifier
                .width(190.dp)
                .clip(RoundedCornerShape(isDensity440.judge(22.dp, 20.dp)))
                .background(
                    colorResource(R.color.bg_dialog)
                )
                .onGloballyPositioned { coordinates ->
                    popupHeight = coordinates.size.height + 6.dp.toPx.toInt()
                },) {
                Column(modifier = Modifier) {
                    //Category list
                    LazyColumn(
                        modifier = Modifier
                            .heightIn(max = 292.dp)// 限制弹出菜单高度，使其可以滚动
                            .padding(horizontal = isDensity440.judge(22.dp, 20.dp), vertical = isDensity440.judge(8.dp, 4.dp)),
                    ) {
                        items(homeCategoryUiState.categories) { item ->
                            val name = item.name
                            if(name?.isNotEmpty() == true){
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = isDensity440.judge(9.dp, 10.dp))
                                        .clickable {
                                            onTypeSelected(item)
                                            onDismissRequest.invoke()
                                        }
                                ) {
                                    var  textColor = colorResource(R.color.text_title)
                                    if(item.id.toLong() == currentCategoryId){
                                        textColor = TclTheme.colorScheme.highlightColor
                                    }
                                    Text(
                                        text = name,
                                        color = textColor,
                                        fontSize = isDensity440.judge(16.sp, 14.sp),
                                        fontWeight = FontWeight.Medium
                                    )
                                    Spacer(modifier = Modifier.weight(1f))
                                    if (item.id.toLong() == currentCategoryId) {
                                        Image(painter = painterResource(id = com.tcl.ai.note.journaldashboard.R.drawable.ic_check),
                                            contentDescription = null)
                                    }
                                }
                            }
                        }
                    }

                    Divider(color = colorResource(R.color.bg_outline_10), thickness = 1.dp, modifier = Modifier.padding(horizontal = 20.dp))

                    //New Category
//                                val context = LocalContext.current
                    Box(
                        modifier = Modifier
                            .padding(start = 12.dp, end = 12.dp, top = 12.dp, bottom = 14.dp)
                            .fillMaxWidth()
                            .clickable {
                                onNewCategoryClicked.invoke()
                            }
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_category_add),
                                colorFilter = ColorFilter.tint(TclTheme.tclColorScheme.tctStanderTextPrimary),
                                contentDescription = null,
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = stringResource(R.string.dialog_category_name_title),
                                color = colorResource(R.color.text_title),
                                fontSize = isDensity440.judge(16.sp, 14.sp),
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }

        }
    }
}

@Preview
@Composable
fun JournalTypeDropdownPreview() {
    /*JournalTypeDropdown(
        currentCategoryId = 1L,
        rowPosition = Offset(0f, 0f),
        onDismissRequest = {},
        onTypeSelected = {},
        onNewCategoryClicked = {},
        homeCategoryUiState =
    )*/
}