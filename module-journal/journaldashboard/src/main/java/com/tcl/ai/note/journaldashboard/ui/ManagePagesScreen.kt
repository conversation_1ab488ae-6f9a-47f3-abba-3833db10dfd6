package com.tcl.ai.note.journaldashboard.ui

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import com.tcl.ai.note.database.entity.PageInfo
import com.tcl.ai.note.journalbase.BottomSheetDialog
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.utils.getNavBarHeight
import com.tcl.ai.note.utils.isDarkMode
import com.tcl.ai.note.utils.isDensity440
import com.tct.theme.core.designsystem.theme.TclTheme
import com.tcl.ai.note.utils.judge

/**
 * 页面管理弹层
 */
@SuppressLint("MutableCollectionMutableState")
@Composable
fun ManagePagesScreen(
    curPageIndex: Int,
    pageInfos: List<PageInfo>,
    forceUpdatePageIndex: Int?,
    forceUpdateTimeMillis: Long?,
    onDismissRequest: () -> Unit = {},
    onPageClick: (Int) -> Unit,
    deletePages: (List<PageInfo>) -> Unit,
    shareAsImagePages: (List<PageInfo>) -> Unit,
    shareAsPdfPages: (List<PageInfo>) -> Unit
) {
    val context = LocalContext.current
    val isDarkMode = isDarkMode(context)
    //是否处于编辑模式
    var editMode by remember { mutableStateOf(false) }
    // 选中的page数据
    var selectedPages by remember { mutableStateOf(mutableMapOf<Int, PageInfo>()) }
    // 选中的item数据在items中的下标值集合
    var selectedItemCounts by remember { mutableStateOf(setOf<Int>()) }
    // 列表长度
    val itemCount by remember(pageInfos.size) {
        mutableIntStateOf(pageInfos.size)
    }

    LaunchedEffect(pageInfos) {
        if (pageInfos.isEmpty()) {
            onDismissRequest() // 当 pageInfos 为空时，关闭页面
        }
    }

    // 是否全选
    var isSelectedAllMode by remember(selectedItemCounts, itemCount) {
        mutableStateOf(
            selectedItemCounts.size == itemCount
        )
    }
    // 是否显示删除page数据弹框
    var showDeletePageDialog by remember { mutableStateOf(false) }
    // 是否显示分享page数据弹框
    var showSharePageDialog by remember { mutableStateOf(false) }

    val screenWidthDp = LocalConfiguration.current.screenWidthDp.dp
    val pageWidth = remember {
        (screenWidthDp - (16 * 2).dp - (17 * 2).dp) / 3
    }
    val pageHeight = remember(pageWidth) {
        pageWidth / 98 * 150
    }
    val forceUpdateIndex by rememberUpdatedState(forceUpdatePageIndex)
    val forceUpdateTime by rememberUpdatedState(forceUpdateTimeMillis)

    BottomSheetDialog(
        modifier = Modifier,
        isDarkMode = isDarkMode,
        visible = true,
        onDismissRequest = {
            onDismissRequest()
        },
        canceledOnTouchOutside = false,
        extraTopPadding = true,
        isNavigationBarsPaddingNeeded = true,
        onDismissCallback = {},
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            if (!editMode) {
                NormalModeTopBar {
                    editMode = true
                }
            } else {
                EditModeTopBar(
                    selectedItemCount = selectedItemCounts,
                    isSelectedAllMode = isSelectedAllMode,
                    onCancel = {
                        // 取消已选中的回到显示模式
                        editMode = false
                        selectedItemCounts = emptySet()
                        selectedPages = mutableMapOf()
                        isSelectedAllMode = false
                    },
                    onSelectedAll = {
                        // 全选
                        isSelectedAllMode = !isSelectedAllMode
                        selectedItemCounts = emptySet()
                        val tempSelectedPages = mutableMapOf<Int, PageInfo>()
                        if (isSelectedAllMode) {
                            pageInfos.forEachIndexed { index, pageInfo ->
                                tempSelectedPages[index] = pageInfo
                                selectedItemCounts = selectedItemCounts + index
                            }
                        }
                        selectedPages = tempSelectedPages
                    }
                )
            }

            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                modifier = Modifier
                    .weight(1f)
                    .padding(bottom = getNavBarHeight()),
                contentPadding = PaddingValues(horizontal = 16.dp, vertical = 24.dp),
                horizontalArrangement = Arrangement.spacedBy(17.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                items(itemCount) { index ->
                    Column(
                        Modifier
                            .width(pageWidth)
                            .wrapContentHeight(),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Box(
                            modifier = Modifier
                                .width(pageWidth)
                                .height(pageHeight)
                        ) {
                            PageItem(
                                curPageIndex = curPageIndex,
                                forceUpdateIndex = forceUpdateIndex,
                                forceUpdateTime = forceUpdateTime,
                                editMode = editMode,
                                isSelected = selectedPages.contains(index),
                                index = index,
                                pageInfos = pageInfos,
                                onClick = { index ->
                                    if (editMode) {
                                        selectedItemCounts =
                                            if (selectedItemCounts.contains(index)) {
                                                selectedItemCounts - index
                                            } else {
                                                selectedItemCounts + index
                                            }
                                        val tempSelectedPages = mutableMapOf<Int, PageInfo>()
                                        tempSelectedPages.putAll(selectedPages)
                                        if (tempSelectedPages.contains(index)) {
                                            tempSelectedPages.remove(index)
                                        } else {
                                            tempSelectedPages[index] = pageInfos[index]
                                            //onPageClick(index)
                                        }
                                        selectedPages = tempSelectedPages
                                    } else {
                                        onPageClick(index)
                                    }
                                },
                                onLongClick = { index ->
                                    if (!editMode) {
                                        editMode = true

                                        if (!selectedItemCounts.contains(index)) {
                                            selectedItemCounts = selectedItemCounts + index
                                        }
                                        val tempSelectedPages = mutableMapOf<Int, PageInfo>()
                                        tempSelectedPages[index] = pageInfos[index]
                                        selectedPages = tempSelectedPages
                                    }
                                },
                            )
                        }

                        Text(
                            text = (index + 1).toString(),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Normal,
                            modifier = Modifier.padding(top = 8.dp),
                            color = TclTheme.colorScheme.tctStanderTextPrimary,
                        )
                    }
                }
            }

            if (editMode) {
                BottomBar(
                    deleteEnabled = isSelectedAllMode || !selectedItemCounts.isEmpty() && pageInfos.size >= 1,
                    shareEnabled = selectedItemCounts.isNotEmpty(),
                    onDeleteClick = {
                        showDeletePageDialog = true
                    },
                    onShareClick = {
                        showSharePageDialog = true
                    }
                )
            }

            if (showDeletePageDialog) {
                DeletePageDialog(
                    text = stringResource(R.string.text_delete_page_instructions),
                    onDelete = {
                        showDeletePageDialog = false
                        // 删除选中的items
                        deletePages(selectedPages.values.toList())
                        editMode = false
                        selectedItemCounts = emptySet()
                        selectedPages = mutableMapOf()
                        isSelectedAllMode = false
                    },
                    onDismiss = {
                        showDeletePageDialog = false
                    }
                )
            }

            if (showSharePageDialog) {
                ShowShareMethodDialog(
                    onDismiss = {
                        showSharePageDialog = false
                    },
                    onShareAsImage = {
                        showSharePageDialog = false
                        // 分享选中的items
                        shareAsImagePages(selectedPages.values.toList())
                        editMode = false
                        selectedItemCounts = emptySet()
                        selectedPages = mutableMapOf()
                        isSelectedAllMode = false
                    },
                    onShareAsPdf = {
                        showSharePageDialog = false
                        // 分享选中的items
                        shareAsPdfPages(selectedPages.values.toList())
                        editMode = false
                        selectedItemCounts = emptySet()
                        selectedPages = mutableMapOf()
                        isSelectedAllMode = false
                    }
                )
            }
        }
    }
}

@Composable
private fun NormalModeTopBar(onSelect: () -> Unit) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(isDensity440.judge(65.dp, 56.dp)),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = stringResource(id = R.string.title_manage_pages),
            fontSize = isDensity440.judge(22.sp, 20.sp),
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(start = isDensity440.judge(26.dp, 24.dp)).focusable(),
            color = TclTheme.colorScheme.tctStanderTextPrimary,
        )
        Button(
            onClick = onSelect,
            colors = ButtonDefaults.buttonColors(
                containerColor = Color.Transparent,
                contentColor = TclTheme.colorScheme.tctStanderTextPrimary,
            )
        ) {
            Text(
                text = stringResource(id = R.string.text_select),
                fontSize = isDensity440.judge(17.sp, 16.sp),
                fontWeight = FontWeight.Normal,
                modifier = Modifier.padding(
                    end = isDensity440.judge(2.dp, 4.dp)
                ),
            )
        }
    }
}

@Composable
private fun EditModeTopBar(
    isSelectedAllMode: Boolean,
    selectedItemCount: Set<Int>,
    onCancel: () -> Unit,
    onSelectedAll: () -> Unit,
) {
    val context = LocalContext.current
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(isDensity440.judge(65.dp, 56.dp))
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 取消已选中的回到显示模式
        IconButton(onClick = onCancel) {
            Icon(
                painter = painterResource(id = com.tcl.ai.note.base.R.drawable.ic_cancel_selected),
                contentDescription = stringResource(id = com.tcl.ai.note.base.R.string.cancel),
                tint = TclTheme.colorScheme.tctStanderTextPrimary,
            )
        }
        val selectedCountStr by remember(selectedItemCount) {
            mutableStateOf(
                String.format(
                    context.getString(R.string.manage_pages_title_selected),
                    selectedItemCount.size
                )
            )
        }
        Text(
            text = selectedCountStr,
            fontSize = isDensity440.judge(22.sp, 20.sp),
            fontWeight = FontWeight.Medium,
            color = TclTheme.colorScheme.tctStanderTextPrimary,
            modifier = Modifier
                .weight(1f)
                .padding(start = isDensity440.judge(8.dp, 16.dp))
        )

        // 全选
        IconButton(
            onClick = onSelectedAll,
        ) {
            Image(
                painter = painterResource(
                    id = isSelectedAllMode.judge(
                        com.tcl.ai.note.base.R.drawable.ic_selected_all_checked,
                        com.tcl.ai.note.base.R.drawable.ic_selected_all
                    )
                ),
                contentDescription = stringResource(id = com.tcl.ai.note.base.R.string.item_top_check_all),
            )
        }
    }
}

@Composable
fun PageItem(
    curPageIndex: Int,
    forceUpdateIndex: Int?,
    forceUpdateTime: Long?,
    editMode: Boolean = false,
    isSelected: Boolean = false,
    index: Int,
    pageInfos: List<PageInfo>,
    onClick: (Int) -> Unit = {},
    onLongClick: (Int) -> Unit = {},
) {
    val pageInfo = pageInfos[index]
    Box(
        modifier = Modifier
            .fillMaxSize()
            .clip(RoundedCornerShape(12.dp))
            .border(
                (!editMode && curPageIndex == index).judge(2.dp, 1.dp),
                (!editMode && curPageIndex == index).judge(Color(0xff155BF0), Color(0x334983FF)),
                RoundedCornerShape(12.dp)
            )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(color = colorResource(pageInfo.bgColorResId ?: R.color.white))
                .focusable(true)
                .semantics {
                    contentDescription = "Page ${index + 1}"
                    role = Role.Button
                },
        ) {
            pageInfo.bgImageResId?.let {
                Image(
                    painter = painterResource(id = it),
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop,
                )
            }
            key((index == forceUpdateIndex).judge(forceUpdateTime, pageInfo.bitmapFilePath)) {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(pageInfo.bitmapFilePath)
                        .diskCacheKey(pageInfo.bitmapFilePath)
                        .memoryCacheKey(pageInfo.bitmapFilePath)
                        .build(),
                    contentDescription = null,
                    modifier = Modifier
                        .fillMaxSize()
                        .pointerInput(curPageIndex, pageInfos.size, pageInfo.bitmapFilePath) {
                            detectTapGestures(
                                onTap = {
                                    onClick(index)
                                },
                                onLongPress = {
                                    onLongClick(index)
                                }
                            )
                        },
                    contentScale = ContentScale.Crop
                )
            }
        }
        if (editMode) {
            Image(
                modifier = Modifier
                    .padding(top = 8.dp, start = 8.dp)
                    .size(20.dp)
                    .align(Alignment.TopStart),
                painter = painterResource(
                    id = if (isSelected) {
                        R.drawable.ic_check_selected
                    } else {
                        R.drawable.ic_check_unselected
                    }
                ),
                contentDescription = "Selected",
            )
        }
    }
}

@Composable
fun NoScaleText(content: @Composable () -> Unit) {
    val currentDensity = LocalDensity.current
    CompositionLocalProvider(
        LocalDensity provides object : Density by currentDensity {
            override val fontScale: Float
                get() = 1f
        }
    ) {
        content()
    }
}

@Composable
fun BottomBar(
    deleteEnabled: Boolean = false,
    shareEnabled: Boolean = false,
    onDeleteClick: () -> Unit,
    onShareClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(TclTheme.colorScheme.tctStanderBgDialog)
            .navigationBarsPadding()
            .wrapContentHeight(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        OperationItem(
            modifier = Modifier.weight(1f),
            enabled = deleteEnabled,
            painter = painterResource(id = com.tcl.ai.note.base.R.drawable.ic_delete_selected),
            contentDescription = "delete icon",
            name = stringResource(id = com.tcl.ai.note.base.R.string.delete)
        ) {
            onDeleteClick()
        }

        OperationItem(
            modifier = Modifier.weight(1f),
            enabled = shareEnabled,
            painter = painterResource(id = com.tcl.ai.note.base.R.drawable.ic_menu_share),
            contentDescription = "share icon",
            name = stringResource(id = com.tcl.ai.note.base.R.string.show_content_share)
        ) {
            onShareClick()
        }
    }
}

@Composable
fun OperationItem(
    modifier: Modifier = Modifier,
    enabled: Boolean = false,
    painter: Painter,
    contentDescription: String,
    name: String,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        modifier = modifier.padding(vertical = 11.dp),
        enabled = enabled,
        colors = ButtonDefaults.buttonColors(
            containerColor = Color.Transparent,
            contentColor = TclTheme.colorScheme.tctStanderTextPrimary,
            disabledContainerColor = Color.Transparent,
            disabledContentColor = TclTheme.colorScheme.tctStanderTextDisabled
        )
    ) {
        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                modifier = Modifier
                    .size(24.dp),
                painter = painter,
                contentDescription = contentDescription
            )

            Text(
                text = name,
                fontSize = 12.sp,
                fontWeight = FontWeight.Normal,
                modifier = Modifier.padding(top = isDensity440.judge(3.dp, 2.dp))
            )
        }
    }
}