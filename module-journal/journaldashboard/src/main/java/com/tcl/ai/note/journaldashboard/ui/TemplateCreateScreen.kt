package com.tcl.ai.note.journaldashboard.ui

import android.annotation.SuppressLint
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.journalbase.BottomSheetDialog
import com.tcl.ai.note.theme.NoteTclTheme
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.ui.layout.ContentScale
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.fontscaling.MathUtils.lerp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.inspiration.ui.MomentScreen
import com.tcl.ai.note.inspiration.viewmodel.AddPageViewModel
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.picturetotext.AIWritingScreen
import com.tcl.ai.note.template.ui.SelectPhotosScreen
import com.tcl.ai.note.template.ui.TemplateScreen
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.widget.MarqueeText
import androidx.navigation.NavController
import coil3.compose.rememberAsyncImagePainter
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.journaldashboard.bean.BigImageInfo
import com.tcl.ai.note.journaldashboard.vm.TemplateCreateViewModel
import com.tcl.ai.note.picturetotext.newPageStartTime
import com.tcl.ai.note.utils.judge
import com.tct.theme.core.designsystem.theme.TclTheme

@Composable
fun TemplateCreateScreen(
    navController: NavController,
    operationType: String = DataStoreParam.OPERATION_TYPE_ADD,
    viewModel: TemplateCreateViewModel = hiltViewModel(),
    addPageViewModel: AddPageViewModel = hiltViewModel(),
    darkTheme: Boolean = isSystemInDarkTheme(),
    generateClick: (String, String) -> Unit,
    backHandler: () -> Unit,
    onDismissRequest: () -> Unit = {},
) {
    LaunchedEffect(operationType) {
        newPageStartTime = System.currentTimeMillis()
        if (operationType == DataStoreParam.OPERATION_TYPE_ADD || operationType == DataStoreParam.OPERATION_TYPE_SENSITIVE) {
            viewModel.resetData()
        }
    }
    val steps = listOf(
        R.string.step_moment.stringRes(),
        R.string.step_select_photos.stringRes(),
        R.string.step_template.stringRes(),
        R.string.step_ai_writing.stringRes(),
    )

    val context = LocalContext.current
    // 是否全选
    var isSelectedAllMode by remember(viewModel.albumImagePaths, viewModel.selectedImages) {
        mutableStateOf(
            viewModel.albumImagePaths.size == viewModel.selectedImages.size
                    || viewModel.selectedImages.size == 9
        )
    }
    // 是否展示大图
    var showBigImage by remember { mutableStateOf(false) }
    //屏幕尺寸大小
    val screenSize = remember {
        val displayMetrics = context.resources.displayMetrics
        IntSize(displayMetrics.widthPixels, displayMetrics.heightPixels)
    }
    var info by remember { mutableStateOf(BigImageInfo()) }

    NoteTclTheme {
        Box(modifier = Modifier.fillMaxSize()) {
            BottomSheetDialog(
                modifier = Modifier,
                visible = true,
                onDismissRequest = {
                    onDismissRequest()
                },
                canceledOnTouchOutside = false,
                showFullScreenCallBack = {

                },
                backHandler = {
                    backHandler()
                },
                bgColor = darkTheme.judge(
                    Color(0xFF0F121B),
                    Color(0xFFF5F5F5),
                ),
                horizontalDividerColor = darkTheme.judge(
                    Color.White.copy(alpha = 0.2f),
                    Color.White,
                ),
                topBgResId = R.drawable.template_create_top_bg,
                extraTopPadding = true,
                isNavigationBarsPaddingNeeded = true,
                onDismissCallback = {

                },
            ) {
                Column(
                    modifier = Modifier.fillMaxSize(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    //步骤条
                    Stepper(
                        steps = steps,
                        activeStep = viewModel.activeStep,
                        modifier = Modifier
                            .padding(top = 6.dp, bottom = 2.dp)
                            .fillMaxWidth()
                    ) { index ->
                        if (index <= viewModel.activeStep) {
                            viewModel.gotoStep(index)
                        }
                    }

                    //内容区
                    when (viewModel.currentStep) {
                        0 -> {
                            MomentScreen(
                                onAlbumClick = {
                                    viewModel.albumImagePaths = it
                                    viewModel.selectedImages = it.take(9)
                                    viewModel.gotoNextStep()
                                },
                                enableClick = {
                                    navController.navigate(ROUTE_SETTINGS_SCREEN)
                                }
                            )
                        }

                        1 -> {
                            SelectPhotosScreen(
                                images = viewModel.albumImagePaths,
                                selectedImages = viewModel.selectedImages,
                                isSelectedAllMode = isSelectedAllMode,
                                onSelectedAll = {
                                    // 全选
                                    isSelectedAllMode = !isSelectedAllMode
                                    val tempSelectedImages = mutableListOf<String>()
                                    if (isSelectedAllMode) {
                                        tempSelectedImages.addAll(viewModel.albumImagePaths)
                                    }
                                    viewModel.selectedImages = tempSelectedImages
                                },
                                imageClick = { index, _, bounds ->
                                    bounds?.let {
                                        info = BigImageInfo(viewModel.albumImagePaths, index, bounds)
                                        showBigImage = true
                                    }
                                },
                                checkBoxClick = { imageUrl ->
                                    if (viewModel.selectedImages.contains(imageUrl)) {
                                        viewModel.selectedImages -= imageUrl
                                    } else {
                                        if (viewModel.selectedImages.size >= 9) {
                                            ToastUtils.makeWithCancel(
                                                String.format(
                                                    context.getString(R.string.msg_picture_select_max),
                                                    9
                                                )
                                            )
                                        } else {
                                            viewModel.selectedImages += imageUrl
                                        }
                                    }
                                },
                                onSelectTemplateBtnClicked = {
                                    viewModel.gotoNextStep()
                                }
                            )
                        }

                        2 -> {
                            TemplateScreen(
                                selectedImages = viewModel.selectedImages,
                                onTemplateSelected = { template ->
                                    addPageViewModel.handleTemplateSelected(
                                        template.contentFile,
                                        addPageViewModel.selectedImages.filter {
                                            viewModel.selectedImages.contains(
                                                it.path
                                            )
                                        }
                                    )
                                    viewModel.gotoNextStep()
                                },
                            )
                        }

                        3 -> {
                            AIWritingScreen(
                                imageGroups = addPageViewModel.imageGroups,
                                createType = 0,
                                operationType = operationType,
                                generateClick = {
                                    generateClick(operationType, addPageViewModel.configInfoJson)
                                }
                            )
                        }
                    }
                }
            }

            if (showBigImage) {
                ImageViewerDialog(
                    info = info,
                    screenSize = screenSize,
                    onDismiss = {
                        showBigImage = false
                    }
                )
            }
        }
    }
}

@Composable
fun Stepper(
    steps: List<String>,
    activeStep: Int,
    modifier: Modifier = Modifier,
    onStepClick: (Int) -> Unit,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        steps.forEachIndexed { index, step ->
            StepItem(
                modifier = Modifier.weight(1f),
                index = index,
                step = step,
                isActive = index == activeStep,
                isCompleted = index < activeStep,
                isLast = index == steps.lastIndex,
                onStepClick = onStepClick
            )
        }
    }
}

@SuppressLint("DesignSystem")
@Composable
fun StepItem(
    modifier: Modifier,
    index: Int,
    step: String,
    isActive: Boolean,
    isCompleted: Boolean,
    isLast: Boolean,
    darkTheme: Boolean = isSystemInDarkTheme(),
    onStepClick: (Int) -> Unit
) {
    val completedColor = remember {
        darkTheme.judge(
            Color.White.copy(alpha = 0.45f),
            Color(0x4D155BF0),
        )
    }
    val normalColor = remember {
        darkTheme.judge(
            Color.White.copy(alpha = 0.2f),
            Color.Black.copy(alpha = 0.1f),
        )
    }
    Box(modifier = modifier) {
        //横线
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(36.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(1.dp)
                    .background(
                        if (index == 0) {
                            Color.Transparent
                        } else if (isCompleted || isActive) {
                            completedColor
                        } else {
                            normalColor
                        }
                    )
            )
            Box(modifier = Modifier.size(36.dp))
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(1.dp)
                    .background(
                        if (isLast) {
                            Color.Transparent
                        } else if (isCompleted) {
                            completedColor
                        } else {
                            normalColor
                        }
                    )
            )
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(enabled = isCompleted || isActive) {
                    onStepClick(index)
                },
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (isActive) {
                Image(
                    painter = painterResource(R.drawable.icon_step_active),
                    contentDescription = null,
                    modifier = Modifier.size(36.dp)
                )
            } else {
                Box(
                    modifier = Modifier.size(36.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Box(
                        modifier = Modifier
                            .size(24.dp)
                            .clip(CircleShape)
                            .background(
                                if (isCompleted) {
                                    darkTheme.judge(
                                        Color(0xFF4882FF).copy(alpha = 0.7f),
                                        Color(0xFF155BF0).copy(alpha = 0.7f),
                                    )
                                } else {
                                    darkTheme.judge(
                                        Color.White.copy(alpha = 0.1f),
                                        Color.Black.copy(alpha = 0.4f),
                                    )
                                }
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        if (isCompleted) {
                            Icon(
                                painter = painterResource(com.tcl.ai.note.journaldashboard.R.drawable.ic_check),
                                contentDescription = null,
                                modifier = Modifier.size(16.dp),
                                tint = Color.White
                            )
                        } else {
                            Text(
                                text = (index + 1).toString(),
                                color = darkTheme.judge(
                                    Color.White.copy(alpha = 0.5f),
                                    Color.White,
                                ),
                                fontSize = 12.sp,
                                lineHeight = 16.sp,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(4.dp))

            if (isActive) {
                MarqueeText(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 5.dp),
                    text = step,
                    color = TclTheme.colorScheme.tctStanderTextPrimary,
                    fontSize = 10.sp,
                    lineHeight = 16.sp,
                    textAlign = TextAlign.Center,
                )
            } else {
                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 5.dp),
                    text = step,
                    color = if (isCompleted) {
                        TclTheme.colorScheme.tctStanderTextPrimary
                    } else {
                        darkTheme.judge(
                            Color.White.copy(alpha = 0.5f),
                            Color.Black.copy(alpha = 0.7f),
                        )
                    },
                    fontSize = 10.sp,
                    lineHeight = 16.sp,
                    maxLines = 1,
                    textAlign = TextAlign.Center,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}

@SuppressLint("RestrictedApi")
@Composable
fun ImageViewerDialog(
    info: BigImageInfo,
    screenSize: IntSize,
    onDismiss: () -> Unit
) {
    val pagerState = rememberPagerState(
        initialPage = info.startIndex,
        pageCount = { info.images.size }
    )
    val animProgress = remember { Animatable(0f) }

    LaunchedEffect(Unit) {
        animProgress.animateTo(1f, tween(340, easing = FastOutSlowInEasing))
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xff010101))
            .pointerInput(Unit) {
                detectVerticalDragGestures(
                    onDragEnd = { onDismiss() },
                    onVerticalDrag = { _, _ -> }
                )
            }
    ) {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize()
        ) { pageIndex ->
            // 动效计算
            val isInitAnimPage = (pageIndex == info.startIndex)
            val localAnimProgress = if (isInitAnimPage) animProgress.value else 1f

            // 计算缩放&偏移
            val thumbW = info.startThumbRect.width
            val thumbH = info.startThumbRect.height
            val screenW = screenSize.width.toFloat()
            val screenH = screenSize.height.toFloat()
            val centerOffsetX =
                (info.startThumbRect.center.x - screenW / 2) * (1 - localAnimProgress)
            val centerOffsetY =
                (info.startThumbRect.center.y - screenH / 2) * (1 - localAnimProgress)
            val scaleXVal = lerp(thumbW / screenW, 1f, localAnimProgress)
            val scaleYVal = lerp(thumbH / screenH, 1f, localAnimProgress)

            Box(
                Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = rememberAsyncImagePainter(model = info.images[pageIndex]),
                    contentDescription = null,
                    modifier = Modifier
                        .fillMaxSize()
                        .graphicsLayer {
                            scaleX = scaleXVal
                            scaleY = scaleYVal
                            translationX = centerOffsetX
                            translationY = centerOffsetY
                        }
                        .pointerInput(Unit) {
                            detectTapGestures(onTap = { onDismiss() })
                        },
                    contentScale = ContentScale.Fit
                )
            }
        }
    }
}