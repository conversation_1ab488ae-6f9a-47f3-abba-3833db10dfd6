package com.tcl.ai.note.journaldashboard.vm

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.database.entity.Journal
import com.tcl.ai.note.database.repository.HomeRepository
import com.tcl.ai.note.database.repository.JournalRepository
import com.tcl.ai.note.journaldashboard.intent.JournalIntent
import com.tcl.ai.note.journaldashboard.track.AnalyticsCreateJournalModel
import com.tcl.ai.note.journaldashboard.track.JournalUsageReportWorker
import com.tcl.ai.note.journalhome.components.categorydialog.CategoryDialogCallBackEvent
import com.tcl.ai.note.journalhome.components.categorydialog.CategoryDialogEventManager
import com.tcl.ai.note.utils.Logger
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CreateJournalViewModel @Inject constructor(
    private val journalRepository: JournalRepository,
) : ViewModel() {
    private val _allJournals = MutableStateFlow<List<Journal>>(emptyList())
    val allJournals = _allJournals.asStateFlow()
    private val _createdCategory = MutableStateFlow(Pair(-1L, "")) // 初始值为-1，表示未创建分类
    val createdCategory = _createdCategory.asStateFlow()
    val startTime: Long = System.currentTimeMillis()

    private val _journal = MutableStateFlow<Journal?>(null)
    val journal: StateFlow<Journal?> = _journal.asStateFlow()

    val reportJournal = MutableStateFlow<Journal?>(null)

    init {
        // 初始化时加载所有日记
        AnalyticsCreateJournalModel.loadCreateJournalViewModel(this)
        observeCategoryDialogEvents()
        observeAllJournals()
    }

    private fun observeCategoryDialogEvents() {
        viewModelScope.launch {
            CategoryDialogEventManager.categoryDialogSuccessEvents.collect { event ->
                if (event is CategoryDialogCallBackEvent.OnCategoryCreatedCallBack) {
                    Logger.d("CreateJournalViewModel", "Received new category callback: categoryId=${event.categoryId}")
                    _createdCategory.update { Pair(event.categoryId.toLong(), event.categoryName) }
                } else if(event is CategoryDialogCallBackEvent.OnCategoryDialogDismiss){
                    Logger.d("CreateJournalViewModel", "Received category pop window dismiss callback: categoryId=${event.categoryId}")
                    _createdCategory.update { Pair(-1L, "") }
                }
            }
        }
    }

    private fun observeAllJournals() {
        viewModelScope.launch {
            HomeRepository.getAllJournals().collect { journals ->
                _allJournals.update { journals }
            }
        }
    }

    fun handleEvent(intent: JournalIntent) {
        when (intent) {
            is JournalIntent.CreateJournal -> {
                // Handle create journal intent
                viewModelScope.launch {
                    val journalId = journalRepository.insertJournal(intent.journal)
                    intent.journal.copy(journalId = journalId).also {
                        setJournal(it)
                        JournalUsageReportWorker.recordCreateJournal()
                    }
                }
            }
            is JournalIntent.EditJournal -> {
                // Handle edit journal intent
                viewModelScope.launch {
                    journalRepository.updateNote(intent.journal)
                    setJournal(intent.journal)
                }
            }
            is JournalIntent.DeleteJournal -> {
                // Handle delete journal intent
            }
        }
    }

    fun setJournal(journal: Journal?) {
        _journal.update { journal }
        journal?.let {
            setReportJournal(it)
        }
    }

    private fun setReportJournal(journal: Journal) {
        reportJournal.update { journal }
    }
}