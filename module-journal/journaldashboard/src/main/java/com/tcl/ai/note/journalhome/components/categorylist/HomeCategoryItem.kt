package com.tcl.ai.note.journalhome.components.categorylist

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.indication
import androidx.compose.foundation.interaction.PressInteraction
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.ui.popup.BounceScalePopup
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.journalhome.components.HomePopItem
import com.tcl.ai.note.journalhome.entity.HomeCategoryItemEntity
import com.tcl.ai.note.journalhome.vm.state.CategoryAction
import com.tcl.ai.note.utils.HoverMutableInteractionSource
import com.tcl.ai.note.utils.toPx

@Composable
fun CategoryItemRow(
    modifier: Modifier = Modifier,
    category: HomeCategoryItemEntity,
    onAction: (CategoryAction) -> Unit,
    onClick: () -> Unit,
    // 新增参数：控制菜单显示状态
    isDropdownMenuExpanded: Boolean = false,
    onMenuExpandedChange: (Boolean) -> Unit = {}
) {
    // 防抖机制：记录最后一次长按时间
    var lastLongClickTime by remember { mutableLongStateOf(0L) }

    Column(
        modifier = modifier
            .fillMaxWidth(),
        horizontalAlignment = Alignment.End
    ) {
        CategoryItem(
            category = category,
            isSelected = category.isSelected,
            onClick = onClick,
            onLongClick = {
                val currentTime = System.currentTimeMillis()
                // 防抖：500ms内的重复长按将被忽略
                if (category.isCanOperate &&
                    !isDropdownMenuExpanded &&
                    currentTime - lastLongClickTime > 500
                ) {

                    lastLongClickTime = currentTime
                    onMenuExpandedChange(true)
                    onAction(CategoryAction.OnLongClickSelected(category, true))
                }
            }
        )
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentWidth(Alignment.End)
        ) {
            RenameAndDeleteDropMenu(
                category = category,
                isDropdownMenuExpanded = isDropdownMenuExpanded,
                onAction = onAction,
            ) {
                onMenuExpandedChange(false)
            }
        }
    }
}

@Composable
private fun RenameAndDeleteDropMenu(
    category: HomeCategoryItemEntity,
    isDropdownMenuExpanded: Boolean,
    onAction: (CategoryAction) -> Unit,
    onDismissRequest: () -> Unit
) {
    val menuWidth = 128.dp // 菜单宽度
    // 添加这些变量来计算正确的偏移量
    val layoutDirection = LocalLayoutDirection.current

    val popOffset = IntOffset(
        x = if (layoutDirection == LayoutDirection.Rtl) menuWidth.toPx.toInt() else (-menuWidth).toPx.toInt(),
        y = 2
    )

    val popupWidth: Dp = 128.dp

    if (isDropdownMenuExpanded) {
        BounceScalePopup(
            onDismissRequest = {
                onDismissRequest()
                onAction(CategoryAction.OnLongClickSelected(category, false))
            },
            offset = popOffset,
        ) { closePopup ->
            Box(
                modifier = Modifier
                    .width(popupWidth)
                    .height(88.dp)
                    .defShadow(radius = 20.dp)
            ) {
                Column(
                    modifier = Modifier
                        .width(popupWidth)
                        .background(colorResource(R.color.drop_down_menu_bg))
                        .padding(4.dp)
                )
                {
                    // rename category
                    HomePopItem(
                        text = stringResource(R.string.rename_category),
                        onClick = {
                            onDismissRequest()
                            onAction(CategoryAction.OnRenameCategoryClick(true, category))
                        }
                    )

                    // delete category
                    HomePopItem(
                        text = stringResource(R.string.delete_category),
                        onClick = {
                            onDismissRequest()
                            //顺序很重要 先 长按 才能 显示 删除按钮
                            onAction(CategoryAction.OnShowDeleteCategoryNotesDialog(true))
                        }
                    )
                }
            }
        }
    }
}


/**
 * 分类行组件
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun CategoryItem(
    category: HomeCategoryItemEntity,
    isSelected: Boolean,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        CategoryItemRowHover(
            isSelected = isSelected,
            onLongClick = onLongClick,
            onClick = onClick
        ) {
            category.categoryIcon?.let { icon ->
                Icon(
                    painter = painterResource(icon.icon),
                    contentDescription = category.name,
                    modifier = Modifier
                        .size(24.dp),
                    tint = icon.color?.let { colorResource(it) } ?: Color.Transparent
                )
            }


            Spacer(modifier = Modifier.width(12.dp))

            Text(
                text = category.name ?: stringResource(com.tcl.ai.note.resources.R.string.all_journals),
                fontSize = 14.sp,
                color = if (isSelected) colorResource(R.color.home_title_color) else colorResource(R.color.text_title),
                modifier = Modifier.weight(1f),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Spacer(modifier = Modifier.width(19.dp))
            Text(
                text = category.noteCounts.toString(),
                fontSize = 14.sp,
                color = colorResource(R.color.text_summary)
            )
        }
    }

}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun CategoryItemRowHover(
    isSelected: Boolean,
    onLongClick: () -> Unit,
    onClick: () -> Unit = {},
    content: @Composable RowScope.() -> Unit
) {
    val hoverInteraction = remember {
        HoverMutableInteractionSource()
    }
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp)
            .clip(RoundedCornerShape(8.dp))
            .pointerInput(Unit) {
                detectTapGestures(
                    onLongPress = {
                        onLongClick()
                    },
                    onPress = { offset ->
                        val press = PressInteraction.Press(offset)
                        hoverInteraction.emit(press)
                        tryAwaitRelease()
                        hoverInteraction.emit(PressInteraction.Release(press))
                    },
                    onTap = {
                        onClick()
                    }
                )
            }
            .indication(hoverInteraction, indication = ripple())
            .background(
                if (isSelected) colorResource(R.color.bottom_sheet_dialog_drag_bar_color) else Color.Transparent,
                RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        content()
    }
}

