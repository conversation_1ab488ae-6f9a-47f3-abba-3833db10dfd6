package com.tcl.ai.note.polish.assistant

import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.exception.AIChatException
import com.tcl.ai.note.state.ChatStreamingMsg
import com.tcl.ai.note.state.ERR_TRANSLATE_SENSITIVE_WORDS
import com.tcl.ai.note.state.ERR_USAGE_LIMITED
import com.tcl.ai.note.state.Result
import com.tcl.ai.note.state.sendErrorResult
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.sdk.assistant.AssistantServiceManager
import com.tcl.ai.sdk.assistant.ICallResult
import com.tcl.ai.sdk.assistant.callback.IChatStreamingCallback
import com.tcl.ai.sdk.assistant.entity.StreamingStatus
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.ProducerScope
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * AI 助手 API
 */
class PolishAIAssistantApi {
    private val TAG = "AIAssistantApi"
    val stopSignal = MutableStateFlow(false) // 用于控制 Flow 是否继续执行
    var currentJobId: Int? = null

    suspend fun obtainAssistSuspend(): Int = suspendCancellableCoroutine { continuation ->
        AssistantServiceManager.Companion.instance.obtainAssistant(
            object : ICallResult.Stub() {
                override fun onSuccess(callId: Int) {
                    // 恢复协程并传递 callId
                    continuation.resume(callId)
                }

                override fun onFailure(errorCode: Int) {
                    // 恢复协程并抛出异常
                    Logger.i(TAG, "obtainAssist --> failure, errCode: $errorCode")
                    continuation.resumeWithException(
                        AIChatException(
                            errorCode,
                            "Assistant failed with code $errorCode"
                        )
                    )
                }
            }
        )
    }

    fun sendUserMsg(
        assistCallId: Int,
        preset: String,
        originText: String,
        languageCode: String
    ): Flow<Result<ChatStreamingMsg>?> = callbackFlow {
        Logger.d(
            TAG, "sendUserMsg assistCallId :  $assistCallId\" " +
                    "languageCode : $languageCode\"" +
                    "authorization  : ${AccountController.token}\"" +
                    "serverUrl  : ${AccountController.baseHost}\" + " +
                    "preset  : $preset\" " +
                    "originText :$originText\" " +
                    "langCode :  $languageCode\" " +
                    "countryCode : ${AccountController.countryCode}\" "
        )

        // 创建一个协程作用域，用于在回调中启动协程
        val scope = CoroutineScope(coroutineContext)

        AssistantServiceManager.instance.presetCompletionsStreaming(
            callId = assistCallId,
            authorization = AccountController.token,
            serverUrl = AccountController.baseHost,
            preset = preset,
            originText = originText,
            langCode = languageCode,
            countryCode = AccountController.countryCode,
            functionName = "Rewrite",
            callback = object : IChatStreamingCallback {
                override fun onSuccess(streamingMsg: com.tcl.ai.sdk.assistant.entity.ChatStreamingMsg) {
                    Logger.i(TAG, "IChatStreamingCallback success: $streamingMsg")
                    if (stopSignal.value) {
                        Logger.i(TAG, "IChatStreamingCallback success: close $streamingMsg")
                        close() // 关闭 Flow
                        return
                    }
                    sendSuccessMsgFlow(streamingMsg, scope)
                }

                override fun onFailure(errCode: Int) {
                    Logger.i(TAG, "asr fail !!! $errCode")
                    scope.launch(Dispatchers.Default) {
                        try {
                            send(Result.Error(Exception("SDK error"), errCode))
                            close()
                        } catch (e: Exception) {
                            Logger.e(TAG, "发送错误消息失败: ${e.message}")
                        }
                    }
                }
            },
            object : ICallResult.Stub() {
                override fun onSuccess(jobId: Int) {  // 这里的 jobId 是服务端对该 Id 的记录，可以调用 cancelXXX 方法进行取消。
                    Logger.i(TAG, "obtainAssistSuspend --> success, jobId: $jobId")
                    currentJobId = jobId
                }

                override fun onFailure(errorCode: Int) {
                    Logger.i(TAG, "obtainAssistSuspend --> success, errorCode: $errorCode")

                }
            }
        )
        awaitClose {
            // 取消所有在此作用域中启动的协程
            Logger.d(TAG, "awaitClose")
//            scope.cancel()
        }
    }.flowOn(Dispatchers.IO)



    /**
     * 用户点击取消或者切换不同的润色type，停止即将生成或者正在AI服务
     * 可以节约用户的资源以及防止生成内容混乱
     */
    fun stopAIAssistant(assistCallId: Int) {
        currentJobId?.let {
            AssistantServiceManager.instance.cancelPresetCompletionsStreamingTask(
                callId = assistCallId,
                jobId = it,
                callResult = object : ICallResult.Stub() {
                    override fun onSuccess(p0: Int) {
                        Logger.i(TAG, "cancelStreamingMsg --> success")
                    }

                    override fun onFailure(p0: Int) {
                        Logger.w(TAG, "cancelStreamingMsg --> onFailure: $p0")
                    }
                }
            )
        }

    }
    private fun ProducerScope<Result<ChatStreamingMsg>>.sendSuccessMsgFlow(
        streamingMsg: com.tcl.ai.sdk.assistant.entity.ChatStreamingMsg,
        scope: CoroutineScope
    ) {
        val myMsg = streamingMsg.toMyChatStreamingMsg() // 使用扩展函数转换
        // 在协程中使用 send
        scope.launch(Dispatchers.Default) {
            try {
                send(Result.Success(myMsg))
                val tmpMsg = streamingMsg.status
                if (tmpMsg is StreamingStatus.Stopped) {
                    /**
                     *Stopped 异常结束需要关注 errorCode 敏感词 在 StreamingStatus.Stopped 中
                     */
                    val errorCode = tmpMsg.errorCode
                    sendErrorResult(errorCode)
                }
                // 如果是最后一条消息，关闭流
                if (myMsg.status == com.tcl.ai.note.state.StreamingStatus.COMPLETED) {
                    close()
                }
            } catch (e: Exception) {
                Logger.e(TAG, "发送消息失败: ${e.message}")
            }
        }
    }

    // 为 SDK 的类添加扩展函数
    fun com.tcl.ai.sdk.assistant.entity.ChatStreamingMsg.toMyChatStreamingMsg(): ChatStreamingMsg {
        return ChatStreamingMsg(
            status = when (status) { // 确保状态枚举值匹配
                is StreamingStatus.InProgress -> com.tcl.ai.note.state.StreamingStatus.IN_PROGRESS
                is StreamingStatus.Completed -> com.tcl.ai.note.state.StreamingStatus.COMPLETED
                is StreamingStatus.Stopped -> com.tcl.ai.note.state.StreamingStatus.STOPPED
                else -> {
                    Logger.i(TAG, "toMyChatStreamingMsg status $status ")
                    throw IllegalArgumentException("Unknown status")
                }
            },
            text = this.text,
            threadId = this.threadId,
            userMessageId = this.userMessageId
        )
    }
}