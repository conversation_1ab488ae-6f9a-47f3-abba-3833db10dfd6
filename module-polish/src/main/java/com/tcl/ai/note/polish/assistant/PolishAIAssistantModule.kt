package com.tcl.ai.note.polish.assistant

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class PolishAIAssistantModule {

    @Provides
    @Singleton
    fun provideAIAssistantApi(): PolishAIAssistantApi {
        return PolishAIAssistantApi()
    }

}