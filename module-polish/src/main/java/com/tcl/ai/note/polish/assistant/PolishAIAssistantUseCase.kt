package com.tcl.ai.note.polish.assistant

import com.tcl.ai.note.base.R
import com.tcl.ai.note.exception.AIChatException
import com.tcl.ai.note.handwritingtext.database.NoteRepository
import com.tcl.ai.note.handwritingtext.richtext.utils.NoteContentUtil
import com.tcl.ai.note.net.NetworkMonitor
import com.tcl.ai.note.polish.state.ReWriteState
import com.tcl.ai.note.state.ChartStreamingMsgUtils
import com.tcl.ai.note.state.ChatEffect
import com.tcl.ai.note.state.ChatStreamingMsg
import com.tcl.ai.note.state.Result
import com.tcl.ai.note.state.StreamingStatus
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getAIApiLangCode
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 重写AI的usecase
 */
class PolishAIAssistantUseCase @Inject constructor(
    private val networkMonitor: NetworkMonitor,
    private val polishAiAssistantApi: PolishAIAssistantApi,
) {
    private var mNoteId: Long = -1
    private lateinit var _isOffline: StateFlow<Boolean>
    private val isOffline: StateFlow<Boolean> get() = _isOffline
    val reWriteState = MutableStateFlow(ReWriteState())


    val chatStreamingMsgSate = MutableStateFlow<Result<ChatStreamingMsg>?>(null)
    val effect = MutableStateFlow<ChatEffect?>(
        null
    )
    private var isHasClickStop = false

    private val TAG = "PolishAIAssistantUseCase"
    private var assistCallId: Int = -1
    private var mOriginalText: String = ""
    private var accumulatedText = StringBuilder()

    fun setupNetworkMonitor(scope: CoroutineScope) {
        _isOffline = networkMonitor.isOnline.map(Boolean::not)
            .stateIn(
                scope = scope,
                started = SharingStarted.Companion.Eagerly,
                initialValue = false
            )
        scope.launch {
            _isOffline.collectLatest {
                if (it) {
                    (chatStreamingMsgSate.value as? Result.Success)?.let { state ->
                        val successData = state.data
                        if (successData.status == StreamingStatus.IN_PROGRESS) {
                            effect.emit(ChatEffect.ShowToastRes(R.string.network_error))
                        }
                    }
                }
            }
        }
    }

    /**
     * 发送消息
     */
    suspend fun sendMsg(preset: String) {

        if (assistCallId == -1) {
            // 说明没有初始化assistCallId
            generateText(mNoteId, preset)
            return
        }
        sendMsgWithSate(preset = preset, content = mOriginalText)
    }

    /**
     * 首次
     * 设置NoteId和获取内容
     */
    suspend fun initOriginalText(noteId: Long) {
        mNoteId = noteId
        val content = getNoteContent(noteId)
        mOriginalText = content
        if (mOriginalText.isNotEmpty()) {
            reWriteState.update {
                it.copy(text = mOriginalText)
            }
        }
    }

    /**
     * 停止生成文本
     */
    fun stopGenerateText() {
        isHasClickStop = true
        ChartStreamingMsgUtils.setMsgStateStop(chatStreamingMsgSate)
        polishAiAssistantApi.stopAIAssistant(assistCallId)
    }

    /**
     * 清除之前的结果
     * 在切换指令时调用，确保上一个指令的结果不会显示在当前指令的内容上
     */
    fun clearPreviousResult() {
        // 重置累积文本
        accumulatedText = StringBuilder()

        // 重置流式消息状态
        chatStreamingMsgSate.value = null

        // 重置效果
        effect.value = null

        // 更新重写状态，清除生成的内容
        reWriteState.update {
            it.copy(
                recognitionResult = "",
                isLoading = false,
                status = com.tcl.ai.note.polish.state.StreamingStatus.IDLE
            )
        }
    }
    /**
     * 清除
     */
    fun clear() {
        mNoteId = -1
        mOriginalText = ""
        assistCallId = -1
        accumulatedText = StringBuilder()
        Logger.d(TAG, "clear mNoteId: $mNoteId mOriginalText: $mOriginalText")
    }

    /**
     * 生成文本
     */
    suspend fun generateText(
        noteId: Long,
        preset: String,
    ) {
        try {
            // 等待异步回调完成
            val callId = polishAiAssistantApi.obtainAssistSuspend()
            Logger.i(TAG, "obtainAssist --> callId: $callId mNoteId: $noteId mOriginalText: $mOriginalText")
            assistCallId = callId
            if (mOriginalText.isEmpty()) {
                // 回调成功后执行后续操作
                val content = getNoteContent(noteId)
                mOriginalText = content
            }
            reWriteState.update {
                it.copy(text = mOriginalText)
            }
            Logger.d(TAG, " content :   $mOriginalText")
            sendMsgWithSate(preset, mOriginalText)
        } catch (e: Exception) {
            if (e is AIChatException) {
                Logger.e(TAG, "Failed to obtain assist: ${e.message}")
                effect.value = ChartStreamingMsgUtils.dealWithErrorResult(e.code)
            }
        }
    }

    private suspend fun getNoteContent(noteId: Long): String {
        val note = NoteRepository.getNote(noteId)
        return NoteContentUtil.dealWithNoteContentForAI(note)
    }

    /*
     *   发送消息 并收集 结果状态
     */
    private suspend fun sendMsgWithSate(preset: String, content: String) {
        if (isOffline.value) {
            effect.value = ChatEffect.ShowToastRes(R.string.network_error)
            return
        }

        // 清除之前的效果和状态
        effect.value = null
        chatStreamingMsgSate.value = Result.Loading

        // 更新重写状态，显示操作区域
        reWriteState.update {
            it.copy(
                showOperation = true,
                recognitionResult = "", // 清除之前的生成结果
                status = com.tcl.ai.note.polish.state.StreamingStatus.IN_PROGRESS // 设置状态为进行中
            )
        }

        isHasClickStop = false
        // 重置累积文本，确保每次请求都从空开始
        accumulatedText = StringBuilder()
        polishAiAssistantApi.sendUserMsg(assistCallId, preset, content, getAIApiLangCode())
            .onEach { delay(50) }
            .onCompletion { cause ->
            Logger.d(TAG, "sendMsgWithSate:onCompletion  $cause")
        }.collect {
            Logger.d(TAG, "sendMsgWithSate: $it")
            if (it is Result.Success && !isHasClickStop) {
                accumulatedText.append(it.data.text)
                chatStreamingMsgSate.value =
                    it.copy(data = it.data.copy(text = accumulatedText.toString()))
            } else {
                if (it is Result.Error) {
                    val code = it.code
                    ChartStreamingMsgUtils.setErrorResultMsgState(chatStreamingMsgSate)
                    Logger.e(TAG, "Result.Error: ${it.exception} code: $code")
                    effect.value = ChartStreamingMsgUtils.dealWithErrorResult(code, it)
                }
            }
        }
    }
}