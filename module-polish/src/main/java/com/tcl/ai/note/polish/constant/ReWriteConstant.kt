package com.tcl.ai.note.polish.constant

import com.tcl.ai.note.base.R
import com.tcl.ai.sdk.assistant.PresetCmd

internal val rewriteFuncList = listOf(
    R.string.text_polishing,
    R.string.fix_pelling,
    R.string.make_shorter,
    R.string.make_longer,
    R.string.professional,
    R.string.casual
)

/**
 * 重写 列表 文字和preset对应 map 关系
 */
internal val rewriteFuncListConfig = listOf(
    R.string.text_polishing to PresetCmd.POLISHING,
    R.string.fix_pelling to PresetCmd.CORRECTION,
    R.string.make_shorter to PresetCmd.SHORTEN,
    R.string.make_longer to PresetCmd.EXPAND,
    R.string.professional to PresetCmd.PROFESSIONALIZE,
    R.string.casual to PresetCmd.DUMB_DOWN,
)
internal val rewriteIconList = listOf(
    R.drawable.improve_writing,
    R.drawable.fix_spelling,
    R.drawable.rewrite_shorter,
    <PERSON><PERSON>drawable.rewrite_longer,
    R.drawable.rewrite_professional,
    R.drawable.rewrite_casual
)

val rewriteFunctionList = listOf(
    "Improve Writing",
    "Fix spelling & grammar",
    "Make shorter",
    "Make longer",
    "Professional",
    "Casual"
)