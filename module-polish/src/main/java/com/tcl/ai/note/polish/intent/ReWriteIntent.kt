package com.tcl.ai.note.polish.intent

sealed class ReWriteIntent {
    /**
     * 生成
     */
    data class Generates(val noteId : Long)  : ReWriteIntent()
    /**
     * copy
     */
    data object Copy  : ReWriteIntent()

    /**
     * Retry
     */
    data object Retry : ReWriteIntent()

    /**
     * Stop
     */
    data object Stop : ReWriteIntent()
    /**
     * Replace
     */
    data class Replace(val content : String) : ReWriteIntent()
    /**
     * SelectItem
     */
    data class SelectItem(val index: Int) : ReWriteIntent()
}