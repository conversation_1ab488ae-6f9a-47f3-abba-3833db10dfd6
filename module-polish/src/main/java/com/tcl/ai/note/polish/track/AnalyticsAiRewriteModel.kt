package com.tcl.ai.note.polish.track

import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.polish.vm.ReWriteViewModel
import com.tcl.ai.note.state.Result
import com.tcl.ai.note.state.StreamingStatus
import com.tcl.ai.note.track.AbsAnalyticsSubModel
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.flow.filterNotNull


/**
 * 分析AI润色使用情况
 *
 * 用于上报埋点
 */
object AnalyticsAiRewriteModel : AbsAnalyticsSubModel() {
    private const val TAG = "AnalyticsAiRewriteModel"
    fun loadReWriteViewModel(rewriteViewModel: ReWriteViewModel) {
        reportAiRewriteCompletionState(rewriteViewModel)
    }

    /**
     * 上报AI润色获取数据的状态
     */
    private fun reportAiRewriteCompletionState(rewriteViewModel: ReWriteViewModel) {
        rewriteViewModel.aiLoadingStatus.collectWithScope(rewriteViewModel.viewModelScope) { result ->
            if (result is Result.Success && result.data.status == StreamingStatus.COMPLETED) {
                // AI润色数据获取成功
                TclAnalytics.reportAiRewriteCompletionState("0")
            }
        }
        rewriteViewModel.effect.filterNotNull().collectWithScope(rewriteViewModel.viewModelScope) {
            Logger.d(TAG, "effect: $it")
            // AI润色数据获取失败
            TclAnalytics.reportAiRewriteCompletionState("1")
        }
    }
}