package com.tcl.ai.note.polish.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInParent
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.polish.ui.components.ReWriteFuncMenuListPanel
import com.tcl.ai.note.polish.ui.components.ReWriteHeader
import com.tcl.ai.note.polish.ui.components.ReWriteResultPanel
import com.tcl.ai.note.state.ChatStreamingMsg
import com.tcl.ai.note.state.Result
import com.tcl.ai.note.state.testSuccessResult
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.widget.ExpandPanel
import com.tcl.ai.note.widget.components.AIShowContentText
import com.tcl.ai.note.widget.components.ExpandedTextContent

/**
 * 润色内容的布局
 * @param originalText 原始文本
 * @param selectedIndex 选中的索引
 * @param showCloseIcon 是否显示关闭按钮
 * @param showOperation 是否显示操作按钮
 * @param isShowOriginalText 是否显示原始文本
 * @param onCloseClick 点击关闭按钮
 * @param onFuncListSelected 点击功能列表
 * @param onRetryClick 点击重试按钮
 * @param onCopyClick 点击复制按钮
 * @param onReplaceClick 点击替换按钮
 * @param onStopClick 点击停止按钮
 * @param onOutput 点击输出按钮
 * @param showToast 显示toast
 * @param changeShowOperation 切换显示操作按钮
 * @param aiLoadingStatus AI加载状态
 * onFuncListSelected 点击功能列表
 * changeShowOperation 和onFuncListSelected回调 重复了没用到
 */
@Composable
internal fun ReWriteContent(
    modifier: Modifier,
    originalText: String,
    selectedIndex: Int,
    showCloseIcon: Boolean = true,
    showOperation: Boolean = false,
    isShowOriginalText: Boolean = false,
    onCloseClick: () -> Unit = {},
    onFuncListSelected: (Int) -> Unit,
    aiLoadingStatus: Result<ChatStreamingMsg>?,
    onRetryClick: () -> Unit,
    onCopyClick: () -> Unit,
    onReplaceClick: (String) -> Unit,
    onStopClick: (Boolean) -> Unit,
    onOutput: (String) -> Unit,
    showToast: (toastId: Int) -> Unit = {},//默认不显示切换
    changeShowOperation: () -> Unit = {},
    changeTextExpanded: () -> Unit = {}
){

    var paddingTop by rememberSaveable{
        mutableFloatStateOf(0f)
    }
    var textExpanded by rememberSaveable{
        mutableStateOf(false)
    }
    var showExpandIcon by rememberSaveable {
        mutableStateOf(false)
    }
    Logger.d("ReWriteContent", "aiLoadingStatus : $aiLoadingStatus")
    Box{
        Column {
            ReWriteHeader(
                showCloseIcon,
                selectedIndex,
                showOperation,
                onFuncListSelected,
                onCloseClick
            )
            if (isShowOriginalText) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .onGloballyPositioned {
                            paddingTop = if (isTablet) {
                                (it.positionInParent().y + it.size.height) / 2.2f
                            } else {
                                (it.positionInParent().y + it.size.height) / 3.3f
                            }
                        }
                        .padding(start = 20.dp, end = 16.dp)
                        .height(40.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {

                    Text(
                        text = originalText,
                        overflow = TextOverflow.Ellipsis,
                        fontSize = 16.sp,
                        maxLines = 1,
                        color = TclTheme.tclColorScheme.tctStanderTextSecondary,
                        modifier = Modifier
                            .weight(1f)
                            .padding(end = 6.dp),
                        onTextLayout = {
                            showExpandIcon = it.hasVisualOverflow
                        }
                    )

                    val focusManager = LocalFocusManager.current
                    if (showExpandIcon) {
                        Image(
                            painter = painterResource(id = if (textExpanded) R.drawable.fold else R.drawable.unfold),
                            contentDescription = "",
                            modifier = Modifier.clickable(
                                interactionSource = null,
                                indication = ripple(bounded = false)
                            ) {
                                changeTextExpanded()
                                focusManager.clearFocus()
                                textExpanded = !textExpanded
                                Logger.d(
                                    tag = "ReWriteContent",
                                    message = "textExpanded:$textExpanded"
                                )
                            })
                    }
                }
            }
            if (showOperation){
                ReWriteResultPanel(
                    aiLoadingStatus = aiLoadingStatus,
                    onRetryClick = onRetryClick,
                    onCopyClick = onCopyClick,
                    onStopClick = {
                        onStopClick(it)
                    },
                    onOutput = onOutput,
                    modifier = modifier,
                    showToast = showToast,
                    onReplaceClick=onReplaceClick
                )
            }else{
                ReWriteFuncMenuListPanel(onFuncClick = onFuncListSelected, changeShowOperation = changeShowOperation)
            }
        }
        if (isShowOriginalText) {
            ExpandedTextContent(textExpanded, changeTextExpanded = {
                textExpanded = !textExpanded
            }, paddingTop, originalText)
        }
    }

}


@Composable
@Preview(showBackground = true)
private fun ReWriteContentPreview(){
    Column {
        ReWriteContent(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth(),
            originalText = "AAAAAAAAAAAAAA",
            selectedIndex = 1,
            showCloseIcon = true,
            showOperation = true,
            onFuncListSelected = {},
            aiLoadingStatus = testSuccessResult,
            onRetryClick = {},
            onCopyClick = {},
            onReplaceClick = {},
            onStopClick = {},
            onOutput = {},
            showToast = {},
            changeShowOperation = {},
        )
    }

}
