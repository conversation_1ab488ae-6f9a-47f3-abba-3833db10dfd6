package com.tcl.ai.note.polish.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tcl.ai.note.controller.OnResumeLoginStateEffect
import com.tcl.ai.note.polish.intent.ReWriteIntent
import com.tcl.ai.note.polish.state.ReWriteActions
import com.tcl.ai.note.polish.state.ReWriteState
import com.tcl.ai.note.polish.vm.ReWriteViewModel
import com.tcl.ai.note.state.ChatEffect
import com.tcl.ai.note.state.ChatStreamingMsg
import com.tcl.ai.note.state.Result
import com.tcl.ai.note.state.testSuccessResult
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.widget.BottomSheetDialog
import com.tcl.ai.note.widget.UsageLimitDialog
import com.tcl.ai.note.widget.components.ShowToastAndDialog
import kotlinx.coroutines.launch

@Composable
fun ReWriteScreenRoute(
    onDismissRequest: () -> Unit,
    reWriteViewModel: ReWriteViewModel = hiltViewModel()
) {
    val TAG = "ReWriteScreen"
    val writeState = reWriteViewModel.reWriteState.collectAsState()
    val reWriteActions = rememberReWriteActions(reWriteViewModel,onDismissRequest)
    val aiLoadingStatus by reWriteViewModel.aiLoadingStatus.collectAsStateWithLifecycle()
    val effectState by reWriteViewModel.effect.collectAsStateWithLifecycle()
    ShowToastAndDialog(effectState){
        onDismissRequest()
    }
    OnResumeLoginStateEffect(){
        onDismissRequest()
    }
    ReWriteScreen(onDismissRequest, reWriteActions, aiLoadingStatus,writeState.value)
}

@Composable
fun rememberReWriteActions(viewmodel: ReWriteViewModel, onDismissRequest: () -> Unit,): ReWriteActions {
    return remember(viewmodel) {
        ReWriteActions(
            onRetryClick = { viewmodel.handleIntent(ReWriteIntent.Retry) },
            onCopyClick = { viewmodel.handleIntent(ReWriteIntent.Copy) },
            onStopClick = {
                viewmodel.handleIntent(ReWriteIntent.Stop)
                ToastUtils.makeWithCancel(com.tcl.ai.note.base.R.string.stopped_answer)
                          },
            onOutput = {},
            onReplaceClick = {
                viewmodel.handleIntent(ReWriteIntent.Replace(it))
            },
            onFuncListSelected = {
                viewmodel.handleIntent(ReWriteIntent.SelectItem(it))
            },
            onToast = {
                ToastUtils.makeWithCancel(it)
            },
        )
    }
}

@Composable
private fun ReWriteScreen(
    onDismissRequest: () -> Unit={},
    reWriteActions: ReWriteActions,
    aiLoadingStatus: Result<ChatStreamingMsg>?,
    reWriteState: ReWriteState
) {
    val coroutineScope = rememberCoroutineScope()
    var dismissDialogCallback: (suspend () -> Unit)? = null
    //这种关闭对话框的方式 才不会造成阴影背景往下移动的情况
    fun closeDialog() {
        coroutineScope.launch {
            dismissDialogCallback?.invoke()
        }
    }
    BottomSheetDialog(
        modifier = Modifier.fillMaxHeight(),
        visible = true,
        onDismissRequest = {
            onDismissRequest()
        },
        canceledOnTouchOutside = false,
        showFullScreenCallBack = { isFullScreen ->
            if (isFullScreen) {
                TclAnalytics.reportAiRewriteMax("0")
            }
        },
        backHandler = {},
        extraTopPadding = true,
        onDismissCallback = { callback ->
            dismissDialogCallback = callback
        },
    ) {
        var startRewriteMillis by remember { mutableStateOf(System.currentTimeMillis()) }
        Column(
            modifier = Modifier
                .navigationBarsPadding()
                .fillMaxWidth()
                .fillMaxSize()
        ) {

            ReWriteContent(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                originalText =reWriteState.text,
                selectedIndex = reWriteState.selectedIndex,
                showOperation = reWriteState.showOperation,
                onCloseClick = {
                    closeDialog()
                               },
                onFuncListSelected = { reWriteActions.onFuncListSelected(it) },
                aiLoadingStatus = aiLoadingStatus,
                onRetryClick = {
                    reWriteActions.onRetryClick()
                    TclAnalytics.reportAiRewriteReset("0")
                    startRewriteMillis = System.currentTimeMillis()
                },
                onCopyClick = {
                    reWriteActions.onCopyClick()
                    TclAnalytics.reportAiRewriteCopy("0")
                },
                onReplaceClick = {
                    reWriteActions.onReplaceClick(it)
                    closeDialog()
                },
                onStopClick = {
                    reWriteActions.onStopClick(it)
                    TclAnalytics.reportAiRewriteStop("0", (System.currentTimeMillis() - startRewriteMillis).toString())
                    startRewriteMillis = System.currentTimeMillis()
                },
                onOutput = {  },
                showToast = { reWriteActions.onToast(it) },
            )

        }
    }
}




@Preview
@Composable
fun ReWriteScreenSuccessPreview() {
    ReWriteScreen(
        onDismissRequest = {},
        reWriteActions = ReWriteActions(),
        aiLoadingStatus = testSuccessResult,
        reWriteState = ReWriteState(
            text = testOriginalText,
            showOperation = true
        )
    )
}
@Preview
@Composable
fun PreviewSummaryScreenLoading() {
    ReWriteScreen(
        aiLoadingStatus = Result.Loading,
        onDismissRequest = {},
        reWriteActions = ReWriteActions(),
        reWriteState = ReWriteState(
            text = testOriginalText,
            showOperation = true
        )
    )
}

@Preview()
@Composable
fun PreviewSummaryScreenError() {
    ReWriteScreen(
        aiLoadingStatus = Result.Error(Exception("error")),
        reWriteActions = ReWriteActions(),
        reWriteState = ReWriteState(
            text = testOriginalText
        )
    )

}

@Preview(
    widthDp = 360,// 360 // 设置宽度以模拟横屏
    heightDp = 820,// 820 // 设置高度
//    widthDp = 1100, // 设置宽度以模拟横屏
//    heightDp = 720, // 设置高度
    showBackground = true
)
@Composable
fun ReWriteScreenNullPreview() {
    ReWriteScreen(
        onDismissRequest = {},
        reWriteActions = ReWriteActions(),
        aiLoadingStatus = null,
        reWriteState = ReWriteState(
            text = testOriginalText
        )
    )
}
const val testOriginalText="\"在 Jetpack Compose 内部，当使用 mutableStateOf 创建状态时，Compose 会观察此状态的更改，并在状态变化时触发重组。这意味着每次状态改变都会重新执行相关的 Composable 函数，从而确保 UI 恰当更新。\\n\" +\n" +
        "                        \"\\n\" +\n" +
        "                        \"对于你的特定情况，确保 Image 的 clickable 修饰符触发状态改变后，Compose 能正确重新组合并刷新 UI。这是通过正确声明和使用 State 来实现的。\""