package com.tcl.ai.note.polish.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.polish.constant.rewriteFuncListConfig
import com.tcl.ai.note.polish.constant.rewriteIconList
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.widget.MarqueeText


/**
 * 改写功能面板
 */
@Composable
internal fun ReWriteFuncMenuListPanel(
    modifier: Modifier = Modifier,
    onFuncClick: (Int) -> Unit,
    changeShowOperation: ()->Unit = {}
){
    Column(modifier = modifier
        .verticalScroll(rememberScrollState())
        .padding(start = 16.dp, end = 16.dp, bottom = 12.dp)
        .clip(RoundedCornerShape(20.dp))
        .background(color = TclTheme.colorScheme.tctResultBgColor)
        .padding(top = 10.dp, start = 16.dp, end = 16.dp, bottom = 16.dp)) {
        repeat(rewriteFuncListConfig.size){ index ->
            ReWriteAIMenuItem(onFuncClick, index, changeShowOperation)
        }
    }
}

@Composable
private fun ReWriteAIMenuItem(
    onFuncClick: (Int) -> Unit,
    index: Int,
    changeShowOperation: () -> Unit,
) {
    AIMenuItemRow(onItemClick = {
        changeShowOperation()
        onFuncClick(index)
    }){
        Image(
            painter = painterResource(id = rewriteIconList[index]),
            contentDescription = ""
        )
        MarqueeText(
            text = stringResource(id = rewriteFuncListConfig[index].first),
            modifier = Modifier.padding(start = 8.dp),
            fontSize = 14.sp
        )
    }
}

@Composable
private fun AIMenuItemRow(
    modifier: Modifier = Modifier,
    onItemClick: () -> Unit,
    content: @Composable (RowScope.() -> Unit)
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = 6.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        TclTheme.colorScheme.reWriteGradientLeftColor,
                        TclTheme.colorScheme.reWriteGradientRightColor
                    )
                )
            )
            .clickable {
                onItemClick()
            }
            .padding(start = 12.dp, top = 6.dp, bottom = 6.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        content()
    }
}


@Preview()
@Composable
private fun ReWriteFuncPanelPreview(){
    Column(Modifier.fillMaxSize()) {
        ReWriteFuncMenuListPanel(onFuncClick = {},modifier = Modifier.fillMaxWidth())
    }
}