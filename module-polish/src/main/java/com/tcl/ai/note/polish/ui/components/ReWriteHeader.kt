package com.tcl.ai.note.polish.ui.components

import android.graphics.Canvas
import android.graphics.drawable.VectorDrawable
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInParent
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.LiveRegionMode
import androidx.compose.ui.semantics.heading
import androidx.compose.ui.semantics.liveRegion
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import androidx.core.graphics.createBitmap
import com.tcl.ai.note.base.R
import com.tcl.ai.note.polish.constant.rewriteFuncListConfig
import com.tcl.ai.note.theme.AI_TITLE_BOTTOM_PADDING
import com.tcl.ai.note.theme.MENU_VERTICAL_PADDING
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.widget.MarqueeText
import com.tcl.ai.note.widget.components.AIHeaderCloseImage
import com.tcl.ai.note.widget.verticalScrollbar
import com.tct.theme.core.designsystem.component.TclDropDownMenuItem
import com.tct.theme.core.designsystem.component.TclDropdownMenu
import kotlinx.coroutines.launch


@Composable
internal fun ReWriteHeader(
    showCloseIcon: Boolean,
    selectedIndex: Int,
    showOperation: Boolean,
    onFuncListSelected: (Int) -> Unit,
    onCloseClick: () -> Unit
) {
    var expandPaddingY by rememberSaveable {
        mutableFloatStateOf(0f)
    }
    Row(
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .padding(bottom = AI_TITLE_BOTTOM_PADDING)
            .onGloballyPositioned {
                expandPaddingY = it.positionInParent().y
            },
        verticalAlignment = Alignment.CenterVertically
    ) {

        var expanded by rememberSaveable { mutableStateOf(false) }

        val density = LocalDensity.current

        MarqueeText(
            text = stringResource(id = R.string.ai_refinement),
            fontSize = 20.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.widthIn(max = 150.dp).semantics {
                // 设置为标题
                heading()
                // 设置为必须读出的实时区域
                liveRegion = LiveRegionMode.Polite
            }, wrapContentWidth = true
        )

        Box(
            modifier = Modifier
                .weight(1f)
                .padding(end = if (showCloseIcon) 12.dp else 0.dp)
        ) {
            val userHasSelect = selectedIndex in rewriteFuncListConfig.indices
            if (showOperation) {
                Row(
                    modifier = Modifier
                        .padding(start = 8.dp)
                        .clip(RoundedCornerShape(20.dp))
                        .background(
                            brush = Brush.linearGradient(
                                colors = listOf(
                                    colorResource(id = R.color.help_writing_template_bg_gradient_left_color),
                                    colorResource(id = R.color.help_writing_template_bg_gradient_right_color)
                                )
                            )
                        )
                        .clickable { expanded = true }
                        .padding(start = 13.dp, end = 22.dp, top = MENU_VERTICAL_PADDING, bottom =MENU_VERTICAL_PADDING),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    HeaderTitleMenuText(
                        rewriteFuncListConfig[selectedIndex].first,
                        fontSize = 14.sp,
                        expanded
                    )
                }
            }
            val scrollState = rememberScrollState()
            val coroutineScope = rememberCoroutineScope()
            TclDropdownMenu(
                expanded = expanded,
                onDismissRequest = {
                    expanded = false
                },
                modifier = Modifier
                    .background(TclTheme.colorScheme.tctAlertDialogBgColor)
                    .verticalScrollbar(
                        scrollState,
                        offsetX = with(density) { (4.dp).toPx() },
                        offsetY = with(density) { (16.dp).toPx() },
                        scrollbarHeightOffsetY = with(density) { (16.dp).toPx() })
                    .widthIn(min = 156.dp, max = 256.dp)
                    .height(170.dp),
                offset = DpOffset(8.dp, 8.dp),
                scrollState = scrollState
            ) {


                repeat(rewriteFuncListConfig.size) {
                    TclDropDownMenuItem(
                        text = {
                            Text(
                                text = stringResource(id = rewriteFuncListConfig[it].first),
                                overflow = TextOverflow.Ellipsis,
                                maxLines = 1,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium,
                                style = TextStyle(
                                    brush = if (userHasSelect && selectedIndex == it || !userHasSelect && it == 0) Brush.linearGradient(
                                        colors = listOf(
                                            colorResource(id = R.color.help_writing_template_gradient_left_color),
                                            colorResource(id = R.color.help_writing_template_gradient_right_color)
                                        ),
                                    ) else null
                                ),
                                color = if (userHasSelect && selectedIndex == it || !userHasSelect && it == 0) TclTheme.tclColorScheme.tctStanderAccentPrimary else Color.Unspecified,
                                modifier = Modifier
                            )
                        }, onClick = {
                            expanded = !expanded
                            coroutineScope.launch {
                                scrollState.animateScrollTo(0)
                            }
                            onFuncListSelected(it)
                        }, modifier = Modifier
                            .fillMaxWidth()
                            .height(40.dp),
                        trailingIcon = {
                            if (selectedIndex == it) {
                                Image(
                                    painter = painterResource(id = R.drawable.menu_selected),
                                    contentDescription = ""
                                )
                            }
                        })
                }
            }
        }
        if (showCloseIcon) {
            AIHeaderCloseImage(onCloseClick)
        }
    }
}


@Composable
private fun HeaderTitleMenuText(
    strRes: Int,
    fontSize: TextUnit = 14.sp,
    expanded: Boolean,
) {
    val context = LocalContext.current
    Text(
        text = stringResource(id = strRes),
        fontSize = fontSize,
        fontWeight = FontWeight.Medium,
        style = TextStyle(
            brush = Brush.linearGradient(
                colors = listOf(
                    colorResource(id = R.color.help_writing_template_gradient_left_color),
                    colorResource(id = R.color.help_writing_template_gradient_right_color)
                ),
            )
        ),
        maxLines = 1,
        overflow = TextOverflow.Ellipsis,
        modifier = Modifier.drawBehind {
            if (expanded) {
                ContextCompat.getDrawable(
                    context,
                    R.drawable.ic_tag_unfold
                ) as? VectorDrawable
            } else {
                ContextCompat.getDrawable(
                    context,
                    R.drawable.ic_tag_fold
                ) as? VectorDrawable
            }?.let { imageId ->
                val offset = Offset(
                    this.size.width,
                    (this.size.height - imageId.intrinsicHeight) / 2
                )
                val bitmap = createBitmap(
                    imageId.intrinsicWidth,
                    imageId.intrinsicHeight
                )
                val canvas = Canvas(bitmap)
                imageId.setBounds(0, 0, canvas.width, canvas.height)
                imageId.draw(canvas)
                drawImage(
                    topLeft = offset,
                    image = bitmap.asImageBitmap()
                )
            }
        }
    )
}

@PreviewLightDark
@Composable
private fun ReWriteHeaderPreview() {
    var selectedIndex by rememberSaveable {
        mutableIntStateOf(1)
    }
    ReWriteHeader(true, selectedIndex, true, {
        selectedIndex = it
    }, {})
}