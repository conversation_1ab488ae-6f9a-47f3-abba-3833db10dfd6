package com.tcl.ai.note.polish.ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import com.tcl.ai.note.state.ChatStreamingMsg
import com.tcl.ai.note.state.StreamingStatus
import com.tcl.ai.note.state.Result
import com.tcl.ai.note.state.testSuccessResult
import com.tcl.ai.note.widget.components.AICommonResultPanelColumn
import com.tcl.ai.note.widget.components.AIBottomOperateComponent
import com.tcl.ai.note.widget.components.AIBottomPrompt
import com.tcl.ai.note.widget.components.AIShowContentText
import com.tcl.ai.note.widget.components.AIStateLoading

/**
 * 重写的AI结果面板
 */
@Composable
fun ReWriteResultPanel(
    modifier: Modifier,
    aiLoadingStatus: Result<ChatStreamingMsg>?,
    onRetryClick: () -> Unit,
    onCopyClick: () -> Unit,
    onReplaceClick: (String) -> Unit,
    onStopClick: (Boolean) -> Unit,
    onOutput: (String) -> Unit,
    showToast: (toastId: Int) -> Unit = {}
) {
    LaunchedEffect(key1 = aiLoadingStatus) {
        if (aiLoadingStatus is Result.Success && aiLoadingStatus.data.status != StreamingStatus.IN_PROGRESS) {
            onOutput(aiLoadingStatus.data.text)
        }
    }
    val density = LocalDensity.current
    val content = remember(aiLoadingStatus) {
        when (aiLoadingStatus) {
            is Result.Success -> aiLoadingStatus.data.text
            else -> ""
        }
    }
    AICommonResultPanelColumn(modifier) {
        if (aiLoadingStatus != null) {
            if (aiLoadingStatus is Result.Loading) {
                AIStateLoading(isOffline = false, onStopClick = { onStopClick(it) })
            } else if (aiLoadingStatus is Result.Success) {
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                ) {

                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxWidth()
                    ) {
                        AIShowContentText(aiLoadingStatus)
                        if (aiLoadingStatus.data.status == StreamingStatus.IN_PROGRESS) {
                            AIStateLoading(
                                isOffline = false,
                                false,
                                onStopClick = { onStopClick(it) })
                        }
                    }
                    if (aiLoadingStatus.data.status != StreamingStatus.IN_PROGRESS) {

                        AIBottomOperateComponent(
                            content = content,
                            modifier = Modifier.fillMaxWidth(),
                            onRetryClick = onRetryClick,
                            onCopyClick = {
                                onCopyClick()
                                showToast(com.tcl.ai.note.base.R.string.copied)
                            },
                            onReplaceClick = {
                                onReplaceClick(it)
                                showToast(com.tcl.ai.note.base.R.string.replaced)
                            }
                        )
                    }
                }

            }
        } else {
            AIBottomOperateComponent(
                content = content,
                modifier = Modifier.weight(1f),
                onRetryClick = onRetryClick,
                onCopyClick = {
                    onCopyClick()
                    showToast(com.tcl.ai.note.base.R.string.copied)
                },
                onReplaceClick = {
                    onReplaceClick(it)
                    showToast(com.tcl.ai.note.base.R.string.replaced)
                }
            )
        }
        if (aiLoadingStatus !is Result.Loading &&
            !(aiLoadingStatus is Result.Success
                    && aiLoadingStatus.data.status == StreamingStatus.IN_PROGRESS)
        ) {
            AIBottomPrompt()
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun ReWriteResultPanelSuccessPreview() {
    ReWriteResultPanel(Modifier, testSuccessResult, {}, {}, { }, {}, {}, { id -> })
}

@Preview(showBackground = true)
@Composable
private fun ReWriteResultPanelErrorPreview() {
    ReWriteResultPanel(Modifier, aiLoadingStatus = null, {}, {}, { }, {}, {}, { id -> })
}

@Preview(showBackground = true)
@Composable
private fun ReWriteResultPanelLoadingPreview() {
    ReWriteResultPanel(Modifier, aiLoadingStatus = Result.Loading, {}, {}, { }, {}, {}, { id -> })
}