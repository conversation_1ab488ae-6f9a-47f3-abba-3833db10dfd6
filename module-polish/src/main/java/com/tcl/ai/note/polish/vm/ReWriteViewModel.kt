package com.tcl.ai.note.polish.vm

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.event.AIReplaceEvent
import com.tcl.ai.note.polish.assistant.PolishAIAssistantUseCase
import com.tcl.ai.note.polish.constant.rewriteFuncListConfig
import com.tcl.ai.note.polish.intent.ReWriteIntent
import com.tcl.ai.note.polish.track.AnalyticsAiRewriteModel
import com.tcl.ai.note.state.ChatEffect
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.sdk.assistant.PresetCmd
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update

@HiltViewModel
class ReWriteViewModel @Inject constructor(
    private val aiUseCase: PolishAIAssistantUseCase
) : ViewModel(){
    val reWriteState = aiUseCase.reWriteState.asStateFlow()
    private var mHasInit = false
    private var preset = PresetCmd.POLISHING
    private var sendMsgJob: Job? = null
    val aiLoadingStatus = aiUseCase.chatStreamingMsgSate.asStateFlow()
    val effect: StateFlow<ChatEffect?> get() = aiUseCase.effect.asStateFlow()

    init{
        AnalyticsAiRewriteModel.loadReWriteViewModel(this)
        aiUseCase.setupNetworkMonitor(viewModelScope)
        AccountController.connect()
    }

    private fun sendUserMsg(){
//        onStopClick() {  }
        sendMsgJob = viewModelScope.launchIO {
            aiUseCase.sendMsg(preset)
        }
    }

    fun initOriginalText(noteId: Long){
       viewModelScope.launchIO {
           aiUseCase.initOriginalText(noteId)
       }
    }

    fun handleIntent(intent: ReWriteIntent) {
        when(intent){
            is ReWriteIntent.Copy -> {
            }
            is ReWriteIntent.Generates -> {
                generateText(intent.noteId)
            }
            is ReWriteIntent.Retry -> {
                sendUserMsg()
            }
            is ReWriteIntent.Stop -> {
                onStopClick {  }
            }
            is ReWriteIntent.Replace -> {
                Logger.d(TAG, "Replace: content = ${intent.content}")
                AIReplaceEvent.sendAIPolishReplaceEvent(intent.content)
            }
            is ReWriteIntent.SelectItem -> {
                // 先停止当前正在进行的生成
                onStopClick(false) {}

                // 清除之前的结果
                aiUseCase.clearPreviousResult()

                // 更新选中的指令
                preset = rewriteFuncListConfig[intent.index].second
                Logger.d(TAG, "selectItem: preset = $preset")
                aiUseCase.reWriteState.update {
                    it.copy(selectedIndex = intent.index, recognitionResult = "")
                }

                // 发送新的消息
                sendUserMsg()
                TclAnalytics.reportAiRewriteTopic(preset)
            }
        }
    }

    private fun generateText(noteId: Long) {
        sendMsgJob = viewModelScope.launchIO {
            aiUseCase.generateText(noteId, preset)
        }
    }

    override fun onCleared() {
        super.onCleared()
        aiUseCase.clear()
    }

    private fun onStopClick(clickStop: Boolean = true, onStopSuccess: ()-> Unit){
        sendMsgJob?.cancel()
        aiUseCase.stopGenerateText()
    }
    companion object {
        private const val TAG = "ReWriteViewModel"
    }
}
