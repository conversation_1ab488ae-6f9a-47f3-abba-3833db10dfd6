package com.tcl.ai.note.summary.state



/**
 * 页面状态
 */
data class SummaryState(
    /**
    * 正在生成
    * */
    val isLoading: Boolean = false,

    val status : StreamingStatus = StreamingStatus.IDLE,
    val text : String = "",
    /**
    * 正在复制
    * */
    val isCopying: Boolean = false,
    /**
    * 正在替换
    * */
   val isReplacing: Boolean = false,
    /**
    * 正在重试
    * */
    val isRetrying: Boolean = false,
    /**
    * 发生错误
    * */
    val error: String? = null,
    /**
     * 生成的内容
     */
    val recognitionResult: String = "",
)



enum class StreamingStatus {
    IN_PROGRESS,
    COMPLETED,
    STOPPED,
    IDLE,
    ERROR
}

enum class NetworkStatus {
    NO_CONNECTION,  // 无网络连接
    NORMAL,         // 网络正常
    ERROR        // 网络异常
}





