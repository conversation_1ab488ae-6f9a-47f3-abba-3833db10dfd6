package com.tcl.ai.note.summary.ui

import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import com.tcl.ai.note.summary.vm.SummaryViewModel
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.LifecycleResumeEffect
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.controller.OnResumeLoginStateEffect
import com.tcl.ai.note.state.ChatEffect
import com.tcl.ai.note.state.ChatStreamingMsg
import com.tcl.ai.note.state.Result
import com.tcl.ai.note.state.testSuccessResult
import com.tcl.ai.note.summary.intent.SummaryIntent
import com.tcl.ai.note.summary.state.SummaryActions
import com.tcl.ai.note.summary.ui.components.SummaryHeader
import com.tcl.ai.note.summary.ui.components.SummaryResultPanel
import com.tcl.ai.note.theme.NoteTheme
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.startTCLAIChargeActivity
import com.tcl.ai.note.widget.BottomSheetDialog
import com.tcl.ai.note.widget.components.ShowToastAndDialog
import kotlinx.coroutines.launch

@Composable
fun SummaryScreenRoute(
    onDismissRequest: () -> Unit,
    summaryViewModel: SummaryViewModel = hiltViewModel()
) {
    val summaryState = summaryViewModel.summaryState.collectAsState()
    val aiLoadingStatus by summaryViewModel.aiLoadingStatus.collectAsStateWithLifecycle()
    val effectState by summaryViewModel.effect.collectAsStateWithLifecycle()
    ShowToastAndDialog(effectState){
        onDismissRequest()
    }
    OnResumeLoginStateEffect(){
        onDismissRequest()
    }
    SummaryScreen(
        aiLoadingStatus,
        summaryActions = rememberSummaryActions(summaryViewModel),
        effectState,
        onDismissRequest
    )
}



/**
 * 创建一个SummaryActions对象，并使用remember函数将其保存在缓存中，以便在组件重新创建时保留状态。
 */
@Composable
fun rememberSummaryActions(summaryViewModel: SummaryViewModel): SummaryActions {
    return remember(summaryViewModel) {
        SummaryActions(
            onRetryClick = { summaryViewModel.handleIntent(SummaryIntent.Retry) },
            onCopyClick = { summaryViewModel.handleIntent(SummaryIntent.Copy) },
            onStopClick = { clickStop ->
                summaryViewModel.handleIntent(
                    SummaryIntent.Stop
                )
            },
            onOutput = {
//                summaryViewModel.handleIntent(SummaryIntent.Output)
            },
            showToast = {
                ToastUtils.makeWithCancel(it)
            },
        )
    }
}

@Composable
private fun SummaryScreen(
    aiLoadingStatus: Result<ChatStreamingMsg>?,
    summaryActions: SummaryActions,
    effect: ChatEffect? = null,
    onDismissRequest: () -> Unit = {}
) {
    var startAiSummaryMillis by remember { mutableStateOf(System.currentTimeMillis()) }
    val coroutineScope = rememberCoroutineScope()
    var dismissDialogCallback: (suspend () -> Unit)? = null
    BottomSheetDialog(
        modifier = Modifier.fillMaxHeight(),
        visible = true,
        onDismissRequest = {
            onDismissRequest()
        },
        canceledOnTouchOutside = false,
        showFullScreenCallBack = { isFullScreen ->
            if (isFullScreen) {
                TclAnalytics.reportAiSummaryMax("0")
            }
        },
        backHandler = {},
        extraTopPadding = true,
        onDismissCallback = { callback ->
            dismissDialogCallback = callback
        },
    ) {

        Column(
            modifier = Modifier
                .navigationBarsPadding()
                .fillMaxWidth()
                .fillMaxSize()
        ) {
            SummaryHeader(
                onCloseClick = {
                    coroutineScope.launch {
                        dismissDialogCallback?.invoke()
                    }
                }
            )
            SummaryResultPanel(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                aiLoadingStatus = aiLoadingStatus,
                isOffline = effect is ChatEffect.ShowToastRes,
                onRetryClick = {
                    summaryActions.onRetryClick()
                    TclAnalytics.reportAiSummaryReset("0")
                    startAiSummaryMillis = System.currentTimeMillis()
                },
                onCopyClick = {
                    summaryActions.onCopyClick()
                    TclAnalytics.reportAiSummaryCopy("0")
                },
                onStopClick = { clickStop ->
                    summaryActions.onStopClick(clickStop)
                    TclAnalytics.reportAiSummaryStop("0", (System.currentTimeMillis() - startAiSummaryMillis).toString())
                    startAiSummaryMillis = System.currentTimeMillis()
                },
                onOutput = {
                    summaryActions.onOutput()
                }
            ) {
                summaryActions.showToast(it)
            }
        }

    }
}

@Preview()
@Composable
fun PreviewSummaryScreenLoading() {
    SummaryScreen(
        aiLoadingStatus = Result.Loading,
        summaryActions = SummaryActions()
    )
}

@Preview()
@Composable
fun PreviewSummaryScreenError() {
    SummaryScreen(
        aiLoadingStatus = Result.Error(Exception("error")),
        summaryActions = SummaryActions()
    )
}

@Preview
@Composable
fun PreviewSummaryScreenSuccess() {
    SummaryScreen(
        aiLoadingStatus = testSuccessResult,
        summaryActions = SummaryActions()
    )
}

@Preview(
    showSystemUi = true, device = "spec:parent=pixel_9_pro_fold,orientation=landscape"
)
@Composable
fun PreviewSummaryScreenNull() {
    NoteTheme {
        SummaryScreen(
            aiLoadingStatus = null,
            summaryActions = SummaryActions()
        )
    }
}
