package com.tcl.ai.note.summary.ui.components

import androidx.compose.runtime.Composable
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.heading
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.AI_TITLE_BOTTOM_PADDING
import com.tcl.ai.note.widget.MarqueeText
import com.tcl.ai.note.widget.components.AIHeaderCloseImage

@Composable
fun SummaryHeader(
    onCloseClick: () -> Unit = {}
) {
    val summaryTitle = stringResource(id = R.string.ai_summary)
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .padding(bottom =AI_TITLE_BOTTOM_PADDING),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Box(modifier = Modifier.weight(1f).padding(end = 8.dp)) {
            MarqueeText(
                modifier = Modifier.semantics {
                    heading()
                    contentDescription= summaryTitle
                },
                text = stringResource(id = R.string.ai_summary),
                fontSize = 20.sp,
                fontWeight = FontWeight.Medium
            )
        }
        AIHeaderCloseImage(onCloseClick)
    }
}

@Preview(showBackground = true, locale = "hr", fontScale = 1.8f)
@Composable
private fun SummaryHeaderPreview() {
    Column {
        SummaryHeader()
    }
}