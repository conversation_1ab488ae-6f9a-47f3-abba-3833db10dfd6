package com.tcl.ai.note.summary.ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.state.ChatStreamingMsg
import com.tcl.ai.note.state.StreamingStatus
import com.tcl.ai.note.state.Result
import com.tcl.ai.note.state.testSuccessResult
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.widget.components.AICommonResultPanelColumn
import com.tcl.ai.note.widget.components.AIBottomPrompt
import com.tcl.ai.note.widget.components.AIBottomOperateComponent
import com.tcl.ai.note.widget.components.AIShowContentText
import com.tcl.ai.note.widget.components.AIStateLoading

@Composable
fun SummaryResultPanel(
    modifier: Modifier = Modifier,
    aiLoadingStatus: Result<ChatStreamingMsg>?,
    isOffline: Boolean = false,
    onRetryClick: () -> Unit,
    onCopyClick: () -> Unit,
    onStopClick: (Boolean) -> Unit,
    onOutput: (String) -> Unit,
    showToast: (toastId: Int) -> Unit = {}
) {
    LaunchedEffect(key1 = aiLoadingStatus) {
        Logger.d("SummaryResultPanel", "aiLoadingStatus.data.status : ${aiLoadingStatus}")
        if (aiLoadingStatus is Result.Success && aiLoadingStatus.data.status != StreamingStatus.IN_PROGRESS) {
            onOutput(aiLoadingStatus.data.text)
        }
    }


    AICommonResultPanelColumn(modifier = modifier) {
        Logger.d("SummaryResultPanel", "aiLoadingStatus : $aiLoadingStatus")
        if (aiLoadingStatus != null) {
            when (aiLoadingStatus) {
                is Result.Loading -> {
                    AIStateLoading(isOffline, onStopClick = {
                        onStopClick(true)
                        showToast(R.string.stopped_answer)
                    })
                }

                is Result.Success -> {
                    Box(modifier = Modifier.weight(1f)) {
                        SummarySuccessContent(
                            aiLoadingStatus,
                            isOffline,
                            onStopClick,
                            showToast,
                            onRetryClick,
                            onCopyClick = {
                                onCopyClick()
                                showToast(R.string.copied)
                            }
                        )
                    }
                }

                else -> {
                    AIBottomOperateComponent(
                        modifier = Modifier.weight(1f),
                        onRetryClick = onRetryClick,
                        onCopyClick={
                            onCopyClick()
                            showToast(R.string.copied)
                        }
                    )
                }
            }
        } else {
            AIBottomOperateComponent(
                modifier = Modifier.weight(1f),
                onRetryClick = onRetryClick,
                onCopyClick={
                    onCopyClick()
                    showToast(R.string.copied)
                }
            )
        }
        if (aiLoadingStatus !is Result.Loading &&
            !(aiLoadingStatus is Result.Success
                    && aiLoadingStatus.data.status == StreamingStatus.IN_PROGRESS)
        ) {
            AIBottomPrompt()
        }
    }
}

@Composable
private fun SummarySuccessContent(
    aiLoadingStatus: Result.Success<ChatStreamingMsg>,
    isOffline: Boolean,
    onStopClick: (Boolean) -> Unit,
    showToast: (Int) -> Unit,
    onRetryClick: () -> Unit,
    onCopyClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .wrapContentHeight()
    ) {

        Box(modifier = Modifier
            .weight(1f)
            .fillMaxWidth()
        ) {
            AIShowContentText(aiLoadingStatus)
            if (aiLoadingStatus.data.status == StreamingStatus.IN_PROGRESS) {
                AIStateLoading(
                    isOffline = isOffline,
                    false,
                    onStopClick = {
                        onStopClick(it)
                        showToast(R.string.stopped_answer)
                    })
            }
        }
        if (aiLoadingStatus.data.status != StreamingStatus.IN_PROGRESS) {
            // 底部操作组件固定在底部
            AIBottomOperateComponent(
                modifier = Modifier.fillMaxWidth(),
                aiLoadingStatus.data.text,
                onRetryClick,
                onCopyClick
            )
        }
    }
}
@Preview(showBackground = true)
@Composable
fun SummarySuccessContentPreview(
) {
    Box(modifier = Modifier
        .height(300.dp)
        .width(300.dp)) {
        SummarySuccessContent(
            testSuccessResult,
            false,
            { },
            { },
            { },
            { }
        )
    }
}


@Preview
@Composable
private fun SummaryResultNullPreview() {
    Box(modifier = Modifier.height(300.dp)) {
        SummaryResultPanel(
            aiLoadingStatus = testSuccessResult,
            onRetryClick = {},
            onCopyClick = {},
            onStopClick = { },
            onOutput = {}) { id -> }
    }
}