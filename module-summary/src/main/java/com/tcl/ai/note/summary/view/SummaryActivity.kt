package com.tcl.ai.note.summary.view

import android.content.res.Configuration
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.tcl.ai.note.summary.ui.SummaryScreenRoute
import com.tcl.ai.note.theme.NoteTheme
import com.tcl.ai.note.utils.Logger
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SummaryActivity : ComponentActivity() {
    private var lastSmallestScreenWidthDp = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Logger.d(TAG, "onCreate: $this")

        enableEdgeToEdge()
        lastSmallestScreenWidthDp = resources.configuration.smallestScreenWidthDp
        intent?.getLongExtra("noteId", 0)?.let {
            Logger.d(TAG, "noteId =  $it")
        }

        setContent {
            NoteTheme {
                SummaryScreenRoute(onDismissRequest = {
                    Logger.d(TAG, " --- finish() ---")
                    finish()
                })
            }
        }
    }

    /**
     * TCL AI方案 切换暗黑模式 重新设置enableEdgeToEdge()
     */
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enableEdgeToEdge()
//        if (newConfig.smallestScreenWidthDp != lastSmallestScreenWidthDp) {
//            lastSmallestScreenWidthDp = newConfig.smallestScreenWidthDp;
//            Logger.d(TAG, "smallestScreenWidthDp: " + newConfig.smallestScreenWidthDp + "dp")
//            return
//        }
    }

    companion object {
        private const val TAG = "SummaryActivity"
    }
}
