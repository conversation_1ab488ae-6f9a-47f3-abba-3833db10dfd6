package com.tcl.ai.note.summary.vm

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.summary.intent.SummaryIntent
import com.tcl.ai.note.summary.state.SummaryState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.state.ChatEffect
import com.tcl.ai.note.summary.assistant.SummaryAIAssistantUseCase
import com.tcl.ai.note.summary.track.AnalyticsAiSummaryModel
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.sdk.assistant.PresetCmd
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.StateFlow

@HiltViewModel
class SummaryViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val aiUseCase: SummaryAIAssistantUseCase
) : ViewModel() {
    private val _summaryState = MutableStateFlow(SummaryState())
    val summaryState = _summaryState.asStateFlow()

    val aiLoadingStatus = aiUseCase.chatStreamingMsgSate.asStateFlow()
    val effect: StateFlow<ChatEffect?> get() = aiUseCase.effect.asStateFlow()

    private var assistCallId = -1
    private var mHasInit = false
    private val preset = PresetCmd.SUMMARIZE
    // 从SavedStateHandle获取noteId
    private val noteId: Long = savedStateHandle.get<Long>("noteId") ?: 0L
    init {
        AnalyticsAiSummaryModel.loadHelpWritingViewModel(this)
        aiUseCase.setupNetworkMonitor(viewModelScope)
        clearData()
        AccountController.connect()
        handleIntent(SummaryIntent.Generates(noteId))
    }


    private var sendMsgJob: Job? = null

    private fun sendUserMsg(clickStop: Boolean = true) {

        onStopClick(clickStop) { }
        sendMsgJob = viewModelScope.launch {
            aiUseCase.reSendMsg(preset)
        }
    }


    private fun clearData() {
        onStopClick { }
    }


    fun handleIntent(intent: SummaryIntent) {
        when (intent) {
            is SummaryIntent.Copy -> {

            }
            // 生成文本,activity启动时会调用一次
            is SummaryIntent.Generates -> {
                Logger.d(TAG, "handleIntent: ${intent.noteId}")
                generateText(intent)
            }

            is SummaryIntent.Retry -> {
                sendUserMsg()
            }

            is SummaryIntent.Stop -> {
                onStopClick { }
            }
        }
    }

    private fun generateText(intent: SummaryIntent.Generates) {
        sendMsgJob = viewModelScope.launch {
            Logger.d(TAG, "generateText: ${intent.noteId}")
            aiUseCase.generateText(intent.noteId, preset)
        }
    }

    private fun onStopClick(clickStop: Boolean = true, onStopSuccess: () -> Unit) {
        sendMsgJob?.cancel()
        aiUseCase.stopGenerateText()
    }

    override fun onCleared() {
        super.onCleared()
        destroyAssist()
    }

    private fun destroyAssist() {
        viewModelScope.launch {
            assistCallId = -1
        }
    }


    companion object {
        private const val TAG = "SummaryViewModel"
    }
}
