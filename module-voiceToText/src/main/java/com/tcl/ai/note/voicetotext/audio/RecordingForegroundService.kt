package com.tcl.ai.note.voicetotext.audio

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK
import android.os.IBinder
import android.widget.RemoteViews
import androidx.core.app.NotificationCompat
import androidx.lifecycle.LifecycleService
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.voicetotext.R
/**
 * 录音需要使用前台服务，否则录制过程中会出现声音无法录入的问题
 * */
class RecordingForegroundService: LifecycleService() {
    companion object {
        private const val TAG = "RecordingForegroundService"
        private const val CHANNEL_ID = "NOTE_RECORDING_CHANNEL"

        fun startServiceForStartRecording(audioPath: String) {
            val intent = Intent(GlobalContext.instance, RecordingForegroundService::class.java).apply {
                action = "startRecording"
                putExtra("audioPath", audioPath)
            }
            GlobalContext.instance.startForegroundService(intent)
        }

        fun stopRecordingService() {
            val intent = Intent(GlobalContext.instance, RecordingForegroundService::class.java)
            GlobalContext.instance.stopService(intent)
        }
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        startForeground(1, createNotification(), FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK)
        if (intent?.action == "startRecording") {
            val audioPath = intent.getStringExtra("audioPath")?: ""
            ShowAudioRecorder.startRecord(audioPath)
        }
        return START_NOT_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        // 确保在服务被销毁时停止录音
        if (ShowAudioRecorder.isRecording) {
            ShowAudioRecorder.stopRecord()
        }
        Logger.d(TAG, "onDestroy: 服务被销毁，停止录音")
    }

    private fun createNotification() : Notification {
        val customView = RemoteViews(this.packageName, R.layout.note_recording_notification_layout)
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContent(customView)
            .setCustomBigContentView(customView)
            .setContentIntent(null)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setSmallIcon(R.drawable.ic_recorded_file)
            .build()
    }

    private fun createNotificationChannel() {
        val serviceChannel = NotificationChannel(
            CHANNEL_ID,
            getString(com.tcl.ai.note.base.R.string.note_recording_channel_name),
            NotificationManager.IMPORTANCE_LOW
        )
        val manager = getSystemService(NotificationManager::class.java)
        manager?.createNotificationChannel(serviceChannel)
    }
}