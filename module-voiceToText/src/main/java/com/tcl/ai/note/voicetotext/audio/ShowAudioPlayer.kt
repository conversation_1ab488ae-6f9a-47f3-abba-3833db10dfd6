package com.tcl.ai.note.voicetotext.audio

import android.content.Context
import android.media.AudioManager
import android.media.AudioManager.AUDIOFOCUS_REQUEST_FAILED
import android.media.AudioManager.OnAudioFocusChangeListener
import android.media.MediaPlayer
import android.media.MediaPlayer.OnCompletionListener
import android.text.TextUtils
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.utils.AppActivityManager
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.Logger.e
import com.tcl.ai.note.voicetotext.states.RecordPlayingState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException

/**
 * 用于显示音频播放器的类
 *
 * 该类负责管理音频播放的状态，包括开始、暂停和停止播放音频，
 * 并更新 UI 显示音频播放的状态。
 */
object ShowAudioPlayer {
    private val TAG = "ShowAudioPlayer"

    // 存储上一次播放的音频路径
    private var lastAudioPath: String? = null

    // 播放标志，用于标识当前是否正在播放
    private var playingFlag = false

    // MediaPlayer对象，用于播放音频
    private var mediaPlayer: MediaPlayer? = null


    private val _playerState = MutableStateFlow(RecordPlayingState())
    val playerState = _playerState.asStateFlow()

    private var runningJob: Job? = null
    private var lifecycleJob: Job? = null

    /**
     * 更新 UI 为播放状态
     * @param audioPath 当前播放的音频路径
     * @param playingPosition 当前播放的位置
     */
    private fun showPlay(audioPath: String?, playingPosition: Int) {
        _playerState.value = RecordPlayingState(
            isPlaying = false,
            audioPath = audioPath,
            playingPosition = playingPosition,
            totalDuration = mediaPlayer?.duration ?: 0
        )
    }

    /**
     * 更新 UI 为暂停状态
     */
    private fun showPause() {
        _playerState.value = RecordPlayingState(
            isPlaying = true,
            audioPath = lastAudioPath,
            playingPosition = mediaPlayer?.currentPosition ?: 0,
            totalDuration = mediaPlayer?.duration ?: 0
        )
    }

    /**
     * 开始播放音频
     * @param audioPath 要播放的音频文件路径
     */
    fun startPlay(audioPath: String?) {
        Logger.d(TAG, "startPlay: $audioPath")
        if (TextUtils.isEmpty(audioPath) || !File(audioPath!!).exists()) return

        // 如果当前有音频在播放，先停止它
        if (isPlaying && audioPath != lastAudioPath) {
            stopPlay()
        }

        if (audioPath == lastAudioPath) {
            if (!isPlaying) {
                start()
            }
            return
        }
        if (mediaPlayer == null) {
            mediaPlayer = MediaPlayer()
        }

        try {
            mediaPlayer?.setDataSource(audioPath)
            lastAudioPath = audioPath
        } catch (e: IOException) {
            releasePlayer()
            e(TAG, "startPlay io exception: ${e.message}")
            return
        } catch (e: IllegalArgumentException) {
            releasePlayer()
            e(TAG, "startPlay illegal argument exception: ${e.message}")
            return
        } catch (e: SecurityException) {
            releasePlayer()
            e(TAG, "startPlay security exception: ${e.message}")
            return
        } catch (e: IllegalStateException) {
            releasePlayer()
            e(TAG, "startPlay illegal state exception: ${e.message}")
            return
        }

        mediaPlayer?.setOnCompletionListener(completionListener)
        mediaPlayer?.setOnErrorListener(onErrorListener)

        mediaPlayer?.setVolume(1f, 1f)
        mediaPlayer?.isLooping = false

        try {
            mediaPlayer?.prepare()
        } catch (e: IOException) {
            releasePlayer()
            e(TAG, "startPlay prepare io exception: ${e.message}")
            return
        } catch (e: IllegalStateException) {
            releasePlayer()
            e(TAG, "startPlay prepare illegal state exception: ${e.message}")
            return
        }

        requestAudioFocus()
        start()
    }

    fun seekTo(position: Int, audioPath: String) {
        if (lastAudioPath != audioPath) {
            startPlay(audioPath)
        } else if (!isPlaying) {
            start()
        }
        mediaPlayer?.seekTo(position)
        showPause()
    }

    /**
     * 暂停播放音频
     */
    fun pausePlay() {
        if (isPlaying) {
            playingFlag = false
            showPlay(lastAudioPath, mediaPlayer?.currentPosition ?: 0)
            pause()
        }
    }

    // 判断当前是否正在播放
    private val isPlaying: Boolean
        get() {
            return playingFlag && mediaPlayer?.isPlaying == true
        }

    /**
     * 公开方法：判断当前是否正在播放
     */
    fun isCurrentlyPlaying(): Boolean {
        return isPlaying
    }

    /**
     * 停止播放音频
     */
    fun stopPlay() {
        releasePlayer()
    }

    /**
     * 释放播放器资源
     */
    private fun releasePlayer() {
        showPlay(lastAudioPath, 0)
        lastAudioPath = null
        playingFlag = false
        abandonAudioFocus()
        runningJob?.cancel()
        runningJob = null
        mediaPlayer?.reset()
        mediaPlayer?.release()
        mediaPlayer = null
    }

    /**
     * 开始播放
     */
    private fun start() {
        try {
            mediaPlayer?.start()
            playingFlag = true
        } catch (e: RuntimeException) {
            releasePlayer()
            e(TAG, "startPlay start runtime exception: ${e.message}")
            return
        }
        showPause()
        GlobalContext.applicationScope.launch {
            runningJob?.cancelAndJoin()
            runningJob = launch {
                while (isPlaying) {
                    delay(500)
                    if (isPlaying) {
                        withContext(Dispatchers.Main) {
                            showPause()
                        }
                    }
                }
            }
        }
    }

    /**
     * 暂停播放
     */
    private fun pause() {
        try {
            mediaPlayer?.pause()
        } catch (e: RuntimeException) {
            e(TAG, "pausePlay runtime exception: ${e.message}")
        }
    }

    /**
     * 请求音频焦点
     * @return 请求结果状态
     */
    private fun requestAudioFocus(): Int {
        val am = GlobalContext.instance.getSystemService(AudioManager::class.java)
        return am?.requestAudioFocus(
            afChangeListener,
            AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
        )?: AUDIOFOCUS_REQUEST_FAILED
    }

    /**
     * 放弃音频焦点
     */
    private fun abandonAudioFocus() {
        val am = GlobalContext.instance.getSystemService(AudioManager::class.java)
        am?.abandonAudioFocus(afChangeListener)
    }

    // 播放完成监听器
    private var completionListener: OnCompletionListener = OnCompletionListener {
        Logger.d(TAG, "onCompletion: ")
        stopPlay()
    }

    // 播放错误监听器
    private var onErrorListener: MediaPlayer.OnErrorListener = MediaPlayer.OnErrorListener { mp, what, extra ->
        Logger.d(TAG, "onError: what=$what")
        stopPlay()
        true
    }

    // 音频焦点变化监听器
    private var afChangeListener: OnAudioFocusChangeListener = OnAudioFocusChangeListener { focusChange ->
        when (focusChange) {
            AudioManager.AUDIOFOCUS_LOSS -> {
                // 永久失去音频焦点，停止播放
                stopPlay()
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                // 暂时失去音频焦点，暂停播放
                pausePlay()
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                // 可以降低音量继续播放，但为了用户体验，选择暂停
                pausePlay()
            }
            AudioManager.AUDIOFOCUS_GAIN -> {
                // 重新获得音频焦点，但不自动恢复播放，让用户手动控制
            }
        }
    }
}