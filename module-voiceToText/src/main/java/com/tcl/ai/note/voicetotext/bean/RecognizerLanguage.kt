package com.tcl.ai.note.voicetotext.bean


val translationLanguageList = listOf(
    "en-US" to "English (United States)",
    "en-GB" to "English (United Kingdom)",
    "zh-CN" to "中文 (简体)",
    "zh-HK" to "中文 (繁體)",
    "es" to "Español (Latinoamérica)",
    "es-ES" to "Español (España)",
    "pt-BR" to "Português (Brasil)",
    "ar" to "العربية (الإمارات العربية المتحدة)",//
    "fr-FR" to "Français (France)",
    "fr-CA" to "Français (Canada)",
    "ru" to "Русский (Россия)",//
    "ja" to "日本語 (日本)",//
    "de-DE" to "Deutsch (Deutschland)",
    "ko" to "한국어 (대한민국)",//
    "id" to "Bahasa Indonesia (Indonesia)",//
    "tr-TR" to "<PERSON>ürk<PERSON><PERSON> (Türkiye)",
    "it-IT" to "Italiano (Italia)",
    "pt-PT" to "Português (Portugal)",
    "pl" to "Polski (Polska)",//
    "nl-NL" to "Nederlands (Nederland)",
    "uk" to "Українська (Україна)",//
    "th" to "ไทย (ประเทศไทย)",//
    "vi" to "Tiếng Việt (Việt Nam)",//
    "fa" to "فارسی (ایران)",//
    "el" to "Ελληνικά (Ελλάδα)",//
    "hu" to "Magyar (Magyarország)",//
    "ro-RO" to "Română (România)",
    "cs" to "Čeština (Česko)",//
    "bg" to "Български (България)",//
    "da" to "Dansk (Danmark)",//
    "no" to "Norsk (Norge)",
    "sv-SE" to "Svenska (Sverige)",
    "fi" to "Suomi (Suomi)"//
)

val translationLanguageKeys = listOf(
    "en-US",
    "en-GB",
    "zh-CN",
    "zh-HK",
    "es-419",
    "es-ES",
    "pt-BR",
    "ar-AE",
    "fr-FR",
    "fr-CA",
    "ru-RU",
    "ja-JP",
    "de-DE",
    "ko-KR",
    "id-ID",
    "tr-TR",
    "it-IT",
    "pt-PT",
    "pl-PL",
    "nl-NL",
    "uk-UA",
    "th-TH",
    "vi-VN",
    "fa-IR",
    "el-GR",
    "hu-HU",
    "ro-RO",
    "cs-CZ",
    "bg-BG",
    "da-DK",
    "no-NO",
    "sv-SE",
    "fi-FI"
)
