package com.tcl.ai.note.voicetotext.data

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase

/**
 * 数据库类，用于管理应用的数据库配置。
 * 使用 Room 库创建一个数据库实例，包含会议传输实体。
 */
@Database(entities = [AudioTransferEntity::class], version = 1, exportSchema = false)
abstract class AppDatabase : RoomDatabase() {
    abstract val dao: AudioDao

    companion object {
        @Volatile
        private var instance: AppDatabase? = null

        /**
         * 获取数据库实例的方法。
         * 使用双重检查锁定（Double-Checked Locking）模式来确保实例的唯一性。
         * @param context 上下文对象，用于创建数据库。
         * @return AppDatabase 数据库实例。
         */
        fun getInstance(context: Context): AppDatabase =
            instance ?: synchronized(this) {
                instance ?: buildDatabase(context).also { instance = it }
            }

        /**
         * 创建数据库的方法。
         * @param context 上下文对象，用于数据库构建。
         * @return AppDatabase 新创建的数据库实例。
         */
        private fun buildDatabase(context: Context) =
            Room.databaseBuilder(
                context.applicationContext,
                AppDatabase::class.java, "audio_info.db"
            ).build()
    }
}