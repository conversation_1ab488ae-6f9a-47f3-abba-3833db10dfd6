package com.tcl.ai.note.voicetotext.data

import androidx.room.ColumnInfo
import androidx.room.Dao
import androidx.room.Entity
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.PrimaryKey
import androidx.room.Query
import kotlinx.coroutines.flow.Flow

/**
 * 数据类 AudioTransferEntity 音频转录实体
 *
 * @property id 主键，自动生成的标识符
 * @property audioPath 音频文件的路径
 * @property content 转录内容
 * @property startTime 转录开始时间
 * @property endTime 转录结束时间
 * @property personNum 说话人编号
 */
@Entity(tableName = "transcription")
data class AudioTransferEntity(
    @PrimaryKey(autoGenerate = true)
    var id: Long = 0,
    @ColumnInfo(name = "audio_path")
    val audioPath: String,
    @ColumnInfo(name = "content")
    val content: String,
    @ColumnInfo(name = "start_time")
    val startTime: Long,
    @ColumnInfo(name = "end_time")
    val endTime: Long,
    @ColumnInfo(name = "person_num")
    val personNum: String
)

/**
 * 数据访问对象 (DAO) 接口 AudioDao，用于操作数据库中的转录数据
 */
@Dao
interface AudioDao {

    /**
     * 插入传输记录
     *
     * @param audioTransfers 要插入的 AudioTransferEntity 列表
     */
    @Insert
    suspend fun insertAudioTransfers(audioTransfers: List<AudioTransferEntity>)

    /**
     * 根据音频路径查询转录记录
     *
     * @param audioPath 音频文件路径
     * @return 返回匹配的 AudioTransferEntity 列表
     */
    @Query("SELECT * FROM transcription WHERE audio_path = :audioPath")
    suspend fun getAudioTransfers(audioPath: String): List<AudioTransferEntity>

    /**
     * 根据音频路径删除转录记录
     *
     * @param audioPath 音频文件路径
     */
    @Query("DELETE FROM transcription WHERE audio_path = :audioPath")
    suspend fun deleteTransfersByAudioPath(audioPath: String)

    /**
     * 更新重命名文件路径
     */
    @Query("UPDATE transcription SET audio_path = :newPath WHERE audio_path = :oldPath")
    suspend fun updateRenameFilePath(oldPath: String, newPath: String)
}