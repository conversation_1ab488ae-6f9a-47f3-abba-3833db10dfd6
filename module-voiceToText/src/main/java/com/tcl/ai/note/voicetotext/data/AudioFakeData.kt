package com.tcl.ai.note.voicetotext.data

import com.tcl.ai.note.voicetotext.states.AudioResult
import com.tcl.ai.note.voicetotext.states.AudioStreamingMsg
import com.tcl.ai.note.voicetotext.states.AudioStreamingStatus


 fun getAudioTestSuccessState(): AudioResult.Success<AudioStreamingMsg> {
    val transfers = mutableListOf<AudioTransferEntity>()
    for (i in 0..30) {
        transfers.add(
            AudioTransferEntity(
                i.toLong(),
                "1",
                "Hello World",
                0,
                100,
                "1"
            )
        )
    }
    return AudioResult.Success(
        AudioStreamingMsg(
        status = AudioStreamingStatus.COMPLETED,
        transfers = transfers,
        threadId = "",
        userMessageId = ""
    )
    )
}
val audioErrorState = AudioResult.Error(Exception())

