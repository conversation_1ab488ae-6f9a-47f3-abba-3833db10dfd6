package com.tcl.ai.note.voicetotext.data

import com.tcl.ai.note.voicetotext.states.LanguageConfigState
import kotlinx.coroutines.flow.Flow

interface AudioRepository {
    /**
     * 获取指定音频路径的音频转录数据
     * @param audioPath 音频文件路径
     * @return 音频传输实体列表的流
     */
    suspend fun getAudioTransfers(audioPath: String): List<AudioTransferEntity>

    /**
     * 观察指定音频路径的音频删除状态
     * @param audioPath 音频文件路径
     * @return 布尔值的流，表示音频是否被删除
     */
    fun observeAudioDeletion(audioPath: String): Flow<Boolean>

    /**
     * 删除指定音频路径的转录记录
     * @param audioPath 音频文件路径
     */
    suspend fun deleteTransfersByAudioPath(audioPath: String)

    /**
     * 保存转录文本到数据库中
     * @param audioPath 音频文件路径
     * @param transfers 转录文本列表
     */
    suspend fun saveTranscription(audioPath: String, transfers: List<AudioTransferEntity>)


    /**
     * 保存当前用户所选择的语言的索引
     * @param selectLanguageIndex 要保存的当前所选语言索引
     */
    fun saveCurrentLanguage(selectLanguageIndex: Int)

    /**
     * 加载默认配置的方法
     * @return 配置状态对象
     */
    fun loadDefaultConfig() : LanguageConfigState

    fun getLanguage(): Int

    /**
     * 保存当前用户正在录音转文的文件路径
     * @param filePath 文件路径
     * */
    fun saveCurrentTranscriptionFile(filePath: String)

    /**
     * 获取当前用户正在录音转文的文件路径
     * */
    fun getCurrentTranscriptionFile(): String

    /**
     * 获取上一次转写的开始时间
     * */
    fun getLastTranscriptionTime(): Long

    /**
     * 重命名录音文件
     * */
    suspend fun renameAudioFile(oldAudioPath: String, newAudioPath: String): Boolean
}