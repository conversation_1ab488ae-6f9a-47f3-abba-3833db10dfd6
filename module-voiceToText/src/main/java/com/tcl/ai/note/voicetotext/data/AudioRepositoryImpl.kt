package com.tcl.ai.note.voicetotext.data

import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.utils.SPUtils
import com.tcl.ai.note.utils.systemLang
import com.tcl.ai.note.utils.systemLangCountry
import com.tcl.ai.note.voicetotext.bean.translationLanguageKeys
import com.tcl.ai.note.voicetotext.states.LanguageConfigState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import java.io.File
import java.util.Locale
import javax.inject.Inject


class AudioRepositoryImpl @Inject constructor(
    private val audioDao: AudioDao,
) : AudioRepository {

    /**
     * 观察指定音频路径的音频删除状态
     * @param audioPath 音频文件路径
     * @return 布尔值的流，表示音频是否被删除
     */
    override fun observeAudioDeletion(audioPath: String): Flow<Boolean> = callbackFlow {
        getFileFlow(audioPath).collect {
            trySend(!it.exists())
        }
        awaitClose {
            // do nothing
        }
    }

    /**
     * 获取文件流的方法
     *
     * @param audioPath 音频文件的路径
     * @return 返回一个文件流，流中的元素为指定路径的文件
     */
    private fun getFileFlow(audioPath: String): Flow<File> = flow {
        emit(File(audioPath))
    }

    /**
     * 获取指定音频路径的音频转录数据
     * @param audioPath 音频文件路径
     * @return 音频传输实体列表的流
     */
    override suspend fun getAudioTransfers(audioPath: String): List<AudioTransferEntity> {
        return audioDao.getAudioTransfers(audioPath)
    }

    /**
     * 删除指定音频路径的转录记录
     * @param audioPath 音频文件路径
     */
    override suspend fun deleteTransfersByAudioPath(audioPath: String) {
        audioDao.deleteTransfersByAudioPath(audioPath)
    }

    /**
     * 保存转录文本到数据库中
     * @param audioPath 音频文件路径
     * @param transfers 转录文本列表
     */
    override suspend fun saveTranscription(audioPath: String, transfers: List<AudioTransferEntity>) {
        if (transfers.isEmpty() || audioPath.isEmpty()) {
            return
        }
        withContext(Dispatchers.IO) {
            //重新生成，先删除已经存在的转写数据
            deleteTransfersByAudioPath(audioPath)
            audioDao.insertAudioTransfers(transfers)
        }
    }

    /**
     * 保存当前用户所选择的语言的索引
     * @param selectLanguageIndex 要保存的当前所选语言索引
     */
    override fun saveCurrentLanguage(selectLanguageIndex: Int){
        SPUtils.setSync(
            key = DataStoreParam.KEY_AUDIO_CURRENT_LANGUAGE,
            value = selectLanguageIndex
        )
    }

    /**
     * 加载默认配置的方法
     * @return 配置状态对象
     */
    override fun loadDefaultConfig() : LanguageConfigState {
        val currentLanguageIndex =  getLanguage()
        return LanguageConfigState(
            selectLanguageIndex = currentLanguageIndex
        )
    }

    override fun getLanguage(): Int {
        val currentLanguage = getCurrentLanguage()
        return if (currentLanguage == DataStoreParam.DEFAULT_AUDIO_LANGUAGE) {
            getIndexForSystemLanguage()
        } else {
            currentLanguage
        }
    }

    private fun getIndexForSystemLanguage(): Int {
        val languageCode = getSystemLanguageCode()
        val systemLanInList = translationLanguageKeys.contains(languageCode)
        return if (systemLanInList) {
            translationLanguageKeys.indexOf(languageCode)
        } else {
            0
        }
    }

    private fun getSystemLanguageCode(): String {
        return if (systemLang.isNotEmpty()) {
            "$systemLang-$systemLangCountry"
        } else {
            if (Locale.getDefault().language.equals("zh")) {
                "zh-HK"
            } else {
                "en-US"
            }
        }
    }

    /**
     * 获取当前用户设置或者初始默认的语言
     * @return 当前用户选择的语言
     * DataStoreParam.KEY_CURRENT_LANGUAGE 语言的key
     * DataStoreParam.DEFAULT_LANGUAGE 初始默认语言索引
     */
    private fun getCurrentLanguage(): Int {
        return SPUtils.getInt(
            key = DataStoreParam.KEY_AUDIO_CURRENT_LANGUAGE,
            defValue = DataStoreParam.DEFAULT_AUDIO_LANGUAGE
        )
    }

    /**
     * 保存当前用户正在录音转文的文件路径
     * @param filePath 文件路径
     * */
    override fun saveCurrentTranscriptionFile(filePath: String) {
        SPUtils.setSync(
            key = DataStoreParam.KEY_AUDIO_CURRENT_TRANSCRIPTION_FILE,
            value = filePath
        )
        if (filePath.isNotEmpty()) {
            SPUtils.setSync(
                key = DataStoreParam.KEY_AUDIO_CURRENT_TRANSCRIPTION_TIME,
                value = System.currentTimeMillis().toString()
            )
        }
    }

    /**
     * 获取当前用户正在录音转文的文件路径
     * */
    override fun getCurrentTranscriptionFile(): String {
        return SPUtils.getString(
            key = DataStoreParam.KEY_AUDIO_CURRENT_TRANSCRIPTION_FILE,
            defValue = ""
        ) ?: ""
    }

    /**
     * 获取当前用户正在录音转文的文件路径
     * */
    override fun getLastTranscriptionTime(): Long {
        val time = SPUtils.getString(
            key = DataStoreParam.KEY_AUDIO_CURRENT_TRANSCRIPTION_TIME,
            defValue = "0"
        )?: "0"
        return time.toLongOrNull() ?: System.currentTimeMillis()
    }

    /**
     * 重命名录音文件
     * @param filePath 文件路径
     * @param newFileName 新文件名
     * */
    override suspend fun renameAudioFile(oldAudioPath: String, newAudioPath: String): Boolean {
        if (oldAudioPath.isEmpty() || newAudioPath.isEmpty()) {
            return false
        }
        withContext(Dispatchers.IO) {
            audioDao.updateRenameFilePath(oldAudioPath, newAudioPath)
        }
        return true
    }
}

