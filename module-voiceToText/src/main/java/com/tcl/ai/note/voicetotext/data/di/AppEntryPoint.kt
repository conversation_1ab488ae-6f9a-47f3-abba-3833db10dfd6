package com.tcl.ai.note.voicetotext.data.di

import com.tcl.ai.note.voicetotext.data.AudioRepository
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * 用于为依赖项注入容器指定入口点
 * 此接口将在 SingletonComponent 范围内安装
 */
@EntryPoint
@InstallIn(SingletonComponent::class)
interface AppEntryPoint {

    /**
     * 提供 RecorderRepository 实例的方法
     * @return 返回 RecorderRepository 对象
     */
    fun recordRepository(): AudioRepository
}