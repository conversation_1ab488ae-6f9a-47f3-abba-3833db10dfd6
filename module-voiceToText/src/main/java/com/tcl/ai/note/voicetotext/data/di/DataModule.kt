package com.tcl.ai.note.voicetotext.data.di

import com.tcl.ai.note.voicetotext.data.AudioRepositoryImpl
import com.tcl.ai.note.voicetotext.data.AudioRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
interface DataModule {

    /**
     * 绑定 AudioRepositoryImpl 实现到 RecorderRepository 接口
     * @param recorderRepository AudioRepositoryImpl 实例，作为接口的实现
     * @return 返回 RecorderRepository 接口的绑定实现
     */
    @Singleton
    @Binds
    fun bindsRecorderRoomRepository(
        recorderRepository: AudioRepositoryImpl
    ): AudioRepository

}
