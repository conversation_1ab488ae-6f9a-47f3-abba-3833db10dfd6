package com.tcl.ai.note.voicetotext.data.di

import android.content.Context
import androidx.room.Room
import com.tcl.ai.note.voicetotext.data.AppDatabase
import com.tcl.ai.note.voicetotext.data.AudioDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 用于提供数据库相关的依赖项。
 * 它被安装在 SingletonComponent 中，使其单例。
 */
@Module
@InstallIn(SingletonComponent::class)
class DatabaseModule {

    /**
     * 提供 RecorderRoomDao 的依赖项。
     * @param appDatabase 应用程序的 AppDatabase 实例。
     * @return AudioDao 数据访问对象。
     */
    @Provides
    fun provideRecorderRoomDao(appDatabase: AppDatabase): AudioDao {
        return appDatabase.dao
    }

    /**
     * 提供 RecorderDatabase 的依赖项，以单例形式提供。
     * @param appContext 应用程序的 Context 用于数据库构建。
     * @return AppDatabase 实例。
     */
    @Provides
    @Singleton
    fun provideRecorderDatabase(@ApplicationContext appContext: Context): AppDatabase {
        return Room.databaseBuilder(
            appContext,
            AppDatabase::class.java,
            "audio_transcription.db"
        ).build()
    }
}