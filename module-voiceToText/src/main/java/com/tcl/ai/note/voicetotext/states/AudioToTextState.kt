package com.tcl.ai.note.voicetotext.states

import com.tcl.ai.note.voicetotext.data.AudioTransferEntity

/**
 * 录音转文本状态
 */
data class AudioToTextState(
    val isLoading: Boolean = false,
    val audioPath: String? = null,
    val transfers: List<AudioTransferEntity>? = null,
    val errorState: ErrorState? = null
)
/**
 * 录音转文本错误状态
 */
sealed class ErrorState {
    data object AudioDurationError : ErrorState()
    data object NetworkError : ErrorState()
    data object GenerateError : ErrorState()
    data object UsageLimit : ErrorState()
    data object SensitiveWords : ErrorState()
    data object OtherFileInTranscription : ErrorState()
}

/**
 * 录音状态的数据类
 * @property isRecording 指示是否正在录音
 * @property recordingIconVisible 指示录音图标是否可见
 * @property recordDuration 录音的持续时间，以毫秒为单位
 * @property audioPath 录音的音频文件路径
 * @property specialState 录音的特殊状态
 */
data class RecordingState(
    val isRecording: Boolean = false,
    val recordingIconVisible: Boolean = false,
    val recordDuration: Long = 0,
    val audioPath: String? = null,
    val specialState: RecordSpecialState? = null
)

/**
 * 录音错误状态
 */
sealed class RecordSpecialState {
    data class RecordingError(val message: String) : RecordSpecialState()
    data object AudioFocusLost : RecordSpecialState()
    data object MaxDurationReached : RecordSpecialState()
    data object SaveAndStartNew : RecordSpecialState() // 新增：保存并开始新录音的状态
}

/**
 * 播放状态的数据类
 * @property isPlaying 指示是否正在播放
 * @property playingPosition 播放的位置，以毫秒为单位
 * @property totalDuration 播放的持续时间，以毫秒为单位
 */
data class RecordPlayingState(
    val isPlaying: Boolean = false,
    val audioPath: String? = null,
    val playingPosition: Int = -1,
    val totalDuration: Int = 0
)

data class LanguageConfigState(
    val language: String = "",
    /**
     * 发生错误
     * */
    val selectLanguageIndex :Int = -1
)

/**
 * 录音转文本状态
 */
data class GenerateResultState(
    val isGenerateComplete: Boolean = false,
    val filePath: String? = null,
    val result: List<AudioTransferEntity>? = null,
    val errorState: ErrorState? = null
)






