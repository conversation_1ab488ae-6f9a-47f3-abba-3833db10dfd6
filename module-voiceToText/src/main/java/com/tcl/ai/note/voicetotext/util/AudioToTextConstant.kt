package com.tcl.ai.note.voicetotext.util

/**
 * 录音时间配置类
 * 提供不同的配置模式（测试模式和生产模式）
 */
object AudioToTextConstant {
    // 其他常量
    const val EXTRA_AUDIO_RECORD = "EXTRA_AUDIO_RECORD"
    const val EXTRA_SHOULD_REGENERATE = "EXTRA_SHOULD_REGENERATE"
    const val MIN_TRANSCRIPTION_INTERVAL = 3 * 60 * 1000L //3min
    const val DEFAULT_DISPLAY_TIME = "00:00:00"
    const val DEFAULT_DISPLAY_TIME_MM_SS = "00:00"

    // 最短录制时间，所有模式都一样
    const val MIN_RECORD_DURATION = 2000L //2秒

    /**
     * 录音时间配置接口
     */
    interface RecordingTimeConfig {
        /** 最短转录时间 */
        val minTranscriptionDuration: Long
        /** 最大录音时长 */
        val maxTranscriptionDuration: Long
        /** 录音即将达到最大时长的提示时间点 */
        val willReachMaxTime: Long
        /** 录音达到最大时长前的最后一分钟提示时间点 */
        val reachMaxTime: Long
    }

    /**
     * 测试模式配置（短时间）
     */
    object TestConfig : RecordingTimeConfig {
        private const val THIRTY_SECONDS = 30 * 1000L // 30秒
        override val minTranscriptionDuration = 10 * 1000L // 10秒
        override val maxTranscriptionDuration = 2 * 60 * 1000L+THIRTY_SECONDS // 2分钟+30秒 倒计时有1分钟
        override val willReachMaxTime = 1 * 60 * 1000L // 1分钟
        override val reachMaxTime =willReachMaxTime+THIRTY_SECONDS // 1分钟 + 30秒
    }

    /**
     * 生产模式配置（长时间）
     */
    object ProductionConfig : RecordingTimeConfig {
        override val minTranscriptionDuration = 15 * 1000L // 15秒
        override val maxTranscriptionDuration = 2 * 60 * 60 * 1000L // 2小时
        override val willReachMaxTime = (60 + 55) * 60 * 1000L // 1小时55分钟 ergo上定义是1小时55分弹弹窗
        override val reachMaxTime = (60 + 59) * 60 * 1000L // 1小时59分钟

    }

    // 当前使用的配置，可以在运行时切换
    private var currentConfig: RecordingTimeConfig = ProductionConfig

    // 对外暴露的常量，使用当前配置
    val MIN_TRANSCRIPTION_DURATION get() = currentConfig.minTranscriptionDuration
    val MAX_TRANSCRIPTION_DURATION get() = currentConfig.maxTranscriptionDuration
    val AUDIO_WILL_REACH_MAX_TRANSCRIPTION_TIME get() = currentConfig.willReachMaxTime
    val AUDIO_REACH_MAX_TRANSCRIPTION_TIME get() = currentConfig.reachMaxTime

    /**
     * 切换到测试模式
     */
    fun useTestMode() {
        currentConfig = TestConfig
    }

    /**
     * 切换到生产模式
     */
    fun useProductionMode() {
        currentConfig = ProductionConfig
    }
}