package com.tcl.ai.note.voicetotext.util

import android.annotation.SuppressLint
import android.content.Intent
import android.media.MediaPlayer
import android.net.Uri
import android.provider.Settings
import android.text.format.DateUtils
import androidx.activity.ComponentActivity
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.systemLang
import com.tcl.ai.note.utils.systemLangCountry
import com.tcl.ai.note.voicetotext.bean.translationLanguageKeys
import com.tcl.ai.note.voicetotext.bean.translationLanguageList
import com.tcl.ai.note.voicetotext.util.AudioToTextConstant.DEFAULT_DISPLAY_TIME
import com.tcl.ai.note.voicetotext.util.AudioToTextConstant.DEFAULT_DISPLAY_TIME_MM_SS
import java.io.File
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.attribute.BasicFileAttributes
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.Locale
import java.util.TimeZone


/**
 * 获取音频文件的持续时间。
 * @param audioFilePath 音频文件的路径
 * @return 音频的持续时间（以毫秒为单位），如果出错则返回0
 * 改方法 耗时50ms左右
 */
fun getAudioDuration(audioFilePath: String?): Long {
    if (audioFilePath.isNullOrEmpty()) return 0L
    val mediaPlayer = MediaPlayer()
    return try {
        mediaPlayer.setDataSource(audioFilePath)
        mediaPlayer.prepare()
        mediaPlayer.duration.toLong()
    } catch (e: Exception) {
        Logger.e("getAudioDuration", "Error getting audio duration: $e")
        0L  // 返回 0 表示出错
    } finally {
        mediaPlayer.release()
    }
}

/**
 * 格式化音频时间的方法
 * @param duration 音频时长，以毫秒为单位
 * @return 格式化后的时间字符串，格式为 "HH:mm:ss"
 */
fun formatAudioTime(duration: Long): String {
    if (duration <= 0) return DEFAULT_DISPLAY_TIME
    val timeDeviation = duration % DateUtils.SECOND_IN_MILLIS
    var tmpDuration = duration
    if (duration >= AudioToTextConstant.MIN_TRANSCRIPTION_DURATION && timeDeviation >= 500L) {
        tmpDuration += DateUtils.SECOND_IN_MILLIS - timeDeviation
    }
    val sdf = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    sdf.timeZone = TimeZone.getTimeZone("GMT+0")
    val formatResult = sdf.format(Date(tmpDuration))
    return formatResult
}

/**
 * 格式化音频时间为分秒格式的方法
 * @param duration 音频时长，以毫秒为单位
 * @return 格式化后的时间字符串，格式为 "mm:ss"
 */
fun formatAudioTimeMinuteSecond(duration: Long): String {
    if (duration <= 0) return DEFAULT_DISPLAY_TIME_MM_SS
    val timeDeviation = duration % DateUtils.SECOND_IN_MILLIS
    var tmpDuration = duration
    if (duration >= AudioToTextConstant.MIN_TRANSCRIPTION_DURATION && timeDeviation >= 500L) {
        tmpDuration += DateUtils.SECOND_IN_MILLIS - timeDeviation
    }
    val sdf = SimpleDateFormat("mm:ss", Locale.getDefault())
    sdf.timeZone = TimeZone.getTimeZone("GMT+0")
    val formatResult = sdf.format(Date(tmpDuration))
    return formatResult
}

/**
 * 生成录音文件路径，使用 "Audio 00N" 格式命名
 * @param noteId 笔记ID
 * @return 录音文件的完整路径
 */
@SuppressLint("DefaultLocale")
fun generateAudioPath(noteId: Long): String {
    val folderPath = getAudioFileFolder(noteId)
    val nextAudioNumber = getNextAudioNumber(folderPath)
    val audioName = String.format("Audio %03d", nextAudioNumber)
    return folderPath.absolutePath + File.separator + audioName + ".amr"
}

/**
 * 获取下一个可用的录音序号
 * @param audioFolder 录音文件夹
 * @return 下一个可用的序号（从1开始）
 */
private fun getNextAudioNumber(audioFolder: File): Int {
    if (!audioFolder.exists()) {
        return 1
    }

    val audioFiles = audioFolder.listFiles { file ->
        file.isFile && file.name.endsWith(".amr") && file.name.startsWith("Audio ")
    } ?: return 1

    val existingNumbers = audioFiles.mapNotNull { file ->
        val nameWithoutExtension = file.nameWithoutExtension
        // 提取 "Audio 001" 中的数字部分
        val regex = Regex("Audio (\\d+)")
        regex.find(nameWithoutExtension)?.groupValues?.get(1)?.toIntOrNull()
    }.sorted()

    // 找到第一个缺失的序号，如果没有缺失则返回最大值+1
    for (i in 1..existingNumbers.size + 1) {
        if (i !in existingNumbers) {
            return i
        }
    }

    return 1
}

fun getAudioFileFolder(noteId: Long): File {
    val folderPath = File(GlobalContext.instance.filesDir, "Audio/$noteId").apply {
        if (!exists()) mkdirs() // 确保目录存在
    }
    return folderPath
}

/**
 * 从录音文件路径中提取noteId
 * 路径格式: /data/data/com.tcl.ai.note/files/Audio/{noteId}/Audio 001.amr
 * @param audioPath 录音文件路径
 * @return noteId，如果提取失败返回0L
 */
fun extractNoteIdFromAudioPath(audioPath: String?): Long {
    if (audioPath.isNullOrEmpty()) return 0L

    try {
        // 路径格式: /data/data/com.tcl.ai.note/files/Audio/{noteId}/Audio 001.amr
        val file = File(audioPath)
        val parentDir = file.parentFile
        if (parentDir != null && parentDir.name.toLongOrNull() != null) {
            return parentDir.name.toLong()
        }
    } catch (e: Exception) {
        Logger.e("extractNoteIdFromAudioPath", "Error extracting noteId from path: $audioPath, error: $e")
    }

    return 0L
}

/**
 * 删除文件
 */
fun deleteFile(path: String?) {
    path?.let {
        val file = File(it)
        if (file.exists()) file.delete()
    }
}

/**
 * 重命名文件
 */
fun renameFile(oldAudioPath: String, newAudioPath: String): Boolean {
    val oldFile = File(oldAudioPath)
    if (oldFile.exists()) {
        return if (oldAudioPath == newAudioPath) {
            true // 如果路径相同，直接返回true
        } else {
            oldFile.renameTo(File(newAudioPath))
        }
    }
    return false
}

/**
 * 获取系统当前语言码
 * */
val languageCode = "$systemLang-$systemLangCountry"

val defaultLanguage: String
    get() = getIndexForSystemLanguage()

private fun getIndexForSystemLanguage() :String{
    val languageCode = "$systemLang-$systemLangCountry"
    val systemLanInList = translationLanguageKeys.contains(languageCode)
    return if (systemLanInList) {
        translationLanguageList[translationLanguageKeys.indexOf(languageCode)].first

    } else {
        translationLanguageList[0].first
    }
}
/**
 * 跳转到应用设置界面
 */
internal fun toAppSetting(activity: ComponentActivity) {
    val intent = Intent().apply {
        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
        data = Uri.fromParts("package", activity.packageName, null)
    }
    activity.startActivity(intent)
}

fun getFileCreateFormatTime(filePath: String): String {
    val file = File(filePath)
    if (file.exists()) {
        val path: Path = file.toPath()
        val attr: BasicFileAttributes = Files.readAttributes(path, BasicFileAttributes::class.java)
        val instant: Instant = attr.creationTime().toInstant()

        // 根据系统语言选择不同的时间格式
        val pattern = if (systemLang == "zh") {
            "yyyy年MM月dd日 HH:mm"
        } else {
            "dd MMM yyyy HH:mm"
        }

        val formatter = DateTimeFormatter.ofPattern(pattern, Locale.getDefault())
            .withZone(ZoneId.systemDefault())

        return formatter.format(instant)
    }
    return ""
}