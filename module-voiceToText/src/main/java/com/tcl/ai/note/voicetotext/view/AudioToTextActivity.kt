package com.tcl.ai.note.voicetotext.view

import android.content.res.Configuration
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.tcl.ai.note.base.BaseActivity
import com.tcl.ai.note.theme.NoteTclTheme
import com.tcl.ai.note.utils.Logger
import dagger.hilt.android.AndroidEntryPoint

/**
 * 展示转录结果
 * */
@AndroidEntryPoint
class AudioToTextActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            NoteTclTheme {
                AudioToTextScreen(
                    intent.getStringExtra("audioPath") ?: "",
                    onDismissRequest = {
                        Logger.d(TAG, " --- AudioSummaryScreen dismiss ---")
                        finish()
                    })
            }
        }
    }

    /**
     * TCL AI方案 切换暗黑模式 重新设置enableEdgeToEdge()
     */
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enableEdgeToEdge()
    }

    companion object {
        private const val TAG = "AudioToTextActivity"
    }
}


