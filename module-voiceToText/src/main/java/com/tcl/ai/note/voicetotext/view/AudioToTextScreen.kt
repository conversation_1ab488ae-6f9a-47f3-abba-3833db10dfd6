package com.tcl.ai.note.voicetotext.view

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.isTraversalGroup
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.controller.LoginErrorResult
import com.tcl.ai.note.controller.rememberLoginHandler
import com.tcl.ai.note.voicetotext.intent.AudioToTextIntent
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.startTCLAIChargeActivity
import com.tcl.ai.note.voicetotext.BuildConfig
import com.tcl.ai.note.voicetotext.data.AudioTransferEntity
import com.tcl.ai.note.voicetotext.util.getAudioDuration
import com.tcl.ai.note.voicetotext.states.AudioResult
import com.tcl.ai.note.voicetotext.states.AudioStreamingMsg
import com.tcl.ai.note.voicetotext.states.AudioStreamingStatus
import com.tcl.ai.note.voicetotext.states.ErrorState
import com.tcl.ai.note.voicetotext.view.widget.AudioToTextHeader
import com.tcl.ai.note.voicetotext.view.widget.AudioToTextResultPanel
import com.tcl.ai.note.voicetotext.view.widget.AudioToTextTopPlayView
import com.tcl.ai.note.voicetotext.view.widget.UsageLimitDialog
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel
import com.tcl.ai.note.widget.components.AIServiceDialog
import com.tcl.ai.note.widget.BottomSheetDialog
import kotlinx.coroutines.launch

@Composable
fun AudioToTextScreen(
    audioPath: String,
    onDismissRequest: () -> Unit,
    viewModel: AudioToTextViewModel = hiltViewModel()
) {
    val TAG = "AudioSummaryScreen"
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    var dismissDialogCallback: (suspend () -> Unit)? = null
    val audioToTextState by viewModel.audioToTextState.collectAsState()
    val recordPlayingState by viewModel.recordPlayingState.collectAsState()
    val languageConfigState by viewModel.languageConfigState.collectAsState()
    val isOffline by viewModel.isOffline.collectAsState()
    var showUsageLimitDialog by remember { mutableIntStateOf(-1) } //(0 余额不足) (1 敏感词)
    val showAiServiceDialog = remember { mutableStateOf(false) }
    val loginHandler = rememberLoginHandler(onFailure = {
        if (it == LoginErrorResult.LunchError) {
            showAiServiceDialog.value = true
            Logger.d("AudioBlock", "loginHandler onFailure")
        }
    })
    AIServiceDialog(
        isShow = showAiServiceDialog.value,
        title = context.getString(R.string.audio_to_text),
        onDismiss = { showAiServiceDialog.value = false },
        onGoToSettings = {
            showAiServiceDialog.value = false
        }
    )
    //val chargeHandler = rememberChargeHandler(getAudioDuration(audioPath))
    var showContentFromLocal by remember { mutableStateOf(true) }
    val aiStatus = remember { mutableStateMapOf<String, List<AudioTransferEntity>>() }
    var isHalfScreen by remember { mutableStateOf(true) }

    if (showContentFromLocal) {
        viewModel.handleTranscriptionIntent(audioPath, intent = AudioToTextIntent.GetLocalTranscription(null))
        showContentFromLocal = false
    }

    LaunchedEffect(audioToTextState) {
        if (audioPath == audioToTextState.audioPath) {
            if (audioToTextState.errorState is ErrorState.NetworkError) {
                Logger.d(TAG, "NetworkError toast ")
                ToastUtils.makeWithCancel(com.tcl.ai.note.base.R.string.network_error)
            } else if (audioToTextState.errorState is ErrorState.OtherFileInTranscription) {
                ToastUtils.makeWithCancel(com.tcl.ai.note.base.R.string.toast_in_generating)
            }
            //viewModel.resetAudioToTextState()
        }
    }

    // 使用LaunchedEffect更新aiStatus，避免在重组过程中修改状态
    LaunchedEffect(audioToTextState, audioPath) {
        if (audioPath == audioToTextState.audioPath) {
            if (audioToTextState.isLoading) {
                aiStatus.clear()
                aiStatus["loading"] = emptyList()
            } else if (audioToTextState.transfers?.isNotEmpty() == true) {
                aiStatus.clear()
                aiStatus["success"] = audioToTextState.transfers!!
            } else if (audioToTextState.errorState != null) {
                // 只有在没有成功数据或网络错误时才清除状态
                if (!(audioToTextState.errorState == ErrorState.NetworkError && aiStatus["success"]?.isNotEmpty() == true)) {
                    aiStatus.clear()
                    aiStatus["error"] = emptyList()
                }

                // 处理特殊错误状态
                if (audioToTextState.errorState is ErrorState.UsageLimit) {
                    showUsageLimitDialog = 0
                } else if (audioToTextState.errorState is ErrorState.SensitiveWords) {
                    showUsageLimitDialog = 1
                }
            }
        }
    }

    // 使用remember计算aiLoadingStatus，避免在每次重组时重新计算
    val aiLoadingStatus = remember(audioToTextState, audioPath, aiStatus) {
        when {
            audioPath == audioToTextState.audioPath -> {
                if (audioToTextState.isLoading) {
                    AudioResult.Loading
                } else if (audioToTextState.transfers?.isNotEmpty() == true) {
                    AudioResult.Success(
                        AudioStreamingMsg(
                            status = AudioStreamingStatus.COMPLETED,
                            transfers = audioToTextState.transfers!!,
                            threadId = "",
                            userMessageId = ""
                        )
                    )
                } else {
                    if (audioToTextState.errorState == ErrorState.NetworkError
                        && aiStatus["success"]?.isNotEmpty() == true) {
                        AudioResult.Success(
                            AudioStreamingMsg(
                                status = AudioStreamingStatus.COMPLETED,
                                transfers = aiStatus["success"]!!,
                                threadId = "",
                                userMessageId = ""
                            )
                        )
                    } else {
                        AudioResult.Error(Exception())
                    }
                }
            }
            else -> {
                when (aiStatus.keys.firstOrNull()) {
                    "loading" -> AudioResult.Loading
                    "error" -> AudioResult.Error(Exception())
                    "success"-> {
                        if (aiStatus["success"]?.isNotEmpty() == true) {
                            AudioResult.Success(
                                AudioStreamingMsg(
                                    status = AudioStreamingStatus.COMPLETED,
                                    transfers = aiStatus["success"]!!,
                                    threadId = "",
                                    userMessageId = ""
                                )
                            )
                        } else {
                            AudioResult.Error(Exception())
                        }
                    }
                    else -> AudioResult.Loading
                }
            }
        }
    }

    fun tryAgain() {
        coroutineScope.launch {
            if (AccountController.getLoginState()) {
                viewModel.handleTranscriptionIntent(audioPath, intent = AudioToTextIntent.GetAITranscription)
            } else {

                loginHandler {
                    viewModel.handleTranscriptionIntent(audioPath, intent = AudioToTextIntent.GetAITranscription)
                }
            }
        }
        TclAnalytics.reportRecordToTextReset("0")
    }
    BottomSheetDialog(
        modifier = Modifier,
        visible = true,
        onDismissRequest = {
            onDismissRequest()
            viewModel.pausePlay()
        },
        canceledOnTouchOutside = false,
        showFullScreenCallBack = {
            isHalfScreen = !it
            /*if (isHalfScreen) {
                viewModel.pausePlay()
            }*/
            if (it) {
                TclAnalytics.reportRecordToTextMax("0")
            }
        },
        backHandler = {},
        extraTopPadding = true,
        isNavigationBarsPaddingNeeded=true,
        onDismissCallback = { callback ->
            dismissDialogCallback = callback
        },
    ) {
        var startRecordToTextMillis by remember { mutableStateOf(System.currentTimeMillis()) }
        Column(modifier = Modifier
            .fillMaxWidth()
            .fillMaxSize()
            .navigationBarsPadding()
            .semantics(mergeDescendants = true) {
                isTraversalGroup = true
        }
        ){
            AudioToTextHeader(
                languageConfigState = languageConfigState,
                showCloseIcon = true,
                onCloseClick = {
                    coroutineScope.launch {
                        dismissDialogCallback?.invoke()
                    } }
            )
            if (!isHalfScreen || !BuildConfig.IS_PHONE) {
                AudioToTextTopPlayView(
                    audioPath = audioPath,
                    getAudioDuration(audioPath),
                    onPauseClick = {
                        viewModel.pausePlay()
                    },
                    onPlayClick = {
                        viewModel.playAudio(audioPath)
                    },
                    recordPlayingState = recordPlayingState
                )
                Spacer(modifier = Modifier.height(12.dp))
            }

            AudioToTextResultPanel(
                aiLoadingStatus = aiLoadingStatus,
                isOffline=isOffline,
                onRetryClick = {
                    tryAgain()
                    startRecordToTextMillis = System.currentTimeMillis()
                },
                onCopyClick = {
                    TclAnalytics.reportRecordToTextCopy("0")
                },
                onStopClick = { _, _ ->
                    onDismissRequest.invoke()
                    TclAnalytics.reportRecordToTextPause("0", (System.currentTimeMillis() - startRecordToTextMillis).toString())
                    startRecordToTextMillis = System.currentTimeMillis()
                },
                onTryAgainClick = {
                    tryAgain()
                    TclAnalytics.reportRecordToTextReset("0")
                },
                onOutput = {  },
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                showToast ={
                    ToastUtils.makeWithCancel(it)
                },
                isHalfScreen = isHalfScreen
            )
        }
    }

    when (showUsageLimitDialog) {
        0 -> {
            UsageLimitDialog(
                onDismissRequest = {
                    showUsageLimitDialog = -1
                    viewModel.resetAudioToTextState()
                    TclAnalytics.reportRecordToTextPurchaseIntention("0")
                },
                onLearnMoreClick = {
                    showUsageLimitDialog = -1
                    viewModel.resetAudioToTextState()
                    context.startTCLAIChargeActivity()
                    TclAnalytics.reportRecordToTextPurchaseIntention("1")
                }
            )
        }
        1 -> {
            showUsageLimitDialog = -1
            ToastUtils.makeWithCancel(com.tcl.ai.note.base.R.string.message_generate_failed_since_sensitive_words)
            viewModel.resetAudioToTextState()
        }
    }
}

@Preview(
    widthDp = 360,// 360 // 设置宽度以模拟横屏
    heightDp = 820,// 820 // 设置高度
//    widthDp = 1100, // 设置宽度以模拟横屏
//    heightDp = 720, // 设置高度
    showBackground = true
)
@Composable
fun PreviewAudioToTextScreen() {
    AudioToTextScreen("", onDismissRequest = {}, hiltViewModel())
}
