package com.tcl.ai.note.voicetotext.view.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.*
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.invisibleToUser
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.controller.LoginErrorResult
import com.tcl.ai.note.controller.isAIServiceEnable
import com.tcl.ai.note.controller.rememberLoginHandler
import com.tcl.ai.note.theme.TCLThemeComponent
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.voicetotext.BuildConfig
import com.tcl.ai.note.voicetotext.R
import com.tcl.ai.note.voicetotext.intent.AudioToTextIntent
import com.tcl.ai.note.voicetotext.states.AudioToTextState
import com.tcl.ai.note.voicetotext.states.ErrorState
import com.tcl.ai.note.voicetotext.states.RecordPlayingState
import com.tcl.ai.note.voicetotext.states.RecordSpecialState
import com.tcl.ai.note.voicetotext.states.RecordingState
import com.tcl.ai.note.voicetotext.util.AudioToTextConstant
import com.tcl.ai.note.voicetotext.util.formatAudioTime
import com.tcl.ai.note.voicetotext.util.getAudioDuration
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel
import com.tcl.ai.note.voicetotext.vm.RecordingViewModel
import com.tcl.ai.note.widget.HoverProofIconButton
import com.tcl.ai.note.widget.components.AIServiceDialog
import com.tct.theme.core.designsystem.component.TclDropDownMenuItem
import com.tct.theme.core.designsystem.component.TclDropdownMenu
import com.tct.theme.core.designsystem.component.TclTextField
import kotlinx.coroutines.launch


/**
 * 录音内容块,显示再富文本中
 * */
@Composable
fun AudioBlock(
    modifier: Modifier = Modifier,
    darkTheme: Boolean =false,
    enabled: Boolean =true,
    focusRequester: FocusRequester,
    audioPath: String,
    onStopRecordClick: (audioPath: String?) -> Unit = {},
    onMoreClick: () -> Unit = {},
    onAudioToTextClick: () -> Unit = {},
    onDeleteClick: () -> Unit = {},  // 简化为无参数函数
    onEnterKeyPressed: () -> Unit,
    isTabletPreviewMode: Boolean = false,
    isPhone: Boolean = BuildConfig.IS_PHONE,
    // 状态相关参数
    isCurrentRecording: Boolean = false,
    isCurrentPlaying: Boolean = false,
    durationText: String = "",
    recordingIconVisible: Boolean = true,
    // 操作相关函数
    onPlayAudio: (String) -> Unit = {},
    onPausePlay: () -> Unit = {},
    onStopRecord: () -> Unit = {},
    onDeleteAudioFile: (String) -> Unit = {},
    onHandleAudioToText: suspend () -> Unit = {},
) {
    val coroutineScope = rememberCoroutineScope()
    var text by remember { mutableStateOf("") }
    var isDeleteDialogVisible by remember { mutableStateOf(false) }
    var isDropdownMenuExpanded by remember { mutableStateOf(false) }

    val headImage = if (isCurrentRecording) R.drawable.ic_recording_file else R.drawable.ic_recorded_file

    Box(
        contentAlignment = Alignment.CenterStart,
        modifier = modifier
            .height(if (isPhone) 64.dp else 78.dp)
            .padding(top = 8.dp, bottom = 8.dp)
            .background(
                color =darkTheme.judge(R.color.phone_audio_bg_color_night,R.color.phone_audio_bg_color).colorRes(),
                shape = RoundedCornerShape(8.dp)
            )
    ) {
        Row(verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .wrapContentWidth()
                .padding(start = if (isPhone) 0.dp else 4.dp, end = 24.dp)){
            Image(
                painter = painterResource(id = headImage),
                contentDescription = null,
                modifier = Modifier
                    .height(48.dp)
                    .padding(start = 16.dp, end = 8.dp),
                alpha = if (isCurrentRecording && !recordingIconVisible) 0f else 1f
            )

            Text(
                text = durationText ,
                fontSize = 14.sp,
                color = if (isPhone){
                    darkTheme.judge(R.color.phone_audio_duration_night,R.color.phone_audio_duration).colorRes()
                } else {
                    darkTheme.judge(R.color.tablet_audio_duration_night,R.color.tablet_audio_duration).colorRes()
                },
                modifier = Modifier
                    .wrapContentWidth()
                    .padding(if (isPhone) 0.dp else 9.dp)
            )
        }

        Box(modifier = Modifier
            .wrapContentWidth()
            .align(Alignment.CenterEnd)
            .padding(end = if (isPhone) 0.dp else 6.dp)) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                if (isCurrentRecording) {

                    //需求要求去除
                  /*  HoverProofIconButton(
                        modifier = Modifier.size(48.dp),
                        enabled = enabled,
                        onClick = {
                            onStopRecordClick(audioPath)
                            onStopRecord()
                        }
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_audio_stop),
                            contentDescription = stringResource(com.tcl.ai.note.base.R.string.stop_recording),
                        )
                    }*/

                } else {
                    if (isCurrentPlaying) {
                        HoverProofIconButton(
                            modifier = Modifier.size(if (isPhone) 24.dp else 48.dp),
                            enabled = enabled,
                            onClick = { onPausePlay() }
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_audio_pause),
                                contentDescription = stringResource(com.tcl.ai.note.base.R.string.audio_pause),
                            )
                        }
                    } else {
                        HoverProofIconButton(
                            modifier = Modifier.size(if (isPhone) 24.dp else 48.dp),
                            enabled = enabled,
                            onClick = { onPlayAudio(audioPath) }
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_audio_play_arrow),
                                contentDescription = stringResource(com.tcl.ai.note.base.R.string.audio_play),
                            )
                        }
                    }

                    if (!isPhone&&isAIServiceEnable) {
                        HoverProofIconButton(
                            modifier = Modifier.size(24.dp),
                            enabled = enabled,
                            onClick = {
                                coroutineScope.launch {
                                    onHandleAudioToText()
                                }
                                onAudioToTextClick()
                            }
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_audio_to_text),
                                contentDescription = stringResource(id = com.tcl.ai.note.base.R.string.audio_to_text),
                            )
                        }
                    }
                    if (isPhone || !isTabletPreviewMode) {
                        HoverProofIconButton(
                            enabled = enabled,
                            onClick = {
                                isDropdownMenuExpanded = !isDropdownMenuExpanded
                                onMoreClick()
                            }
                        ) {
                            Icon(
                                painter = painterResource(id = com.tcl.ai.note.base.R.drawable.ic_sort),
                                contentDescription = stringResource(com.tcl.ai.note.base.R.string.audio_more),
                            )
                        }
                    } else {
                        Spacer(modifier = Modifier.width(20.dp))
                    }
                }
            }
            TCLThemeComponent {
                TclDropdownMenu(
                    expanded = isDropdownMenuExpanded,
                    onDismissRequest = { isDropdownMenuExpanded = false },
                    offset = DpOffset(if (isPhone) 0.dp else 8.dp, if (isPhone) 4.dp else 12.dp)
                ) {
                    if (isPhone&&isAIServiceEnable) {
                        TclDropDownMenuItem(
                            text = { Text(stringResource(id = com.tcl.ai.note.base.R.string.audio_to_text)) },
                            onClick = {
                                coroutineScope.launch {
                                    onHandleAudioToText()
                                }
                                onAudioToTextClick()
                                isDropdownMenuExpanded = false
                            },
                        )
                    }
                    TclDropDownMenuItem(
                        text = { Text(stringResource(id = com.tcl.ai.note.base.R.string.menu_delete_recording)) },
                        onClick = {
                            isDeleteDialogVisible = true
                            isDropdownMenuExpanded = false
                        },
                    )
                }
            }
        }

        TclTextField(
            value = text,
            onValueChange = { text = it },
            modifier = Modifier
                .semantics {
                    invisibleToUser()
                }
                .focusRequester(focusRequester) // 使用 block 自己的焦点控制器
                .fillMaxHeight()
                .width(0.dp)
                .onPreviewKeyEvent { keyEvent -> // 使用 onPreviewKeyEvent 捕获事件
                    if (keyEvent.type == KeyEventType.KeyDown) {
                        when (keyEvent.key) {
                            Key.Backspace -> {
                                if (text.isEmpty()) {
                                    isDeleteDialogVisible = true // 显示对话框
                                    true // 拦截事件，停止冒泡
                                } else {
                                    false // 不删除，交给默认行为
                                }
                            }

                            else -> {
                                false
                            }
                        }
                    } else {
                        false
                    }
                },
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            ),
            keyboardActions = KeyboardActions(
                onNext = { onEnterKeyPressed() }
            ),
        )
        if (isDeleteDialogVisible) {
            DeleteAudioDialog(
                onDismiss = { isDeleteDialogVisible = false },
                onDelete = {
                    onDeleteClick()  // 调用传入的onDeleteClick函数
                    isDeleteDialogVisible = false
                }
            )
        }
    }
}

fun handleAudioToTextState(/*audioPath: String,*/ audioToTextState: AudioToTextState, audioToTextViewModel: AudioToTextViewModel) {
    //if (audioPath == audioToTextState.audioPath) {
        when (audioToTextState.errorState) {
            ErrorState.AudioDurationError -> {
                ToastUtils.makeWithCancel(com.tcl.ai.note.base.R.string.invalid_duration_for_audio_to_text_new, assertive = true)
            }
            ErrorState.SensitiveWords -> {
                ToastUtils.makeWithCancel(com.tcl.ai.note.base.R.string.message_generate_failed_since_sensitive_words)
            }
            ErrorState.NetworkError -> {
                ToastUtils.makeWithCancel(com.tcl.ai.note.base.R.string.network_error)
            }
            ErrorState.OtherFileInTranscription -> {
                ToastUtils.makeWithCancel(com.tcl.ai.note.base.R.string.toast_in_generating)
            }
            else -> {
                // do nothing
            }
        }
        if (audioToTextState.errorState != null) {
            audioToTextViewModel.resetAudioToTextState()
        }
    //}
}

private fun handleRecordingState(
    audioPath: String,
    recordingState: RecordingState,
    onDeleteClick: (audioPath: String?) -> Unit,
    recordingViewModel: RecordingViewModel
) {
    if (audioPath == recordingState.audioPath
        && (recordingState.specialState is RecordSpecialState.RecordingError || (!recordingState.isRecording && recordingState.recordDuration > 0 && recordingState.recordDuration < AudioToTextConstant.MIN_RECORD_DURATION))) {
        if (recordingState.specialState is RecordSpecialState.RecordingError) {
            ToastUtils.makeWithCancel(recordingState.specialState.message)
        } else {
            ToastUtils.makeWithCancel(com.tcl.ai.note.base.R.string.edit_audio_record_shortest_time)
        }
        onDeleteClick(recordingState.audioPath)
        recordingViewModel.deleteAudioFile(recordingState.audioPath)
        recordingViewModel.resetRecordingState()
    }
}

suspend fun handleAudioToTextClick(
    audioPath: String,
    audioToTextViewModel: AudioToTextViewModel,
    loginHandler: (action: () -> Unit) -> Unit
) {
    // 先检查音频时长是否有效，这个操作相对较快
    if (audioToTextViewModel.checkAudioTimeIsInValid(audioPath)) {
        audioToTextViewModel.setAudioToTextStateAudioDurationError(audioPath)
        return
    }

    // 检查本地转录数据，如果已有转录数据，直接开始转录
    val localTranscription = audioToTextViewModel.getLocalTranscription(audioPath)
    if (localTranscription.isNotEmpty()) {
        audioToTextViewModel.handleTranscriptionIntent(audioPath, intent = AudioToTextIntent.StartTranscription)
        return
    }

    // 检查登录状态
    val startTime = System.currentTimeMillis()
    val isLoggedIn = AccountController.getLoginState()
    val endTime = System.currentTimeMillis()
    Logger.d("AudioBlock", "getLoginState cost time: ${endTime - startTime} ms")

    if (isLoggedIn) {
        audioToTextViewModel.handleTranscriptionIntent(audioPath, intent = AudioToTextIntent.StartTranscription)
    } else {
        loginHandler {
            audioToTextViewModel.handleTranscriptionIntent(
                audioPath,
                intent = AudioToTextIntent.StartTranscription
            )
        }
    }
}

fun getDurationText(
    audioPath: String,
    recordingState: RecordingState,
    recordPlayingState: RecordPlayingState,
    durationMap: MutableState<MutableMap<String, String>>
): String {
    return when {
        // 正在录音时显示实时录音时长
        recordingState.audioPath == audioPath && recordingState.isRecording -> formatAudioTime(recordingState.recordDuration)
        // 播放时显示播放进度和总时长
        recordPlayingState.audioPath == audioPath && recordPlayingState.playingPosition > 0 -> {
            "${formatAudioTime(recordPlayingState.playingPosition.toLong())} / ${formatAudioTime(recordPlayingState.totalDuration.toLong())}"
        }
        // 停止录音后但尚未播放时，优先使用 recordingState 中的时长
        recordingState.audioPath == audioPath && recordingState.recordDuration > 0 -> {
            val duration = formatAudioTime(recordingState.recordDuration)
            durationMap.value[audioPath] = duration
            duration
        }
        // 其他情况，从缓存或文件中获取时长
        else -> {
            if (!durationMap.value.containsKey(audioPath)) {
                durationMap.value[audioPath] = formatAudioTime(getAudioDuration(audioPath))
            }
            durationMap.value[audioPath]!!
        }
    }
}

/**
 * 带ViewModel的AudioBlock包装器
 */
@Composable
fun AudioBlockComponent(
    modifier: Modifier = Modifier,
    enabled: Boolean =true,
    darkTheme: Boolean,
    focusRequester: FocusRequester,
    audioPath: String,
    onStopRecordClick: (audioPath: String?) -> Unit = {},
    onMoreClick: () -> Unit = {},
    onAudioToTextClick: () -> Unit = {},
    onDeleteClick: (audioPath: String?) -> Unit = {},
    onEnterKeyPressed: () -> Unit,
    isTabletPreviewMode: Boolean = false,
    isPhone: Boolean = BuildConfig.IS_PHONE,
    recordingViewModel: RecordingViewModel = hiltViewModel(),
    audioToTextViewModel: AudioToTextViewModel = hiltViewModel()
) {
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    val showAiServiceDialog = remember { mutableStateOf(false) }
    val loginHandler = rememberLoginHandler(onFailure = {
        if (it == LoginErrorResult.LunchError) {
            showAiServiceDialog.value = true
            Logger.d("AudioBlock", "loginHandler onFailure")
        }
    })
    AIServiceDialog(
        isShow = showAiServiceDialog.value,
        title = context.getString(com.tcl.ai.note.base.R.string.audio_to_text),
        onDismiss = { showAiServiceDialog.value = false },
        onGoToSettings = {
            showAiServiceDialog.value = false
        }
    )
    val recordingState by recordingViewModel.recordState.collectAsState()
    val recordPlayingState by audioToTextViewModel.recordPlayingState.collectAsState()
    val audioToTextState by audioToTextViewModel.audioToTextState.collectAsState()

    val isCurrentRecording = recordingState.audioPath == audioPath && recordingState.isRecording
    val isCurrentPlaying = recordPlayingState.isPlaying && recordPlayingState.audioPath == audioPath
    val durationMap = remember { mutableStateOf(mutableMapOf<String, String>()) }
    val durationText = getDurationText(audioPath, recordingState, recordPlayingState, durationMap)
    Logger.d("AudioBlock", "durationText: $durationText")

    LaunchedEffect(audioToTextState) {
        handleAudioToTextState(/*audioPath, */audioToTextState, audioToTextViewModel)
    }

    LaunchedEffect(recordingState) {
        handleRecordingState(audioPath, recordingState, onDeleteClick, recordingViewModel)
    }

    // 定义删除操作的处理函数
    val handleDelete = {
        onDeleteClick(audioPath)
        if (isCurrentRecording) {
            recordingViewModel.stopRecord()
            recordingViewModel.deleteAudioFile(recordingState.audioPath)
        } else {
            audioToTextViewModel.stopPlay()
            audioToTextViewModel.handleTranscriptionIntent(audioPath, intent = AudioToTextIntent.DeleteAudioRecord)
        }
    }

    AudioBlock(
        modifier = modifier,
        darkTheme = darkTheme,
        enabled = enabled,
        focusRequester = focusRequester,
        audioPath = audioPath,
        onStopRecordClick = onStopRecordClick,
        onMoreClick = onMoreClick,
        onAudioToTextClick = onAudioToTextClick,
        onDeleteClick = { handleDelete() },  // 使用本地定义的处理函数
        onEnterKeyPressed = onEnterKeyPressed,
        isTabletPreviewMode = isTabletPreviewMode,
        isPhone = isPhone,
        isCurrentRecording = isCurrentRecording,
        isCurrentPlaying = isCurrentPlaying,
        durationText = durationText,
        recordingIconVisible = recordingState.recordingIconVisible,
        onPlayAudio = { audioToTextViewModel.playAudio(it) },
        onPausePlay = { audioToTextViewModel.pausePlay() },
        onStopRecord = { recordingViewModel.stopRecord() },
        onDeleteAudioFile = { recordingViewModel.deleteAudioFile(it) },
        onHandleAudioToText = {
            handleAudioToTextClick(audioPath, audioToTextViewModel, loginHandler)
        }
    )
}

@Composable
@Preview
fun AudioItemViewPreview() {
    AudioBlock(
        modifier = Modifier.width(300.dp),
        darkTheme = false,
        focusRequester = FocusRequester(),
        audioPath = "audioPath",
        onEnterKeyPressed = {},
        durationText = "00:30",
        isCurrentRecording = false,
        isCurrentPlaying = false
    )
}

@Composable
@Preview
fun AudioItemRecordingPreview() {
    AudioBlock(
        modifier = Modifier.width(300.dp),
        darkTheme = false,
        focusRequester = FocusRequester(),
        audioPath = "audioPath",
        onEnterKeyPressed = {},
        durationText = "00:15",
        isCurrentRecording = true,
        isCurrentPlaying = false
    )
}

@Composable
@Preview
fun AudioItemPlayingPreview() {
    AudioBlock(
        modifier = Modifier.width(300.dp),
        darkTheme = false,
        focusRequester = FocusRequester(),
        audioPath = "audioPath",
        onEnterKeyPressed = {},
        durationText = "00:10 / 00:30",
        isCurrentRecording = false,
        isCurrentPlaying = true
    )
}
