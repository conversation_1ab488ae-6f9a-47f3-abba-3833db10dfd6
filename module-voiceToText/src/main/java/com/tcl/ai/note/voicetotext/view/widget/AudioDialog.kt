package com.tcl.ai.note.voicetotext.view.widget

import android.os.CountDownTimer
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.DialogProperties
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.CursorColor
import com.tcl.ai.note.theme.NoteTclTheme
import com.tcl.ai.note.utils.ComposableToast
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.appendSemanticsButton
import com.tcl.ai.note.utils.globalDialogWidth
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.utils.substringByCodePoints
import com.tct.theme.core.designsystem.component.TclDialog
import com.tct.theme.core.designsystem.component.TclTextButton
import com.tct.theme.core.designsystem.component.TclWarningButton

/**
 * 显示录音即将 2小时对话框
 * @param onDismiss 对话框关闭时候执行的操作
 */
@Composable
fun ShowWill2HourDialog (
    onDismiss: () -> Unit
) {
    TclDialog(
        show = true,
        onDismissRequest = { onDismiss() },
        properties = DialogProperties(dismissOnClickOutside = false, dismissOnBackPress = false),
        content = {
            Text(
                text = stringResource(id = com.tcl.ai.note.base.R.string.text_max_transcribe_duration)
            )
        },
        actions = {
            TclTextButton(onClick = { onDismiss() }) { Text(stringResource(id = com.tcl.ai.note.base.R.string.dialog_positive_text_ok)) }
        },
    )
}

/**
 * 显示录音2小时对话框
 * @param onSaveAndStartNew 保存并开始新录制时执行的操作
 * @param onFinish 结束录制时执行的操作
 * @param onContinue 继续录制时执行的操作
 */
@Composable
fun Show2HourDialog (
    onSaveAndStartNew: () -> Unit, onFinish: () -> Unit, onContinue: () -> Unit
) {
    var timeLeft by remember { mutableIntStateOf(60) }
    DisposableEffect(key1 = Unit) {
        val timer = object : CountDownTimer(60300, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                timeLeft = (millisUntilFinished / 1000).toInt()
            }

            override fun onFinish() {
                timeLeft = 0
                // 不自动执行保存并开始新录音，让MaxDurationReached逻辑处理
                // onSaveAndStartNew.invoke()
            }
        }
        timer.start()

        onDispose {
            timer.cancel()
        }
    }
    TclDialog(
        show = true,
        onDismissRequest = { onSaveAndStartNew.invoke() },
        properties = DialogProperties(dismissOnClickOutside = false, dismissOnBackPress = false),
        content = {
            Text(
                text = stringResource(id = com.tcl.ai.note.base.R.string.text_reach_2_hour_message)
            )
        },
        actions = {
            TclTextButton(onClick = { onSaveAndStartNew.invoke() }) { Text(String.format(stringResource(id = com.tcl.ai.note.base.R.string.btn_save_and_create_new), timeLeft)) }
            TclTextButton(onClick = { onFinish.invoke() }) { Text(stringResource(id = com.tcl.ai.note.base.R.string.btn_finish)) }
            TclTextButton(onClick = { onContinue.invoke() }) { Text(stringResource(id = com.tcl.ai.note.base.R.string.btn_continue)) }
        },
    )
}

/**
 * 删除音频对话框组件
 * @param onDismiss 当对话框被关闭时执行的操作
 * @param onDelete 当用户选择删除时执行的操作
 */
@Composable
fun DeleteAudioDialog (
    isDeleteOne: Boolean = true,
    onDismiss: () -> Unit,
    onDelete: () -> Unit
) {

    val context = LocalContext.current
    val contentDescription = context.getString(
        R.string.dialog_semantics_description,
        context.getString(R.string.dialog_type_semantics),                         // 对话框类型
        R.string.message_delete_audio.stringRes(),                                                                      // 主要内容
        context.getString(R.string.btn_cancel).appendSemanticsButton(),            // 取消按钮
        context.getString(R.string.menu_delete_recording).appendSemanticsButton()  // 删除按钮
    )

    NoteTclTheme {
        TclDialog(
            onDismissRequest = { onDismiss.invoke() },
            show = true,
            dialogMaxWidth = globalDialogWidth(),
            content = {
                Text(
                    modifier = Modifier.semantics {
                        this.contentDescription = contentDescription
                    },
                    text = stringResource(id = if (isDeleteOne) R.string.message_delete_audio else R.string.delete_these_audios),
                )
            },
            actions = {
                TclTextButton(onClick = { onDismiss.invoke() }, contentColor = colorResource(id = R.color.home_title_color)) { Text(stringResource(id = com.tcl.ai.note.base.R.string.btn_cancel)) }
                TclWarningButton(
                    onClick = { onDelete.invoke() }) { Text(stringResource(id = com.tcl.ai.note.base.R.string.menu_delete_recording)) }
            })
    }
}

/**
 * 录音列表专用删除音频对话框组件
 * @param isDeleteOne 是否删除单个录音
 * @param audioCount 当前删除的录音条数
 * @param isDeleteAll 是否全部删除（参数列表全部选中）
 * @param onDismiss 当对话框被关闭时执行的操作
 * @param onDelete 当用户选择删除时执行的操作
 */
@Composable
fun DeleteRecordingListAudioDialog (
    audioCount: Int = 1,
    isDeleteAll: Boolean = false,
    onDismiss: () -> Unit,
    onDelete: () -> Unit
) {

    val context = LocalContext.current
    val contentText = if (isDeleteAll && audioCount>1) {
        stringResource(id = com.tcl.ai.note.base.R.string.dialog_title_delete_all_audio)
    } else {
        String.format(stringResource(id = com.tcl.ai.note.base.R.string.dialog_title_delete_multiple_audio), audioCount)
    }

    val contentDescription = context.getString(
        R.string.dialog_semantics_description,
        context.getString(R.string.dialog_type_semantics),                         // 对话框类型
        contentText,                                                               // 主要内容
        context.getString(R.string.btn_cancel).appendSemanticsButton(),            // 取消按钮
        context.getString(R.string.menu_delete_recording).appendSemanticsButton()  // 删除按钮
    )

    NoteTclTheme {
        TclDialog(
            onDismissRequest = { onDismiss.invoke() },
            show = true,
            dialogMaxWidth = globalDialogWidth(),
            title = {
                Text(
                    text = stringResource(id = R.string.dialog_title_delete_audio),
                    color = colorResource(id = R.color.dialog_title),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.W500
                )
            },
            content = {
                Text(
                    modifier = Modifier.semantics {
                        this.contentDescription = contentDescription
                    },
                    text = contentText
                )
            },
            actions = {
                TclTextButton(
                    onClick = { onDismiss.invoke() },
                    contentColor = colorResource(id = R.color.home_title_color)
                ) {
                    Text(stringResource(id = com.tcl.ai.note.base.R.string.btn_cancel))
                }
                TclWarningButton(
                    onClick = { onDelete.invoke() }
                ) {
                    Text(stringResource(id = com.tcl.ai.note.base.R.string.menu_delete_recording))
                }
            })
    }
}


/**
 * 理由对话框组件，提示用户需要权限并提供打开设置的选项。
 * @param onDismiss 当对话框被关闭时执行的操作
 * @param onGoToSettings 当用户选择打开设置时执行的操作
 */
@Composable
fun RationaleDialog(onDismiss: () -> Unit, onGoToSettings: () -> Unit) {
    TclDialog(
        onDismissRequest = { onDismiss.invoke() },
        show = true,
        content = {
            Text(
                text = stringResource(id = com.tcl.ai.note.base.R.string.dialog_grant_microphone_permission),
            )
        },
        actions = {
            TclTextButton(onClick = { onDismiss.invoke() }) { Text(stringResource(id = com.tcl.ai.note.base.R.string.btn_cancel)) }
            TclTextButton(
                onClick = { onDismiss.invoke()
                    onGoToSettings.invoke() }) { Text(stringResource(id = com.tcl.ai.note.base.R.string.btn_go_settings)) }
        })
}

@Composable
fun UsageLimitDialog(onDismissRequest: () -> Unit, onLearnMoreClick: () -> Unit) {
    // 获取当前屏幕方向
    val configuration = LocalConfiguration.current
    val screenOrientation = configuration.orientation

    // 使用屏幕方向作为key，确保方向变化时重组
    key(screenOrientation) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    Color(0X20000000)
                ),
        ) {
            Logger.d("AudioToTextScreen", "UsageLimitDialog 2")
            TclDialog(
                onDismissRequest = { onDismissRequest.invoke() },
                show = true,
                content = {
                    Text(
                        text = stringResource(id = com.tcl.ai.note.base.R.string.message_transcription_limit),
                    )
                },
                actions = {
                    TclTextButton(onClick = { onLearnMoreClick.invoke() }) { Text(stringResource(id = com.tcl.ai.note.base.R.string.btn_learn_more)) }
                })
        }
    }
}

@Composable
fun InputDialog(
    title: String,
    text: String,
    error: String = "",
    isError: Boolean = false,
    maxLength: Int = Int.MAX_VALUE,
    onValueChange: (String) -> Unit,
    onConfirm: (String) -> Unit,
    onDismissRequest: () -> Unit
) {
    val context = LocalContext.current
    val initialText = if (text.codePointCount(0, text.length) <= maxLength) text else substringByCodePoints(text, maxLength)
    var textFieldValue by remember {
        mutableStateOf(
            TextFieldValue(
                text = initialText,
                selection = TextRange(initialText.length)
            )
        )
    }
    var autoFocused by remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }
    val tip = stringResource(R.string.exceeds_maximum_characters)

    val dividerColor = colorResource(R.color.text_field_border)
    var divider by remember { mutableStateOf(dividerColor) }

    val density = LocalDensity.current

    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
        autoFocused = true
    }

    TclDialog(
        show = true,
        dialogMaxWidth = globalDialogWidth(),
        onDismissRequest = onDismissRequest,
        title = { Text(text = title) },
        content = {
            Column{
                Row(   modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp),
                    verticalAlignment = Alignment.CenterVertically) {
                    BasicTextField(
                        value = textFieldValue,
                        singleLine = true,
                        onValueChange = {newValue ->
                            val newLen = newValue.text.codePointCount(0, newValue.text.length)
                            if (newLen > maxLength) {
                                // 截取
                                val maxText = substringByCodePoints(newValue.text, maxLength)
                                // 每次都弹超出长度提示信息
                                ComposableToast.show(context, tip)
                                // 截取后，光标移至末尾
                                textFieldValue = TextFieldValue(
                                    text = maxText,
                                    selection = TextRange(maxText.length)
                                )
                                onValueChange(maxText)
                                return@BasicTextField
                            }
                            textFieldValue = if (!autoFocused) {
                                TextFieldValue(
                                    text = newValue.text,
                                    selection = TextRange(newValue.text.length)
                                )
                            } else {
                                if (newValue.text == textFieldValue.text) {
                                    newValue
                                } else {
                                    TextFieldValue(
                                        text = newValue.text,
                                        selection = newValue.selection
                                    )
                                }
                            }
                            onValueChange(newValue.text)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .focusRequester(focusRequester), // 添加自动获取焦点
                        cursorBrush = SolidColor(CursorColor),
                        textStyle = LocalTextStyle.current.copy(
                            color = colorResource(R.color.text_title),
                            fontSize = 16.sp,
                            lineHeight = with(density) { 22.dp.toSp() },
                        )
                    )
                }
            }

            HorizontalDivider(
                color = if (isError) colorResource(R.color.text_field_border_error) else divider,
                thickness = 2.dp,
            )

            Column(
                modifier = Modifier.fillMaxWidth().animateContentSize(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioLowBouncy,
                        stiffness = Spring.StiffnessLow
                    )
                )
            ) {
                if (isError) {
                    Text(
                        text = error,
                        color = colorResource(R.color.text_field_border_error),
                        fontSize = 14.sp,
                        lineHeight = with(density) { 16.dp.toSp() },
                        modifier = Modifier.padding(top = 6.dp)
                    )
                }
            }

        },
        actions = {
//            TclTextButton(onClick = { onDismissRequest.invoke() }) { Text(text = stringResource(id = R.string.cancel)) }
            TclTextButton(
                onClick = { onDismissRequest.invoke() },
                contentColor = colorResource(id = R.color.home_title_color),
            ) {
                Text(
                    text = stringResource(R.string.category_cancel),
                    color = colorResource(R.color.btn_new_category_cancel),
                    fontSize = 16.sp
                )
            }

//            TclTextButton(
//                enabled = textFieldValue.text.isNotEmpty() && !isError,
//                onClick = { onConfirm.invoke(textFieldValue.text) }
//            )
//            { Text(text = stringResource(id = R.string.dialog_positive_text_ok)) }

            TclTextButton(
                enabled = textFieldValue.text.isNotEmpty() && !isError,
                onClick = { onConfirm.invoke(textFieldValue.text) },
                contentColor = colorResource(id = R.color.home_title_color),
            )
            { Text(text = stringResource(id = R.string.dialog_positive_text_ok)) }
        },
    )
}

