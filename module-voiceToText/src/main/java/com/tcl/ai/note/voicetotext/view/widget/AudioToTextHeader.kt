package com.tcl.ai.note.voicetotext.view.widget

import androidx.compose.runtime.Composable
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ripple
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.ui.graphics.Color

import androidx.compose.ui.res.colorResource
import androidx.compose.ui.semantics.heading
import androidx.compose.ui.semantics.semantics

import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.AI_TITLE_BOTTOM_PADDING
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.voicetotext.states.LanguageConfigState
import com.tcl.ai.note.widget.MarqueeText
import com.tcl.ai.note.widget.testUIBorder

@Composable
fun AudioToTextHeader(
    languageConfigState: LanguageConfigState,
    showCloseIcon: Boolean = true,
    onCloseClick: () -> Unit = {}
) {
    val bottomPadding = if (isTablet) 8.dp else AI_TITLE_BOTTOM_PADDING
    Row(
        modifier = Modifier
            .padding(start = 16.dp, end = 16.dp, bottom = bottomPadding)
            .wrapContentHeight().semantics {
                heading()
            },
        verticalAlignment = Alignment.CenterVertically
    ) {
        MarqueeText(
            text = stringResource(id = R.string.audio_to_text),
            fontSize = if (isTablet) 14.sp else 20.sp,
            fontWeight = if (isTablet) FontWeight.Normal else FontWeight.Medium,
            modifier = Modifier.widthIn(max = 150.dp),
            wrapContentWidth = true,
            color = if (isTablet) colorResource(id = R.color.text_summary) else Color.Unspecified
        )
        Spacer(modifier = Modifier.weight(1f))

        if (showCloseIcon) {
            Image(
                painter = painterResource(id = R.drawable.close),
                contentDescription = stringResource(R.string.close),
                modifier = Modifier.size(24.dp).clickable(
                    interactionSource = null,
                    indication = ripple(bounded = false)
                ) {
                    onCloseClick()
                }
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun SummaryScreenPreview() {
    Column {
        AudioToTextHeader(
            languageConfigState = LanguageConfigState()
        )
    }
}