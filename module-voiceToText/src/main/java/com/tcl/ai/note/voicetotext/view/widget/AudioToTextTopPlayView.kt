package com.tcl.ai.note.voicetotext.view.widget

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.voicetotext.BuildConfig
import com.tcl.ai.note.voicetotext.R
import com.tcl.ai.note.voicetotext.states.RecordPlayingState
import com.tcl.ai.note.voicetotext.util.formatAudioTime
import com.tcl.ai.note.widget.HoverProofIconButton

/**
 * 录音转写弹框上的录音播放控件
 * */
@Composable
fun AudioToTextTopPlayView (
    audioPath: String,
    duration: Long,
    onPlayClick: () -> Unit = {},
    onPauseClick: () -> Unit = {},
    recordPlayingState: RecordPlayingState,
    isPhone: Boolean = BuildConfig.IS_PHONE,
) {
    val displayDuration by remember { mutableStateOf(formatAudioTime(duration)) }
    val text = if (recordPlayingState.playingPosition > 0) {
        "${formatAudioTime(recordPlayingState.playingPosition.toLong())} / $displayDuration"
    } else {
        displayDuration
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 16.dp, end = 16.dp)
            .height(if (isPhone)64.dp else 52.dp)
            .background(
                color = TclTheme.colorScheme.tctResultBgColor,
                shape = RoundedCornerShape(16.dp)
            )
    ) {

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(if (isPhone)52.dp else 40.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_audio_summary_file),
                contentDescription = null,
                modifier = Modifier
                    .padding(start = if (isPhone)16.dp else 21.dp, end = 8.dp)
            )
            Text(
                text = text,
                fontSize = 14.sp,
                color = TclTheme.colorScheme.tctStanderTextPrimary,
                modifier = Modifier
                    .weight(1.0f)
                    .fillMaxWidth(),
                textAlign = TextAlign.Start
            )
            if (recordPlayingState.isPlaying) {
                HoverProofIconButton(
                    modifier = Modifier
                        .padding(end = if (isPhone)0.dp else 12.dp),
                    onClick = {
                        onPauseClick.invoke()
                    }
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_audio_summary_pause),
                        contentDescription = stringResource(id =com.tcl.ai.note.base.R.string.pause_play),
                    )
                }
            } else {
                HoverProofIconButton(
                    modifier = Modifier
                        .padding(end = if (isPhone)0.dp else 12.dp),
                    onClick = {
                        onPlayClick.invoke()
                    }
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_audio_summary_play),
                        contentDescription = stringResource(id =com.tcl.ai.note.base.R.string.start_play),
                        )
                }
            }
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = if (isPhone) 16.dp else 21.dp,
                    end = if (isPhone)16.dp else 27.dp,
                    top = if (isPhone) 47.dp else 39.dp)
        ) {
            GradientProgressBar(
                progress = if (duration.toFloat() == 0f) 0f else recordPlayingState.playingPosition.toFloat() / duration.toFloat(),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(if (isPhone)4.dp else 2.dp)
                    .clip(RoundedCornerShape(4.dp))
            )
        }
    }
}

/**
 * 渐变播放进度条
 * */
@Composable
fun GradientProgressBar(
    progress: Float,
    modifier: Modifier = Modifier
) {
    val gradientColors = listOf(
        Color(0xFFD798FA),
        Color(0xFF4D6BF2)
    )
    val backgroundColor =if (isSystemInDarkTheme()) Color(0xE6EBEDEF) else Color(0xFFF2F2F2)

    Canvas(
        modifier = modifier
    ) {
        val brush = Brush.linearGradient(
            colors = gradientColors,
            start = Offset(0f, 0f),
            end = Offset(size.width, size.height)
        )

        // 绘制背景线
        drawRoundRect(
            color = backgroundColor,
            size = size,
            cornerRadius = CornerRadius(12.dp.toPx())
        )

        // 绘制渐变进度条
        drawRoundRect(
            brush = brush,
            size = size.copy(width = size.width * progress),
            cornerRadius = CornerRadius(12.dp.toPx())
        )
    }
}

@Preview(showBackground = true)
@Composable
fun GradientProgressBarPreview() {
    TclTheme {
        GradientProgressBar(
            progress = 50f,
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .clip(RoundedCornerShape(4.dp))
        )
    }
}

@Composable
@Preview
fun AudioSummaryScreenPlayViewPreview() {
    AudioToTextTopPlayView("", 0, {}, {}, RecordPlayingState())
}