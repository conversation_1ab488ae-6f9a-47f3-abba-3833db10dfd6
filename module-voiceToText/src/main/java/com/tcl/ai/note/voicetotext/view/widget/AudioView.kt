package com.tcl.ai.note.voicetotext.view.widget

import android.Manifest
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.activity.ComponentActivity
import androidx.activity.compose.LocalActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Column
import androidx.compose.material3.LocalContentColor
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.core.app.ActivityCompat.shouldShowRequestPermissionRationale
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.NoteTclTheme
import com.tcl.ai.note.utils.isFastClick
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.voicetotext.audio.ShowAudioRecorder
import com.tcl.ai.note.voicetotext.intent.RecordIntent
import com.tcl.ai.note.voicetotext.util.generateAudioPath
import com.tcl.ai.note.voicetotext.util.toAppSetting
import com.tcl.ai.note.voicetotext.vm.RecordingViewModel
import com.tcl.ai.note.widget.DelayedBackgroundIconButton
import com.tcl.ai.note.widget.IconThemeSwitcher
import com.tcl.ai.note.voicetotext.audio.RecordingForegroundService
import android.os.Handler
import android.os.Looper

/**
 * 录音组件，用于请求音频录制权限及录音处理。
 */
@Composable
fun AudioView(
    noteId: Long = 0L,
    viewModel: RecordingViewModel = hiltViewModel(),
    btnSize: Dp,
    iconSize:Dp = btnSize,
    painterNor: Painter,
    painterEnable: Painter,
    onAudioClick: (audioPath: String, isAdd: Boolean) -> Unit
) {
    val activity = LocalActivity.current as? ComponentActivity
    var showRationaleDialog by remember { mutableStateOf(false) }
    val recordState by viewModel.recordState.collectAsState()
    val will2HourState by ShowAudioRecorder.will2HourDialog.collectAsState()
    val reach2HourState by ShowAudioRecorder.reach2HourDialog.collectAsState()
    var isShowWill2HourDialog by remember { mutableStateOf(false) }
    var isShow2HourDialog by remember { mutableStateOf(false) }

    // 创建权限请求启动器，用于处理权限请求结果
    val permissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            if (recordState.isRecording) {
                viewModel.stopRecord()
                val audioPath = recordState.audioPath
                audioPath?.let { onAudioClick.invoke(it, false) }
            } else {
                val audioPath = generateAudioPath(noteId)
                viewModel.recordingIntent(RecordIntent.StartRecord(audioPath))
                onAudioClick.invoke(audioPath, true)
            }
        } else {
            activity?.let {
                if (!shouldShowRequestPermissionRationale(
                        it,
                        Manifest.permission.RECORD_AUDIO
                    )
                ) {
                    // 更新状态以显示理由对话框
                    showRationaleDialog = true
                }
            }
        }
    }

    if (will2HourState) {
        isShowWill2HourDialog = true
        ShowAudioRecorder.reset2HourTipState()
    }

    if (reach2HourState) {
        isShowWill2HourDialog = false
        isShow2HourDialog = true
        ShowAudioRecorder.reset2HourTipState()
    }

    NoteTclTheme {
        Column {
            // 图标按钮，用于启动音频录制权限请求
            IconThemeSwitcher(
                btnSize = btnSize,
                iconSize = iconSize,
                painter = (recordState.isRecording).judge(
                    // 如果正在录音，显示停止图标
                    painterEnable,
                    // 如果未录音，显示录音图标
                    painterNor
                ),
                contentDescription =(recordState.isRecording).judge(R.string.menu_stop_recording,R.string.menu_start_recording).stringRes() ,
                tint =  (recordState.isRecording).judge(Color.Unspecified, LocalContentColor.current),
                onClick = {
                    if (isFastClick()) {
                        return@IconThemeSwitcher
                    }
                    permissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
                }

            )

            // 如果需要，显示理由对话框-引导开启录音权限
            if (showRationaleDialog) {
                RationaleDialog(
                    onDismiss = { showRationaleDialog = false },
                    onGoToSettings = {
                        showRationaleDialog = false
                        activity?.let { toAppSetting(it) }
                    }
                )
            }

            if (isShowWill2HourDialog) {
                ShowWill2HourDialog(
                    onDismiss = {
                        isShowWill2HourDialog = false
                    },
                )
            }

            if (isShow2HourDialog) {
                Show2HourDialog(
                    onSaveAndStartNew = {
                        isShow2HourDialog = false
                        saveAndStartNewAudio(noteId, viewModel)
                        // 不调用onAudioClick，让正常的录音流程处理新录音的添加
                    },
                    onFinish = {
                        isShow2HourDialog = false
                        viewModel.stopRecord()
                    },
                    onContinue = {
                        isShow2HourDialog = false
                    }
                )
            }
        }
    }
}

private fun saveAndStartNewAudio(
    noteId: Long,
    viewModel: RecordingViewModel
) {
    // 获取当前录音路径
    val currentAudioPath = ShowAudioRecorder.audioFilePath

    // 先停止当前录音
    if (ShowAudioRecorder.isRecording) {
        val duration = ShowAudioRecorder.stopRecord()
        // 手动更新录音状态，标记为"保存并开始新录音"操作
        viewModel.updateRecordingStateForSaveAndStartNew(
            audioPath = currentAudioPath,
            duration = duration
        )
    }
    RecordingForegroundService.stopRecordingService()

    // 延迟一段时间确保当前录音完全停止并保存
    Handler(Looper.getMainLooper()).postDelayed({
        val newAudioPath = generateAudioPath(noteId)
        viewModel.recordingIntent(RecordIntent.StartRecord(newAudioPath))
        // 不调用任何回调，让正常的录音流程处理新录音的添加
    }, 200) // 增加延迟时间确保录音状态正确切换
}
