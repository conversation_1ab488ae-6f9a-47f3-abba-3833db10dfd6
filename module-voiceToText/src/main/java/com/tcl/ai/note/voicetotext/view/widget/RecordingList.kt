package com.tcl.ai.note.voicetotext.view.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.role
import com.tcl.ai.note.controller.LoginErrorResult
import com.tcl.ai.note.controller.isAIServiceEnable
import com.tcl.ai.note.controller.rememberLoginHandler
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.rememberScreenWidth
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.voicetotext.R
import com.tcl.ai.note.voicetotext.util.formatAudioTime
import com.tcl.ai.note.voicetotext.util.getAudioDuration
import com.tcl.ai.note.voicetotext.util.getAudioFileFolder
import com.tcl.ai.note.voicetotext.util.getFileCreateFormatTime
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel
import com.tcl.ai.note.widget.HoverProofIconButton
import com.tcl.ai.note.widget.VerticalLine
import com.tcl.ai.note.widget.verticalScrollbar
import com.tcl.ai.note.widget.components.AIServiceDialog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.io.File

@Composable
fun RecordingList(
    noteId: Long,
    audioPaths: List<String>, // 数据源
    currentDisplayedAudioPath: String? = null, // 当前在播放条中显示的音频路径
    onDeleteAudios: (List<String>) -> Unit,
    onDismissRequest: () -> Unit,
    onAudioToTextClick: () -> Unit,
    onItemClick: (String) -> Unit,
    onConfirmRename: (String, String) -> Unit,
    audioToTextViewModel: AudioToTextViewModel,
) {
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    val showAiServiceDialog = remember { mutableStateOf(false) }
    val loginHandler = rememberLoginHandler(onFailure = {
        if (it == LoginErrorResult.LunchError) {
            showAiServiceDialog.value = true
            Logger.d("AudioBlock", "loginHandler onFailure")
        }
    })
    AIServiceDialog(
        isShow = showAiServiceDialog.value,
        title = context.getString(com.tcl.ai.note.base.R.string.audio_to_text),
        onDismiss = { showAiServiceDialog.value = false },
        onGoToSettings = {
            showAiServiceDialog.value = false
        }
    )

    val audioToTextState by audioToTextViewModel.audioToTextState.collectAsState()
    var isEditMode by remember { mutableStateOf(false) }
    var selectedAudios by remember { mutableStateOf(emptyList<String>()) }
    var showDeleteAudioDialog by remember { mutableStateOf(false) }
    val durationMap = remember { mutableStateOf(mutableMapOf<String, String>()) }
    val tintColor = if(isSystemInDarkTheme()) Color(0xFFE9E9E9) else Color(0xFF191919)
    val lazyListState = rememberLazyListState()

    // 计算Popup的偏移量
    val density = LocalDensity.current
    val alignment = if (isTablet) Alignment.TopEnd else Alignment.TopCenter
    val xOffset = if (isTablet) with(density) { (-24).dp.toPx().toInt() } else 0

    LaunchedEffect(audioToTextState) {
        handleAudioToTextState(audioToTextState, audioToTextViewModel)
    }

    Popup(
        alignment = alignment,
        offset = IntOffset(xOffset, with(density) { 102.dp.toPx().toInt() }),
        onDismissRequest = onDismissRequest,
        properties = PopupProperties(
            focusable = true
        )
    ) {
        Box(
            modifier = if (isTablet) {
                Modifier
                    .width(328.dp)
                    .heightIn(max = 364.dp)
                    .shadow(4.dp, RoundedCornerShape(16.dp))
                    .background(TclTheme.colorScheme.reWriteExpandBg, RoundedCornerShape(16.dp))
            } else {
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .heightIn(max = 364.dp)
                    .shadow(4.dp, RoundedCornerShape(16.dp))
                    .background(TclTheme.colorScheme.reWriteExpandBg, RoundedCornerShape(16.dp))
            }
        ) {
            Column(modifier = Modifier.padding(vertical = 12.dp)) {

                // 顶部区域
                Row(
                    Modifier
                        .fillMaxWidth()
                        .padding(start = 18.dp, end = 15.dp)
                        .height(40.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (isEditMode) {
                        Text(
                            text = String.format(
                                stringResource(com.tcl.ai.note.base.R.string.selected_audios),
                                selectedAudios.size
                            ),
                            color = TclTheme.colorScheme.tctStanderTextPrimary,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier
                                .weight(1f)
                                .padding(start = 8.dp)
                        )

                        val selectAllDescription = stringResource(
                            if (audioPaths.size == selectedAudios.size)
                                com.tcl.ai.note.base.R.string.deselect_all_audios
                            else
                                com.tcl.ai.note.base.R.string.select_all_audios
                        )
                        Column(Modifier
                            .semantics {
                                contentDescription = selectAllDescription
                                role = Role.Checkbox
                            }
                            .clickable {
                                selectedAudios = if (audioPaths.size == selectedAudios.size) {
                                    selectedAudios - selectedAudios.toSet()
                                } else {
                                    audioPaths
                                }
                            }) {
                            Box(
                                Modifier
                                    .size(24.dp), Alignment.Center) {
                                Image(
                                    painter = painterResource(id = if (audioPaths.size == selectedAudios.size) com.tcl.ai.note.base.R.drawable.ic_selected_all_checked else com.tcl.ai.note.base.R.drawable.ic_selected_all),
                                    contentDescription = stringResource(com.tcl.ai.note.base.R.string.accessibility_select_all_recordings)
                                )
                            }
                        }

                        Spacer(modifier = Modifier.width(18.dp))

                        val deleteDescription = stringResource(com.tcl.ai.note.base.R.string.delete_selected_audios)
                        HoverProofIconButton(
                            modifier = Modifier
                                .size(24.dp)
                                .semantics {
                                    contentDescription = deleteDescription
                                },
                            enabled = selectedAudios.isNotEmpty(),
                            onClick = { showDeleteAudioDialog = true },
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_audio_delete),
                                contentDescription = null,
                                alpha = if (selectedAudios.isNotEmpty()) 1f else 0.5f
                            )
                        }
                    } else {
                        Text(
                            text = stringResource(id=com.tcl.ai.note.base.R.string.recording_audio),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier
                                .weight(1f),
                            color = tintColor
                        )
                        // 编辑按钮
                        val editDescription = stringResource(com.tcl.ai.note.base.R.string.accessibility_edit_recordings)
                        Text(
                            text = stringResource(id=com.tcl.ai.note.base.R.string.edit),
                            fontSize = 14.sp,
                            color = tintColor,
                            modifier = Modifier
                                .semantics {
                                    contentDescription = editDescription
                                    role = Role.Button
                                }
                                .clickable { isEditMode = true }
                        )
                    }
                }
                LazyColumn(
                    state = lazyListState,
                    modifier = Modifier.verticalScrollbar(
                        state = lazyListState,
                        alwaysShowScrollBar = false,
                        scrollbarWidth = 3.dp,
                        offsetX = 8f
                    )
                ) {
                    items(audioPaths.size) { index ->
                        val audioPath = audioPaths[index]
                        val durationText = getRecordingItemDurationText(audioPath, durationMap)
                        RecordingItem(
                            noteId = noteId,
                            editMode = isEditMode,
                            isSelected = selectedAudios.contains(audioPath),
                            isDisplayed = currentDisplayedAudioPath == audioPath,
                            audioPath = audioPath,
                            durationText = durationText,
                            onItemClick = { path ->
                                Logger.d("RecordingList", "onItemClick: $path, isEditMode: $isEditMode")
                                if (isEditMode) {
                                    selectedAudios = if (selectedAudios.contains(path)) {
                                        selectedAudios - path
                                    } else {
                                        selectedAudios + path
                                    }
                                } else {
                                    onItemClick(path)
                                }
                            },
                            onConfirmRename = { oldPath, newPath ->
                                audioToTextViewModel.renameAudioFile(oldPath, newPath)
                                onConfirmRename(oldPath, newPath)
                            },
                            onAudioToTextClick = {
                                onAudioToTextClick()
                            },
                            onHandleAudioToText = {
                                handleAudioToTextClick(audioPath, audioToTextViewModel, loginHandler)
                            },
                            coroutineScope = coroutineScope
                        )
                    }
                }
            }

            if (showDeleteAudioDialog) {
                DeleteRecordingListAudioDialog(
                    audioCount = selectedAudios.size,
                    isDeleteAll = selectedAudios.size == audioPaths.size,
                    onDismiss = {
                        showDeleteAudioDialog = false
                    },
                    onDelete = {
                        onDeleteAudios(selectedAudios)
                        selectedAudios = emptyList()
                        showDeleteAudioDialog = false
                    },
                )
            }
        }
    }
}

@Composable
private fun RecordingItem(
    noteId: Long,
    editMode: Boolean = false,
    isSelected: Boolean = false,
    isDisplayed: Boolean = false, // 是否为当前在播放条中显示的录音
    audioPath: String,
    durationText: String,
    onItemClick: (String) -> Unit = {},
    onConfirmRename: (String, String) -> Unit,
    onAudioToTextClick: () -> Unit = {},
    onHandleAudioToText: suspend () -> Unit = {},
    coroutineScope: CoroutineScope,
) {
    val dimens = getGlobalDimens()
    var showRenameDialog by remember { mutableStateOf(false) }
    var hasSameNameAudioFile by remember { mutableStateOf(false) }
    var renamedFile by remember { mutableStateOf(File(audioPath)) }

    // 判断是否应该显示选中状态（编辑模式下的选中或当前显示在播放条中）
    val isHighlighted = (editMode && isSelected) || (!editMode && isDisplayed)

    // 录音名称颜色：选中/当前显示时为橙色，默认为黑色
    val titleColor = if (isHighlighted) {
        Color(0xFFFF9E00) // 橙色
    } else {
        TclTheme.colorScheme.tctStanderTextPrimary // 默认黑色，透明度90%
    }

    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    val itemDescription = if (editMode) {
        stringResource(
            if (isSelected) com.tcl.ai.note.base.R.string.audio_item_selected_description
            else com.tcl.ai.note.base.R.string.audio_item_unselected_description,
            File(audioPath).nameWithoutExtension
        )
    } else {
        stringResource(
            com.tcl.ai.note.base.R.string.audio_item_description,
            File(audioPath).nameWithoutExtension,
            durationText
        )
    }

    Row(
        Modifier
            .fillMaxWidth()
            .height(48.dp)
            .padding(horizontal = 12.dp)
            .semantics {
                contentDescription = itemDescription
                role = if (editMode) Role.Checkbox else Role.Button
            }
            .then(
                if (isPressed) {
                    Modifier.background(
                        color = Color(0x1A000000), // 背景颜色，透明度10%
                        shape = RoundedCornerShape(8.dp)
                    )
                } else {
                    Modifier
                }
            )
            .clickable(
                interactionSource = interactionSource,
                indication = null, // 可选：去掉水波
                onClick = { onItemClick(audioPath) }
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {

        if(editMode){
            // 编辑模式下显示复选图标
            if(isSelected){
                Box(
                    Modifier
                        .padding(start = 12.dp)
                        .size(20.dp), Alignment.Center) {
                    Image(
                        painter = painterResource(id = com.tcl.ai.note.base.R.drawable.ic_note_checkbox_selected),
                        contentDescription = stringResource(com.tcl.ai.note.base.R.string.accessibility_recording_selected)
                    )
                }
            } else {
                Box(
                    Modifier
                        .padding(start = 12.dp)
                        .size(20.dp), Alignment.Center) {
                    Image(
                        painter = painterResource(id = com.tcl.ai.note.base.R.drawable.ic_note_checkbox_normal),
                        contentDescription = stringResource(com.tcl.ai.note.base.R.string.accessibility_recording_not_selected)
                    )
                }
            }
        }

        Column(
            modifier = Modifier.padding(start = if (editMode) 8.dp else 12.dp, end = 12.dp).weight(1f)
                .align(Alignment.CenterVertically)
        ) {
            Text(
                text = File(audioPath).nameWithoutExtension,
                color = titleColor,
                fontSize = 14.sp,
                modifier = Modifier
                    .fillMaxWidth().wrapContentHeight(unbounded = true),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = getFileCreateFormatTime(audioPath),
                fontSize = 10.sp,
                color = TclTheme.colorScheme.tctStanderTextSecondary,
                modifier = Modifier
                    .fillMaxWidth().wrapContentHeight(unbounded = true)
                    .offset(y = (-6).dp),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        Text(
            text = durationText,
            fontSize = 14.sp,
            color = TclTheme.colorScheme.tctStanderTextPrimary,
            textAlign = TextAlign.End,
            modifier = Modifier
                .padding(end = 12.dp)
        )

        if (!editMode) {
            VerticalLine(modifier = Modifier.padding(vertical = 12.dp))
            val renameDescription = stringResource(com.tcl.ai.note.base.R.string.rename_audio_button)
            HoverProofIconButton(
                modifier = Modifier
                    .padding(start = 12.dp)
                    .size(dimens.btnSize)
                    .semantics {
                        contentDescription = renameDescription
                    },
                enabled = true,
                onClick = {
                    showRenameDialog = true
                }
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_audio_rename),
                    contentDescription = stringResource(id = com.tcl.ai.note.base.R.string.accessibility_rename_recording),
                    modifier = Modifier.size(dimens.iconSize),
                )
            }
            if (isAIServiceEnable) {
                val audioToTextDescription = stringResource(com.tcl.ai.note.base.R.string.audio_to_text_button)
                HoverProofIconButton(
                    modifier = Modifier
                        .padding(end = 12.dp)
                        .size(dimens.btnSize)
                        .semantics {
                            contentDescription = audioToTextDescription
                        },
                    enabled = true,
                    onClick = {
                        coroutineScope.launch {
                            onHandleAudioToText()
                        }
                        onAudioToTextClick()
                    }
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_audio_to_text_new),
                        contentDescription = null,
                        modifier = Modifier.size(dimens.iconSize),
                    )
                }
            }
        }

    }
    if (showRenameDialog) {
        Logger.d("RecordingList", "showRenameDialog: $audioPath")
        InputDialog(
            title = stringResource(id = com.tcl.ai.note.base.R.string.rename_audio),
            text = File(audioPath).nameWithoutExtension,
            onValueChange = {
                renamedFile = File(getAudioFileFolder(noteId = noteId), "$it.amr")
                hasSameNameAudioFile = renamedFile.exists() && renamedFile != File(audioPath)
            },
            isError = hasSameNameAudioFile,
            error = stringResource(id = com.tcl.ai.note.base.R.string.text_same_audio_file),
            maxLength = 50,
            onConfirm = {
                coroutineScope.launch {
                    onConfirmRename(audioPath, renamedFile.absolutePath)
                }
                showRenameDialog = false
            },
            onDismissRequest = {
                showRenameDialog = false
                hasSameNameAudioFile = false
                //renamedFile = File("")
            })
    }
}

private fun getRecordingItemDurationText(
    audioPath: String,
    durationMap: MutableState<MutableMap<String, String>>
): String {
    if (!durationMap.value.containsKey(audioPath)) {
        durationMap.value[audioPath] = formatAudioTime(getAudioDuration(audioPath))
    }
    return durationMap.value[audioPath]!!
}