package com.tcl.ai.note.voicetotext.view.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.isTablet
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.res.stringResource
import com.tcl.ai.note.voicetotext.R
import com.tcl.ai.note.voicetotext.audio.ShowAudioRecorder
import com.tcl.ai.note.voicetotext.intent.RecordIntent
import com.tcl.ai.note.voicetotext.states.RecordSpecialState
import com.tcl.ai.note.voicetotext.states.RecordingState
import com.tcl.ai.note.voicetotext.util.AudioToTextConstant
import com.tcl.ai.note.voicetotext.util.formatAudioTime
import com.tcl.ai.note.voicetotext.util.formatAudioTimeMinuteSecond
import com.tcl.ai.note.voicetotext.util.generateAudioPath
import com.tcl.ai.note.voicetotext.vm.RecordingViewModel
import com.tcl.ai.note.voicetotext.audio.RecordingForegroundService
import android.os.Handler
import android.os.Looper


/**
 * 录音内容块,显示在平板标题栏中
 * */
@Composable
fun TitleAudioBlock(
    noteId: Long,
    darkTheme: Boolean =false,
    audioPath: String?,
    onStopRecordClick: (audioPath: String) -> Unit = {},
    onRecordingError: (audioPath: String?) -> Unit = {},
    onAddAudio: (String) -> Unit = {},
    recordingViewModel: RecordingViewModel = hiltViewModel(),
) {
    val recordingState by recordingViewModel.recordState.collectAsState()
    val will2HourState by ShowAudioRecorder.will2HourDialog.collectAsState()
    val reach2HourState by ShowAudioRecorder.reach2HourDialog.collectAsState()
    var isShowWill2HourDialog by remember { mutableStateOf(false) }
    var isShow2HourDialog by remember { mutableStateOf(false) }
    if (will2HourState) {
        isShowWill2HourDialog = true
        ShowAudioRecorder.reset2HourTipState()
    }

    if (reach2HourState) {
        isShowWill2HourDialog = false
        isShow2HourDialog = true
        ShowAudioRecorder.reset2HourTipState()
    }
    val isCurrentRecording = recordingState.audioPath == audioPath && recordingState.isRecording
    Logger.d("TitleAudioBlock", "isCurrentRecording = $isCurrentRecording, audioPath = $audioPath, recordingState path = ${recordingState.audioPath}")

    if (isCurrentRecording && audioPath?.isNotEmpty() == true) {
        LaunchedEffect(recordingState) {
            handleRecordingState(audioPath, recordingState, onRecordingError)
        }

        val context = LocalContext.current
        val recordingBlockDescription = stringResource(com.tcl.ai.note.base.R.string.accessibility_recording_block)

        Box(
            contentAlignment = Alignment.CenterStart,
            modifier = Modifier
                .wrapContentSize()
                .semantics {
                    contentDescription = recordingBlockDescription
                }
                .clickable {
                    onStopRecordClick(audioPath)
                    //onStopRecord()
                }
                .background(
                    color = darkTheme
                        .judge(R.color.title_audio_bg_color_night, R.color.title_audio_bg_color)
                        .colorRes(),
                    shape = RoundedCornerShape(277.dp)
                )
        ) {
            Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.padding(vertical = 1.dp)){
                Image(
                    painter = painterResource(id = com.tcl.ai.note.base.R.drawable.ic_tablet_menu_recording_stop_new),
                    contentDescription = stringResource(com.tcl.ai.note.base.R.string.accessibility_stop_recording),
                    modifier = Modifier
                        .padding(start = 4.dp, end = 4.dp)
                        .size(20.dp)
                )

                val durationText = if (isTablet) {
                    formatAudioTime(recordingState.recordDuration)
                } else {
                    formatAudioTimeMinuteSecond(recordingState.recordDuration)
                }
                val durationDescription = String.format(
                    context.getString(com.tcl.ai.note.base.R.string.accessibility_recording_duration),
                    durationText
                )

                Text(
                    text = durationText,
                    fontSize = 14.sp,
                    color = darkTheme.judge(R.color.tablet_audio_duration_night,R.color.tablet_audio_duration).colorRes(),
                    modifier = Modifier
                        .wrapContentWidth()
                        .padding(end = 6.dp)
                        .semantics {
                            contentDescription = durationDescription
                        }
                )
            }

            if (isShowWill2HourDialog) {
                ShowWill2HourDialog(
                    onDismiss = {
                        isShowWill2HourDialog = false
                    },
                )
            }

            if (isShow2HourDialog) {
                Show2HourDialog(
                    onSaveAndStartNew = {
                        isShow2HourDialog = false
                        saveAndStartNewAudio(noteId, recordingViewModel)
                        // 不调用onAddAudio，因为它会触发rememberAudioPermissionHelper
                        // 重新生成路径并开始录音，导致重复录音问题
                    },
                    onFinish = {
                        isShow2HourDialog = false
                        recordingViewModel.stopRecord()
                    },
                    onContinue = {
                        isShow2HourDialog = false
                    }
                )
            }
        }
    }
}

private fun handleRecordingState(
    audioPath: String,
    recordingState: RecordingState,
    onRecordingError: (audioPath: String?) -> Unit,
) {
    if (audioPath == recordingState.audioPath
        && (recordingState.specialState is RecordSpecialState.RecordingError || (!recordingState.isRecording && recordingState.recordDuration > 0 && recordingState.recordDuration < AudioToTextConstant.MIN_RECORD_DURATION))) {
        if (recordingState.specialState is RecordSpecialState.RecordingError) {
            ToastUtils.makeWithCancel(recordingState.specialState.message)
        } else {
            ToastUtils.makeWithCancel(com.tcl.ai.note.base.R.string.edit_audio_record_shortest_time)
        }
        onRecordingError(recordingState.audioPath)
    }
}

private fun saveAndStartNewAudio(
    noteId: Long,
    viewModel: RecordingViewModel
) {
    // 获取当前录音路径
    val currentAudioPath = ShowAudioRecorder.audioFilePath

    // 先停止当前录音
    if (ShowAudioRecorder.isRecording) {
        val duration = ShowAudioRecorder.stopRecord()
        // 手动更新录音状态，标记为"保存并开始新录音"操作
        viewModel.updateRecordingStateForSaveAndStartNew(
            audioPath = currentAudioPath,
            duration = duration
        )
    }
    RecordingForegroundService.stopRecordingService()

    // 延迟一段时间确保当前录音完全停止并保存
    Handler(Looper.getMainLooper()).postDelayed({
        val newAudioPath = generateAudioPath(noteId)
        viewModel.recordingIntent(RecordIntent.StartRecord(newAudioPath))
        // 不调用任何回调，让正常的录音流程处理新录音的添加
    }, 200) // 增加延迟时间确保录音状态正确切换
}


@Composable
@Preview
private fun TitleAudioBlockPreview() {
    TitleAudioBlock(
        noteId = 123L,
        darkTheme = false,
        audioPath = "audioPath",
        onStopRecordClick = {},
        onRecordingError = {},
    )
}
