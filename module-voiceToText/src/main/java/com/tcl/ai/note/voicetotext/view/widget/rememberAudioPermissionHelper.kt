package com.tcl.ai.note.voicetotext.view.widget

import android.Manifest
import androidx.activity.ComponentActivity
import androidx.activity.compose.LocalActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.core.app.ActivityCompat.shouldShowRequestPermissionRationale
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.voicetotext.intent.RecordIntent
import com.tcl.ai.note.voicetotext.util.generateAudioPath
import com.tcl.ai.note.voicetotext.util.toAppSetting
import com.tcl.ai.note.voicetotext.vm.RecordingViewModel

@Composable
fun rememberAudioPermissionHelper(
    noteId: Long,
    viewModel: RecordingViewModel = hiltViewModel(),
    onStartRecording:(audioPath: String) -> Unit,
): (action: () -> Unit) -> Unit {
    val activity = LocalActivity.current as? ComponentActivity
    val recordState by viewModel.recordState.collectAsState()
    var showRationaleDialog by remember { mutableStateOf(false) }

    val permissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        //onResetCheckPermission.invoke()
        if (isGranted) {
            if (!recordState.isRecording) {
                val audioPath = generateAudioPath(noteId)
                viewModel.recordingIntent(RecordIntent.StartRecord(audioPath))
                onStartRecording(audioPath)
            }
        } else {
            activity?.let {
                if (!shouldShowRequestPermissionRationale(
                        it,
                        Manifest.permission.RECORD_AUDIO
                    )
                ) {
                    // 更新状态以显示理由对话框
                    showRationaleDialog = true
                }
            }
        }
    }

    /*if (checkAudioPermission) {
        permissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
    }*/

    // 如果需要，显示理由对话框-引导开启录音权限
    if (showRationaleDialog) {
        RationaleDialog(
            onDismiss = { showRationaleDialog = false },
            onGoToSettings = {
                showRationaleDialog = false
                activity?.let { toAppSetting(it) }
            }
        )
    }

    return { action ->
        // 如果没有录音，先请求权限
        if (activity != null) {
            permissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
        }
    }
}