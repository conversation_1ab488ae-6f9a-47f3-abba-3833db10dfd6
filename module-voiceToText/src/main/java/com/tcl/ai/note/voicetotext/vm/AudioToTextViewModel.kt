package com.tcl.ai.note.voicetotext.vm

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.work.Constraints
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.OutOfQuotaPolicy
import androidx.work.WorkManager
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.net.NetworkUseCase
import com.tcl.ai.note.utils.AppActivityManager
import com.tcl.ai.note.voicetotext.util.AudioToTextConstant
import com.tcl.ai.note.voicetotext.data.AudioRepository
import com.tcl.ai.note.voicetotext.intent.AudioToTextIntent
import com.tcl.ai.note.voicetotext.states.AudioToTextState
import com.tcl.ai.note.voicetotext.states.ErrorState
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.utils.startAudioToText
import com.tcl.ai.note.voicetotext.audio.ShowAudioPlayer
import com.tcl.ai.note.voicetotext.audio.ShowAudioRecorder
import com.tcl.ai.note.voicetotext.data.AudioTransferEntity
import com.tcl.ai.note.voicetotext.states.LanguageConfigState
import com.tcl.ai.note.voicetotext.util.deleteFile
import com.tcl.ai.note.voicetotext.util.getAudioDuration
import com.tcl.ai.note.voicetotext.states.RecordPlayingState
import com.tcl.ai.note.voicetotext.track.AnalyticsVoiceToTextModel
import com.tcl.ai.note.voicetotext.util.renameFile
import com.tcl.ai.note.voicetotext.worker.MeetingMinutesWorker
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject
import kotlin.math.abs

/**
 * 音频转文本的 ViewModel 类
 * 依赖注入了应用程序上下文和音频数据仓库
 */
@HiltViewModel
class AudioToTextViewModel @Inject constructor(
    @ApplicationContext val context: Context,
    private val networkUseCase: NetworkUseCase,
    private val repository: AudioRepository) : ViewModel() {

    init {
        // 初始化时连接账户控制器
        AccountController.connect()
    }

    // 音频播放器的展示类
    //private val showAudioPlayer: ShowAudioPlayer = ShowAudioPlayer
    // 音频转文本的状态流
    private val _audioToTextState = MutableStateFlow(AudioToTextState())
    val audioToTextState = _audioToTextState.asStateFlow()


    private val _languageConfigState = MutableStateFlow(LanguageConfigState())
    val languageConfigState = _languageConfigState.asStateFlow()
    private var newIndex = 0

    // 录音状态流
    private val _recordPlayingState = MutableStateFlow(RecordPlayingState())
    val recordPlayingState = _recordPlayingState.asStateFlow()

    // 跟踪转写任务是否正在进行
    private val _isTranscriptionInProgress = MutableStateFlow(false)
    val isTranscriptionInProgress = _isTranscriptionInProgress.asStateFlow()

    // 工作管理器实例
    private val workManager = WorkManager.getInstance(context)
     val isOffline = networkUseCase.isOffline(viewModelScope)

    init {
        AnalyticsVoiceToTextModel.loadAudioToTextViewModel(this)
        loadDefaultConfigState()
        updateAudioToTextState()
        updatePlayingState()
        monitorNetworkStatus()
        monitorAppLifecycle()
    }

    private fun loadDefaultConfigState() {
        viewModelScope.launch {
            val defaultState = repository.loadDefaultConfig()
            newIndex = defaultState.selectLanguageIndex

            // 更新状态
            _languageConfigState.update {
                it.copy(selectLanguageIndex = newIndex)
            }
        }
    }
    private fun updateAudioToTextState() {
        viewModelScope.launch {
            MeetingMinutesWorker.generateResultFlow.collect { result ->
                Logger.i(TAG, "generateResultFlow: $result")
                if (result.isGenerateComplete) {
                    _audioToTextState.value = AudioToTextState(
                        isLoading = false,
                        audioPath = result.filePath,
                        transfers = result.result,
                        errorState = result.errorState
                    )
                }
            }
        }
    }
    /**
     * 处理音频转录意图的方法
     * @param audioPath 音频文件路径
     * @param intent 转录意图
     */
    fun handleTranscriptionIntent(audioPath: String, intent: AudioToTextIntent) {
        Logger.d(TAG, "handleIntent: $intent, audioPath: $audioPath")
        when (intent) {
            is AudioToTextIntent.StartTranscription -> {
                _audioToTextState.value = AudioToTextState(isLoading = false, audioPath)
                handleTranscriptionIntent(audioPath, AudioToTextIntent.CheckAudioDuration)
            }
            is AudioToTextIntent.CheckAudioDuration -> {
               viewModelScope.launchIO {
                   val duration = getAudioDuration(audioPath)
                   if (checkDurationIsValid(duration)) {
                       _audioToTextState.value = AudioToTextState(
                           audioPath = audioPath,
                           errorState = ErrorState.AudioDurationError
                       )
                   } else {
                           val transcription = getLocalTranscription(audioPath)
                           if (transcription.isNotEmpty()) {
                               showTranscriptionScreen(audioPath)
                               handleTranscriptionIntent(
                                   audioPath,
                                   AudioToTextIntent.GetLocalTranscription(transfers = transcription)
                               )
                           } else if (isNetworkAvailable()) {
                               if (checkOtherFileInTranscription(audioPath)) {
                                   Logger.d(TAG, "Other file is in transcription")
                                   _audioToTextState.value = AudioToTextState(
                                       audioPath = audioPath,
                                       errorState = ErrorState.OtherFileInTranscription
                                   )
                               } else {
                                   showTranscriptionScreen(audioPath)
                                   handleTranscriptionIntent(
                                       audioPath,
                                       AudioToTextIntent.GetLocalTranscription(transfers = transcription)
                                   )
                               }
                           } else {
                               _audioToTextState.value = AudioToTextState(
                                   audioPath = audioPath,
                                   errorState = ErrorState.NetworkError
                               )
                           }
                   }
               }
            }
            is AudioToTextIntent.GetLocalTranscription -> {
                viewModelScope.launch(Dispatchers.Default) {
                    var transcription = intent.transfers
                    if (transcription.isNullOrEmpty()) {
                        transcription = getLocalTranscription(audioPath)
                    }
                    if (transcription.isNotEmpty()) {
                        _audioToTextState.value = AudioToTextState(isLoading = false, audioPath = audioPath, transfers = transcription)
                    } else {
                        handleTranscriptionIntent(audioPath, AudioToTextIntent.GetAITranscription)
                    }
                }
            }
            //真正开始录音转文的地方
            is AudioToTextIntent.GetAITranscription -> {
                if (isNetworkAvailable()) {
                    if (checkOtherFileInTranscription(audioPath)) {
                        Logger.d(TAG, "Other file is in transcription when get AI transcription")
                        ToastUtils.makeWithCancel(com.tcl.ai.note.base.R.string.toast_in_generating)
                    } else {
                        _audioToTextState.value = AudioToTextState(isLoading = true, audioPath = audioPath)
                        enqueueLatestMeetingMinutesWork(audioPath)
                    }
                } else {
                    ToastUtils.makeWithCancel(com.tcl.ai.note.base.R.string.network_error)
                    //_audioToTextState.value = AudioToTextState(audioPath = audioPath, errorState = ErrorState.NetworkError)
                }
            }
            is AudioToTextIntent.GetTranscriptionOnLanguageChanged -> {
                newIndex = intent.index
                repository.saveCurrentLanguage(newIndex)
                _languageConfigState.update {
                    it.copy(selectLanguageIndex = newIndex)
                }
                handleTranscriptionIntent(audioPath, AudioToTextIntent.GetAITranscription)
            }
            is AudioToTextIntent.DeleteAudioRecord -> {
                deleteAudioFile(audioPath)
            }
        }
    }

    /**
     * 检查音频时长是否有效
     */
    fun checkAudioTimeIsInValid(audioPath: String): Boolean {
        val duration = getAudioDuration(audioPath)
        return checkDurationIsValid(duration)
    }
    /**
     * 检查音频时长
     * 必须得实际时长为2分钟，不然Ability检查不足两分钟实际时长，会直接报错
     */
    private fun checkDurationIsValid(duration: Long): Boolean {

        return (duration ) < AudioToTextConstant.MIN_TRANSCRIPTION_DURATION || duration > AudioToTextConstant.MAX_TRANSCRIPTION_DURATION
    }

    /**
     * 设置音频转文本状态为音频时长错误
     */
    fun setAudioToTextStateAudioDurationError(audioPath: String) {
        _audioToTextState.value = AudioToTextState(audioPath = audioPath, errorState = ErrorState.AudioDurationError)
    }

    private fun checkOtherFileInTranscription(audioPath: String): Boolean {
        val currentTranscriptionFile = File(repository.getCurrentTranscriptionFile())
        val intervalTime = System.currentTimeMillis() - repository.getLastTranscriptionTime()
        val isOtherFileInTranscription = currentTranscriptionFile.exists()
                && currentTranscriptionFile.absolutePath != File(audioPath).absolutePath
                && abs(intervalTime) < AudioToTextConstant.MIN_TRANSCRIPTION_INTERVAL
        return isOtherFileInTranscription
    }

    private fun showTranscriptionScreen(audioPath: String) {
        context.startAudioToText(audioPath)
    }

    /**
     * 将最新的会议纪要工作入队到 WorkManager 中
     * @param audioPath 音频文件路径
     */
    private fun enqueueLatestMeetingMinutesWork(audioPath: String) {
        viewModelScope.launch {
            val constraints =
//            Constraints.Builder().setRequiredNetworkType(NetworkType.CONNECTED).build()
                Constraints.Builder().build() // 有网络下才会启动转写，转写任务很快，任务是在TCL AI 中处理，需要保持链接

            val data = Data.Builder().putString(AudioToTextConstant.EXTRA_AUDIO_RECORD, audioPath).build()
            workManager.beginUniqueWork(
                MeetingMinutesWorker.TAG,
                ExistingWorkPolicy.REPLACE,
                OneTimeWorkRequestBuilder<MeetingMinutesWorker>().addTag(audioPath)
                    .setInputData(data).setConstraints(constraints)
                    .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
                    .build()
            ).enqueue()

            // 设置转写正在进行的状态
            _isTranscriptionInProgress.value = true

            // 监听转写任务的状态
            workManager.getWorkInfosByTagLiveData(audioPath).observeForever { workInfoList ->
                if (workInfoList != null && workInfoList.isNotEmpty()) {
                    val workInfo = workInfoList[0]
                    when (workInfo.state) {
                        androidx.work.WorkInfo.State.SUCCEEDED,
                        androidx.work.WorkInfo.State.FAILED,
                        androidx.work.WorkInfo.State.CANCELLED -> {
                            // 当任务完成、失败或取消时，重置转写状态
                            _isTranscriptionInProgress.value = false
                            Logger.d(TAG, "Transcription task completed with state: ${workInfo.state}")
                        }
                        else -> {
                            // 其他状态表示任务正在进行
                            _isTranscriptionInProgress.value = true
                            Logger.d(TAG, "Transcription task in progress with state: ${workInfo.state}")
                        }
                    }
                }
            }
        }
    }

    fun resetAudioToTextState() {
        _audioToTextState.update { it.copy(errorState = null) }
        //MeetingMinutesWorker.resetGenerateResult()
    }

    /**
     * 更新播放状态
     */
    private fun updatePlayingState() {
        viewModelScope.launch {
            ShowAudioPlayer.playerState.collect { state ->
                _recordPlayingState.value = state
            }
        }
    }

    /**
     * 播放音频
     * @param audioPath 音频文件路径
     */
    fun playAudio(audioPath: String?) {
        ShowAudioPlayer.startPlay(audioPath)
    }

    fun seekTo(position: Int, audioPath: String) {
        ShowAudioPlayer.seekTo(position, audioPath)
    }

    /**
     * 暂停播放
     */
    fun pausePlay() {
        ShowAudioPlayer.pausePlay()
    }

    /**
     * 停止播放
     */
    fun stopPlay() {
        ShowAudioPlayer.stopPlay()
    }

    /**
     * 删除音频文件
     * @param audioPath 音频文件路径
     */
    fun deleteAudioFile(audioPath: String?) {
        viewModelScope.launchIO {
            Logger.d(TAG, "deleteAudioFile: $audioPath")
            deleteFile(audioPath)
            audioPath?.let {
                repository.deleteTransfersByAudioPath(audioPath)
            }
        }
    }

    /**
     * 重命名音频文件
     * @param oldAudioPath 旧音频文件路径
     * @param newAudioPath 新音频文件路径
     */
    fun renameAudioFile(oldAudioPath: String, newAudioPath: String) {
        viewModelScope.launchIO {
            if (renameFile(oldAudioPath, newAudioPath)) {
                repository.renameAudioFile(oldAudioPath, newAudioPath)
            }
        }
    }

    /**
     * 获取本地转录数据
     * @param audioPath 音频文件路径
     */
    suspend fun getLocalTranscription(audioPath: String): List<AudioTransferEntity> {
        return repository.getAudioTransfers(audioPath)
    }

    /**
     * 判断网络是否可用
     */
    private fun isNetworkAvailable(): Boolean {
        return !isOffline.value
    }

    /**
     * 监听网络状态变化
     * 当用户在录音转文本过程中断网时，显示网络错误提示
     */
    private fun monitorNetworkStatus() {
        viewModelScope.launch {
            // 使用变量跟踪上一次的网络状态，避免重复处理
            var lastOfflineState: Boolean? = null
            isOffline.collectLatest { offline ->
                // 只在状态变化或首次收集时处理
                if (lastOfflineState != offline) {
                    Logger.d(TAG, "Network status changed: offline = $offline")
                    lastOfflineState = offline

                    if (offline) {
                        // 使用更可靠的方式检测是否正在进行转文本操作
                        val isInProgress = _isTranscriptionInProgress.value || _audioToTextState.value.isLoading
                        if (isInProgress) {
                            Logger.d(TAG, "Network disconnected during audio to text conversion. isInProgress=$isInProgress, hasRunningTask=$")
                            // 更新状态为网络错误
                            _audioToTextState.update {
                                it.copy(
                                    isLoading = false,
                                    errorState = ErrorState.NetworkError
                                )
                            }
                            // 重置转写状态
                            _isTranscriptionInProgress.value = false
                            // 取消正在进行的转写任务
                        }
                    }
                }
            }
        }
    }

    private var _topAudioVisible = MutableStateFlow(false)
    val topAudioVisibleState = _topAudioVisible.asStateFlow()
    fun updateTopAudioBarState(visible: Boolean) {
        _topAudioVisible.value = visible
    }

    /**
     * 监听应用生命周期变化
     */
    private fun monitorAppLifecycle() {
        viewModelScope.launch {
            // 监听应用是否离开提示状态（多任务切换、回到桌面等）
            AppActivityManager.isLeaveHintStateFlow.collect { isLeaveHint ->
                if (isLeaveHint && _recordPlayingState.value.isPlaying) {
                    pausePlay()
                }
            }
        }

        viewModelScope.launch {
            // 监听应用前后台状态
            AppActivityManager.appInForegroundStateFlow.collect { isInForeground ->
                if (!isInForeground && _recordPlayingState.value.isPlaying) {
                    pausePlay()
                }
            }
        }
    }

    companion object {
        private const val TAG = "AudioToTextViewModel"
    }
}