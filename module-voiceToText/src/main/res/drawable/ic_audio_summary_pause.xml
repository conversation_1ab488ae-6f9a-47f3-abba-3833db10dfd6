<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="25dp"
    android:height="25dp"
    android:viewportWidth="25"
    android:viewportHeight="25">
  <path
      android:pathData="M8.926,6.93V18.93"
      android:strokeWidth="2"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="9.426"
          android:startY="6.93"
          android:endX="9.426"
          android:endY="18.93"
          android:type="linear">
        <item android:offset="0" android:color="#FF4968EE"/>
        <item android:offset="1" android:color="#FF7E65F7"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16.926,6.93V18.93"
      android:strokeWidth="2"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="17.426"
          android:startY="6.93"
          android:endX="17.426"
          android:endY="18.93"
          android:type="linear">
        <item android:offset="0" android:color="#FF4968EE"/>
        <item android:offset="1" android:color="#FF7E65F7"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
