<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="64dp"
    android:height="64dp"
    android:viewportWidth="64"
    android:viewportHeight="64">
    <group>
        <clip-path android:pathData="M0,32a32,32 0,1 0,64 0a32,32 0,1 0,-64 0z" />
        <path
            android:fillType="evenOdd"
            android:pathData="M28.308,22.442Q27.226,17.574 27.48,12.64Q27.486,12.53 27.497,12.421Q27.508,12.311 27.525,12.202Q27.541,12.094 27.563,11.986Q27.584,11.878 27.611,11.771Q27.638,11.665 27.671,11.56Q27.703,11.454 27.74,11.351Q27.777,11.248 27.82,11.146Q27.862,11.045 27.909,10.945Q27.957,10.846 28.009,10.749Q28.061,10.652 28.118,10.558Q28.174,10.464 28.236,10.373Q28.297,10.281 28.363,10.193Q28.429,10.105 28.498,10.02Q28.568,9.935 28.643,9.854Q28.717,9.773 28.795,9.695Q28.873,9.618 28.954,9.544Q29.036,9.47 29.121,9.401Q29.206,9.331 29.295,9.266Q29.383,9.2 29.475,9.14Q29.566,9.079 29.661,9.022Q29.755,8.966 29.852,8.915Q29.95,8.863 30.049,8.816Q30.149,8.769 30.25,8.728Q30.352,8.686 30.456,8.649Q30.559,8.612 30.665,8.58Q30.77,8.549 30.877,8.522Q30.984,8.496 31.091,8.475Q31.199,8.454 31.308,8.438Q31.417,8.422 31.526,8.411Q31.636,8.401 31.746,8.396Q31.856,8.391 31.966,8.391Q32.076,8.391 32.185,8.397Q32.295,8.402 32.405,8.413Q32.514,8.425 32.623,8.441Q32.732,8.457 32.839,8.479Q32.947,8.501 33.054,8.528Q33.16,8.555 33.266,8.587Q33.371,8.619 33.474,8.656Q33.578,8.694 33.679,8.736Q33.781,8.778 33.88,8.826Q33.979,8.873 34.076,8.925Q34.173,8.977 34.267,9.034Q34.361,9.091 34.453,9.152Q34.544,9.213 34.632,9.279Q34.72,9.345 34.805,9.415Q34.89,9.485 34.971,9.559Q35.053,9.633 35.13,9.711Q35.208,9.789 35.281,9.87Q35.355,9.952 35.425,10.037Q35.494,10.123 35.559,10.211Q35.625,10.3 35.686,10.391Q35.747,10.483 35.803,10.577Q35.859,10.672 35.911,10.769Q35.962,10.866 36.009,10.965Q36.056,11.065 36.098,11.167Q36.14,11.268 36.176,11.372Q36.213,11.476 36.245,11.581Q36.276,11.686 36.303,11.793Q36.329,11.9 36.351,12.008Q36.372,12.116 36.388,12.224Q36.403,12.333 36.414,12.443Q36.425,12.552 36.43,12.662Q36.435,12.772 36.434,12.882Q36.434,12.992 36.429,13.102Q36.237,16.819 37.055,20.497Q38.592,27.414 43.417,33.13Q46.33,36.581 50.29,39.378Q50.65,39.633 50.954,39.953Q51.258,40.273 51.493,40.646Q51.729,41.019 51.887,41.431Q52.046,41.843 52.12,42.278Q52.195,42.713 52.184,43.154Q52.172,43.595 52.075,44.025Q51.978,44.456 51.798,44.859Q51.619,45.262 51.364,45.622Q51.301,45.712 51.233,45.799Q51.165,45.885 51.094,45.969Q51.022,46.052 50.946,46.131Q50.87,46.211 50.79,46.287Q50.71,46.362 50.627,46.434Q50.543,46.506 50.457,46.573Q50.37,46.641 50.28,46.704Q50.19,46.767 50.097,46.826Q50.004,46.885 49.908,46.939Q49.812,46.993 49.714,47.042Q49.616,47.091 49.515,47.136Q49.415,47.18 49.312,47.22Q49.209,47.259 49.105,47.294Q49,47.328 48.894,47.357Q48.788,47.386 48.681,47.41Q48.574,47.434 48.465,47.453Q48.357,47.471 48.248,47.485Q48.138,47.498 48.029,47.506Q47.919,47.514 47.809,47.517Q47.699,47.519 47.589,47.516Q47.479,47.513 47.37,47.505Q47.26,47.497 47.151,47.483Q47.042,47.47 46.933,47.451Q46.825,47.432 46.718,47.407Q46.61,47.383 46.505,47.354Q46.399,47.324 46.294,47.29Q46.19,47.255 46.087,47.215Q45.985,47.176 45.884,47.131Q45.784,47.086 45.686,47.037Q45.587,46.987 45.492,46.933Q45.396,46.878 45.303,46.819Q45.21,46.76 45.121,46.697Q40.223,43.238 36.57,38.909Q30.324,31.509 28.308,22.442Z">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:endX="53.984"
                    android:endY="59.285"
                    android:startX="23.311"
                    android:startY="8.391"
                    android:type="linear">
                    <item
                        android:color="#FFD5B9FF"
                        android:offset="0" />
                    <item
                        android:color="#FF7166FB"
                        android:offset="0.59" />
                    <item
                        android:color="#FF0C1BEC"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
        <path
            android:fillAlpha="0.74795675"
            android:fillType="evenOdd"
            android:pathData="M20.43,44.116Q18.488,45.838 16.762,46.796Q16.666,46.85 16.567,46.899Q16.468,46.947 16.367,46.991Q16.266,47.035 16.163,47.073Q16.061,47.112 15.956,47.146Q15.851,47.179 15.745,47.208Q15.639,47.236 15.531,47.259Q15.424,47.282 15.315,47.3Q15.207,47.318 15.097,47.331Q14.988,47.343 14.878,47.35Q14.769,47.358 14.659,47.359Q14.549,47.361 14.439,47.357Q14.329,47.354 14.219,47.345Q14.11,47.336 14.001,47.321Q13.892,47.307 13.783,47.287Q13.675,47.268 13.568,47.243Q13.461,47.218 13.355,47.187Q13.249,47.157 13.145,47.122Q13.041,47.086 12.939,47.046Q12.837,47.005 12.736,46.96Q12.636,46.915 12.539,46.864Q12.441,46.814 12.345,46.759Q12.25,46.704 12.158,46.644Q12.065,46.585 11.976,46.521Q11.887,46.457 11.801,46.388Q11.714,46.32 11.632,46.247Q11.549,46.175 11.47,46.098Q11.391,46.022 11.316,45.941Q11.241,45.861 11.17,45.777Q11.099,45.693 11.032,45.606Q10.965,45.519 10.902,45.428Q10.84,45.338 10.782,45.244Q10.723,45.151 10.67,45.055Q10.617,44.959 10.568,44.86Q10.519,44.761 10.476,44.661Q10.432,44.56 10.393,44.457Q10.355,44.354 10.321,44.249Q10.287,44.144 10.259,44.038Q10.231,43.932 10.207,43.824Q10.184,43.717 10.166,43.608Q10.148,43.5 10.136,43.391Q10.123,43.281 10.116,43.172Q10.109,43.062 10.107,42.952Q10.105,42.842 10.109,42.732Q10.113,42.622 10.122,42.512Q10.131,42.403 10.145,42.294Q10.16,42.185 10.179,42.077Q10.199,41.968 10.224,41.861Q10.249,41.754 10.279,41.648Q10.309,41.543 10.345,41.439Q10.38,41.334 10.421,41.232Q10.461,41.13 10.507,41.03Q10.552,40.93 10.602,40.832Q10.653,40.734 10.708,40.639Q10.763,40.543 10.822,40.451Q10.882,40.359 10.946,40.269Q11.01,40.18 11.078,40.094Q11.147,40.008 11.219,39.925Q11.292,39.842 11.368,39.763Q11.445,39.684 11.525,39.609Q11.605,39.534 11.689,39.463Q11.773,39.392 11.861,39.325Q11.948,39.258 12.038,39.195Q12.129,39.133 12.222,39.075Q12.316,39.017 12.412,38.963Q13.806,38.189 17.103,34.887Q17.846,34.143 18.225,33.77Q23.301,28.776 25.664,21.645Q27.129,17.226 27.394,12.547Q27.419,12.107 27.529,11.679Q27.639,11.252 27.831,10.855Q28.023,10.457 28.288,10.105Q28.554,9.752 28.883,9.458Q29.212,9.164 29.592,8.94Q29.972,8.716 30.389,8.571Q30.805,8.425 31.242,8.364Q31.679,8.302 32.119,8.327Q32.56,8.352 32.987,8.462Q33.414,8.573 33.812,8.764Q34.209,8.956 34.562,9.221Q34.914,9.487 35.208,9.816Q35.502,10.145 35.726,10.525Q35.95,10.905 36.096,11.322Q36.241,11.738 36.303,12.175Q36.364,12.612 36.34,13.053Q36.008,18.917 34.169,24.465Q31.126,33.646 24.509,40.156Q24.158,40.502 23.443,41.218Q21.477,43.188 20.43,44.116Z"
            android:strokeAlpha="0.74795675">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:endX="40.446"
                    android:endY="47.361"
                    android:startX="7.114"
                    android:startY="8.302"
                    android:type="linear">
                    <item
                        android:color="#FF28AAFF"
                        android:offset="0" />
                    <item
                        android:color="#FF9E8EFF"
                        android:offset="0.448" />
                    <item
                        android:color="#FFA273FF"
                        android:offset="0.69" />
                    <item
                        android:color="#FF6C14FF"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
        <path
            android:fillType="evenOdd"
            android:pathData="M42.573,36.848Q47.347,38.335 51.565,40.874Q51.943,41.101 52.269,41.398Q52.596,41.695 52.858,42.05Q53.12,42.405 53.308,42.804Q53.496,43.204 53.602,43.632Q53.709,44.06 53.729,44.501Q53.75,44.941 53.685,45.378Q53.619,45.814 53.47,46.229Q53.32,46.644 53.093,47.022Q53.036,47.117 52.975,47.208Q52.913,47.299 52.848,47.388Q52.782,47.476 52.712,47.561Q52.642,47.646 52.568,47.727Q52.494,47.808 52.416,47.886Q52.338,47.963 52.257,48.037Q52.175,48.111 52.09,48.181Q52.005,48.25 51.916,48.315Q51.828,48.381 51.736,48.442Q51.645,48.503 51.55,48.559Q51.456,48.615 51.359,48.667Q51.262,48.719 51.162,48.766Q51.063,48.812 50.961,48.854Q50.859,48.896 50.756,48.933Q50.652,48.97 50.547,49.002Q50.441,49.033 50.335,49.06Q50.228,49.086 50.12,49.108Q50.012,49.129 49.903,49.145Q49.795,49.161 49.685,49.171Q49.576,49.182 49.466,49.187Q49.356,49.192 49.246,49.192Q49.136,49.192 49.026,49.186Q48.916,49.181 48.807,49.17Q48.697,49.159 48.589,49.142Q48.48,49.126 48.372,49.104Q48.264,49.083 48.158,49.056Q48.051,49.029 47.946,48.997Q47.841,48.965 47.737,48.927Q47.634,48.89 47.532,48.848Q47.431,48.805 47.331,48.758Q47.232,48.711 47.135,48.659Q47.038,48.607 46.944,48.55Q43.652,46.568 39.909,45.403Q32.7,43.158 25.503,44.33Q21.521,44.979 16.575,47.061Q16.404,47.133 16.321,47.168Q16.219,47.21 16.116,47.248Q16.013,47.285 15.908,47.318Q15.802,47.35 15.696,47.377Q15.589,47.405 15.482,47.427Q15.374,47.448 15.265,47.465Q15.156,47.482 15.047,47.493Q14.938,47.504 14.828,47.51Q14.718,47.516 14.608,47.517Q14.498,47.517 14.388,47.512Q14.278,47.508 14.169,47.497Q14.059,47.487 13.95,47.471Q13.842,47.456 13.734,47.435Q13.626,47.414 13.519,47.388Q13.412,47.361 13.307,47.33Q13.201,47.299 13.097,47.262Q12.994,47.226 12.892,47.184Q12.79,47.142 12.691,47.096Q12.591,47.049 12.494,46.998Q12.396,46.946 12.302,46.89Q12.207,46.834 12.115,46.774Q12.024,46.713 11.935,46.648Q11.846,46.583 11.761,46.513Q11.676,46.444 11.594,46.371Q11.512,46.297 11.434,46.22Q11.356,46.142 11.282,46.061Q11.207,45.98 11.137,45.895Q11.067,45.811 11.001,45.723Q10.935,45.635 10.874,45.544Q10.812,45.452 10.755,45.358Q10.698,45.264 10.646,45.167Q10.593,45.071 10.546,44.972Q10.498,44.872 10.456,44.771Q10.285,44.364 10.197,43.932Q10.109,43.499 10.107,43.058Q10.105,42.617 10.189,42.184Q10.273,41.751 10.44,41.342Q10.607,40.934 10.85,40.566Q11.093,40.198 11.404,39.884Q11.714,39.571 12.08,39.324Q12.446,39.077 12.853,38.906Q12.931,38.873 13.099,38.802Q19.028,36.307 24.063,35.487Q33.345,33.975 42.573,36.848Z">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:endX="56.577"
                    android:endY="50.634"
                    android:startX="5.187"
                    android:startY="31.546"
                    android:type="linear">
                    <item
                        android:color="#3EFFFFFF"
                        android:offset="0" />
                    <item
                        android:color="#FF0092FF"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
</vector>
