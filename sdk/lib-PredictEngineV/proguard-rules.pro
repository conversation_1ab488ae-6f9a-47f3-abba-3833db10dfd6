# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with J<PERSON>, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
#-------------------------------------------基本不用动区域--------------------------------------------
#---------------------------------基本指令区----------------------------------
# 代码混淆压缩比，在0~7之间，默认为5,一般不下需要修改
#指定代码的压缩级别
-optimizationpasses 5
#包明不混合大小写
-dontusemixedcaseclassnames
#不去忽略非公共的库类
-dontskipnonpubliclibraryclasses
#预校验
-dontpreverify
#混淆时是否记录日志
-verbose
 #优化  不优化输入的类文件
-dontoptimize
 #混淆时所采用的算法
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*
#保护注解
-keepattributes *Annotation*
#忽略警告
-ignorewarnings
#记录生成的日志数据,gradle build时在本项目根目录输出
#apk 包内所有 class 的内部结构
-dump SDK/proguard/class_files.txt
#未混淆的类和成员
-printseeds SDK/proguard/seeds.txt
#列出从 apk 中删除的代码
-printusage SDK/proguard/unused.txt
#混淆前后的映射
-printmapping SDK/proguard/mapping.txt
-keepattributes InnerClasses
-dontoptimize
-keepattributes Signature
-dontwarn android.**
-dontwarn android.util.Xml.**
-dontwarn org.**
-dontoptimize
# 抛出异常时保留代码行号
-keepattributes SourceFile,LineNumberTable
#End of configure
######################################################################
#Start of Android

#不混淆R资源类
-keepclassmembers class **.R$* {
    public static <fields>;
}

# 保留枚举类不被混淆
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
#保持 Parcelable 不被混淆
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}
#保持 Serializable 不被混淆
-keepnames class * implements java.io.Serializable
#保持 Serializable 不被混淆并且enum 类也不被混淆
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    !static !transient <fields>;
    !private <fields>;
    !private <methods>;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

##kotlin begin
-keep class kotlin.** { *; }
-keep class kotlinx.coroutines.** { *; }
-keep class kotlin.Metadata { *; }
-keep class kotlin.jvm.internal.Lambda {  }
-dontwarn kotlin.**
-keepclassmembers class **$WhenMappings {
    <fields>;
}
-keepclassmembers class kotlin.Metadata {
    public <methods>;
}
-assumenosideeffects class kotlin.jvm.internal.Intrinsics {
    static void checkParameterIsNotNull(java.lang.Object, java.lang.String);
}
##kotlin end

#保持 native 方法不被混淆
-keepclasseswithmembernames class * {
    native <methods>;
}

-keep class com.jideos.predengine.JidePredManager{
    <init>();
    public *;
}
-keepclassmembers class com.jideos.predengine.JidePredManager {
     public *;
}

#-keep class com.jideos.predengine.*{*;}

-keep class com.jideos.predengine.bean.PointBean{*;}
-keep class com.jideos.predengine.bean.EstimateInfoBean{*;}
-keep class com.jideos.predengine.bean.EstimateBean{*;}
-keep class com.jideos.predengine.bean.EventBean{*;}
-keep class com.jideos.predengine.bean.JniBeanUtil{*;}
-keep class com.jideos.predengine.bean.Constants{*;}
