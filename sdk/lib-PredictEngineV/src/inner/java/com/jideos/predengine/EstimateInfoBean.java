package com.jideos.predengine;

import java.util.ArrayList;

public class EstimateInfoBean {
    private int index;
    private EventBean realPoint;
    private ArrayList<EstimateBean> estimateList;
    private int failedType; // 当前点预测结果类型：1:成功、2:失败、3:不预测，即不满足预测条件
    private float pressure;
    private long time;

    public EstimateInfoBean(int index, EventBean realPoint, ArrayList<EstimateBean> estimateList, int failedType) {
        this.index = index;
        this.realPoint = realPoint;
        this.estimateList = estimateList;
        this.failedType = failedType;
    }

    public EstimateInfoBean(int index, EventBean realPoint, ArrayList<EstimateBean> estimateList, int failedType, float pressure, long time) {
        this.index = index;
        this.realPoint = realPoint;
        this.estimateList = estimateList;
        this.failedType = failedType;
        this.pressure = pressure;
        this.time = time;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public EventBean getRealPoint() {
        return realPoint;
    }

    public void setRealPoint(EventBean realPoint) {
        this.realPoint = realPoint;
    }

    public ArrayList<EstimateBean> getEstimateList() {
        return estimateList;
    }

    public void setEstimateList(ArrayList<EstimateBean> estimateList) {
        this.estimateList = estimateList;
    }

    public int getFailedType() {
        return failedType;
    }

    public void setFailedType(int failedType) {
        this.failedType = failedType;
    }

    public float getPressure() {
        return pressure;
    }

    public void setPressure(float pressure) {
        this.pressure = pressure;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }
}
