package com.jideos.predengine;

import java.util.List;

public class EventBean {
    private float x;
    private float y;
    private int action;
    private float pressure;
    private long eventTime;
    private int type;
    private boolean isLast;
    private List<EventBean> history;
    private float lastDis = -1f;
    public EventBean(float x, float y, int action, long eventTime) {
        this.x = x;
        this.y = y;
        this.action = action;
        this.eventTime = eventTime;
    }

    public EventBean(float x, float y, int action, long eventTime, List<EventBean> history) {
        this.x = x;
        this.y = y;
        this.action = action;
        this.eventTime = eventTime;
        this.history = history;
    }

    public EventBean(float x, float y, int action, float pressure, long eventTime) {
        this.x = x;
        this.y = y;
        this.action = action;
        this.pressure = pressure;
        this.eventTime = eventTime;
    }

    public EventBean(float x, float y, int action, float pressure, long eventTime, int type) {
       this(x,y,action,pressure,eventTime,type,false);
    }

    public EventBean(float x, float y, int action, float pressure, long eventTime, int type,boolean isLast) {
        this.x = x;
        this.y = y;
        this.action = action;
        this.pressure = pressure;
        this.eventTime = eventTime;
        this.type = type;
        this.isLast = isLast;
    }

    public float getX() {
        return x;
    }

    public void setX(float x) {
        this.x = x;
    }

    public float getY() {
        return y;
    }

    public void setY(float y) {
        this.y = y;
    }

    public int getAction() {
        return action;
    }

    public void setAction(int action) {
        this.action = action;
    }

    public float getPressure() {
        return pressure;
    }

    public void setPressure(float pressure) {
        this.pressure = pressure;
    }

    public long getEventTime() {
        return eventTime;
    }

    public void setEventTime(long eventTime) {
        this.eventTime = eventTime;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public boolean isLast() {
        return isLast;
    }

    public void setLast(boolean last) {
        isLast = last;
    }

    public List<EventBean> getHistory() {
        return history;
    }

    public void setHistory(List<EventBean> history) {
        this.history = history;
    }

    public float getLastDis() {
        return lastDis;
    }

    public void setLastDis(float lastDis) {
        this.lastDis = lastDis;
    }

    @Override
    public String toString() {
        return "EventBean{" +
                "x=" + x +
                ", y=" + y +
                ", action=" + action +
                ", pressure=" + pressure +
                ", eventTime=" + eventTime +
                ", type=" + type +
                ", isLast=" + isLast +
                ", history=" + history +
                ", lastDis=" + lastDis +
                '}';
    }
}
