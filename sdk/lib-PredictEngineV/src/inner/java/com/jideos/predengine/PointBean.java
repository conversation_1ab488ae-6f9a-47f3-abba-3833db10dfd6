package com.jideos.predengine;

public class PointBean {
    private float x;
    private float y;
    private float pressure;
    private long eventTime;

    public PointBean() {}

    public PointBean(float x, float y) {
        this.x = x;
        this.y = y;
    }

    public PointBean(float x, float y, float pressure, long eventTime) {
        this.x = x;
        this.y = y;
        this.pressure = pressure;
        this.eventTime = eventTime;
    }

    public float getX() {
        return x;
    }

    public void setX(float x) {
        this.x = x;
    }

    public float getY() {
        return y;
    }

    public void setY(float y) {
        this.y = y;
    }

    public float getPressure() {
        return pressure;
    }

    public void setPressure(float pressure) {
        this.pressure = pressure;
    }

    public long getEventTime() {
        return eventTime;
    }

    public void setEventTime(long eventTime) {
        this.eventTime = eventTime;
    }
}
