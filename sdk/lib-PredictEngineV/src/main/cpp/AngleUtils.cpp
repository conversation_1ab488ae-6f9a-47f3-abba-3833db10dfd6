//
// Created by <PERSON><PERSON> on 2023/11/20.
//

#include <vector>

#include "AngleUtils.h"
#include "EventBean.h"
#include "LogUtil.h"
#include "AngleFeatureBase.h"

int gAngleRealPointSize = 24;
AngleFeatureBase angleFeatureBase;

extern vector<EventBean> recordList;
extern int gLevel;
extern float preTotalTime;
extern float pointTimeInternal;

float AngleUtils::convertHalfToAllAngle(float angle) {
    angle = fmod(angle, 360);
    if (angle < 0) {
        angle += 360;
    }
    return angle;
}

float AngleUtils::convertAllToHalfAngle(float angle) {
    angle = fmod(angle, 360);
    float absAngle = abs(angle);
    if (absAngle < 180) {
        return angle;
    }
    return (360 - absAngle) * (-absAngle / angle);
}

float AngleUtils::getAngleDiff(float angle0, float angle1) {
    float r = angle1 - angle0;
    if (abs(r) > 180) {
        return convertAllToHalfAngle(r);
    }
    return r;
}

static void calcRealAngle(int size) {
    EventBean &curEb = recordList.at(size - 1);
    int pointSize = gAngleRealPointSize;
    if (pointSize > size) {
        pointSize = size;
    }
    curEb.realAnglePointSize = pointSize;
    EventBean firstEb = recordList.at(size - pointSize);
    float realAngle = curEb.getAngle(firstEb);
    curEb.realAngle = realAngle;

    if (size >= 4) {
        EventBean fourEb = recordList.at(size - 4);
        float realAngle4 = curEb.getAngle(fourEb);
        curEb.realAngle4 = realAngle4;
    } else {
        curEb.realAngle4 = realAngle;
    }
    float realAngleTimeInternal = (float) (curEb.time - firstEb.time) / 1000;
    curEb.realAngleTime = realAngleTimeInternal;
    float diffAngle = curEb.realAngle4 - realAngle;
    if (abs(diffAngle) > 180) {
        diffAngle = AngleUtils::convertAllToHalfAngle(diffAngle);
    }
    curEb.realDiffAngle = diffAngle;
}

static float getRegularValidPreTime() {
    if (gLevel == 1) {
        return 0.0004;
    }
    if (gLevel == 2) {
        return 0.008;
    }
    if (gLevel == 3) {
        return 0.016;
    }
    return 0.008;
}

static void calcInvalidAngle(EventBean &eb, int pointSize) {
    LogUtil::logV("doPredict 符合无规律");
    float angle = AngleUtils::convertHalfToAllAngle(eb.realAngle);
    eb.preAngle = angle;
    eb.lastAngle = angle;
    eb.preAnglePointSize = pointSize;
    eb.angleChangeSum = angleFeatureBase.angleChangeSum;
    eb.preAngleType = 4;
    eb.preAngleA = 0;
    eb.angleValidPreTime = 0;
}

static float getFitPreAngleLimit(float realListSpeed) {
    if (0 <= realListSpeed && realListSpeed < 30) {
        return 5;
    }
    if (realListSpeed < 60) {
        return 3;
    }
    if (realListSpeed < 100) {
        return 2.2;
    }
    return 0.5;
}

static void calcFitAngle(EventBean &eb, int pointSize, float a, float b, float bzcOne, float validPreTime, bool hasLowAngleChangeFeature) {
    float limit = getFitPreAngleLimit(eb.preSpeed);
    if (bzcOne < limit) {
        LogUtil::logV("doPredict 符合一次拟合");
        float preStartTime = pointSize * pointTimeInternal;
        float x = preStartTime + validPreTime;
        float preAngle = AngleUtils::convertAllToHalfAngle(a * x + b);
        eb.preAngle = preAngle;
        eb.lastAngle = AngleUtils::convertHalfToAllAngle(eb.realAngle);
        eb.preAnglePointSize = pointSize;
        eb.hasLowAngleChangeFeature = hasLowAngleChangeFeature;
        eb.angleChangeSum = angleFeatureBase.angleChangeSum;
        eb.preAngleType = 5;
        eb.preAngleA = a;
        eb.angleValidPreTime = validPreTime;
        return;
    }
    calcInvalidAngle(eb, pointSize);
}

static int getPrePointSize(float realListSpeed) {
    if (0 <= realListSpeed && realListSpeed < 30) {
        return 16;
    }
    if (realListSpeed < 60) {
        return 14;
    }
    if (realListSpeed < 80) {
        return 12;
    }
    return 10;
}

static float getAvgPreAngleLimit(float realListSpeed) {
    if (0 <= realListSpeed && realListSpeed < 30) {
        return 2;
    }
    if (realListSpeed < 60) {
        return 1.5;
    }
    if (realListSpeed < 100) {
        return 1;
    }
    return 1.5;
}

/**
 * 此处目的只是为了在包含330 350 359 0 10 20 30 等跨过360°角度的数据集中计算均值使用正值，防止出现计算问题
 * @param halfAngle
 * @param has330
 * @param has30
 * @return
 */
static float getRealAngle(float halfAngle,bool has330,bool has30){
    float realAngleAll = AngleUtils::convertHalfToAllAngle(halfAngle);
    if(has330 && has30 && realAngleAll >= 0 && realAngleAll <= 30){
        realAngleAll += 360;
    }
    return realAngleAll;
}

static void calcAngleFeature(int size, int pointSize) {
    float maxAngle = FLT_MIN;
    float minAngle = FLT_MAX;
    float maxAngle4 = FLT_MIN;
    float minAngle4 = FLT_MAX;
    float maxAngleDiff = FLT_MIN;
    float minAngleDiff = FLT_MAX;
    float angleChangeSum = 0;
    float angle4ChangeSum = 0;
    float tempAngle = 0;
    float tempAngle4 = 0;
    for (int i = size - pointSize; i <= size - 1; ++i) {
        EventBean eb = recordList.at(i);
        tempAngle = AngleUtils::convertHalfToAllAngle(eb.realAngle);
        tempAngle4 = AngleUtils::convertHalfToAllAngle(eb.realAngle4);
        if (i == size - pointSize) {
            angleChangeSum = tempAngle;
            angle4ChangeSum = tempAngle4;
        }
        if (i == size - 1) {
            angleChangeSum = tempAngle - angleChangeSum;
            angle4ChangeSum = tempAngle4 - angle4ChangeSum;
        }
        maxAngle = max(maxAngle, tempAngle);
        minAngle = min(minAngle, tempAngle);
        maxAngle4 = max(maxAngle4, tempAngle4);
        minAngle4 = min(minAngle4, tempAngle4);
        maxAngleDiff = max(maxAngleDiff, abs(eb.realDiffAngle));
        minAngleDiff = min(minAngleDiff, abs(eb.realDiffAngle));
    }
    angleFeatureBase.jzDiff = maxAngle - minAngle;
    angleFeatureBase.jzMax = maxAngle;
    angleFeatureBase.jzMin = minAngle;
    angleFeatureBase.angleChangeSum = angleChangeSum;
    angleFeatureBase.jzDiff4 = maxAngle4 - minAngle4;
    angleFeatureBase.jzMax4 = maxAngle4;
    angleFeatureBase.jzMin4 = minAngle4;
    angleFeatureBase.angle4ChangeSum = angle4ChangeSum;
    angleFeatureBase.jzMaxDiff = maxAngleDiff;
    angleFeatureBase.jzMinDiff = minAngleDiff;
}

static void calcPreAngle(int size) {
    EventBean &curEb = recordList.at(size - 1);
    int pointSize = getPrePointSize(curEb.lastSpeed);
    if (pointSize > size) {
        pointSize = size;
    }
    calcAngleFeature(size, pointSize);
    float limit = getAvgPreAngleLimit(curEb.preSpeed);
    //判定是否介于330-30度之间的衔接处
    bool has330 = false;
    bool has30 = false;
    for (int i = size - 1; i >= size - pointSize; --i) {
        float temp = AngleUtils::convertHalfToAllAngle(recordList.at(i).realAngle);
        if (temp >= 330) {
            has330 = true;
        }
        if(temp <= 30){
            has30 = true;
        }
    }
    // 平均值
    float sumRealAngle = 0;
    for (int i = size - 1; i >= size - pointSize; --i) {
        float realAngleAll = getRealAngle(recordList.at(i).realAngle,has330,has30);
        sumRealAngle += realAngleAll;
    }
    float avg = sumRealAngle / (float) pointSize;
    angleFeatureBase.avg = avg;
    float sumFcSpeed = 0;
    // 最大极值
    float maxDiff = 0;
    for (int i = size - 1; i >= size - pointSize; --i) {
        float realAngleAll = getRealAngle(recordList.at(i).realAngle,has330,has30);
        float temp = realAngleAll - avg;
        sumFcSpeed += pow(temp, 2);
        if (abs(temp) > abs(maxDiff)) {
            maxDiff = temp;
        }
    }
    // 标准差`
    float bzc = sqrt(sumFcSpeed / (float) pointSize);
    if (abs(angleFeatureBase.jzMaxDiff) > 60) {
        LogUtil::logV("doPredict 角度急转");
        curEb.preAngle = curEb.realAngle;
        curEb.preAnglePointSize = pointSize;
        curEb.angleChangeSum = angleFeatureBase.angleChangeSum;
        curEb.preAngleType = 6;
        curEb.preAngleA = 0;
        curEb.angleValidPreTime = 0;
        return;
    }

    // 先做一次拟合，需要和匀速比较看哪个效果好
    float sumX = 0;
    float sumX2 = 0;
    float sumXY = 0;
    float sumY = 0;
    float tempX = 0;
    for (int i = size - pointSize; i <= size - 1; ++i) {
//        float realAngle = AngleUtils::convertHalfToAllAngle(recordList.at(i).realAngle);
        float realAngle = getRealAngle(recordList.at(i).realAngle,has330,has30);
        tempX = pointTimeInternal * (i - size + pointSize + 1);
        sumX += tempX;
        sumX2 += tempX * tempX;
        sumXY += tempX * realAngle;
        sumY += realAngle;
    }
    float a = (pointSize * sumXY - sumX * sumY) / (pointSize * sumX2 - sumX * sumX);
    float b = (sumY - a * sumX) / pointSize;
    LogUtil::logV("doPredict 一次拟合 a:%f; b:%f;", a, b);
    float sumPointToLine = 0;
    float tempY = 0;
    vector<float> points;
    for (int i = size - pointSize; i <= size - 1; ++i) {
//        float realAngle = AngleUtils::convertHalfToAllAngle(recordList.at(i).realAngle);
        float realAngle = getRealAngle(recordList.at(i).realAngle,has330,has30);
        tempX = pointTimeInternal * (i - size + pointSize + 1);
        tempY = a * tempX + b;
        points.push_back(tempY - realAngle);
        sumPointToLine += tempY - realAngle;
    }
    float avgPointToLine = sumPointToLine / (float )pointSize;
    float fcSum = 0;
    for (int i = 0; i < pointSize; ++i) {
        fcSum += pow(points.at(i) - avgPointToLine, 2);
    }
    float bzcOne = sqrt(fcSum / (float) pointSize);
    float validPreTime = preTotalTime;
    if (bzcOne >= bzc && bzc <= limit) {
        LogUtil::logV("doPredict 符合匀速");
        float halfAvg = AngleUtils::convertAllToHalfAngle(avg);
        curEb.preAngle = halfAvg;
        curEb.lastAngle = halfAvg;
        curEb.preAnglePointSize = pointSize;
        curEb.angleChangeSum = angleFeatureBase.angleChangeSum;
        curEb.hasLowAngleChangeFeature = true;
        curEb.preAngleType = 1;
        curEb.preAngleA = 0;
        curEb.angleValidPreTime = validPreTime;
        return;
    }
    bool hasLowAngleChangeFeature = bzc < limit;
    calcFitAngle(curEb, pointSize, a, b, bzcOne, validPreTime, hasLowAngleChangeFeature);
}

void AngleUtils::handAngle(bool isNeedPre) {
    int size = (int) recordList.size();
    if (size <= 1) {
        return;
    }
    calcRealAngle(size);
    if (isNeedPre) {
        calcPreAngle(size);
    }
}




