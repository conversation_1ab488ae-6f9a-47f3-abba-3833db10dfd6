//
// Created by Admin on 2023/10/25.
//

#include <cmath>

#include "EventBean.h"

extern float gPxInOneMm;
extern float gPyInOneMm;

EventBean::EventBean(){}

EventBean::EventBean(float x, float y, float p, long long time, bool isLast)
        : x(x), y(y), p(p), time(time), isLast(isLast), lastDis(0), realListSpeed(0), realSingleSpeed(0) {
}

float EventBean::getPhysicalDistance(EventBean curP) const {
    return (float) sqrt(pow((x - curP.x) / gPxInOneMm, 2) + pow((y - curP.y) / gPyInOneMm, 2));
}

float EventBean::getAngle(EventBean curP) const {
    float dy = y - curP.y;
    float dx = x - curP.x;
    float angleRadius = atan2(dy, dx);
    float angle = angleRadius / M_PI * 180;
    return angle;
}




