//
// Created by Admin on 2023/10/27.
//
#include <ctime>
#include <string>
#include <jni.h>

#include "FilterEngine.h"
#include "LogUtil.h"

using namespace std;

bool FilterEngine::isSelfPkg(JNIEnv *env, jobject context) {
    jclass context_clazz = env->GetObjectClass(context);
    jmethodID getPackageName = env->GetMethodID(context_clazz, "getPackageName", "()Ljava/lang/String;");
    auto packageName = (jstring) env->CallObjectMethod(context, getPackageName);
    const char *curPkg = env->GetStringUTFChars(packageName, nullptr);
    const char *selfPkg = "com.jideos.jnotesss";
    const char *innerSelfPkg = "com.jideos.jnotesss.inner";
    const char *jnotesPkg = "com.jideos.jnotes.overseas.google";
    return strcmp(curPkg, selfPkg) == 0 || strcmp(curPkg, innerSelfPkg) == 0 || strcmp(curPkg, jnotesPkg) == 0 ;
}

bool FilterEngine::isExpired() {
    time_t curTime = time(nullptr);
    tm *time = localtime(&curTime);
    if (time->tm_year > 2024 - 1900) {
        return true;
    }
    if (time->tm_mon >= 6 && time->tm_mday > 30) {
        return true;
    }
    return false;
}

bool FilterEngine::isFastLimit(bool isOpenFastPred, float curSpeed, int maxSpeed) {
    if (isOpenFastPred) {
        return false;
    }
    if (curSpeed > (float) maxSpeed) {
        return true;
    }
    return false;
}

