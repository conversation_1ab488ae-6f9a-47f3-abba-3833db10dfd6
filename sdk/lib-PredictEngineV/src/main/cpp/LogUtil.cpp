//
// Created by <PERSON><PERSON> on 2023/10/27.
//
#include <jni.h>
#include <android/log.h>
#include <stdarg.h>
#include <stdlib.h>

#include "LogUtil.h"

#define TAG    "Note_Prediction"
extern bool gIsInner;
// see [android.util.Log.java] level
extern int gLogLevel;

void LogUtil::logV(const char *fmt, ...) {
    if (ANDROID_LOG_VERBOSE >= gLogLevel) {
        va_list vaList;
        va_start(vaList, fmt);
        __android_log_vprint(ANDROID_LOG_VERBOSE, TAG, fmt, vaList);
        va_end(vaList);
    }
}

void LogUtil::logD(const char *fmt, ...) {
    if (ANDROID_LOG_DEBUG >= gLogLevel) {
        va_list vaList;
        va_start(vaList, fmt);
        __android_log_vprint(ANDROID_LOG_DEBUG, TAG, fmt, vaList);
        va_end(vaList);
    }
}

void LogUtil::logI(const char *fmt, ...) {
    if (ANDROID_LOG_INFO >= gLogLevel) {
        va_list vaList;
        va_start(vaList, fmt);
        __android_log_vprint(ANDROID_LOG_INFO, TAG, fmt, vaList);
        va_end(vaList);
    }
}

