//
// Created by 芳芳 on 2024/1/24.
//
#include <vector>
#include "include/EventBean.h"
#include "include/LogUtil.h"
#include "include/PrePointUtils.h"
#include "include/SpeedUtils.h"
#include "include/AngleUtils.h"
#include <jni.h>
#include <cmath>

extern vector<EventBean> recordList;
extern vector<EventBean> prePointsInner;
extern int gLevel;
extern float pointTimeInternal;
extern float gPxInOneMm;
extern float gPyInOneMm;
extern float preTotalTime;
extern float preTotalTimeMin;

float lastValidTime = 0;

class TempEbWrapper {
public:
    EventBean eb;   // eb
    float maxRealDiff;  // 最大的四角差
    // 成员函数声明
    TempEbWrapper(EventBean eb, float realDiff);
};

TempEbWrapper::TempEbWrapper(EventBean eb, float realDiff) : eb(eb), maxRealDiff(realDiff) {
}

PointF::PointF(float x, float y) : x(x), y(y) {
}

static EventBean getLastRealPoint(EventBean eb) {
    int size = (int) recordList.size();
    for (int i = size - 2; i >= 0; --i) {
        if (recordList.at(i).isLast) {
            return recordList.at(i);
        }
    }
    return eb;
}

static float justValidPreTime1(EventBean &eb, float validPreTime) {
    float angle4Diff = eb.realDiffAngle;
    float anglePreDiff = AngleUtils::getAngleDiff(eb.realAngle, eb.preAngle);
    float absAngle4PreDiff = abs(AngleUtils::getAngleDiff(eb.realAngle4, eb.preAngle));
    float avgSpeed = (2 * eb.lastSpeed + eb.speedValidPreTime * eb.preSpeedA) / 2;
    //角度方向
    float angleOp = angle4Diff * anglePreDiff;
    bool isReverseAngle;
    if (gLevel == 1) {
        isReverseAngle = angleOp < 0 && ((absAngle4PreDiff > 12 && avgSpeed > 40) || (absAngle4PreDiff > 20 && avgSpeed > 30));
    } else {
        isReverseAngle = angleOp < 0 && eb.preAngleType != 1 && !eb.hasLowAngleChangeFeature &&
                         ((absAngle4PreDiff > 6 && avgSpeed > 40) || (absAngle4PreDiff > 10 && avgSpeed > 30));
    }
    if (isReverseAngle) {
        eb.timeWeakenType = 1;
        return 0;
    }
    EventBean beforeEb = getLastRealPoint(eb);
    if (beforeEb.timeWeakenType == 1) {
        eb.timeWeakenType = 2;
        return 0;
    }
    return validPreTime;
}

static float justValidPreTime2(EventBean eb, float validPreTime) {
    float aValidPreTime = eb.angleValidPreTime;
    float avgSpeed = (2 * eb.lastSpeed + eb.speedValidPreTime * eb.preSpeedA) / 2;
    float angleChangeCount = 0;
    if (eb.preAngleA == 0) {
        angleChangeCount = abs(eb.angleChangeSum) / eb.realAngleTime * validPreTime;
    } else {
        angleChangeCount = abs(eb.preAngleA * aValidPreTime);
    }
    float angleR80 = 0.712f * aValidPreTime * avgSpeed;
    float angleR30 = 1.9f * aValidPreTime * avgSpeed;
    float r = 0;
    if (gLevel == 3) {
        r = (-5.2609f * angleChangeCount + 41.6039f) / 1000;
    } else if (gLevel == 2) {
        r = (-4.2745f * angleChangeCount + 32.7911f) / 1000;
    } else {
        r = (-3.28809f * angleChangeCount + 19.9932f) / 1000;
    }

    if (r > preTotalTime) {
        r = preTotalTime;
    }
    if (r <= 0 && gLevel == 1) {
        r = 0;
        return r;
    }
    if (r < preTotalTimeMin) {
        r = preTotalTimeMin;
    }
    if (r > validPreTime) {
        r = validPreTime;
    }
    return r;
}

/**
 *
 * @return
 */
static double justValidPreTimeAll() {
    int size = (int) recordList.size();
    EventBean &curEb = recordList.at(size - 1);
    //1. 取最小值
    float validPreTime = min(curEb.angleValidPreTime, curEb.speedValidPreTime);
    //2. 收敛一次
    validPreTime = justValidPreTime1(curEb, validPreTime);
    if (validPreTime == 0) {
        return 0;
    }
    //3. 收敛二次
    validPreTime = justValidPreTime2(curEb, validPreTime);
    if (validPreTime == 0) {
        return 0;
    }
    //4. 平滑收益 收益变大 变小是否取不同值 >2ms才平滑 减速见效快，加速见效慢
    if (abs(validPreTime - lastValidTime) > 0.002) {
        if (lastValidTime <= 0.001) {
            validPreTime = (validPreTime + lastValidTime) / 2;
        } else {
            double p = validPreTime / lastValidTime;
            p += 1;
            validPreTime = (p - 1) / p * lastValidTime + 1 / p * validPreTime;
        }
    }
    return validPreTime;
}


static float toRadians(float angle) {
    return float(angle * M_PI / 180);
}

static float toDegrees(float radians) {
    return float(radians / M_PI * 180);
}

static TempEbWrapper getPointByLastTimeSum(int count) {
    auto list = recordList;
    int size = list.size();
    if (count <= 0 || size < 2) {
        EventBean curEb = list.at(size - 1);
        return TempEbWrapper(curEb, curEb.realDiffAngle);
    }

    int validCount = min(size, count);
    int startIndex = size - 2;
    int minIndex = size - validCount;

    float realDiffAngle = 0;
    EventBean p1;
    int index = startIndex;
    while (index >= minIndex) {
        p1 = list.at(index + 1);
        if (abs(realDiffAngle) < abs(p1.realDiffAngle)) {
            realDiffAngle = p1.realDiffAngle;
        }
        index--;
    }
    return TempEbWrapper(list.at(minIndex), realDiffAngle);
}

static float getAngle(PointF p0, PointF p1, PointF p2) {
    //        计算向量a和向量b的点乘(dot product)：
    float dotProduct = (p1.x - p0.x) * (p2.x - p1.x) + (p1.y - p0.y) * (p2.y - p1.y);

//    计算向量a和向量b的模长(magnitude)：
    float magnitudeA = sqrt((p1.x - p0.x) * (p1.x - p0.x) + (p1.y - p0.y) * (p1.y - p0.y));
    float magnitudeB = sqrt((p2.x - p1.x) * (p2.x - p1.x) + (p2.y - p1.y) * (p2.y - p1.y));

    float x = dotProduct / (magnitudeA * magnitudeB);
    if (abs(x) > 1) {
        x /= abs(x);
    }
//    计算夹角(theta)：
    float thetaRadius = acos(x);
    float theta = ((thetaRadius / M_PI * 180) * 100) / 100;
    return theta;
}

static float getAngle(PointF p, PointF p1) {
    float angleRadius = atan2((p1.y - p.y), (p1.x - p.x));
    float ang = float(((angleRadius / M_PI * 180) * 100) / 100);
    return ang;
}

static PointF filterVildePoint(PointF p0, PointF p1, PointF p21, PointF p22) {
    auto a1 = getAngle(p0, p1, p21);
    auto a2 = getAngle(p0, p1, p22);
    if (a1 > a2) {
        return p22;
    } else {
        return p21;
    }
}

static PointF getDisC(float b, float angleB, PointF angleC) {
    //特殊处理0 和 180度
    if (angleB == 0) {
        return PointF(b, b);
    }
    float sinB = sin(toRadians(angleB));
    float sinC1 = sin(toRadians(angleC.x));
    float sinC2 = sin(toRadians(angleC.y));
    float c1 = b * sinC1 / sinB;
    float c2 = b * sinC2 / sinB;
    return PointF(c1, c2);
}

static float getAngleB(PointF p0, float angle, PointF p1) {
    float angleP1 = AngleUtils::convertHalfToAllAngle(getAngle(p0, p1));
    float angleP2 = AngleUtils::convertHalfToAllAngle(angle);
    float resultAngle = abs(angleP2 - angleP1);
    //超过180 则取小角
    if (resultAngle > 180) {
        resultAngle = 360 - resultAngle;
    }
    return resultAngle;
}

static PointF getAngleA(float a, float b, float angleB) {
    float radiusB = toRadians(angleB);
    float sinA = a * sin(radiusB) / b;
    float radiusA = (float) asin(sinA);
    float angleA = toDegrees(radiusA);
    float angleA2 = 180 - angleA;
    return PointF(angleA, angleA2);
}

static float getDis(PointF p0, PointF p1) {
    return sqrt((p1.x - p0.x) * (p1.x - p0.x) + (p1.y - p0.y) * (p1.y - p0.y));
}

static PointF getPointByAngleAndDis(float x, float y, float angle, float dis) {
    float radius = toRadians(angle);
    float px = x + cos(radius) * dis;
    float py = y + sin(radius) * dis;
    return PointF(px, py);
}

static EventBean findLastRealPoint() {
    int size = recordList.size();
    int index = size - 2;
    EventBean eb;
    while (index >= 0) {
        eb = recordList.at(index);
        if (eb.isLast) {
            return eb;
        }
        index--;
    }
    return eb;
}

static float getPhysicalDistance(PointF p1, PointF p2) {
    return (float) sqrt(pow((p1.x - p2.x) / gPxInOneMm, 2) + pow((p1.y - p2.y) / gPyInOneMm, 2));
}

// 用物理距离计算，最后再转像素
static PointF getPrePointV2(PointF p1, float angle, PointF p2, float prePhysicalLen, bool isSuccess) {
    // 1.计算垂足点D坐标
    // 求夹角b
    float angleB = getAngleB(p1, angle, p2);
    float physicalDis12 = getPhysicalDistance(p1, p2);
}

// 从垂足开始找最接近的点
static PointF getPrePointV1(PointF p1, float angle, PointF p2, float prePhysicalLen, bool isSuccess) {
    // 1.计算垂足点D坐标
    // 求夹角b
    float angleB = getAngleB(p1, angle, p2);
    float dis12 = getDis(p1, p2);
    float dis13 = sin(toRadians(angleB)) * dis12;
    float p3x = p2.x - sin(angle) * dis13;
    float p3y = p2.y - cos(angle) * dis13;
    PointF p3 = PointF(p3x, p3y);
    float physicalLen = getPhysicalDistance(p2, p3);
    if (physicalLen == prePhysicalLen) {
        LogUtil::logV("doPredictP 刚好是垂足点");
        return p3;
    }
    if (physicalLen > prePhysicalLen) {
        LogUtil::logV("doPredictP 刚好是垂足点");
        return p3;
    }
}

// 利用两个公式解方程
static PointF getPrePointV(PointF p0, float angle, PointF p1, float prePhysicalLen, bool isSuccess) {
    // 1. 正弦值
    float k = tan(toRadians(angle));
    if (angle == 0 || angle == 180) {
        LogUtil::logV("doPredictP 水平 y相同");
        k = 0;
    }
    float x31 = 0;
    float x32 = 0;
    float y31 = 0;
    float y32 = 0;
    if (angle == 90 || angle == -90) {
        LogUtil::logV("doPredictP 垂直 x相同");
        float temp = prePhysicalLen * prePhysicalLen - (p0.x - p1.x) * (p0.x - p1.x) / (gPxInOneMm * gPxInOneMm);
        if (temp < 0) {
            LogUtil::logV("doPredictP 垂直 此方程无根");
            return p1;
        }
        x31 = p0.x;
        x32 = p0.x;
        y31 = p1.y + sqrt(temp) * gPyInOneMm;
        y32 = p1.y - sqrt(temp) * gPyInOneMm;
    } else {
        double a = 1 / (gPxInOneMm * gPxInOneMm) + k * k / (gPyInOneMm * gPyInOneMm);
        double b = 2 * k * (p0.y - k * p0.x - p1.y) / (gPyInOneMm * gPyInOneMm) - 2 * p1.x / (gPxInOneMm * gPxInOneMm);
        double c = p1.x * p1.x / (gPxInOneMm * gPxInOneMm) + (p0.y - k * p0.x - p1.y) * (p0.y - k * p0.x - p1.y) / (gPyInOneMm * gPyInOneMm) - prePhysicalLen * prePhysicalLen;
        float d = b * b - 4 * a * c;
        if (d < 0) {
            if (isSuccess) {
                d = 0;
                float tempC = b * b / (4 * a);
                float tempPhysicalLen = ::sqrt(p1.x * p1.x / (gPxInOneMm * gPxInOneMm) + (p0.y - k * p0.x - p1.y) * (p0.y - k * p0.x - p1.y) / (gPyInOneMm * gPyInOneMm)-tempC);
                LogUtil::logV("doPredictP 之前有根:tempPhysicalLen=%f; prePhysicalLen=%f", tempPhysicalLen, prePhysicalLen);
            } else {
                LogUtil::logV("doPredictP 此方程无根");
                return p1;
            }
        }
        // 两个根
        x31 = (-b + sqrt(d)) / (2 * a);
        x32 = (-b - sqrt(d)) / (2 * a);
        y31 = k * (x31 - p0.x) + p0.y;
        y32 = k * (x32 - p0.x) + p0.y;
    }
    PointF p31 = PointF(x31, y31);
    PointF p32 = PointF(x32, y32);
    // 验证哪个根是对的
    float angle1 = getAngle(p1, p31);
    float angle2 = getAngle(p1, p32);
    if (abs(angle1 - angle) < abs(angle2 - angle)) {
        return p31;
    } else {
        return p32;
    }
}

static PointF getPrePointInner(PointF p0, float angle, PointF p1, float prePxLen) {
    //1.求夹角b
    float angleB = getAngleB(p0, angle, p1);
    //b极小的时候应当认为是直线
    //        val sinB = sin(angleB)
    float sinB = sin(toRadians(angleB));
    float disA = getDis(p0, p1);
    float disB = prePxLen;
    if (angleB == 0 || (angleB < 1 && sinB * (prePxLen + disA) < 1)) {//同向直线 //todo try 待优化 特殊处理才行
        disB = prePxLen + disA;
        PointF p = getPointByAngleAndDis(p0.x, p0.y, angle, disB);
        return p;
    }
    //2.求角a - >
    PointF angleA = getAngleA(disA, disB, angleB);

    //3.求角c
    float angleC1 = 180 - angleA.x - angleB;
    float angleC2 = 180 - angleA.y - angleB;
    //出现负角 则表示无法构成三角形
    if (angleC1 < 0) {
        angleC1 = 0;
    }
    if (angleC2 < 0) {
        angleC2 = 0;
    }
    PointF angleC = PointF(angleC1, angleC2);

    //4.求c长
    PointF disC = getDisC(disB, angleB, angleC);
    //5.求坐标
    PointF p21 = getPointByAngleAndDis(p0.x, p0.y, angle, disC.x);
    PointF p22 = getPointByAngleAndDis(p0.x, p0.y, angle, disC.y);
    //6.筛选符合的坐标
    PointF realP = filterVildePoint(p0, p1, p21, p22);
    return realP;
}

// 物理长度计算像素长度
static float getPrePxLength(float angle, float prePhysicalLen) {
    // 1. 正弦值
    float k = tan(toRadians(angle));
    if (angle == 0 || angle == 180) {
        LogUtil::logV("doPredictP 水平 y相同");
        k = 0;
    }
    float prePxLen = 0;
    if (angle == 90 || angle == -90) {
        LogUtil::logV("doPredictP 垂直 x相同");
        prePxLen = prePhysicalLen * gPyInOneMm;
    } else {
        prePxLen = ::sqrt((1 + k * k) / (gPyInOneMm * gPyInOneMm + k * k * gPxInOneMm * gPxInOneMm)) * prePhysicalLen * gPxInOneMm * gPyInOneMm;
    }
    return prePxLen;
}

PointF PrePointUtils::getPrePoint() {
    int size = (int) recordList.size();
    EventBean &curEb = recordList.at(size - 1);
    //正常收敛
    double validPreTime = justValidPreTimeAll();
    curEb.lastValidPreTime = validPreTime;
    if (validPreTime <= 0) { //预测不出来
        return PointF(curEb.x, curEb.y);
    }
    int validPreCount = 24;
    if (gLevel == 1 && validPreTime < pointTimeInternal) {
        validPreCount = 24;
    } else {
        validPreCount = 24 - static_cast<int>((validPreTime / pointTimeInternal + 0.45));
    }

    TempEbWrapper ebWrapper = getPointByLastTimeSum(validPreCount);
    //5. 拐点不预测 调整收益
    if (abs(ebWrapper.maxRealDiff) > 55) {
        return PointF(curEb.x, curEb.y);
    }

    lastValidTime = validPreTime;
    EventBean p0 = ebWrapper.eb;
    //路程公式 s = v0t + 1/2 * a *t^2
    float v0 = curEb.lastSpeed;
    float a = curEb.preSpeedA;
    // 此处得到的是物理预测长度
    float prePhysicalLen = v0 * validPreTime + 0.5 * a * validPreTime * validPreTime;
    EventBean p1 = curEb;
    double av0 = AngleUtils::convertHalfToAllAngle(p1.lastAngle);
    double aa = curEb.preAngleA;
    double preAngle = av0 + aa * validPreTime;
    double preAngleHalf = AngleUtils::convertAllToHalfAngle(preAngle);
    float prePxLen = getPrePxLength(preAngleHalf, prePhysicalLen);
    PointF pResult = getPrePointInner(PointF(p0.x, p0.y), preAngleHalf, PointF(p1.x, p1.y), prePxLen);
    if (isinf(pResult.x) || isinf(pResult.y) || isnan(pResult.x) || isnan(pResult.y)) {
        LogUtil::logV("doPredictP o or nan");
        pResult = getPointByAngleAndDis(p1.x, p1.y, p1.preAngle, prePxLen);
    }
    return pResult;
}


void PrePointUtils::fillHisPoints(PointF p1) {
    EventBean eb = recordList.at(recordList.size() - 1);
    float p = eb.p;
    if (recordList.size() > 1) {
        EventBean ebLast = recordList.at(recordList.size() - 2);
        p = p + (eb.p - ebLast.p) / 2.0;
    }
    if (p < 0 || p > 1) {//todo try 后期优化，最大值应该使用压感的最大限制（即压感等级）
        p = eb.p;
    }

    float validPreTime = eb.lastValidPreTime;
    //处理x轴和y轴方向的数据
    float dx = p1.x - eb.x;
    float dy = p1.y - eb.y;
    float time = validPreTime;
    float timeInternal = 0.004;
    int countMax = (int) (validPreTime / timeInternal);
    prePointsInner.clear();
    if (validPreTime <= 0) {
        EventBean preEb = EventBean(p1.x, p1.y, p, eb.time, false);
        prePointsInner.push_back(preEb);
        return;
    }
    float tempX = 0;
    float tempY = 0;
    long long tempTime = eb.time;
    for (int i = 0; i < countMax + 1; ++i) {
        time = timeInternal * (float) (i + 1);
        if (i >= countMax) {//最后一个
            time = validPreTime;
        }
        tempX = eb.x + dx * (time / validPreTime);
        tempY = eb.y + dy * (time / validPreTime);
        tempTime = eb.time + (long long) (time * 1000);
        EventBean preEb = EventBean(tempX, tempY, p, tempTime, false);
        prePointsInner.push_back(preEb);
    }
}
