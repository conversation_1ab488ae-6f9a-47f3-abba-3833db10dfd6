#include <jni.h>
#include <string>
#include <vector>
#include <list>
#include <ctime>

#include "FilterEngine.h"
#include "LogUtil.h"
#include "EventBean.h"
#include "SpeedUtils.h"
#include "AngleUtils.h"
#include "PrePointUtils.h"

using namespace std;

int gLevel = 2;
float gPxInOneMm = 10;
float gPyInOneMm = 10;
bool isCurOpen = false;

float preTotalTime = 0.027f;
float preTotalTimeMin = 0.005f;
float pointTimeInternal = 0.004;
vector<EventBean> recordList;
vector<EventBean> prePointsInner;


jclass gPbClass = nullptr;
jmethodID gPbConstruct = nullptr;
jfieldID gX = nullptr;
jfieldID gY = nullptr;
jfieldID gPressure = nullptr;
jfieldID gEventTime = nullptr;

jfieldID gInnerRadius = nullptr;
jfieldID gInnerRegularRadius = nullptr;
jfieldID gInnerRegularMsg = nullptr;
jfieldID gInnerRadiusFactor = nullptr;
jfieldID gInnerProfitRate = nullptr;
jfieldID gInnerDisRate = nullptr;
jfieldID gPredLength = nullptr;

bool gIsInner = true;
int gLogLevel = 2;
bool gIsOpenFastPred = true;
int gMaxSpeed = 300;
int realPointCount = 0;
extern float lastValidTime;

/**
 * 其他常见的Java基本类型签名包括：
"B": byte
"C": char
"D": double
"F": float
"I": int
"J": long
"S": short
"Z": boolean
"[": 数组类型（后面通常会跟着数组元素的类型签名）
"L<classname>;": 对象类型（<classname> 是完整的类名，包括包名）
 */
extern "C"
JNIEXPORT void JNICALL
Java_com_jideos_predengine_PredCore_init(JNIEnv *env, jobject thiz, jobject context, jboolean isInner) {
    gIsInner = isInner;
    jclass pbClass = env->FindClass("com/jideos/predengine/PointBean");
    gPbClass = static_cast<jclass>(env->NewGlobalRef(pbClass));
    gPbConstruct = env->GetMethodID(pbClass, "<init>", "()V");
    gX = env->GetFieldID(gPbClass, "x", "F");
    gY = env->GetFieldID(gPbClass, "y", "F");
    gPressure = env->GetFieldID(gPbClass, "pressure", "F");
    gEventTime = env->GetFieldID(gPbClass, "eventTime", "J");
    if (gIsInner) {
//        gInnerRadius = env->GetFieldID(gPbClass, "radius", "F");
//        gInnerRegularRadius = env->GetFieldID(gPbClass, "regularRadius", "F");
//        gInnerRegularMsg = env->GetFieldID(gPbClass, "regularMsg", "Ljava/lang/String;");
//        gInnerRadiusFactor = env->GetFieldID(gPbClass, "radiusFactor", "F");
//        gInnerProfitRate = env->GetFieldID(gPbClass, "profitRate", "F");
//        gInnerDisRate = env->GetFieldID(gPbClass, "disRate", "F");
//        gPredLength = env->GetFieldID(gPbClass, "predLength", "F");
    }
    LogUtil::logD("doPredict init2 Version = V14 gIsInner：%d;", gIsInner);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_jideos_predengine_PredCore_start(JNIEnv *env, jobject thiz, jfloat x, jfloat y, jfloat p, jlong time) {

}

extern "C"
JNIEXPORT void JNICALL
Java_com_jideos_predengine_PredCore_setJLogLevel(JNIEnv *env, jobject thiz,  jint logLevel) {
    gLogLevel = logLevel;
    LogUtil::logD("doPredict setLogLevel : %d;", gLogLevel);
}

static EventBean& getEventBean(JNIEnv *env, jobject jobj, bool isLast) {
    jfloat x = env->GetFloatField(jobj, gX);
    jfloat y = env->GetFloatField(jobj, gY);
    jfloat p = env->GetFloatField(jobj, gPressure);
    jlong time = env->GetLongField(jobj, gEventTime);
    EventBean eb = EventBean(x, y, p, time, isLast);
    return eb;
}

extern "C"
JNIEXPORT jobjectArray JNICALL
Java_com_jideos_predengine_PredCore_doPredict(JNIEnv *env, jobject thiz, jobject realPoint, jobjectArray hisPoints, jboolean isStylus) {
    realPointCount++;
    EventBean realEb = getEventBean(env, realPoint, true);

    int historySize = 0;
    if (hisPoints != nullptr) {
        historySize = env->GetArrayLength(hisPoints);
    }
    // 没有历史点
    if (historySize == 0) {
        recordList.push_back(realEb);
        SpeedUtils::handSpeed(true);
        AngleUtils::handAngle(true);
        LogUtil::logV("doPredict history size is 0");
        return nullptr;
    }

    // 有历史点
    for (int i = 0; i <= historySize - 1; ++i) {
        jobject hisObj = env->GetObjectArrayElement(hisPoints, i);
        bool isLast = i == historySize - 1;
        bool isNeedPre = isLast && realPointCount > 3;
        EventBean hisEb = getEventBean(env, hisObj, isLast);
        recordList.push_back(hisEb);
        SpeedUtils::handSpeed(isNeedPre);
        AngleUtils::handAngle(isNeedPre);
        if (isNeedPre) {
            if (isStylus && (realEb.x != hisEb.x || realEb.y != hisEb.y)) {
                LogUtil::logV("doPredict last history point is exception");
                // return nullptr;
            }
            EventBean curEb = recordList.at(recordList.size() - 1);

            if (FilterEngine::isFastLimit(gIsOpenFastPred, curEb.realSingleSpeed, gMaxSpeed)) {
                LogUtil::logV("doPredict fastSpeedLimit");
                return nullptr;
            }
            if (curEb.preSpeedType == 0 || curEb.preSpeedType == 4 || curEb.preAngleType == 0 || curEb.preAngleType == 4) {
                LogUtil::logV("doPredict type not need pred");
                return nullptr;
            }
            // 需要预测
            PointF p1 = PrePointUtils::getPrePoint();
            PrePointUtils::fillHisPoints(p1);
            int size = prePointsInner.size();
            if (size == 0) {
                return nullptr;
            }
            // 封装当前历史点
            jobjectArray predPoints = env->NewObjectArray(size+1, gPbClass, nullptr);

            //添加第一个真实点
            jobject realPointBean = env->NewObject(gPbClass, gPbConstruct);
            env->SetFloatField(realPointBean, gX, realEb.x);
            env->SetFloatField(realPointBean, gY, realEb.y);
            env->SetFloatField(realPointBean, gPressure, realEb.p);
            env->SetLongField(realPointBean, gEventTime, realEb.time);
            env->SetObjectArrayElement(predPoints, 0, realPointBean);


            for (int i = 0; i < size; ++i) {
                EventBean preEb = prePointsInner.at(i);
                jobject pointBean = env->NewObject(gPbClass, gPbConstruct);
                env->SetFloatField(pointBean, gX, preEb.x);
                env->SetFloatField(pointBean, gY, preEb.y);
                env->SetFloatField(pointBean, gPressure, preEb.p);
                env->SetLongField(pointBean, gEventTime, preEb.time);
                env->SetObjectArrayElement(predPoints, i+1, pointBean);
            }
            if (recordList.size() > 50) {
                int length = recordList.size() - 50;
                recordList.erase(recordList.begin(), recordList.begin() + length);
            }
            return predPoints;
        }
    }
    return nullptr;
}

extern "C"
JNIEXPORT void JNICALL
Java_com_jideos_predengine_PredCore_reset(JNIEnv *env, jobject thiz) {
    recordList.clear();
    realPointCount = 0;
    lastValidTime = 0;
}

extern "C"
JNIEXPORT void JNICALL
Java_com_jideos_predengine_PredCore_release(JNIEnv *env, jobject thiz) {
    env->DeleteGlobalRef(gPbClass);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_jideos_predengine_PredCore_setJXdpi(JNIEnv *env, jobject thiz, jfloat xdpi, jfloat ydpi) {
    LogUtil::logD("doPredict setXYdpi : %f; ydpi : %f", xdpi, ydpi);
    gPxInOneMm = xdpi / 25.4f;
    gPyInOneMm = ydpi / 25.4f;
}
extern "C"
JNIEXPORT void JNICALL
Java_com_jideos_predengine_PredCore_setJPredLevel(JNIEnv *env, jobject thiz, jint pred_level) {
    gLevel = pred_level;
    LogUtil::logD("doPredict setPredLevel : %d;", gLevel);
    if (gLevel == 1) {
        preTotalTime = 0.014;
        preTotalTimeMin = 0.002;
        return;
    }
    if (gLevel == 2) {
        preTotalTime = 0.027;
        preTotalTimeMin = 0.005;
        return;
    }
    if (gLevel == 3) {
        preTotalTime = 0.033;
        preTotalTimeMin = 0.009;
        return;
    }
}
extern "C"
JNIEXPORT void JNICALL
Java_com_jideos_predengine_PredCore_setJRefreshRate(JNIEnv *env, jobject thiz, jfloat refresh_rate) {

}
extern "C"
JNIEXPORT void JNICALL
Java_com_jideos_predengine_PredCore_setJPressureLevel(JNIEnv *env, jobject thiz, jint pressure_level) {

}
extern "C"
JNIEXPORT void JNICALL
Java_com_jideos_predengine_PredCore_setJPenReportRate(JNIEnv *env, jobject thiz, jfloat pen_report_rate) {

}
extern "C"
JNIEXPORT void JNICALL
Java_com_jideos_predengine_PredCore_setJScreenReportRate(JNIEnv *env, jobject thiz, jfloat screen_report_rate) {

}
extern "C"
JNIEXPORT void JNICALL
Java_com_jideos_predengine_PredCore_setJPredType(JNIEnv *env, jobject thiz, jint pred_type) {

}

extern "C"
JNIEXPORT void JNICALL
Java_com_jideos_predengine_PredCore_setJScreenPx(JNIEnv *env, jobject thiz, jfloat x, jfloat y) {
}

extern "C"
JNIEXPORT void JNICALL
Java_com_jideos_predengine_PredCore_isJOpenFastPred(JNIEnv *env, jobject thiz, jboolean is_open) {
    gIsOpenFastPred = is_open;
}

extern "C"
JNIEXPORT void JNICALL
Java_com_jideos_predengine_PredCore_setJMaxSpeed(JNIEnv *env, jobject thiz, jint max_speed) {
    gMaxSpeed = max_speed;
}
