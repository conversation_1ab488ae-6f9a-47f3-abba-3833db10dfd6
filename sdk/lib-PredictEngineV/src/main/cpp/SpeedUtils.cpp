//
// Created by Admin on 2023/11/20.
//

#include <vector>

#include "SpeedUtils.h"
#include "EventBean.h"
#include "LogUtil.h"

int gSpeedRealPointSize = 16;
int gSpeedPrePointSize = 16;
float speedLimitOne = 0.05;
float speedLimitFit = 0.1;

extern vector<EventBean> recordList;
extern int gLevel;
extern float preTotalTime;
extern float pointTimeInternal;
extern EventBean &lastRealEb;
float lastRealSingleSpeed = 0;

static void calcRealSpeed(int size) {
    EventBean &curEb = recordList[size - 1];
    int pointSize = gSpeedRealPointSize;
    if (pointSize > size) {
        pointSize = size;
    }
    curEb.realSpeedPointSize = pointSize;
    float sumDistance = 0;
    long long totalTime = 0;
    bool isHandleSingle = false;
    for (int i = size - 1; i > size - pointSize; --i) {
        EventBean &eb = recordList[i];
        EventBean lastEb = recordList[i - 1];
        if (i == size - 1 && i - 1 >= 0) {
            float dis = eb.getPhysicalDistance(lastEb);
            eb.lastDis = dis;
        }
        sumDistance += eb.lastDis;
        if (i == size - pointSize + 1) {
            totalTime = curEb.time - lastEb.time;
        }
        if (!isHandleSingle && lastEb.isLast) {
            long long singleTime = curEb.time - lastEb.time;
            float realSingleSpeed = sumDistance / (float) singleTime * 1000;
            curEb.realSingleSpeed = realSingleSpeed;
            lastRealSingleSpeed = lastEb.realSingleSpeed;
            isHandleSingle = true;
        }

    }
    float realListSpeed = sumDistance / (float) totalTime * 1000;
    curEb.realListSpeed = realListSpeed;
    if (!isHandleSingle) {
        curEb.realSingleSpeed = realListSpeed;
    }
}

static float getRegularValidPreTime() {
    if (gLevel == 1) {
        return 0.0004;
    }
    if (gLevel == 2) {
        return 0.008;
    }
    if (gLevel == 3) {
        return 0.016;
    }
    return 0.008;
}

static void calcLastSpeed(EventBean &curEb, int size, int pointSize, float validPreTime) {
    EventBean lastEb = recordList.at(size - 2);
    if (curEb.realListSpeed > 100) {
        LogUtil::logV("doPredict 符合快速,取最后两次平均值");
        float preSpeed = (curEb.realListSpeed + lastEb.realListSpeed) / 2;
        curEb.preSpeedType = 6;
        curEb.preSpeedPointSize = pointSize;
        curEb.preSpeed = preSpeed;
        curEb.lastSpeed = curEb.realListSpeed;
        curEb.preSpeedA = 0;
        curEb.speedValidPreTime = validPreTime;
        return;
    }
    LogUtil::logV("doPredict 符合无规律");
    curEb.preSpeedType = 4;
    curEb.preSpeedPointSize = pointSize;
    curEb.preSpeed = curEb.realListSpeed;
    curEb.lastSpeed = curEb.realListSpeed;
    curEb.preSpeedA = 0;
    curEb.speedValidPreTime = 0;
}

static void calcFitSpeed(EventBean &curEb, int size, int pointSize, float validPreTime) {
    // 非匀速，做一次拟合
    float sumX = 0;
    float sumX2 = 0;
    float sumXY = 0;
    float sumY = 0;
    float tempX = 0;
    for (int i = size - pointSize; i <= size - 1; ++i) {
        float speed = recordList.at(i).realListSpeed;
        tempX = pointTimeInternal * (i - size + pointSize + 1);
        sumX += tempX;
        sumX2 += tempX * tempX;
        sumXY += tempX * speed;
        sumY += speed;
    }
    float a = (pointSize * sumXY - sumX * sumY) / (pointSize * sumX2 - sumX * sumX);
    float b = (sumY - a * sumX) / pointSize;
    LogUtil::logV("doPredict 一次拟合 a:%f; b:%f;", a, b);
    float sumPointToLine = 0;
    float tempY = 0;
    vector<float> points;
    for (int i = size - pointSize; i <= size - 1; ++i) {
        float speed = recordList.at(i).realListSpeed;
        tempX = pointTimeInternal * (i - size + pointSize + 1);
        tempY = a * tempX + b;
        points.push_back(tempY - speed);
        sumPointToLine += tempY - speed;
    }
    float avgPointToLine = sumPointToLine / pointSize;
    float fcSum = 0;
    for (int i = 0; i < pointSize; ++i) {
        fcSum += pow(points.at(i) - avgPointToLine, 2);
    }
    float bzcOne = sqrt(fcSum / (float) pointSize);
    float preStartTime = pointSize * pointTimeInternal;
    float lastSpeedNew = a * preStartTime + b;
    float rate = bzcOne / lastSpeedNew;
    if (rate < speedLimitFit) {
        LogUtil::logV("doPredict 符合一次拟合");
        float speedZeroTime = -b / a;
        float preX = preStartTime + validPreTime;
        if (speedZeroTime > 0 && preX > speedZeroTime) {
            validPreTime = speedZeroTime - preStartTime;
        }
        if (validPreTime < 0) {
            validPreTime = 0;
        }
        preX = preStartTime + validPreTime;
        float preSpeed = a * preX + b;
        curEb.preSpeedType = 5;
        curEb.preSpeedPointSize = pointSize;
        curEb.preSpeed = preSpeed;
        curEb.lastSpeed = curEb.realListSpeed;
        curEb.preSpeedA = a;
        curEb.speedValidPreTime = validPreTime;
        return;
    }
    calcLastSpeed(curEb, size, pointSize, validPreTime);
}

static void calcPreSpeed(int size) {
    EventBean &curEb = recordList[size-1];
    int pointSize = gSpeedPrePointSize;
    if (pointSize > size) {
        pointSize = size;
    }
    // 平均值
    float sumRealSpeed = 0;
    for (int i = size - 1; i >= size - pointSize; --i) {
        sumRealSpeed += recordList.at(i).realListSpeed;
    }
    float avg = sumRealSpeed / (float) pointSize;
    float sumFcSpeed = 0;
    // 最大极值
    float maxDiff = 0;
    for (int i = size - 1; i >= size - pointSize; --i) {
        EventBean eb = recordList[i];
        float temp = eb.realListSpeed - avg;
        sumFcSpeed += pow(temp, 2);
        if (abs(temp) > abs(maxDiff)) {
            maxDiff = temp;
        }
    }
    // 标准差
    float bzc = sqrt(sumFcSpeed / (float) pointSize);
    // 可预测时间
    float validPreTime = preTotalTime;
    float bzcRate = bzc / avg;
    float maxDiffRate = abs(maxDiff) / avg;
//    if ((curEb.realSingleSpeed - lastRealSingleSpeed) / curEb.realSingleSpeed < -0.5 && avg > 30) {
//        validPreTime = getRegularValidPreTime();
//    }
    if ((bzcRate < speedLimitOne && maxDiff >= 0) || (maxDiffRate < speedLimitOne && maxDiff < 0)) {
        // 匀速
        LogUtil::logV("doPredict 符合匀速");
        curEb.preSpeedType = 1;
        curEb.preSpeedPointSize = pointSize;
        curEb.preSpeed = avg;
        curEb.lastSpeed = avg;
        curEb.preSpeedA = 0;
        curEb.speedValidPreTime = validPreTime;
        return;
    }
    calcFitSpeed(curEb, size, pointSize, validPreTime);
}

void SpeedUtils::handSpeed(bool isNeedPre) {
    int size = (int) recordList.size();
    if (size <= 1) {
        return;
    }
    calcRealSpeed(size);
    if (isNeedPre) {
        calcPreSpeed(size);
    }
}



