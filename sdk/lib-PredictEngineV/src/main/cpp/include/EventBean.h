//
// Created by <PERSON><PERSON> on 2023/10/25.
//

#ifndef ESTIMATEDEMO_EVENTBEAN_H
#define ESTIMATEDEMO_EVENTBEAN_H

class EventBean {

public:
    // 点相关信息
    float x;
    float y;
    float p;
    long long time;
    bool isLast;
    // 距离上一个点的距离
    float lastDis = 0;

    // 真实速度
    int realSpeedPointSize = 0;
    float realListSpeed =0;
    float realSingleSpeed = 0;
    // 预测速度相关
    int preSpeedType = 0;
    int preSpeedPointSize =0;
    float preSpeed = 0;
    float lastSpeed = 0;
    float preSpeedA = 0;
    float speedValidPreTime = 0;

    // 真实角度
    int realAnglePointSize = 0;
    float realAngle = 0;//半角制
    float realAngleTemp = 0;//计算使用的真实角度，目前主要用于在350-10度之间  计算均值和标准差。
    float realAngle4 = 0;
    float realDiffAngle = 0;
    float realAngleTime = 0;
    // 预测角度相关
    int preAngleType = 0;
    int preAnglePointSize = 0;
    float preAngle = 0;
    float lastAngle = 0;
    float preAngleA = 0;
    float angleValidPreTime = 0;
    float lastValidPreTime = 0;
    float realAngleTimeInternal = 0;
    float angleChangeSum = 0;
    bool hasLowAngleChangeFeature = false;
    int timeWeakenType = 0;

public:
    EventBean();

    EventBean(float x, float y, float p, long long time,bool isHis);

    float getPhysicalDistance(EventBean curP) const;

    float getAngle(EventBean curP) const;
};

#endif
