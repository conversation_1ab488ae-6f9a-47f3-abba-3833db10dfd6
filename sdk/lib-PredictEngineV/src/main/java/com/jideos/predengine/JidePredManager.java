package com.jideos.predengine;

import android.content.Context;
import android.view.MotionEvent;
import com.jideos.predengine.PointBean;

public class JidePredManager {

    PredCore predCore;

    public JidePredManager(Context context) {
        predCore = new PredCore(context);
    }

    /**
     * 主动
     * 设置预测开关
     *
     * @param predType 0:关闭预测功能 1：仅开启手写笔预测
     *                 2：仅开启手指触摸预测  3：手写笔和手指均开启，但同时刻只有手指或者只有手写笔有效。
     */
    public void setPredType(int predType) {
        predCore.setPredType(predType);
    }

    /**
     * 设置log级别
     * @param logLevel VERBOSE:2, DEBUG:3 ,INFO:4
     *
     */
    public void setLogLevel(int logLevel) {
        predCore.setLogLevel(logLevel);
    }

    /**
     * 主动
     * 设置预测等级，预测等级不同，收益和精确度不一样，详情参考验收文档1.1.6版本
     *
     * @param predLevel 1:等级一 2：等级二 3：等级三 其他默认为2：等级二
     */
    public void setPredLevel(int predLevel) {
        predCore.setPredLevel(predLevel);
    }

    /**
     * 设置xdpi(X维度屏幕每英寸的精确物理像素)和ydpi
     * 用于计算当前设备一毫米包含多少像素。计算方式：xdpi/25.4  即当前屏幕1mm所包含的像素点数
     *
     * @param xdpi 一英寸的像素点数 可通过context.resources.displayMetrics.xdpi获取
     * @param ydpi 一英寸的像素点数 可通过context.resources.displayMetrics.ydpi获取
     */
    public void setXYDpi(float xdpi, float ydpi) {
        predCore.setXYdpi(xdpi, ydpi);
    }

    /**
     * 预测
     *
     * @param event 当前touch事件：所有事件均传递（down、move、up、cancel等）
     * @return Array<PointBean>? 预测结果数据列表，无预测则返回为null。收益最高在队尾即last
     * @see PointBean
     * @notice 全事件均需要传递。比如：down  move。。  up 、cancel  。。。。
     * @see PointBean
     */
    public PointBean[] doPredict(MotionEvent event) {
        return predCore.doPredict(event);
    }

    /**
     * 释放资源
     * 不需要预测后则释放资源。
     */
    public void release() {
        predCore.release();
    }

    /**
     * 设置屏幕分辨率
     *
     * @param x x纬度分辨率
     * @param y y纬度分辨率
     */
    public void setScreenPx(float x, float y) {
        predCore.setScreenPx(x, y);
    }

    /**
     * 设置屏幕刷新率（多次）
     * 可通过如下方式获取：
     * val displayManager: DisplayManager =
     * context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
     * val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
     * var refreshRate = defaultDisplay.refreshRate
     *
     * @param refreshRate 屏幕刷新率
     */
    public void setRefreshRate(Float refreshRate) {
        predCore.setRefreshRate(refreshRate);
    }

    /**
     * 设置压感上限
     *
     * @param pressureLevel 手写笔最大压感值：大于0的任意整数
     */
    public void setPressureLevel(int pressureLevel) {
        predCore.setPressureLevel(pressureLevel);
    }

    /**
     * 设置手写笔的报点率（）
     *
     * @param penReportRate 手写笔的报点率（即一秒钟手写笔产生的点数）
     *                      可以为任意大于0的数 例如：120、240、480
     * @notice 每支笔报一次
     */
    public void setPenReportRate(float penReportRate) {
        predCore.setPenReportRate(penReportRate);
    }

    /**
     * 设置触摸屏幕报点率
     *
     * @param screenReportRate 触摸屏幕报点率
     */
    public void setScreenReportRate(float screenReportRate) {
        predCore.setScreenReportRate(screenReportRate);
    }

    /**
     * 设置是否打开快速（高速）预测
     *
     * @param isOpen true 打开 false 关闭
     */
    public void isOpenFastPred(boolean isOpen) {
        predCore.isOpenFastPred(isOpen);
    }

    /**
     * 设置最大预测速度限制
     *
     * @param maxSpeed 最大的预测速度限制值,必须结合关闭高速预测才有效
     */
    public void setMaxSpeed(int maxSpeed) {
        predCore.setMaxSpeed(maxSpeed);
    }

}
