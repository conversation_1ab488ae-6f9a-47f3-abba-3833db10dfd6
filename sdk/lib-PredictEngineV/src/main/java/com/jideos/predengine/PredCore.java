package com.jideos.predengine;

import android.content.Context;
import android.util.Log;
import android.view.MotionEvent;
import com.jideos.predengine.Constants;
import com.jideos.predengine.PointBean;

public class PredCore {

    public PredCore(Context context) {
        try {
            System.loadLibrary("prediction");
            init(context, Constants.IS_INNER);
            Log.d("", "load native library successfully!");
        } catch (UnsatisfiedLinkError var1) {
            Log.d("", "failed to load native library: " + var1.getMessage());
        }
    }

    public void setLogLevel(int logLevel) {
        setJLogLevel(logLevel);
    }

    public void setXYdpi(float xdpi, float ydpi) {
        setJXdpi(xdpi, ydpi);
    }

    public void setPredLevel(int predLevel) {
        setJPredLevel(predLevel);
    }

    public void setRefreshRate(float refreshRate) {
        setJRefreshRate(refreshRate);
    }

    public void setPressureLevel(int pressureLevel) {
        setJPressureLevel(pressureLevel);
    }

    public void setPenReportRate(float penReportRate) {
        setJPenReportRate(penReportRate);
    }

    public void setScreenReportRate(float screenReportRate) {
        setJScreenReportRate(screenReportRate);
    }

    public void setPredType(int predType) {
        this.mPredType = predType;
    }

    public void isOpenFastPred(boolean isOpen) {
        isJOpenFastPred(isOpen);
    }

    public void setMaxSpeed(int maxSpeed) {
        setJMaxSpeed(maxSpeed);
    }

    public void setScreenPx(float x, float y) {
        setJScreenPx(x, y);
    }

    private int mPredType = 3;

    private boolean isOpenPred = false;

    private void isOpenPred(MotionEvent event) {
        int toolType = event.getToolType(0);
        if (mPredType == 0) {
            isOpenPred = false;
            return;
        }
        if (toolType == MotionEvent.TOOL_TYPE_FINGER && mPredType == 1) {
            isOpenPred = false;
            return;
        }
        if (toolType == MotionEvent.TOOL_TYPE_STYLUS && mPredType == 2) {
            isOpenPred = false;
            return;
        }
        isOpenPred = true;
    }

    public PointBean[] doPredict(MotionEvent event) {
        PointBean[] result = null;
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                isOpenPred(event);
                reset();
                start(event.getX(), event.getY(), event.getPressure(), event.getEventTime());
                break;
            case MotionEvent.ACTION_MOVE:
                if (isOpenPred) {
                    int toolType = event.getToolType(0);
                    boolean isStylus = toolType == MotionEvent.TOOL_TYPE_STYLUS;
                    PointBean curP = new PointBean();
                    curP.setX(event.getX());
                    curP.setY(event.getY());
                    curP.setEventTime(event.getEventTime());
                    curP.setPressure(event.getPressure());
                    result = doPredict(curP, getHistory(event), isStylus);
                }
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                reset();
                break;
        }
        return result;
    }

    private PointBean[] getHistory(MotionEvent event) {
        int size = event.getHistorySize();
        int toolType = event.getToolType(0);
        if (size > 0) {
            PointBean[] historys = new PointBean[size];
            for (int i = 0; i < size; i++) {
                PointBean pointBean = new PointBean();
                pointBean.setX(event.getHistoricalX(i));
                pointBean.setY(event.getHistoricalY(i));
                pointBean.setEventTime(event.getHistoricalEventTime(i));
                pointBean.setPressure(event.getHistoricalPressure(i));
                historys[i] = pointBean;
            }
            return historys;
        }
        return null;
    }

    private native void init(Context context, boolean isInner);

    private native void setJLogLevel(int logLevel);

    private native void setJXdpi(float xdpi, float ydpi);

    private native void setJPredLevel(int predLevel);

    private native void setJRefreshRate(float refreshRate);

    private native void setJPressureLevel(int pressureLevel);

    private native void setJPenReportRate(float penReportRate);

    private native void setJScreenReportRate(float screenReportRate);

    private native void setJScreenPx(float x, float y);

    private native void start(float x, float y, float p, long time);

    private native PointBean[] doPredict(PointBean realP, PointBean[] hisP, boolean isStylus);

    private native void isJOpenFastPred(boolean isOpen);

    private native void setJMaxSpeed(int maxSpeed);

    private native void reset();

    native void release();

}
